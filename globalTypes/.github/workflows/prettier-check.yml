name: Prettier Check

on:
  push:
    branches:
      - main
      - dev
  pull_request:
    branches:
      - main
      - dev

jobs:
  prettier-check:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Code
        uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'

      - name: Install Dependencies
        run: npm install prettier --save-dev

      - name: Run Prettier Check
        run: npx prettier --check "ndrBenchmarkTypes/**/*.ts" "FirewallTypes.ts"
