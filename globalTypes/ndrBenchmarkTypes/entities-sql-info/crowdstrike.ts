export interface CrowdStrikeInfoSqlProperties {
  agentLoadFlags: number;
  agentLocalTime: string;
  agentVersion: string;
  biosManufacturer?: string;
  biosVersion?: string;
  buildNumber?: string;
  cid: string;
  configIdBase: number;
  configIdBuild: number;
  configIdPlatform: number;
  cpuSignature: number;
  deviceId: string;
  email?: string;
  externalIp: string;
  firstSeen: string;
  hostname: string;
  instanceId?: string;
  lastLoginTimestamp?: string;
  localIp?: string;
  macAddress?: string;
  machineDomain?: string;
  majorVersion?: number;
  minorVersion?: number;
  notes?: string;
  osVersion: string;
  platformId: number;
  platformName: string;
  podId?: string;
  productTypeDesc?: string;
  provisionStatus?: string;
  firewallEnabled: boolean;
  reducedFunctionalityMode?: string;
  serialNumber?: string;
  serviceProvider?: string;
  serviceProviderAccountId?: string;
  siteName?: string;
  slowChangingModifiedTimestamp?: string;
  status: string;
  systemManufacturer: string;
  systemProductName: string;
  zoneGroup?: string;
  title: string;
  ou?: string[];
  ips: string[];
}
