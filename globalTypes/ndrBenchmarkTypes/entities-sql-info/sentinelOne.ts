export interface SentinelOneInfoSqlProperties {
  id: string;
  accountId: string;
  accountName: string;
  computerName: string;
  agentVersion: string;
  osType: string;
  osName: string;
  osRevision: string;
  createdAt: Date;
  updatedAt: Date;
  activeThreats: number;
  isActive: boolean;
  infected: boolean;
  lastActiveDate: Date;
  activeDirectory: Record<string, any>;
  appsVulnerabilityStatus: string;
  cloudProviders: string; // JSON.stringify()’d object
  containerizedWorkloadCounts: number;
  coreCount: number;
  cpuCount: number;
  cpuId: string;
  domain: string;
  externalId: string;
  externalIp: string;
  firewallEnabled: boolean;
  groupId: string;
  groupIp: string;
  groupName: string;
  isDecommissioned: boolean;
  isUninstalled: boolean;
  lastLoggedInUserName: string;
  lastSuccessfulScanDate: Date;
  locationType: string;
  machineSid: string;
  machineType: string;
  modelName: string;
  networkQuarantineEnabled: boolean;
  networkStatus: string;
  osArch: string;
  osUsername: string;
  siteId: string;
  siteName: string;
  totalMemory: number;
  uuid: string;
  ips: string[];
}
