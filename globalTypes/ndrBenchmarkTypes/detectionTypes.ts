import { CloudEntity } from './entityTypes';
import { Severity } from './issueTypes';
import { UniqueTraffic } from './trafficPatternTypes';

export const DETECTION_STATUS = {
  OPEN: 'open',
  RESOLVED: 'resolved',
  DISMISSED: 'dismissed'
} as const;

export const ORIGIN_TYPES = {
  PORT0: 'port0',
  USER: 'user'
} as const;

export type DetectionStatus =
  (typeof DETECTION_STATUS)[keyof typeof DETECTION_STATUS];
export type OriginType = (typeof ORIGIN_TYPES)[keyof typeof ORIGIN_TYPES];

export type DetectionControl = {
  id: string;
  name: string;
  category: string;
  query: string;
  subTitle: string;
  explanation: string;
  sequelizedQuery: string | null;
  minThreshold: number;
  severity: Severity;
  emailNotify: boolean;
  origin: OriginType;
  active: boolean;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
};

export type Detection = {
  id: string;
  controlId: string;
  category: string;
  title: string;
  subTitle: string;
  explanation: string;
  severity: Severity;
  relatedEntities: CloudEntity[];
  status: DetectionStatus;
  createdTime?: Date;
  updatedTime?: Date;
  userId?: string;
};

export type DetectionWithControlResults = Detection & {
  controlResults: UniqueTraffic[];
};

export type DetectionControlMetric = {
  id: string;
  hitCount: number;
};

export type StaticDetectionControl = {
  id?: string;
  name: string;
  category: string;
  query: string;
  sequelizedQuery?: string;
  minThreshold?: number;
  severity?: Severity;
  subTitle?: string;
  explanation?: string;
  emailNotify?: boolean;
  origin?: OriginType;
  active?: boolean;
  createdBy?: string;
  createdAt?: Date;
  updatedAt?: Date;
};

export interface IDetectionControlChanges {
  create: DetectionControl[];
  update: DetectionControl[];
  delete: DetectionControl[];
}
