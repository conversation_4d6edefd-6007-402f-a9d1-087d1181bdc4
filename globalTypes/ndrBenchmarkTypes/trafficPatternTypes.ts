import { BaseEntity } from './entityTypes';
import { z } from 'zod';

export type ProviderInformation = {
  agent: string;
  process: string;
};

export const DIRECTIONS = {
  INGRESS: 'ingress',
  EGRESS: 'egress',
  UNKNOWN: 'unknown'
} as const;

export type Direction = (typeof DIRECTIONS)[keyof typeof DIRECTIONS];

// Old interface. Going to be removed
export type TrafficPattern = {
  entityId: string;
  remoteAddr: string;
  localAddr: string;
  port: string;
  direction: Direction;
  remoteEntity: BaseEntity;
  sessionCount: number;
  egressVolume: number;
  ingressVolume: number;
  time: Date;
  providerInformation?: ProviderInformation;
  remoteProviderInformation?: ProviderInformation;
  isDanger?: boolean;
  id: string;
};

export const TrafficSideSchema = z.object({
  id: z.string(),
  name: z.string(),
  addr: z.string(),
  process: z.string(),
  type: z.string()
});

export const BaseTrafficSchema = z.object({
  src: TrafficSideSchema,
  dst: TrafficSideSchema,
  port: z.number()
});

export type TrafficSide = z.infer<typeof TrafficSideSchema>;

export type BaseTraffic = z.infer<typeof BaseTrafficSchema>;

export type RawTraffic = BaseTraffic & { time: number };

export const TRAFFIC_ACTIONS = {
  ACCEPT: 'accept',
  FIREWALL_BLOCK: 'firewall_block',
  FIREWALL_ALLOW: 'firewall_allow'
} as const;

export type TrafficAction =
  (typeof TRAFFIC_ACTIONS)[keyof typeof TRAFFIC_ACTIONS];

export const UniqueTrafficSchema = BaseTrafficSchema.extend({
  id: z.string(),
  sessionCount: z.number(),
  action: z.nativeEnum(TRAFFIC_ACTIONS).optional()
});

export type UniqueTraffic = z.infer<typeof UniqueTrafficSchema>;

export interface SQLTrafficEvent {
  src_id: string | null;
  src_name: string | null;
  src_addr: string | null;
  src_process: string | null;
  src_type: string | null;

  dst_id: string | null;
  dst_name: string | null;
  dst_addr: string | null;
  dst_process: string | null;
  dst_type: string | null;

  port: number;
  time: Date;
  created_at?: Date;
  id: string;
  action?: TrafficAction;

  session_count?: number | null;
}

export type SQLTrafficEventTimeless = Omit<
  SQLTrafficEvent,
  'time' | 'created_at'
>;
