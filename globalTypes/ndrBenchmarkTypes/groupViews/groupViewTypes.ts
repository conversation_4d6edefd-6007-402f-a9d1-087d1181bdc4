export interface GroupViewKey {
  key: string
  displayName?: string
}

export interface GroupView {
  id: string
  name: string
  primaryKey: string
  secondaryKey?: string
  additionalKeys?: string[]
  isDefault: boolean
  createdAt: Date
  updatedAt: Date
  userId: string
  organizationId: string
}

export interface GroupViewWithId extends GroupView {
  id: string
}

export interface CreateGroupViewRequest {
  name: string
  primaryKey: string
  secondaryKey?: string
  additionalKeys?: string[]
  isDefault?: boolean
}

export interface UpdateGroupViewRequest extends Partial<CreateGroupViewRequest> {
  id: string
}

export interface GroupedEntity {
  groupId: string
  groupName: string
  groupKeys: Record<string, string>
  entities: string[]
  entityCount: number
  aggregatedLabels: Record<string, string[]>
}

export interface GroupedEdge {
  id: string
  sourceGroupId: string
  targetGroupId: string
  edgeCount: number
  aggregatedData: {
    totalTraffic: number
    ports: number[]
    protocols: string[]
    isDanger: boolean
  }
}

export interface GroupViewState {
  currentView: GroupView | null
  groupedEntities: GroupedEntity[]
  groupedEdges: GroupedEdge[]
  isGrouped: boolean
  zoomedGroup: string | null
}
