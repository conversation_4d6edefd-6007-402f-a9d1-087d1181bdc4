export const POLICY_TYPES = {
  ACCEPT: 'ACCEPT',
  BLOCK: 'BLOCK'
} as const;

export type PolicyType = (typeof POLICY_TYPES)[keyof typeof POLICY_TYPES];

export const RULE_STATUSES = {
  Enabled: 'Enabled',
  Disabled: 'Disabled'
} as const;

export type RuleStatus = (typeof RULE_STATUSES)[keyof typeof RULE_STATUSES];

export const FIREWALL_DIRECTIONS = {
  INBOUND: 'inbound',
  OUTBOUND: 'outbound'
} as const;

export type FirewallDirection =
  (typeof FIREWALL_DIRECTIONS)[keyof typeof FIREWALL_DIRECTIONS];

export const AUTO_POLICY_ACTIONS = {
  ALLOW: 'allow',
  BLOCK: 'block'
} as const;

export type AutoPolicyAction =
  (typeof AUTO_POLICY_ACTIONS)[keyof typeof AUTO_POLICY_ACTIONS];
