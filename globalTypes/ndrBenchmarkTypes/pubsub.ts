export const TOPICS = {
  FIREWALL_RISK_CALCULATION: 'calculate-firewall-risk-scores-topic',
  DATA_COLLECTOR: 'data-collector-topic',
  VIOLATIONS_PROCESSING: 'violations-processing-topic'
} as const;

export type Topic = (typeof TOPICS)[keyof typeof TOPICS];

export const MESSAGE_TYPES = {
  FIREWALL_RISK_CALCULATION: 'firewall-risk-calculation',
  DATA_COLLECTOR: 'data-collector',
  VIOLATIONS_PROCESSING: 'violations-processing'
} as const;

export type MessageType = (typeof MESSAGE_TYPES)[keyof typeof MESSAGE_TYPES];

export const MESSAGE_TOPICS: Record<MessageType, Topic> = {
  [MESSAGE_TYPES.FIREWALL_RISK_CALCULATION]: TOPICS.FIREWALL_RISK_CALCULATION,
  [MESSAGE_TYPES.DATA_COLLECTOR]: TOPICS.DATA_COLLECTOR,
  [MESSAGE_TYPES.VIOLATIONS_PROCESSING]: TOPICS.VIOLATIONS_PROCESSING
};

export type MessageBodyType = CalculateFirewallRiskScoreMessageBody;

export type CalculateFirewallRiskScoreMessageBody = {
  organizationId: string;
};

export type MessagePayload = {
  type: MessageType;
  body: MessageBodyType;
};

export type CalculateFirewallRiskScoresPayload = MessagePayload & {
  type: keyof typeof MESSAGE_TYPES.FIREWALL_RISK_CALCULATION;
  body: CalculateFirewallRiskScoreMessageBody;
};

export type ParsedMessage = {
  topicName: Topic;
  messageBody: MessageBodyType;
};
