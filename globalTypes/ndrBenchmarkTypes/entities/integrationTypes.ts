export const IntegrationTypes = {
  AWS: 'aws',
  SENTINEL_ONE: 'sentinelOne',
  CROWD_STRIKE: 'crowdstrike',
  MS_DEFENDER: 'msDefender'
} as const;

export type IntegrationType =
  (typeof IntegrationTypes)[keyof typeof IntegrationTypes];

export type SentinelOneApiConfig = {
  endpoint: string;
  token: string;
};

export type CrowdStrikeApiConfig = {
  clientId: string;
  secret: string;
  region: string;
};

export type DefenderApiConfig = {
  clientId: string;
  clientSecret: string;
  tenantId: string;
};

export type AWSIntegrationConfig = {
  accountId: string;
  externalId: string;
  accessKeyId: string;
  secretAccessKey: string;
  assumeRoleArn: string;
  targetAccountId: string;
  flowLogBucketName: string;
};

export type IntegrationConfig =
  | SentinelOneApiConfig
  | CrowdStrikeApiConfig
  | AWSIntegrationConfig
  | DefenderApiConfig;
