export enum DeploymentType {
  ON_PREM = 'On-prem',
  CLOUD = 'Cloud'
}

export interface CommonEntityInfo {
  osName?: string;
  osVersion?: string;
  deviceType?: string;
  deployment?: DeploymentType;
  ipAddresses: string[];
  macAddresses: string[];
  manufacturer?: string;
  cloudProvider?: string;
  cloudAccountId?: string;
  cloudRegion?: string;
  cloudAvailabilityZone?: string;
  cloudVpc?: string;
  activeDirectoryDomain?: string;
  activeDirectoryOu: string[];
  lastLoggedInUser?: string;
}
