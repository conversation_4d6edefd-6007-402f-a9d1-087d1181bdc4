export interface MSDefenderApiEntity {
  Timestamp: string; // ISO string
  DeviceId: string;
  DeviceName: string;
  ClientVersion: string;
  PublicIP: string;
  OSArchitecture: string;
  OSPlatform: string;
  OSBuild?: number;
  IsAzureADJoined: number;
  JoinType: string;
  AadDeviceId: string;
  LoggedOnUsers: string; // JSON stringified array
  RegistryDeviceTag: string;
  OSVersion: string;
  MachineGroup: string;
  ReportId: number;
  OnboardingStatus: string;
  AdditionalFields: string; // JSON stringified array
  DeviceCategory: string;
  DeviceType: string;
  DeviceSubtype: string;
  Model: string;
  Vendor: string;
  OSDistribution: string;
  OSVersionInfo: string;
  MergedDeviceIds: string;
  MergedToDeviceId: string;
  IsInternetFacing: number | null;
  SensorHealthState: string;
  IsExcluded: number;
  ExclusionReason: string;
  ExposureLevel: string;
  AssetValue: string;
  DeviceManualTags: string;
  DeviceDynamicTags: string;
  HardwareUuid: string;
  CloudPlatforms: string; // JSON stringified array
  AzureVmId: string;
  AzureResourceId: string;
  AzureVmSubscriptionId: string;
  GcpFullResourceName: string;
  AwsResourceName: string;
  IsTransient: number | null;
  OsBuildRevision: string;
  HostDeviceId: string;
  MitigationStatus: string;
  ConnectivityType: string;
  DiscoverySources: string;
  IPAddress: string;
  MacAddress: string;
  OU: string[];
  ADDomain: string;
}

export const defenderEntityFields: (keyof MSDefenderApiEntity)[] = [
  'Timestamp',
  'DeviceId',
  'DeviceName',
  'ClientVersion',
  'PublicIP',
  'OSArchitecture',
  'OSPlatform',
  'OSBuild',
  'IsAzureADJoined',
  'JoinType',
  'AadDeviceId',
  'LoggedOnUsers',
  'RegistryDeviceTag',
  'OSVersion',
  'MachineGroup',
  'ReportId',
  'OnboardingStatus',
  'AdditionalFields',
  'DeviceCategory',
  'DeviceType',
  'DeviceSubtype',
  'Model',
  'Vendor',
  'OSDistribution',
  'OSVersionInfo',
  'MergedDeviceIds',
  'MergedToDeviceId',
  'IsInternetFacing',
  'SensorHealthState',
  'IsExcluded',
  'ExclusionReason',
  'ExposureLevel',
  'AssetValue',
  'DeviceManualTags',
  'DeviceDynamicTags',
  'HardwareUuid',
  'CloudPlatforms',
  'AzureVmId',
  'AzureResourceId',
  'AzureVmSubscriptionId',
  'GcpFullResourceName',
  'AwsResourceName',
  'IsTransient',
  'OsBuildRevision',
  'HostDeviceId',
  'MitigationStatus',
  'ConnectivityType',
  'DiscoverySources',
  'IPAddress',
  'MacAddress',
  'OU',
  'ADDomain'
];

export enum NetworkEventsActionType {
  INBOUND_CONNECTION_ACCEPTED = 'InboundConnectionAccepted',
  INBOUND_CONNECTION_ATTEMPT = 'InboundConnectionAttempt',
  CONNECTION_REQUEST = 'ConnectionRequest',
  CONNECTION_SUCCESS = 'ConnectionSuccess',
  CONNECTION_FAILED = 'ConnectionFailed',
  CONNECTION_ATTEMPT = 'ConnectionAttempt',
  CONNECTION_ACKNOWLEDGED = 'ConnectionAcknowledged',
  LISTENING_CONNECTION_CREATED = 'ListeningConnectionCreated',
  SSL_CONNECTION_INSPECTED = 'SslConnectionInspected',
  SSH_CONNECTION_INSPECTED = 'SshConnectionInspected',
  KERBEROS_CONNECTION_INSPECTED = 'KerberosConnectionInspected',
  DNS_CONNECTION_INSPECTED = 'DnsConnectionInspected',
  NTLM_AUTHENTICATION_INSPECTED = 'NtlmAuthenticationInspected',
  NETWORK_SIGNATURE_INSPECTED = 'NetworkSignatureInspected',
  ICMP_CONNECTION_INSPECTED = 'IcmpConnectionInspected',
  HTTP_CONNECTION_INSPECTED = 'HttpConnectionInspected',
  CONNECTION_FOUND = 'ConnectionFound',
  UNKNOWN = 'Unknown'
}

export interface MSDefenderApiTraffic {
  DeviceId: string;
  DeviceName: string;
  ActionType: NetworkEventsActionType;
  InitiatingProcessFileName: string;
  RemoteIP: string;
  LocalIP: string;
  RemoteIPType: 'Private' | 'Public' | 'LinkLocal';
  RemotePort: number;
  LocalPort: number;
  Timestamp: string; // ISO format
  IPAddress: string; //from DeviceNetworkInfo
}

export const defenderTrafficFields: (keyof MSDefenderApiTraffic)[] = [
  'DeviceId',
  'DeviceName',
  'ActionType',
  'InitiatingProcessFileName',
  'RemoteIP',
  'LocalIP',
  'RemoteIPType',
  'RemotePort',
  'LocalPort',
  'Timestamp',
  // 'IPAddress'
];
