import { CommonEntityInfo } from './commonEntityInfo';

export enum AWSEndpointType {
  EC2_INSTANCE = 'aws_ec2_instance',
  EC2_INSTANCE_EKS_NODE = 'aws_ec2_instance_eks_node',
  EKS_CLUSTER = 'aws_eks_cluster',
  RDS_DB_INSTANCE = 'aws_rds_db_instance',
  LAMBDA_FUNCTION = 'aws_lambda_function',
  EC2_APPLICATION_LOAD_BALANCER = 'aws_ec2_application_load_balancer',
  EC2_NETWORK_LOAD_BALANCER = 'aws_ec2_network_load_balancer',
  EC2_GATEWAY_LOAD_BALANCER = 'aws_ec2_gateway_load_balancer',
  NAT_GATEWAY = 'aws_nat_gateway',
  VPC_LINK = 'aws_vpc_link',
  VPC_ENDPOINT = 'aws_vpc_endpoint',
  REDSHIFT_CLUSTER = 'aws_ec2_redshift_cluster',
  ELASTIC_FILESYSTEM = 'aws_elastic_filesystem',
  ELASTICACHE_NODE = 'aws_elasticache_node',
  TRANSIT_GATEWAY = 'aws_transit_gateway',
  UNRECOGNIZED_NETWORK_INTERFACE = 'unrecognized_network_interface'
}

export type AWSEntityType = 'aws_endpoint';

export type AWSSubtypes = `${AWSEndpointType}`;

export interface AWSEndpointInfo extends AWSNetworkInterfaceInfo {
  endpointType: AWSEndpointType;
  arn: string;
}

export interface AWSNetworkInterfaceInfo {
  accountId: string;
  privateIp: string;
  publicIp: string | null;
  networkInterfaceIds: string[];
  region: string;
  availabilityZone: string;
  vpcId: string;
  subnetId: string;
  securityGroups: Partial<AWSSecurityGroup>[];
  ips: string[];
}

export interface AWSResourceInfo extends Partial<CommonEntityInfo> {
  name: string;
  id: string;
}

export interface AWSResourceEntity {
  info: AWSEndpointInfo;
  awsResourceInfo: AWSResourceInfo;
}

export interface AWSSecurityGroupRule {
  id: string;
  region: string;
  accountId: string;
  description: string | null;
  securityGroupId: string;
  direction: AWSDirection;
  fromPort: number | null;
  toPort: number | null;
  protocol: string;
  remoteType: AWSSecurityGroupRuleDataRemoteType;
  remote: string;
}

export interface AWSSecurityGroup {
  id: string;
  name: string;
  description: string | null;
  accountId: string;
  region: string;
}

export interface SQLAwsSecurityGroupRule {
  id: string;
  region: string;
  account_id: string;
  description: string | null;
  security_group_id: string;
  direction: AWSDirection;
  from_port: number | null;
  to_port: number | null;
  protocol: string;
  remote_type: AWSSecurityGroupRuleDataRemoteType;
  remote: string;
}

export interface SQLAwsSecurityGroup {
  id: string;
  name: string;
  description: string | null;
  account_id: string;
  region: string;
  rules?: SQLAwsSecurityGroupRule[];
}

export const AWS_DIRECTION = {
  INGRESS: 'ingress',
  EGRESS: 'egress'
} as const;

export type AWSDirection = (typeof AWS_DIRECTION)[keyof typeof AWS_DIRECTION];

export const AWS_SECURITY_GROUP_REMOTE_TYPE = {
  CIDR_IPV4: 'cidr_ipv4',
  CIDR_IPV6: 'cidr_ipv6',
  PREFIX_LIST: 'prefix_list',
  SECURITY_GROUP: 'security_group'
} as const;

export type AWSSecurityGroupRuleDataRemoteType =
  (typeof AWS_SECURITY_GROUP_REMOTE_TYPE)[keyof typeof AWS_SECURITY_GROUP_REMOTE_TYPE];

export const AWS_SECURITY_GROUP_PROTOCOL = {
  TCP: 'tcp',
  UDP: 'udp',
  ICMP: 'icmp',
  ALL: '-1'
} as const;

export const AWS_SECURITY_GROUP_REMOTE_IDENTIFIER_TYPE = {
  CIDR_IPV4: 'CidrIpv4',
  CIDR_IPV6: 'CidrIpv6',
  SECURITY_GROUP: 'ReferencedGroupId'
} as const;

export type AwsSecurityGroupRemoteIdentifierType =
  (typeof AWS_SECURITY_GROUP_REMOTE_IDENTIFIER_TYPE)[keyof typeof AWS_SECURITY_GROUP_REMOTE_IDENTIFIER_TYPE];

export const AWS_SECURITY_GROUP_OPERATION = {
  CREATE: 'create',
  DELETE: 'delete'
} as const;

export type AwsSecurityGroupOperation =
  (typeof AWS_SECURITY_GROUP_OPERATION)[keyof typeof AWS_SECURITY_GROUP_OPERATION];
