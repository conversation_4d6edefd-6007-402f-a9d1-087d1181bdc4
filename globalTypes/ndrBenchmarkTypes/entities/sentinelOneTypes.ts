import { EndpointEntity } from './baseTypes';

export type SentinelOneEntityType = 'sentinelOne_agent';
export type SentinelOneInfo = Agent;

// Enum Types Based on Schema Enums
export type NetworkStatus =
  | 'connected'
  | 'disconnected'
  | 'connecting'
  | 'disconnecting';
export type MachineType =
  | 'unknown'
  | 'desktop'
  | 'laptop'
  | 'server'
  | 'kubernetes node'
  | 'storage'
  | 'kubernetes pod'
  | 'ecs task'
  | 'kubernetes helper';
export type AppsVulnerabilityStatus =
  | 'patch_required'
  | 'up_to_date'
  | 'not_applicable';
export type OsArch = '32 bit' | '64 bit' | 'ARM64';
export type RangerStatus = 'NotApplicable' | 'Enabled' | 'Disabled';
export type ConsoleMigrationStatus = 'N/A' | 'Pending' | 'Migrated' | 'Failed';
export type ScanStatus = 'none' | 'started' | 'aborted' | 'finished';
export type InstallerType =
  | '.msi'
  | '.exe'
  | '.deb'
  | '.rpm'
  | '.bsx'
  | '.pkg'
  | '.img'
  | 'unknown'
  | '.tar'
  | '.zip'
  | '.gz'
  | '.xz';
export type LocationType =
  | 'not_applicable'
  | 'not_supported'
  | 'specific'
  | 'fallback';
export type OsType = 'windows_legacy' | 'windows' | 'linux' | 'macos';
export type MitigationMode = 'detect' | 'protect';
export type UserActionNeeded =
  | 'none'
  | 'user_action_needed'
  | 'reboot_needed'
  | 'upgrade_needed'
  | 'incompatible_os'
  | 'unprotected'
  | 'rebootless_without_dynamic_detection'
  | 'extended_exclusions_partially_accepted'
  | 'reboot_required'
  | 'pending_deprecation'
  | 'ne_not_running'
  | 'ne_cf_not_active';

export type MissingPermission =
  | 'user_action_needed_fda'
  | 'user_action_needed_rs_fda'
  | 'user_action_needed_fda_helper'
  | 'user_action_needed_fda_sentineld'
  | 'user_action_needed_bluetooth_per'
  | 'user_action_needed_network'
  | 'user_action_needed_notifications';
export type LocationScope = 'site' | 'global' | 'group' | 'account';

// Interfaces Updated with Enums
export interface Agent extends EndpointEntity {
  ips: string[];
  machineSid: string;
  networkStatus: NetworkStatus;
  firewallEnabled: boolean;
  storageType: string;
  hasContainerizedWorkload: boolean;
  siteId: string;
  lastIpToMgmt: string;
  groupName: string;
  firstFullModeTime: Date;
  machineType: MachineType;
  appsVulnerabilityStatus: AppsVulnerabilityStatus;
  accountName: string;
  locations: Location[];
  createdAt: Date;
  allowRemoteShell: boolean;
  remoteProfilingState: string;
  osName: string;
  osStartTime: Date;
  encryptedApplications: boolean;
  operationalState: string;
  policyUpdatedAt: Date;
  operationalStateExpiration: Date;
  externalIp: string;
  osArch: OsArch;
  registeredAt: Date;
  computerName: string;
  activeDirectory: ActiveDirectory;
  domain: string;
  groupIp: string;
  inRemoteShellSession: boolean;
  containerizedWorkloadCounts: ContainerizedWorkloadCounts;
  id: string;
  threatRebootRequired: boolean;
  infected: boolean;
  coreCount: number;
  agentVersion: string;
  lastActiveDate: Date;
  isDecommissioned: boolean;
  osType: OsType;
  lastLoggedInUserName: string;
  serialNumber: string;
  osUsername: string;
  rangerVersion: string;
  osRevision: string;
  rangerStatus: RangerStatus;
  modelName: string;
  tags: Tags;
  isUpToDate: boolean;
  scanAbortedAt: Date;
  scanStartedAt: Date;
  updatedAt: Date;
  showAlertIcon: boolean;
  consoleMigrationStatus: ConsoleMigrationStatus;
  activeThreats: number;
  networkInterfaces: NetworkInterface[];
  isUninstalled: boolean;
  isPendingUninstall: boolean;
  lastSuccessfulScanDate: Date;
  externalId: string;
  accountId: string;
  uuid: string;
  mitigationMode: MitigationMode;
  mitigationModeSuspicious: MitigationMode;
  licenseKey: string;
  scanStatus: ScanStatus;
  installerType: InstallerType;
  locationEnabled: boolean;
  proxyStates: ProxyStates;
  locationType: LocationType;
  cpuId: string;
  cloudProviders: { [key: string]: CloudProvider };
  cpuCount: number;
  networkQuarantineEnabled: boolean;
  userActionsNeeded: UserActionNeeded[];
  storageName: string;
  totalMemory: number;
  detectionState: string;
  fullDiskScanLastUpdatedAt: Date;
  remoteProfilingStateExpiration: Date;
  scanFinishedAt: Date;
  siteName: string;
  missingPermissions: MissingPermission[];
  groupUpdatedAt: Date;
  isActive: boolean;
  isActiveStatus: boolean;
  groupId: string;
}

export interface Location {
  name: string;
  scope: LocationScope;
  id: string;
}

export interface ActiveDirectory {
  lastUserDistinguishedName: string;
  userPrincipalName: string;
  computerDistinguishedName: string;
  computerMemberOf: string[];
  lastUserMemberOf: string[];
  mail: string;
}

export interface ContainerizedWorkloadCounts {
  tasksCount: number;
  podsCount: number;
  containersCount: number;
}

export interface Tags {
  sentinelone: Tag[];
}

export interface Tag {
  value: string;
  assignedBy: string;
  key: string;
  assignedAt: Date;
  id: string;
  assignedById: string;
}

export interface NetworkInterface {
  name: string;
  gatewayIp: string;
  inet6: string[];
  inet: string[];
  physical: string;
  id: string;
  gatewayMacAddress: string;
}

export interface ProxyStates {
  console: boolean;
  deepVisibility: boolean;
}

export interface CloudProvider {
  awsSubnetIds?: string[];
  azureResourceGroup?: string;
  kubernetesNodeLabels?: string[];
  kubernetesVersion?: string;
  ecsServiceName?: string;
  ecsServiceArn?: string;
  cloudNetwork?: string;
  cloudInstanceSize?: string;
  cloudImage?: string;
  agentNamespace?: string;
  ecsTaskArn?: string;
  ecsTaskAvailabilityZone?: string;
  kubernetesNodeName?: string;
  ecsType?: string;
  gcpServiceAccount?: string;
  ecsTaskDefinitionArn?: string;
  clusterName?: string;
  ecsTaskDefinitionFamily?: string;
  cloudLocation?: string;
  awsRole?: string;
  kubernetesType?: string;
  cloudInstanceId?: string;
  cloudTags?: string[];
  cloudAccount?: string;
  agentPodName?: string;
  ecsTaskDefinitionRevision?: string;
  ecsVersion?: string;
  ecsClusterName?: string;
  awsSecurityGroups?: string[];
}
