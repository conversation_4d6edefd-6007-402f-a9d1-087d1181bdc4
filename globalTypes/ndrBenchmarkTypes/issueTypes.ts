import { BaseEntity, SQLAwsSecurityGroupRule } from './entityTypes';
import { UniqueTraffic } from './trafficPatternTypes';
import { FirewallRule } from '../FirewallTypes';

export const SEVERITY_MAP = {
  INFO: 'info',
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical'
} as const;

export type Severity = (typeof SEVERITY_MAP)[keyof typeof SEVERITY_MAP];

export const ISSUE_STATUS = {
  OPEN: 'open',
  RESOLVED: 'resolved',
  DISMISSED: 'dismissed'
} as const;

export type IssueStatus = (typeof ISSUE_STATUS)[keyof typeof ISSUE_STATUS];

export type Issue = {
  entityId: string;
  category: string;
  title: string;
  subTitle: string;
  explanation: string;
  severity: Severity;
  relatedEntities?: BaseEntity[];
  relatedEntitiesIds: string[];
  trafficPatterns: UniqueTraffic[];
  recommendation?: Recommendation;
  remediation?: Remediation;
  status: IssueStatus;
  ruleId?: string;
  rule?: SQLAwsSecurityGroupRule;
  entitiesIds?: string[];
  createdTime?: Date;
  updatedTime?: Date;
  userId?: string;
};

export type PlatformStepsKey = 'manual' | 'aws-cli' | 'terraform' | 'pulumi';

export type Recommendation = {
  description: string;
  platformSteps: Partial<{ [key in PlatformStepsKey]: string[] }>;
  accessSuggestion: string[];
};

export type Remediation = {
  type: 'firewall';
  description: string;
  data: FirewallRemediationData;
};

export type FirewallRemediationData = {
  [entitiyId: string]: FirewallRule[];
};

export const ISSUE_CATEGORIES = {
  VERTEX_ISSUE: 'vertex_issue'
};
