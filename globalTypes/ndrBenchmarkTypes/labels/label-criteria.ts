export const LabelCriteriaCategories = {
  SYSTEM_INFORMATION: 'systemInformation',
  NETWORK: 'network',
  CLOUD: 'cloud',
  ACTIVE_DIRECTORY: 'activeDirectory',
  INTEGRATION: 'integration'
} as const;

export type LabelCriteriaCategory = keyof typeof LabelCriteriaCategories;

export type LabelCriteriaCategoryMapping = {
  [K in LabelCriteriaCategory]: Record<string, string[]>;
};

export const labelCriteriaCategoryMapping: LabelCriteriaCategoryMapping = {
  SYSTEM_INFORMATION: {
    osName: ['common.osName'],
    osVersion: ['common.osVersion'],
    deviceType: ['common.deviceType'],
    manufacturer: ['common.manufacturer'],
    is_active: ['isActive'],
    name: ['name']
  },
  NETWORK: {
    ipAddresses: ['common.ipAddresses', 'traffic.ips'],
    macAddresses: ['common.macAddresses']
  },
  CLOUD: {
    cloudProvider: ['common.cloudProvider'],
    cloudAccountId: ['common.cloudAccountId'],
    cloudRegion: ['common.cloudRegion'],
    cloudAvailabilityZone: ['common.cloudAvailabilityZone'],
    cloudVpc: ['common.cloudVpc'],
    deployment: ['common.deployment']
  },
  ACTIVE_DIRECTORY: {
    activeDirectoryDomain: ['common.activeDirectoryDomain'],
    activeDirectoryOu: ['common.activeDirectoryOu'],
    lastLoggedInUser: ['common.lastLoggedInUser']
  },
  INTEGRATION: {
    integrationType: ['integrationType']
  }
};

//the same Mapping labelCriteriaCategoryMapping but flatted - without categories
export const LabelCriteriaFields = Object.entries(labelCriteriaCategoryMapping)
  .flatMap(([_, fields]) => Object.entries(fields))
  .reduce(
    (acc, [fieldName, path]) => {
      acc[fieldName] = path;
      return acc;
    },
    {} as Record<string, string[]>
  );
