import { GiFamilyTree, GiPortal, GiRadarSweep } from 'react-icons/gi'
import {
    HiOutlineColorSwatch,
    HiOutlineDesktopComputer,
    HiOutlineFingerPrint,
    HiOutlineHome,
    HiOutlineTemplate,
    HiOutlineViewGridAdd,
} from 'react-icons/hi'
import { TbPlugConnected } from 'react-icons/tb'
import { VscVmConnect } from 'react-icons/vsc'
import { GoGitMergeQueue } from 'react-icons/go'

export type NavigationIcons = Record<string, JSX.Element>

const navigationIcon: NavigationIcons = {
    home: <HiOutlineHome />,
    resources: <HiOutlineViewGridAdd />,
    collapseMenu: <HiOutlineTemplate />,
    groupSingleMenu: <HiOutlineDesktopComputer />,
    fingerPrintIcon: <HiOutlineFingerPrint />,
    remoteIcon: <VscVmConnect />,
    groupCollapseMenu: <HiOutlineColorSwatch />,
    portFlowIcon: <GoGitMergeQueue />,
    scannerMenu: <GiRadarSweep />,
    integrations: <TbPlugConnected />,
    portals: <GiPortal />,
}

export default navigationIcon
