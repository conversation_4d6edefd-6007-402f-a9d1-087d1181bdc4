import { SidebarItem } from '@/components/template/next-ui-nav/sidebar'

export const accessNavigationConfig: SidebarItem[] = [
  {
    key: '/access-dashboard',
    href: '/access-dashboard',
    title: 'Dashboard',
    icon: 'solar:chart-linear',x
  },
  {
    key: '/shortcuts',
    href: '/shortcuts', //TODO: update href
    title: 'Shortcuts',
    icon: 'solar:home-angle-2-linear',
  },
  {
    key: '/portals',
    href: '/portals',
    title: 'Portals',
    icon: 'solar:history-2-linear',
  },
  {
    key: '/resources',
    href: '/resources',
    title: 'Resources',
    icon: 'solar:monitor-smartphone-linear',
  },
  {
    key: '/users',
    href: '/users',
    title: 'Users',
    icon: 'solar:users-group-two-rounded-outline',
  },
  {
    key: '/integrations',
    href: '/integrations',
    title: 'Integrations',
    icon: 'solar:widget-add-linear',
  },
]

export const ndrNavigationConfig: SidebarItem[] = [
  {
    key: '/ndr-dashboard',
    href: '/ndr-dashboard',
    title: 'Dashboard',
    icon: 'solar:chart-linear',
  },
  {
    key: '/insights',
    href: '/insights',
    title: 'Insights',
    icon: 'solar:map-point-rotate-linear',
  },
  {
    key: '/recommendations',
    href: '/recommendations',
    title: 'Recommendations',
    icon: 'solar:users-group-two-rounded-outline',
  },
  {
    key: '/inventory',
    href: '/inventory',
    title: 'Inventory',
    icon: 'solar:monitor-smartphone-linear',
  },
  {
    key: '/explore',
    href: '/explore',
    title: 'Explore',
    icon: 'solar:users-group-two-rounded-outline',
  },
  {
    key: '/controls',
    href: '/controls',
    title: 'Controls',
    icon: 'solar:users-group-two-rounded-outline',
  },
  {
    key: '/integrations',
    href: '/integrations',
    title: 'Integrations',
    icon: 'solar:widget-add-linear',
  },
]
