import type { NavigationTree } from '@/@types/navigation'
import {
    NAV_ITEM_TYPE_COLLAPSE,
    NAV_ITEM_TYPE_ITEM,
    NAV_ITEM_TYPE_TITLE,
} from '@/constants/navigation.constant'

const navigationConfig: NavigationTree[] = [
    {
        key: 'home',
        path: '/home',
        title: 'Home',
        translateKey: 'nav.home',
        icon: 'home',
        type: NAV_ITEM_TYPE_ITEM,
        authority: [],
        subMenu: [],
        solarIcon: 'solar:home-2-linear',
    },
    {
        key: 'portals',
        path: '/portals',
        title: 'Portals',
        translateKey: 'nav.portals',
        icon: 'portals',
        type: NAV_ITEM_TYPE_ITEM,
        authority: [],
        subMenu: [],
        solarIcon: 'solar:history-2-linear',
    },
    {
        key: 'resources',
        path: '/resources',
        title: 'Resources',
        translateKey: 'nav.resources',
        icon: 'resources',
        type: NAV_ITEM_TYPE_ITEM,
        authority: [],
        subMenu: [],
        solarIcon: 'solar:monitor-smartphone-linear',
    },

    {
        key: 'adminMenu',
        title: 'Admin Menu',
        authority: [],
        type: NAV_ITEM_TYPE_TITLE,
        icon: '',
        path: '',
        translateKey: 'nav.adminMenu.adminMenu',
        isExternalLink: false,
        solarIcon: 'solar:settings-2-linear',
        subMenu: [
            {
                key: 'adminMenu.dashboard',
                path: '/dashboard',
                title: 'Dashboard',
                translateKey: 'nav.adminMenu.dashboard',
                icon: 'groupSingleMenu',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
                solarIcon: 'solar:chart-linear',
            },
            {
                key: 'menu.integrations',
                path: '/integrations',
                title: 'Integrations',
                translateKey: 'nav.adminMenu.integrations',
                icon: 'integrations',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
                solarIcon: 'solar:widget-add-linear',
            },
            {
                key: 'menu.portpolicy',
                path: '/port-flow',
                title: 'Port Flow',
                translateKey: 'nav.adminMenu.portflow',
                icon: 'portFlowIcon',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
                solarIcon: 'solar:planet-2-linear',
            },
            {
                key: 'menu.scanner',
                path: '/scanner',
                title: 'Scanner',
                translateKey: 'nav.adminMenu.scanner',
                icon: 'scannerMenu',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
                solarIcon: 'solar:radar-2-line-duotone',
            },
        ],
    },

    // {
    //     key: 'adminMenu.collapse',
    //     path: '',
    //     title: 'Group collapse menu',
    //     translateKey: 'nav.adminMenu.collapse.collapse',
    //     icon: 'scannerMenu',
    //     type: NAV_ITEM_TYPE_COLLAPSE,
    //     authority: [],
    //     subMenu: [
    //         // {
    //         //     key: 'adminMenu.collapse.item1',
    //         //     path: '/group-collapse-menu-item-view-1',
    //         //     title: 'Menu item 1',
    //         //     translateKey: 'nav.adminMenu.collapse.item1',
    //         //     icon: '',
    //         //     type: NAV_ITEM_TYPE_ITEM,
    //         //     authority: [],
    //         //     subMenu: [],
    //         // },
    //         {
    //             key: 'adminMenu.collapse.scans',
    //             path: '/scanner/scans',
    //             title: 'Menu item 2',
    //             translateKey: 'nav.adminMenu.collapse.scans',
    //             icon: '',
    //             type: NAV_ITEM_TYPE_ITEM,
    //             authority: [],
    //             subMenu: [],
    //         },
    //     ],
    // },
    // {
    //     key: 'adminMenu',
    //     path: '',
    //     title: 'Admin Menu',
    //     translateKey: 'nav.adminMenu.adminMenu',
    //     icon: '',
    //     type: NAV_ITEM_TYPE_TITLE,
    //     authority: [],
    //     subMenu: [
    //         {
    //             key: 'adminMenu.dashboard',
    //             path: '/dashboard',
    //             title: 'Dashboard',
    //             translateKey: 'nav.adminMenu.dashboard',
    //             icon: 'groupSingleMenu',
    //             type: NAV_ITEM_TYPE_ITEM,
    //             authority: [],
    //             subMenu: [],
    //         },
    //         {
    //             key: 'menu.integrations',
    //             path: '/integrations',
    //             title: 'Integrations',
    //             translateKey: 'nav.adminMenu.integrations',
    //             icon: 'integrations',
    //             type: NAV_ITEM_TYPE_ITEM,
    //             authority: [],
    //             subMenu: [],
    //         },
    //         // {
    //         //     key: 'menu.jitaccess',
    //         //     path: '/jitaccess',
    //         //     title: 'JIT Access',
    //         //     translateKey: 'nav.adminMenu.jitaccess',
    //         //     icon: 'fingerPrintIcon',
    //         //     type: NAV_ITEM_TYPE_ITEM,
    //         //     authority: [],
    //         //     subMenu: [],
    //         // },
    //         {
    //             key: 'menu.portpolicy',
    //             path: '/port-flow',
    //             title: 'Port Flow',
    //             translateKey: 'nav.adminMenu.portflow',
    //             icon: 'portFlowIcon',
    //             type: NAV_ITEM_TYPE_ITEM,
    //             authority: [],
    //             subMenu: [],
    //         },
    //         {
    //             key: 'menu.scanner',
    //             path: '/scanner/scans',
    //             title: 'Scanner',
    //             translateKey: 'nav.adminMenu.scanner',
    //             icon: 'scannerMenu',
    //             type: NAV_ITEM_TYPE_ITEM,
    //             authority: [],
    //             subMenu: [],
    //         },
    //         // {
    //         //     key: 'adminMenu.collapse',
    //         //     path: '',
    //         //     title: 'Group collapse menu',
    //         //     translateKey: 'nav.adminMenu.collapse.collapse',
    //         //     icon: 'scannerMenu',
    //         //     type: NAV_ITEM_TYPE_COLLAPSE,
    //         //     authority: [],
    //         //     subMenu: [
    //         //         // {
    //         //         //     key: 'adminMenu.collapse.item1',
    //         //         //     path: '/group-collapse-menu-item-view-1',
    //         //         //     title: 'Menu item 1',
    //         //         //     translateKey: 'nav.adminMenu.collapse.item1',
    //         //         //     icon: '',
    //         //         //     type: NAV_ITEM_TYPE_ITEM,
    //         //         //     authority: [],
    //         //         //     subMenu: [],
    //         //         // },
    //         //         {
    //         //             key: 'adminMenu.collapse.scans',
    //         //             path: '/scanner/scans',
    //         //             title: 'Menu item 2',
    //         //             translateKey: 'nav.adminMenu.collapse.scans',
    //         //             icon: '',
    //         //             type: NAV_ITEM_TYPE_ITEM,
    //         //             authority: [],
    //         //             subMenu: [],
    //         //         },
    //         //     ],
    //         // },
    //     ],
    // },
]

export default navigationConfig
