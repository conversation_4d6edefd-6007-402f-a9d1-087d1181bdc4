import { AvailableIntegrations } from '@/firestoreQueries/integrations/IntegrationTypes'
import { TrafficPatternWithId } from '@/firestoreQueries/ndr/trafficPatterns/customTrafficPatternsTypes'
import { Timestamp } from 'firebase/firestore'
import { FirestoreTimeRange } from '@/customHooks/useFirestoreTimeRange'

type SpecificResourceQueryKey = {
  resourceId: string
}

type ResourceMembersQueryKey = {
  resourceId: string
  page: number
}

type OneIntegrationQueryKey = {
  integrationId: string
}

type OnePortalQueryKey = {
  portalId: string
}

type ResourcePortalsQueryKey = {
  resourceId: string
}

type ActivitiesQueryKey = {
  timestampFilter?: {
    from: Timestamp
    to: Timestamp
  }
}
type UserActivitiesQueryKey = {
  timestampFilter?: {
    from: Timestamp
    to: Timestamp
  }
  userId: string
}

type DocumentActivitiesQueryKey = {
  timestampFilter?: {
    from: Timestamp
    to: Timestamp
  }
  documentId: string
  documentType: 'resources' | 'portals' | 'integrations'
}

type IntegrtaionVPCsQueryKey = {
  integrationId: string
}

export default {
  scans: () => ['scans'],
  resources: () => ['resources'],
  portals: () => ['portals'],
  specificPortals: ({ portalsIds, organizationId }: { portalsIds: string[]; organizationId: string }) => [
    organizationId,
    'portals',
    { portals: portalsIds },
  ],
  groupPortals: ({ groupId, organizationId }: { organizationId: string; groupId: string }) => [
    organizationId,
    'groupPortals',
    groupId,
  ],
  groupResources: ({ groupId, organizationId }: { organizationId: string; groupId: string }) => [
    organizationId,
    'groupResources',
    groupId,
  ],
  activities: ({ timestampFilter }: ActivitiesQueryKey) => ['activities', { filters: timestampFilter }],

  specificGroups: ({ organizationId, groupsIds }: { organizationId: string; groupsIds: string[] }) => [
    organizationId,
    'groups',
    { groupsIds },
  ],
  groupsLatestActivity: ({
    organizationId,
    groupId,
    portalsIds,
  }: {
    organizationId: string
    groupId: string
    portalsIds: string[]
  }) => [organizationId, 'groupsLatestActivity', groupId, { portalsIds }],

  userActivities: ({ timestampFilter, userId }: UserActivitiesQueryKey) => [
    'userActivities',
    userId,
    { filters: timestampFilter },
  ],
  documentActivities: ({ documentId, documentType, timestampFilter }: DocumentActivitiesQueryKey) => [
    'documentActivities',
    documentType,
    documentId,
    { filters: timestampFilter },
  ],
  oldActivities: ({ timestampFilter }: ActivitiesQueryKey) => ['oldActivities', { filters: timestampFilter }],
  resourcePortals: ({ resourceId }: ResourcePortalsQueryKey) => ['resourcePortals', resourceId],
  oneResource: ({ resourceId }: SpecificResourceQueryKey) => ['resources', resourceId],
  resourceMembers: ({ resourceId, page }: ResourceMembersQueryKey) => [resourceId, 'members', String(page)],
  initializedResources: () => ['initializedResources'],
  integrations: (filters?: Array<AvailableIntegrations>) =>
    !filters ? ['integrations'] : (['integrations', { filters }] as string[]),
  integrationVPCs: ({ integrationId }: IntegrtaionVPCsQueryKey) => ['integrationVPCs', integrationId],
  oneIntegration: ({ integrationId }: OneIntegrationQueryKey) => ['integrations', integrationId],
  onePortal: ({ portalId }: OnePortalQueryKey) => ['portals', portalId],
  user: () => ['user'],
  specificResources: ({ resourcesIds, organizationId }: { resourcesIds: string[]; organizationId: string }) => [
    organizationId,
    'resources',
    { resourcesIds },
  ],
  scanIssues: ({ organizationId, scanId }: { organizationId: string; scanId: string }) => [
    'scanIssues',
    organizationId,
    scanId,
  ],
  oneInterfacePorfile: ({ interfaceId }: { interfaceId: string }) => ['interfaceProfile', interfaceId],

  groups: ({ organizationId }: { organizationId: string }) => [organizationId, 'groups'],
  oneGroup: ({ organizationId, groupId }: { organizationId: string; groupId: string }) => [
    organizationId,
    'groups',
    groupId,
  ],
  riskDataMap: ({ organizationId }: { organizationId: string | undefined }) => ['riskDataMap', organizationId],
  paginatedEntities: ({
    organizationId,
    pageSize,
    searchQuery,
    offset,
    showInactiveItems,
  }: {
    organizationId: string | undefined
    pageSize: number
    searchQuery: string
    offset?: number
    showInactiveItems?: boolean
  }) => ['paginatedEntities', organizationId, pageSize, searchQuery, offset, showInactiveItems],
}

export const elasticSearchQueryKeyStore = {
  trafficOverTime: ({ query }: { query: object }) => ['trafficOverTime', { query }],
  trafficByProtocl: ({ query }: { query: object }) => ['trafficByProtocl', { query }],
}

export const ndrQueryKeyStore = {
  savedPortflowQueries: (organizationId: string | undefined) => [organizationId, 'saved-portflow-queries'],
  portflowShortcuts: (organizationId: string | undefined) => [organizationId, 'portflow-shortcuts'],

  entities: ({ organizationId }: { organizationId: string | undefined }) => [organizationId, 'entities'],
  entitiesPaginated: ({
    organizationId,
    pageSize,
    searchQuery,
  }: {
    organizationId: string | undefined
    pageSize: number
    searchQuery?: string
  }) => [organizationId, 'entities-paginated', pageSize, searchQuery],
  dashboardEntities: ({ organizationId }: { organizationId: string | undefined }) => [
    organizationId,
    'dashboard-entities',
  ],
  oneEntity: ({ organizationId, entityId }: { organizationId: string | undefined; entityId: string }) => [
    organizationId,
    'entities',
    entityId,
  ],
  entityTrafficPatterns: ({ organizationId, entityId }: { organizationId: string; entityId: string }) => [
    organizationId,
    'entityTrafficPatterns',
    entityId,
  ],
  entityIssues: ({ organizationId, entityId }: { organizationId: string; entityId: string }) => [
    organizationId,
    'entityIssues',
    entityId,
  ],
  entityTrafficPatternsGraphData: ({
    organizationId,
    entityId,
    filteredTrafficPatterns,
  }: {
    organizationId: string
    entityId: string
    filteredTrafficPatterns: Array<TrafficPatternWithId> | undefined
  }) => [organizationId, 'entityTrafficPatternsGraphData', entityId, { filters: filteredTrafficPatterns }],
  issueTrafficPattern: ({
    organizationId,
    issueId,
    entityId,
  }: {
    organizationId: string
    issueId: string
    entityId: string
  }) => [organizationId, issueId, 'trafficPatterns', entityId],
  issues: ({
    organizationId,
    timestamp,
  }: {
    organizationId: string
    timestamp?: { from: Timestamp; to: Timestamp }
  }) => [organizationId, 'issues', { filters: timestamp }],
  oneIssue: ({ organizationId, issueId }: { organizationId: string; issueId: string }) => [
    organizationId,
    'issues',
    issueId,
  ],
  notifications: (organizationId: string | undefined) => [organizationId, 'notifications'],
  labels: (organizationId: string | undefined) => [organizationId, 'labels'],
  labelHostnames: ({ organizationId, labelId }: { organizationId: string | undefined; labelId: string }) => [
    organizationId,
    'labels',
    labelId,
    'hostnames',
  ],
  detections: ({
    organizationId,
    timestamp,
    category,
  }: {
    organizationId: string
    category?: string
    timestamp?: FirestoreTimeRange
  }) => [organizationId, 'detections', { timestamp, category }],
  detectionControls: ({ organizationId, timestamp }: { organizationId: string; timestamp?: FirestoreTimeRange }) => [
    'detectionControls',
    organizationId,
    timestamp,
  ],
  detectionControlMetrics: ({ organizationId }: { organizationId: string }) => [
    organizationId,
    'detectionControlMetrics',
  ],
  detectionCategories: ({ organizationId }: { organizationId: string }) => [organizationId, 'detectionCategories'],
  entityLabels: ({ organizationId, entityId }: { organizationId: string | undefined; entityId: string }) => [
    organizationId,
    'entityLabels',
    entityId,
  ],
  securityGroups: ({ organizationId }: { organizationId: string | undefined }) => [organizationId, 'securityGroups'],
  riskDataMap:({ organizationId }: { organizationId: string | undefined }) => [organizationId, 'riskDataMap'],
  groupViews: ({ organizationId }: { organizationId: string | undefined }) => [organizationId, 'groupViews'],
  oneGroupView: ({ organizationId, groupViewId }: { organizationId: string | undefined; groupViewId: string }) => [
    organizationId,
    'groupViews',
    groupViewId,
  ],
}

export const accessQueryKeyStore = {
  portalGroups: ({ organizationId }: { organizationId: string }) => [organizationId, 'portalGroups'],
  onePortalGroup: ({ organizationId, portalGroupId }: { organizationId: string; portalGroupId: string }) => [
    organizationId,
    'portalGroups',
    portalGroupId,
  ],
  groupPortalGroups: ({ groupId, organizationId }: { organizationId: string; groupId: string }) => [
    organizationId,
    'groupPortalGroups',
    groupId,
  ],
  specificPortalGroups: ({
    organizationId,
    portalGroupsIds,
  }: {
    organizationId: string
    portalGroupsIds: string[]
  }) => [organizationId, 'portalGroups', { portalGroups: portalGroupsIds }],
}
