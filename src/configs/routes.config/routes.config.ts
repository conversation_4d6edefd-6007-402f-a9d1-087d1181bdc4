import type { Routes } from '@/@types/routes'
import { lazy } from 'react'

export const publicRoutes: Routes = [
  // {
  //   key: 'access-onboarding',
  //   path: '/access-onboarding',
  //   component: lazy(() => import('@/views/Access/OnboardingPage')),
  //   authority: [],
  // },
]

export const protectedRoutes = [
  {
    key: 'portals',
    path: '/portals',
    component: lazy(() => import('@/views/Access/Portals')),
    authority: [],
  },
  {
    key: 'resources',
    path: '/resources',
    component: lazy(() => import('@/views/Access/Resources')),
    authority: [],
  },
  {
    key: 'groups',
    path: '/groups',
    component: lazy(() => import('@/views/Organization/Groups')),
    authority: [],
  },
]

export const adminRoutes: Routes = [
  {
    key: 'activities',
    path: '/activities',
    component: lazy(() => import('@/views/Organization/Activities')),
    authority: [],
  },
  {
    key: 'ndr-dashboard',
    path: '/ndr-dashboard',
    component: lazy(() => import('@/views/NDR/Dashboard')),
    authority: [],
  },
  {
    key: 'users',
    path: '/users',
    component: lazy(() => import('@/views/Users')),
    authority: [],
  },
  {
    key: 'access-dashboard',
    path: '/access-dashboard',
    component: lazy(() => import('@/views/Access/AccessDashboard')),
    authority: [],
  },
  {
    key: 'insights',
    path: '/insights',
    component: lazy(() => import('@/views/NDR/Issues/Issues')),
    authority: [],
  },
  {
    key: 'ndr-overview',
    path: '/ndr-overview',
    component: lazy(() => import('@/views/NDR/NdrOverview')),
    authority: [],
  },
  {
    key: 'graph',
    path: '/graph',
    component: lazy(() => import('@/views/NDR/PortflowPage')),
    authority: [],
  },
  {
    key: 'inventory',
    path: '/inventory',
    component: lazy(() => import('@/views/NDR/Inventory/index')),
    authority: [],
  },
  {
    key: 'integrations',
    path: '/integrations',
    component: lazy(() => import('@/views/Integrations')),
    authority: [],
  },
  {
    key: 'scanner',
    path: '/scanner',
    component: lazy(() => import('@/views/Scans')),
    authority: [],
  },
  {
    key: 'scanner/scans/:id/scan-profile',
    path: '/scanner/scans/:id/scan-profile',
    component: lazy(() => import('@/views/Scans/ScanProfile')),
    authority: [],
  },
  {
    key: 'scanner/scans/:id/scan-profile/overview',
    path: '/scanner/scans/:id/scan-profile/overview',
    component: lazy(() => import('@/views/Scans/ScanProfile')),
    authority: [],
  },
  {
    key: 'scanner/scans/:id/scan-profile/open-ports',
    path: '/scanner/scans/:id/scan-profile/open-ports',
    component: lazy(() => import('@/views/Scans/ScanProfile')),
    authority: [],
  },
  {
    key: 'scanner/scans/:id/scan-profile/technologies',
    path: '/scanner/scans/:id/scan-profile/technologies',
    component: lazy(() => import('@/views/Scans/ScanProfile')),
    authority: [],
  },
  {
    key: 'scanner/scans/:id/scan-profile/issues',
    path: '/scanner/scans/:id/scan-profile/issues',
    component: lazy(() => import('@/views/Scans/ScanProfile')),
    authority: [],
  },
  {
    key: 'scanner/scans/:id/scan-profile/issues/overflow',
    path: '/scanner/scans/:id/scan-profile/issues/overflow',
    component: lazy(() => import('@/views/Scans/ScanProfile')),
    authority: [],
  },
  {
    key: 'scanner/scans/:id/scan-profile/issues/issues',
    path: '/scanner/scans/:id/scan-profile/issues/issues',
    component: lazy(() => import('@/views/Scans/ScanProfile')),
    authority: [],
  },
]
