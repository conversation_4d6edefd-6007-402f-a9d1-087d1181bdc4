import { Route, Routes } from '@/@types/routes'
import { lazy } from 'react'
import { mockAdminRoutes, mockProtectedRoutes, mockPublicRoutes } from './routes.config.demo'

export function getRoutes(
  isMock: boolean,
  showAccessDashboard: boolean,
  showScannerModule: boolean,
  showOrganizationModule: boolean,
) {
  if (isMock)
    return {
      protectedRoutes: mockAdminRoutes,
      adminRoutes: mockProtectedRoutes,
      publicRoutes: mockPublicRoutes,
    }

  const protectedRoutes = getProtectedRoutes(showAccessDashboard, showScannerModule, showOrganizationModule)
  const adminRoutes = getAdminRoutes(showAccessDashboard, showScannerModule, showOrganizationModule)
  const publicRoutes = getPublicRoutes(showAccessDashboard, showScannerModule, showOrganizationModule)
  return { protectedRoutes, adminRoutes, publicRoutes }
}

function getProtectedRoutes(showAccessDashboard: boolean, showScannerModule: boolean, showOrganizationModule: boolean) {
  const routes: Routes = []

  if (showAccessDashboard) {
    accessRoutes.forEach((route) => {
      if (route.permissions === 'protected') routes.push(route)
    })
  }
  ndrRoutes.forEach((route) => {
    if (route.permissions) routes.push(route)
  })
  hunterXRoutes.forEach((route) => {
    if (route.permissions) routes.push(route)
  })
  if (showOrganizationModule) {
    organizationRoutes.forEach((route) => {
      if (route.permissions) routes.push(route)
    })
  }
  if (showScannerModule) {
    scannerRoutes.forEach((route) => {
      if (route.permissions) routes.push(route)
    })
  }
  return routes
}

function getAdminRoutes(showAccessDashboard: boolean, showScannerModule: boolean, showOrganizationModule: boolean) {
  const routes: Routes = []

  if (showAccessDashboard) {
    accessRoutes.forEach((route) => {
      if (route.permissions === 'admin') routes.push(route)
    })
  }
  ndrRoutes.forEach((route) => {
    if (route.permissions === 'admin') routes.push(route)
  })
  hunterXRoutes.forEach((route) => {
    if (route.permissions === 'admin') routes.push(route)
  })

  if (showOrganizationModule) {
    organizationRoutes.forEach((route) => {
      if (route.permissions === 'admin') routes.push(route)
    })
  }
  if (showScannerModule) {
    scannerRoutes.forEach((route) => {
      if (route.permissions === 'admin') routes.push(route)
    })
  }
  return routes
}

function getPublicRoutes(showAccessDashboard: boolean, showScannerModule: boolean, showOrganizationModule: boolean) {
  const routes: Routes = []

  if (showAccessDashboard) {
    accessRoutes.forEach((route) => {
      if (route.permissions === 'public') routes.push(route)
    })
  }
  ndrRoutes.forEach((route) => {
    if (route.permissions === 'public') routes.push(route)
  })
  hunterXRoutes.forEach((route) => {
    if (route.permissions === 'public') routes.push(route)
  })

  if (showOrganizationModule) {
    organizationRoutes.forEach((route) => {
      if (route.permissions === 'public') routes.push(route)
    })
  }
  if (showScannerModule) {
    scannerRoutes.forEach((route) => {
      if (route.permissions === 'public') routes.push(route)
    })
  }
  return routes
}

export const accessRoutes: Array<Route & { permissions: 'public' | 'admin' | 'protected' }> = [
  {
    permissions: 'admin',
    key: 'access-dashboard',
    path: '/access-dashboard',
    component: lazy(() => import('@/views/Access/AccessDashboard')),
    authority: [],
  },
  {
    permissions: 'protected',
    key: 'portals',
    path: '/portals',
    component: lazy(() => import('@/views/Access/Portals')),
    authority: [],
  },
  {
    permissions: 'protected',
    key: 'resources',
    path: '/resources',
    component: lazy(() => import('@/views/Access/Resources')),
    authority: [],
  },
]

export const ndrRoutes: Array<Route & { permissions: 'public' | 'admin' | 'protected' }> = [
  {
    permissions: 'admin',
    key: 'ndr-dashboard',
    path: '/ndr-dashboard',
    component: lazy(() => import('@/views/NDR/Dashboard')),
    authority: [],
  },
  {
    permissions: 'admin',
    key: 'events',
    path: '/events',
    component: lazy(() => import('@/views/NDR/NotificationsPage')),
    authority: [],
  },
  {
    permissions: 'admin',
    key: 'ndr-overview',
    path: '/ndr-overview',
    component: lazy(() => import('@/views/NDR/NdrOverview')),
    authority: [],
  },
  {
    permissions: 'admin',
    key: 'insights',
    path: '/insights',
    component: lazy(() => import('@/views/NDR/Issues/Issues')),
    authority: [],
  },
  {
    permissions: 'admin',
    key: 'graph',
    path: '/graph',
    component: lazy(() => import('@/views/NDR/PortflowPage/PortflowGraph')),
    withoutPaddings: true,
    authority: [],
  },
  {
    permissions: 'admin',
    key: 'inventory',
    path: '/inventory',
    component: lazy(() => import('@/views/NDR/Inventory/index')),
    authority: [],
  },
  {
    permissions: 'admin',
    key: 'auto-policy',
    path: '/auto-policy',
    component: lazy(() => import('@/views/NDR/AutoSegmentation/index')),
    authority: [],
  },
]

export const organizationRoutes: Array<Route & { permissions: 'public' | 'admin' | 'protected' }> = [
  {
    permissions: 'admin',
    key: 'activities',
    path: '/activities',
    component: lazy(() => import('@/views/Organization/Activities')),
    authority: [],
  },
  {
    permissions: 'admin',
    key: 'users',
    path: '/users',
    component: lazy(() => import('@/views/Users')),
    authority: [],
  },
  {
    permissions: 'protected',
    key: 'groups',
    path: '/groups',
    component: lazy(() => import('@/views/Organization/Groups')),
    authority: [],
  },
  {
    permissions: 'admin',
    key: 'integrations',
    path: '/integrations',
    component: lazy(() => import('@/views/Integrations')),
    authority: [],
  },
]

export const hunterXRoutes: Array<Route & { permissions: 'public' | 'admin' | 'protected' }> = [
  {
    permissions: 'admin',
    key: 'hunterx-dashboard',
    path: '/hunterx-dashboard',
    component: lazy(() => import('@/views/HunterX/Dashboard')),
    pageContainerType: 'gutterless',
    authority: [],
  },
  {
    permissions: 'admin',
    key: 'hunterx-events',
    path: '/hunterx-events',
    component: lazy(() => import('@/views/HunterX/NotificationsPage')),
    authority: [],
  },
  {
    permissions: 'admin',
    key: 'hunterx-inspect',
    path: '/hunterx-inspect',
    component: lazy(() => import('@/views/HunterX/EntityInspector')),
    authority: [],
  },
  {
    permissions: 'admin',
    key: 'hunterx-issues',
    path: '/hunterx-issues',
    component: lazy(() => import('@/views/HunterX/Issues/Issues')),
    authority: [],
  },
  {
    permissions: 'admin',
    key: 'hunterx-graph',
    path: '/hunterx-graph',
    component: lazy(() => import('@/views/NDR/PortflowPage/HunterGraph')),
    authority: [],
  },
  {
    permissions: 'admin',
    key: 'hunterx-inventory',
    path: '/hunterx-inventory',
    component: lazy(() => import('@/views/HunterX/Inventory/index')),
    authority: [],
  },
  {
    permissions: 'admin',
    key: 'hunterx-detections',
    path: '/hunterx-detections',
    component: lazy(() => import('@/views/HunterX/Detections/DetectionsPage')),
    authority: [],
  },
  {
    permissions: 'admin',
    key: 'hunterx-detection-controls',
    path: '/hunterx-controls',
    component: lazy(() => import('@/views/HunterX/DetectionControls/index')),
    authority: [],
  },
]

const scannerRoutes: Array<Route & { permissions: 'public' | 'admin' | 'protected' }> = [
  {
    permissions: 'admin',
    key: 'scanner',
    path: '/scanner',
    component: lazy(() => import('@/views/Scans')),
    authority: [],
  },
  {
    permissions: 'admin',
    key: 'scanner/scans/:id/scan-profile',
    path: '/scanner/scans/:id/scan-profile',
    component: lazy(() => import('@/views/Scans/ScanProfile')),
    authority: [],
  },
  {
    permissions: 'admin',
    key: 'scanner/scans/:id/scan-profile/overview',
    path: '/scanner/scans/:id/scan-profile/overview',
    component: lazy(() => import('@/views/Scans/ScanProfile')),
    authority: [],
  },
  {
    permissions: 'admin',
    key: 'scanner/scans/:id/scan-profile/open-ports',
    path: '/scanner/scans/:id/scan-profile/open-ports',
    component: lazy(() => import('@/views/Scans/ScanProfile')),
    authority: [],
  },
  {
    permissions: 'admin',
    key: 'scanner/scans/:id/scan-profile/technologies',
    path: '/scanner/scans/:id/scan-profile/technologies',
    component: lazy(() => import('@/views/Scans/ScanProfile')),
    authority: [],
  },
  {
    permissions: 'admin',
    key: 'scanner/scans/:id/scan-profile/issues',
    path: '/scanner/scans/:id/scan-profile/issues',
    component: lazy(() => import('@/views/Scans/ScanProfile')),
    authority: [],
  },
  {
    permissions: 'admin',
    key: 'scanner/scans/:id/scan-profile/issues/overflow',
    path: '/scanner/scans/:id/scan-profile/issues/overflow',
    component: lazy(() => import('@/views/Scans/ScanProfile')),
    authority: [],
  },
  {
    permissions: 'admin',
    key: 'scanner/scans/:id/scan-profile/issues/issues',
    path: '/scanner/scans/:id/scan-profile/issues/issues',
    component: lazy(() => import('@/views/Scans/ScanProfile')),
    authority: [],
  },
]
