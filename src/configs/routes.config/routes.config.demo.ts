import type { Routes } from '@/@types/routes'
import { lazy } from 'react'

export const mockPublicRoutes: Routes = []

export const mockProtectedRoutes = [
  {
    key: 'shortcuts',
    path: '/shortcuts',
    component: lazy(() => import('@/views/Access/Shortcuts')),
    authority: [],
  },
  {
    key: 'portals',
    path: '/portals',
    component: lazy(() => import('@/views/Access/Portals')),
    authority: [],
  },

  {
    key: 'users',
    path: '/users',
    component: lazy(() => import('@/views/Users')),
    authority: [],
  },
  {
    key: 'activities',
    path: '/activities',
    component: lazy(() => import('@/views/Organization/Activities')),
    authority: [],
  },
  {
    key: 'userProfile',
    path: '/users/:userId/profile',
    component: lazy(() => import('@/views/Users/<USER>')),
    authority: [],
  },
  {
    key: 'userProfile.activity',
    path: '/users/:userId/activity',
    component: lazy(() => import('@/views/Users/<USER>')),
    authority: [],
  },
  {
    key: 'resources',
    path: '/resources',
    component: lazy(() => import('@/views/Access/Resources')),
    authority: [],
  },
]

export const mockAdminRoutes: Routes = [
  {
    key: 'groups',
    path: '/groups',
    component: lazy(() => import('@/views/Organization/Groups')),
    authority: [],
  },
  {
    key: 'settings',
    path: '/settings',
    component: lazy(() => import('@/views/Organization/Settings')),
    authority: [],
  },
  {
    key: 'billing',
    path: '/billing',
    component: lazy(() => import('@/views/Organization/Billing')),
    authority: [],
  },
  {
    key: 'schedules',
    path: '/schedules',
    component: lazy(() => import('@/views/Organization/Schedules')),
    authority: [],
  },
  {
    key: 'jit-access',
    path: '/jit-access',
    component: lazy(() => import('@/views/Organization/JITAccess')),
    authority: [],
  },
  {
    key: 'access-dashboard',
    path: '/access-dashboard',
    component: lazy(() => import('@/views/Access/AccessDashboard')),
    authority: [],
  },
  {
    key: 'ndr-overview',
    path: '/ndr-overview',
    component: lazy(() => import('@/views/NDR/NdrOverview')),
    authority: [],
  },
  {
    key: 'controls',
    path: '/controls',
    component: lazy(() => import('@/views/NDR/Controls')),
    authority: [],
  },
  {
    key: 'ndr-dashboard',
    path: '/ndr-dashboard',
    component: lazy(() => import('@/views/NDR/Dashboard')),
    authority: [],
  },
  {
    key: 'insights',
    path: '/insights',
    component: lazy(() => import('@/views/NDR/Issues')),
    authority: [],
  },
  {
    key: 'insights.overview',
    path: '/insights/overview',
    component: lazy(() => import('@/views/NDR/Issues')),
    authority: [],
  },
  {
    key: 'insights.insights',
    path: '/insights/insights',
    component: lazy(() => import('@/views/NDR/Issues')),
    authority: [],
  },
  {
    key: 'insights.visualization',
    path: '/insights/visualization',
    component: lazy(() => import('@/views/NDR/Issues')),
    authority: [],
  },
  {
    key: 'insights.traffic-logs',
    path: '/insights/traffic-logs',
    component: lazy(() => import('@/views/NDR/Issues')),
    authority: [],
  },
  {
    key: 'inventory',
    path: '/inventory',
    component: lazy(() => import('@/views/NDR/Inventory/index')),
    authority: [],
  },
  {
    key: 'explore',
    path: '/explore',
    component: lazy(() => import('@/views/NDR/Explore')),
    authority: [],
  },
  {
    key: 'integrations',
    path: '/integrations',
    component: lazy(() => import('@/views/Integrations')),
    authority: [],
  },
  {
    key: 'portflow',
    path: '/port-flow',
    component: lazy(() => import('@/views/PortPolicy')),
    authority: [],
  },
  {
    key: 'scanner',
    path: '/scanner',
    component: lazy(() => import('@/views/Scans')),
    authority: [],
  },
  {
    key: 'scanner/scans/:id/scan-profile',
    path: '/scanner/scans/:id/scan-profile',
    component: lazy(() => import('@/views/Scans/ScanProfile')),
    authority: [],
  },
  {
    key: 'scanner/scans/:id/scan-profile/overview',
    path: '/scanner/scans/:id/scan-profile/overview',
    component: lazy(() => import('@/views/Scans/ScanProfile')),
    authority: [],
  },
  {
    key: 'scanner/scans/:id/scan-profile/open-ports',
    path: '/scanner/scans/:id/scan-profile/open-ports',
    component: lazy(() => import('@/views/Scans/ScanProfile')),
    authority: [],
  },
  {
    key: 'scanner/scans/:id/scan-profile/technologies',
    path: '/scanner/scans/:id/scan-profile/technologies',
    component: lazy(() => import('@/views/Scans/ScanProfile')),
    authority: [],
  },
  {
    key: 'scanner/scans/:id/scan-profile/issues',
    path: '/scanner/scans/:id/scan-profile/issues',
    component: lazy(() => import('@/views/Scans/ScanProfile')),
    authority: [],
  },
  {
    key: 'scanner/scans/:id/scan-profile/issues/overflow',
    path: '/scanner/scans/:id/scan-profile/issues/overflow',
    component: lazy(() => import('@/views/Scans/ScanProfile')),
    authority: [],
  },
  {
    key: 'scanner/scans/:id/scan-profile/issues/issues',
    path: '/scanner/scans/:id/scan-profile/issues/issues',
    component: lazy(() => import('@/views/Scans/ScanProfile')),
    authority: [],
  },
]
