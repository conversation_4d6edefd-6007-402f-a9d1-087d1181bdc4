import reactQueryKeyStore from '@/configs/reactQueryKeyStore'
import { useQuery } from '@tanstack/react-query'
import getGroupsLatestConnection from '../queries/getGroupsLatestConnection'

type Args = {
  organizationId: string | undefined
  groupId: string
  portalsIds: Array<string>
}

export default function ({ organizationId, portalsIds, groupId }: Args) {
  return useQuery({
    queryKey: reactQueryKeyStore.groupsLatestActivity({ groupId, organizationId: organizationId!, portalsIds }),
    queryFn: async () => await getGroupsLatestConnection({ organizationId, portalsIds }),
  })
}
