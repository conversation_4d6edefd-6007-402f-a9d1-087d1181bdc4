import reactQueryKeyStore from '@/configs/reactQueryKeyStore'
import { queryClient } from '@/Providers/ReactQueryProvider'
import { useMutation } from '@tanstack/react-query'
import { GroupTypeWIthId } from '../groupTypes'
import updateGroup from '../queries/updateGroup'

type Args = {
  organizationId: string | undefined
  group: GroupTypeWIthId
}

export default function () {
  return useMutation({
    mutationFn: async ({ organizationId, group }: Args) => await updateGroup({ group, organizationId }),

    onMutate: ({ group, organizationId }) => {
      const groupQuery = reactQueryKeyStore.oneGroup({ groupId: group.id, organizationId: organizationId! })
      queryClient.setQueryData(groupQuery, group)

      const allGroupsQuery = reactQueryKeyStore.groups({ organizationId: organizationId! })

      const prevGroup: GroupTypeWIthId = queryClient.getQueryData(groupQuery) || ({} as GroupTypeWIthId)
      const prevAllGroups: Array<GroupTypeWIthId> = queryClient.getQueryData(allGroupsQuery) || []

      const newGroups = prevAllGroups.map((g) => {
        if (g.id === group.id) return group
        return g
      })

      queryClient.setQueryData(allGroupsQuery, newGroups)

      function rollback() {
        queryClient.setQueryData(allGroupsQuery, prevAllGroups)
        queryClient.setQueryData(groupQuery, prevGroup)
      }

      return { rollback, groupQuery, allGroupsQuery }
    },

    onSuccess: (_, __, { allGroupsQuery, groupQuery }) => {
      queryClient.refetchQueries({ queryKey: groupQuery })
      queryClient.refetchQueries({ queryKey: allGroupsQuery })
    },

    onError: (error, _, context) => {
      if (context) context.rollback()
      return error
    },
  })
}
