import reactQueryKeyStore from '@/configs/reactQueryKeyStore'
import { useQuery } from '@tanstack/react-query'
import getGroupsByIds from '../queries/getGroupsByIds'

type Args = {
  organizationId: string | undefined
  groupsIds: string[]
}

export default function ({ organizationId, groupsIds }: Args) {
  return useQuery({
    queryKey: reactQueryKeyStore.specificGroups({ organizationId: organizationId!, groupsIds }),
    queryFn: async () => await getGroupsByIds({ organizationId, groupsIds }),
  })
}
