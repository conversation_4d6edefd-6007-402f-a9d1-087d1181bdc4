import { useMutation } from '@tanstack/react-query'
import { GroupTypeFromDB, GroupTypeWIthId } from '../groupTypes'
import createGroup from '../queries/createGroup'
import reactQueryKeyStore from '@/configs/reactQueryKeyStore'
import { queryClient } from '@/Providers/ReactQueryProvider'

type Args = {
  organizationId: string | undefined
  group: GroupTypeFromDB
}

export default function () {
  return useMutation({
    mutationFn: async ({ organizationId, group }: Args) => await createGroup({ organizationId, group }),
    onSuccess: (group, { organizationId }) => {
      const queryKey = reactQueryKeyStore.groups({ organizationId: organizationId! })
      const prev: GroupTypeWIthId[] = queryClient.getQueryData(queryKey) || []

      queryClient.setQueryData(queryKey, [...prev, group])
    },
    onError: (error) => {
      return error
    },
  })
}
