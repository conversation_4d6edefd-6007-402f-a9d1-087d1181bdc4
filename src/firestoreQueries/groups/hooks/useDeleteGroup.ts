import reactQueryKeyStore from '@/configs/reactQueryKeyStore'
import { queryClient } from '@/Providers/ReactQueryProvider'
import { useMutation } from '@tanstack/react-query'
import { GroupTypeWIthId } from '../groupTypes'
import deleteGroup from '../queries/deleteGroup'

type Args = {
  organizationId: string | undefined
  groupId: string
}

export default function () {
  return useMutation({
    mutationFn: async ({ organizationId, groupId }: Args) => await deleteGroup({ organizationId, groupId }),

    onMutate: ({ organizationId, groupId }) => {
      const allGroupsQueryKey = reactQueryKeyStore.groups({ organizationId: organizationId! })
      const portalGroupQueryKey = reactQueryKeyStore.oneGroup({
        organizationId: organizationId!,
        groupId,
      })

      const prevGroup: GroupTypeWIthId = queryClient.getQueryData(portalGroupQueryKey) || ({} as GroupTypeWIthId)

      const prevAllGroups: GroupTypeWIthId[] = queryClient.getQueryData(allGroupsQueryKey) || []
      const newGroups = prevAllGroups.filter((group: any) => group.id !== groupId)

      queryClient.setQueryData(allGroupsQueryKey, newGroups)
      queryClient.removeQueries({ queryKey: portalGroupQueryKey })

      function rollback() {
        queryClient.setQueryData(allGroupsQueryKey, prevAllGroups)
        queryClient.setQueryData(portalGroupQueryKey, prevGroup)
      }
      return {
        rollback,
      }
    },
    onError: (error, _, context) => {
      if (context) context.rollback()
      return error
    },
  })
}
