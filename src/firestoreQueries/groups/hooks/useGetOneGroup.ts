import reactQueryKeyStore from '@/configs/reactQueryKeyStore'
import { useQuery } from '@tanstack/react-query'
import getOneGroup from '../queries/getOneGroup'

type Args = {
  organizationId: string | undefined
  groupId: string
}

export default function ({ organizationId, groupId }: Args) {
  return useQuery({
    queryKey: reactQueryKeyStore.oneGroup({ organizationId: organizationId!, groupId }),
    queryFn: async () => await getOneGroup({ groupId, organizationId }),
  })
}
