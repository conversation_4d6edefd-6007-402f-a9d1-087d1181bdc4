import reactQueryKeyStore from '@/configs/reactQueryKeyStore'
import { useQuery } from '@tanstack/react-query'
import getGroups from '../queries/getGroups'

type Args = {
  organizationId: string | undefined
}

export default function ({ organizationId }: Args) {
  return useQuery({
    queryKey: reactQueryKeyStore.groups({ organizationId: organizationId! }),
    queryFn: async () => await getGroups({ organizationId }),
  })
}
