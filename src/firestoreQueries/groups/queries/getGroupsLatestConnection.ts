import { db } from '@/configs/firebase'
import { ConnectToPortalActivityLogTypeFromDB } from '@/firestoreQueries/activities/activitiesTypes'
import pathStore from '@/firestoreQueries/utils/pathStore'
import { captureException } from '@sentry/react'
import { collection, doc, DocumentReference, getDocs, limit, orderBy, query, where } from 'firebase/firestore'

type Args = {
  organizationId: string | undefined
  portalsIds: Array<string>
}

export default async function ({ organizationId, portalsIds }: Args) {
  try {
    if (!organizationId) throw new Error('organizationId is undefined')

    if (portalsIds.length === 0) return null

    const refsArray = portalsIds.map((id) => createRef(id, organizationId))

    const path = pathStore.activities({ organizationId })
    const chunkSize = 10
    const chunks = splitIntoChunks(refsArray, chunkSize)

    const promises = chunks.map((chunk) => fetchByChunk(path, chunk))
    const resolvedPromises = Promise.all(promises)

    return (await resolvedPromises).flat()[0]
  } catch (error) {
    captureException(error)
    throw error
  }
}

function createRef(portalId: string, organizationId: string): DocumentReference {
  const path = pathStore.onePortal({ organizationId, portalId })
  return doc(db, path)
}

function splitIntoChunks(array: DocumentReference[], chunkSize: number): DocumentReference[][] {
  const chunks = []
  for (let i = 0; i < array.length; i += chunkSize) {
    chunks.push(array.slice(i, i + chunkSize))
  }
  return chunks
}

async function fetchByChunk(path: string, chunk: DocumentReference[]) {
  const ref = collection(db, path)
  const q = query(
    ref,
    where('type', '==', 'connectToPortal'),
    where('details.portalRef', 'in', chunk),
    orderBy('timestamp', 'desc'),
    limit(1),
  )

  const snap = await getDocs(q)

  return snap.docs[0].data() as ConnectToPortalActivityLogTypeFromDB
}
