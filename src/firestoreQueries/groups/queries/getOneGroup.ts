import { db } from '@/configs/firebase'
import pathStore from '@/firestoreQueries/utils/pathStore'
import { captureException } from '@sentry/react'
import { doc, getDoc } from 'firebase/firestore'
import { GroupTypeWIthId } from '../groupTypes'

type Args = {
  organizationId: string | undefined
  groupId: string
}

export default async function ({ organizationId, groupId }: Args) {
  try {
    if (!organizationId) throw new Error('organizationId is undefined')

    const path = pathStore.oneGroup({ organizationId, groupId })
    const ref = doc(db, path)

    const snap = await getDoc(ref)
    return {
      ...snap.data(),
      id: groupId,
    } as GroupTypeWIthId
  } catch (error) {
    captureException(error)
    throw error
  }
}
