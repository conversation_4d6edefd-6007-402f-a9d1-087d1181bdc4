import { db } from '@/configs/firebase'
import pathStore from '@/firestoreQueries/utils/pathStore'
import { captureException } from '@sentry/react'
import { collection, getDocs } from 'firebase/firestore'
import { GroupTypeWIthId } from '../groupTypes'

type Args = {
  organizationId: string | undefined
}

export default async function ({ organizationId }: Args) {
  try {
    if (!organizationId) throw new Error('organizationId is undefiend')

    const path = pathStore.groups({ organizationId })
    const ref = collection(db, path)
    const snap = await getDocs(ref)

    const groups: Array<GroupTypeWIthId> = []
    snap.forEach((doc) => {
      groups.push({ ...doc.data(), id: doc.id } as GroupTypeWIthId)
    })

    return groups
  } catch (error) {
    captureException(error)
    throw error
  }
}
