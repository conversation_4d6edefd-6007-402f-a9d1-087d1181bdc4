import { db } from '@/configs/firebase'
import pathStore from '@/firestoreQueries/utils/pathStore'
import { captureException } from '@sentry/react'
import { deleteDoc, doc } from 'firebase/firestore'

type Args = {
  organizationId: string | undefined
  groupId: string
}

export default async function ({ organizationId, groupId }: Args) {
  try {
    if (!organizationId) throw new Error('organizationId is undefined')
    const path = pathStore.oneGroup({ organizationId, groupId })
    await deleteDoc(doc(db, path))
  } catch (error) {
    captureException(error)
    throw error
  }
}
