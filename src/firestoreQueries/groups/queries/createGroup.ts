import { db } from '@/configs/firebase'
import pathStore from '@/firestoreQueries/utils/pathStore'
import { captureException } from '@sentry/react'
import { addDoc, collection } from 'firebase/firestore'
import { GroupTypeFromDB, GroupTypeWIthId } from '../groupTypes'

type Args = {
  organizationId: string | undefined
  group: GroupTypeFromDB
}

export default async function ({ organizationId, group }: Args) {
  try {
    if (!organizationId) throw new Error('organizationId is undefined')

    const path = pathStore.groups({ organizationId })
    const collectionRef = collection(db, path)

    const ref = await addDoc(collectionRef, group)

    return {
      ...group,
      id: ref.id,
    } as GroupTypeWIthId
  } catch (error) {
    captureException(error)
    throw error
  }
}
