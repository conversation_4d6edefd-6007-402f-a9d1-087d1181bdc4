import { fetchByChunk, splitIntoChunks } from '@/firestoreQueries/utils/chunkUtils'
import pathStore from '@/firestoreQueries/utils/pathStore'
import { captureException } from '@sentry/react'
import { GroupTypeWIthId } from '../groupTypes'

type Args = {
  organizationId: string | undefined
  groupsIds: Array<string>
}

export default async function ({ organizationId, groupsIds }: Args) {
  try {
    if (!organizationId) throw new Error('organizationId is undefined')
    if (groupsIds.length === 0) return [] as GroupTypeWIthId[]

    const path = pathStore.groups({ organizationId })
    const chunkSize = 10
    const chunks = splitIntoChunks(groupsIds, chunkSize)

    const promises = chunks.map((chunk) => fetchByChunk<GroupTypeWIthId>(path, chunk))
    const resolvedPromises = await Promise.all(promises)

    return resolvedPromises.flat()
  } catch (error) {
    captureException(error)
    throw error
  }
}
