import { captureException } from '@sentry/react'
import { GroupTypeFromDB, GroupTypeWIthId } from '../groupTypes'
import pathStore from '@/firestoreQueries/utils/pathStore'
import { doc, updateDoc } from 'firebase/firestore'
import { db } from '@/configs/firebase'
import getOneGroup from './getOneGroup'

type Args = {
  organizationId: string | undefined
  group: GroupTypeWIthId
}

export default async function ({ organizationId, group }: Args) {
  try {
    if (!organizationId) throw new Error('organizationId is undefined')

    const { id, portalsIds, resourcesIds, userIds, groupInfo, portalGroupsIds } = group
    const newGroup: GroupTypeFromDB = { portalsIds, resourcesIds, userIds, groupInfo, portalGroupsIds }

    const path = pathStore.oneGroup({ organizationId, groupId: id })
    const ref = doc(db, path)
    await updateDoc(ref, newGroup as any)

    return await getOneGroup({ groupId: id, organizationId })
  } catch (error) {
    captureException(error)
  }
}
