import { db } from '@/configs/firebase'
import * as Sentry from '@sentry/react'
import { Query, Timestamp, collection, getDocs, orderBy, query, where } from 'firebase/firestore'
import { ActivityLogTypeFromDB } from '../activitiesTypes'

type Args = {
  organizationId: string | undefined
  timestampFilter?: {
    from: Timestamp
    to: Timestamp
  }
  activitiesPath: string
}

export default async ({ organizationId, timestampFilter, activitiesPath }: Args) => {
  try {
    if (!organizationId) throw new Error('Missing organizationId')
    const ref = collection(db, activitiesPath)

    let q: Query = query(ref, orderBy('timestamp', 'desc'))

    if (timestampFilter) {
      const start = timestampFilter.from
      const end = timestampFilter.to
      q = query(ref, where('timestamp', '>=', start), where('timestamp', '<=', end), orderBy('timestamp', 'desc'))
    }

    const querySnapshot = await getDocs(q)

    const activitiesData: ActivityLogTypeFromDB[] = []

    querySnapshot.forEach((doc) => {
      const activityData = doc.data() as ActivityLogTypeFromDB
      activitiesData.push({
        ...activityData,
      })
    })

    return activitiesData
  } catch (error) {
    Sentry.captureException(error)
    throw error
  }
}
