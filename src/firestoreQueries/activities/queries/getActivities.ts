import { db } from '@/configs/firebase'
import pathStore from '@/firestoreQueries/utils/pathStore'
import * as Sentry from '@sentry/react'
import { Query, Timestamp, collection, getDocs, orderBy, query, where } from 'firebase/firestore'
import { ActivityLogTypeFromDB } from '../activitiesTypes'

type Args = {
  organizationId: string | undefined
  timestampFilter?: {
    from: Timestamp
    to: Timestamp
  }
}

export default async ({ organizationId, timestampFilter }: Args) => {
  try {
    if (!organizationId) throw new Error('Missing organizationId')

    const path = pathStore.activities({ organizationId })
    const ref = collection(db, path)

    let q: Query = query(ref, orderBy('timestamp', 'desc'))

    if (timestampFilter) {
      q = query(
        ref,
        where('timestamp', '>=', timestampFilter.from),
        where('timestamp', '<=', timestampFilter.to),
        orderBy('timestamp', 'desc'),
      )
    }

    const querySnapshot = await getDocs(q)

    const activitiesData: ActivityLogTypeFromDB[] = []

    querySnapshot.forEach((doc) => {
      const activityData = doc.data() as ActivityLogTypeFromDB
      activitiesData.push({
        ...activityData,
      })
    })

    return activitiesData
  } catch (error) {
    Sentry.captureException(error)
    throw error
  }
}
