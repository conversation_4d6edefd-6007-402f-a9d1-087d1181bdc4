import { db } from '@/configs/firebase'
import pathStore from '@/firestoreQueries/utils/pathStore'
import {
    Query,
    Timestamp,
    collection,
    doc,
    getDocs,
    query,
    where,
} from 'firebase/firestore'
import { ActivityLogTypeFromDB } from '../activitiesTypes'
import * as Sentry from '@sentry/react'

type Args = {
    organizationId: string | undefined
    userId: string
    timestampFilter?: {
        from: Timestamp
        to: Timestamp
    }
}

export default async ({ organizationId, userId, timestampFilter }: Args) => {
    try {
        if (!organizationId) throw new Error('Missing organizationId')
        const path = pathStore.activities({ organizationId })
        const ref = collection(db, path)

        const userPath = pathStore.user({ userId })
        const userRef = doc(db, userPath)

        let q: Query = query(ref, where('userRef', '==', userRef))
        if (timestampFilter) {
            const start = timestampFilter.from
            const end = timestampFilter.to

            q = query(
                ref,
                where('userRef', '==', userRef),
                where('timestamp', '>=', start),
                where('timestamp', '<=', end)
            )
        }
        const querySnapshot = await getDocs(q)
        const activitiesData: ActivityLogTypeFromDB[] = []
        querySnapshot.forEach((doc) => {
            const activityData = doc.data() as ActivityLogTypeFromDB
            activitiesData.push({
                ...activityData,
            })
        })
        return activitiesData
    } catch (error) {
        Sentry.captureException(error)
        throw error
    }
}
