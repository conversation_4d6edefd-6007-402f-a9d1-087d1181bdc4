import { DocumentReference, Timestamp } from 'firebase/firestore'
import { ResourceTypeFromDB } from '../resources/resourcesTypes'
import { PortalTypeFromDB } from '../portals/portalTypes'
import { IntegrationTypeFromDB } from '../integrations/IntegrationTypes'

export type DocumentActivityOperationType = 'update' | 'delete' | 'create'

export type ActivityType = 'connectToPortal' | 'document'

export type DocumentType = 'portal' | 'resource' | 'integration'

export type UpdateType =
    | Partial<ResourceTypeFromDB>
    | Partial<PortalTypeFromDB>
    | Partial<IntegrationTypeFromDB>

export type DocumentActivityDetailsType = {
    documentRef: DocumentReference
    operation: DocumentActivityOperationType
    documentType: DocumentType
    update: UpdateType
}

export type ConnectToPortalActivityDetailsType = {
    portalRef: DocumentReference
    resourceRef: DocumentReference
}

export type DocumentActivityLogTypeFromDB = {
    details: DocumentActivityDetailsType
    timestamp: Timestamp
    userRef: DocumentReference
    type: 'document'
}

export type ConnectToPortalActivityLogTypeFromDB = {
    details: ConnectToPortalActivityDetailsType
    timestamp: Timestamp
    userRef: DocumentReference
    type: 'connectToPortal'
}

export type ActivityLogTypeFromDB =
    | DocumentActivityLogTypeFromDB
    | ConnectToPortalActivityLogTypeFromDB
