import reactQueryKeyStore from '@/configs/reactQueryKeyStore'
import { useQuery } from '@tanstack/react-query'
import { Timestamp } from 'firebase/firestore'
import getActivities from '../queries/getActivities'

type Args = {
    organizationId: string | undefined
    timestampFilter?: {
        from: Timestamp
        to: Timestamp
    }
    queryKey?: Array<
        string | { filters: { from: Timestamp; to: Timestamp } | undefined }
    >
}

export default ({ organizationId, timestampFilter, queryKey }: Args) => {
    const defaultQueryKey = reactQueryKeyStore.activities({ timestampFilter })
    return useQuery({
        queryKey: queryKey ?? defaultQueryKey,
        queryFn: async () => getActivities({ organizationId, timestampFilter }),
        staleTime: 1000 * 60 * 5, // 5 minutes
        gcTime: 1000 * 60 * 1, // 1 minute
        placeholderData: [],
    })
}
