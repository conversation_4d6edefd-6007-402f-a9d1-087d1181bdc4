import { useQuery } from '@tanstack/react-query'
import getDocumentActivities from '../queries/getDocumentActivities'
import { Timestamp } from 'firebase/firestore'

type Args = {
    organizationId: string | undefined
    timestampFilter?: {
        from: Timestamp
        to: Timestamp
    }
    queryKey: Array<
        string | { filters: { from: Timestamp; to: Timestamp } | undefined }
    >
    activitiesPath: string
}

export default ({
    organizationId,
    timestampFilter,
    queryKey,
    activitiesPath,
}: Args) => {
    return useQuery({
        queryKey,
        queryFn: async () =>
            getDocumentActivities({
                organizationId,
                timestampFilter,
                activitiesPath,
            }),
        gcTime: 1000 * 60 * 1, // 1 minute
    })
}
