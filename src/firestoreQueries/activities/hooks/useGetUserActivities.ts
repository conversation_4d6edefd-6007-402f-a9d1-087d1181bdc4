import reactQueryKeyStore from '@/configs/reactQueryKeyStore'
import { useQuery } from '@tanstack/react-query'
import { Timestamp } from 'firebase/firestore'
import getUserActivities from '../queries/getUserActivities'

type Args = {
    organizationId: string | undefined
    timestampFilter?: {
        from: Timestamp
        to: Timestamp
    }
    userId: string
}

export default ({ organizationId, timestampFilter, userId }: Args) => {
    const queryKey = reactQueryKeyStore.userActivities({
        timestampFilter,
        userId,
    })
    return useQuery({
        queryKey: queryKey,
        queryFn: async () =>
            getUserActivities({ organizationId, timestampFilter, userId }),
        staleTime: 1000 * 60 * 5, // 5 minutes
    })
}
