import reactQueryKeyStore from '@/configs/reactQueryKeyStore'
import { useQuery } from '@tanstack/react-query'
import getScanIssues from '../queries/getScanIssues'
import processScanIssues from '../utils/processScanIssues'

type Args = {
    organizationId: string | undefined
    scanId: string
}

export default ({ organizationId, scanId }: Args) => {
    const queryKey = reactQueryKeyStore.scanIssues({
        organizationId: organizationId!,
        scanId,
    })
    return useQuery({
        queryKey,
        queryFn: async () => {
            const res = await getScanIssues({ organizationId, scanId })
            return processScanIssues(res)
        },
        staleTime: 1000 * 60 * 5, // 5 minutes
    })
}
