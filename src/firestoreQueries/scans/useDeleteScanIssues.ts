import { useMutation } from '@tanstack/react-query'
import deleteScanIssues from './queries/deleteScanIssues'
import { queryClient } from '@/Providers/ReactQueryProvider'
import { ScanIssue } from './scansTypes'

type Args = {
    scanId: string
    organizationId: string | undefined
    queryKey: Array<string>
}

export default () => {
    return useMutation({
        mutationFn: async ({ scanId, organizationId }: Args) => {
            await deleteScanIssues({ scanId, organizationId })
        },
        onMutate: ({ scanId, queryKey }) => {
            const prev: ScanIssue[] = queryClient.getQueryData(queryKey) ?? []

            queryClient.setQueryData(
                queryKey,
                prev.filter((scan) => scan.scanId !== scanId)
            )

            return () => queryClient.setQueryData(queryKey, prev)
        },
        onSuccess: (_, { scanId, queryKey }) => {
            const prev: ScanIssue[] = queryClient.getQueryData(queryKey) ?? []

            queryClient.setQueryData(
                queryKey,
                prev.filter((scan) => scan.scanId !== scanId)
            )
        },
        onError: (_, __, rollback) => {
            if (rollback) rollback()
        },
    })
}
