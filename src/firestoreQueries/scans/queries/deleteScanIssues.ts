import { db } from '@/configs/firebase'
import pathStore from '@/firestoreQueries/utils/pathStore'
import { deleteDoc, doc } from 'firebase/firestore'
import * as Sentry from '@sentry/react'

type Args = {
    scanId: string
    organizationId: string | undefined
}

export default async ({ scanId, organizationId }: Args) => {
    try {
        if (!organizationId) throw new Error('Missing organziationId')

        const path = pathStore.oneScan({ organizationId, scanId })

        const ref = doc(db, path)
        await deleteDoc(ref)
    } catch (error) {
        Sentry.captureException(error)
        throw error
    }
}
