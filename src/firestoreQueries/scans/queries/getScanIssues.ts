import { db } from '@/configs/firebase'
import pathStore from '@/firestoreQueries/utils/pathStore'
import { collection, getDocs } from 'firebase/firestore'
import { ScanIssue } from '../scansTypes'
import * as Sentry from '@sentry/react'

type Args = {
    organizationId: string | undefined
    scanId: string
}

export default async ({ organizationId, scanId }: Args) => {
    try {
        if (!organizationId) throw new Error('Missing organizationId')

        const collectionPath = pathStore.scanIssues({ organizationId, scanId })
        const collectionRef = collection(db, collectionPath)
        const snap = await getDocs(collectionRef)

        const issues: ScanIssue[] = []
        snap.forEach((doc) => {
            issues.push(doc.data() as ScanIssue)
        })

        return issues
    } catch (error) {
        Sentry.captureException(error)
        throw error
    }
}
