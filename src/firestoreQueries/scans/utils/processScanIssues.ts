import { ScanIssue } from '../scansTypes'

export default (scanIssues: ScanIssue[]) => {
    //create a map of {category: issue}
    const issuesMap: { [key: string]: ScanIssue } = {}

    //iterate over all issues, for each issue, if it's category exists, append the affectedResources (make sure they are unique)
    scanIssues.forEach((issue) => {
        if (!issuesMap[issue.category]) {
            issuesMap[issue.category] = issue
        } else {
            const affectedResourcesFromMap =
                issuesMap[issue.category].affectedResources

            issue.affectedResources.forEach((resource) => {
                if (!affectedResourcesFromMap.includes(resource)) {
                    affectedResourcesFromMap.push(resource)
                }
            })

            const newIssue = {
                ...issuesMap[issue.category],
                affectedResources: affectedResourcesFromMap,
            }

            issuesMap[issue.category] = newIssue
        }
    })
    //create a new array of issues
    const newIssues = Object.values(issuesMap)

    return newIssues
}
