import { db } from '@/configs/firebase'
import { doc, getDoc } from 'firebase/firestore'
import { OrganizationType } from '../organizationsTypes'
import * as Sentry from '@sentry/react'

type Args = {
    organizationId: string | undefined
}

export default async ({ organizationId }: Args) => {
    try {
        if (!organizationId) throw new Error('Missing OrganizationId')

        const path = `organizations/${organizationId}`
        const ref = doc(db, path)
        const snapshot = await getDoc(ref)

        return snapshot.data() as OrganizationType
    } catch (error) {
        Sentry.captureException(error)
        throw error
    }
}
