import { useQuery } from '@tanstack/react-query'
import getOrganization from '../queries/getOrganization'

type Args = {
    organizationId: string | undefined
}

export default ({ organizationId }: Args) => {
    return useQuery({
        queryKey: ['organization', organizationId],
        queryFn: async () => getOrganization({ organizationId }),
        staleTime: 1000 * 60 * 60 * 24, // 24 hours
    })
}
