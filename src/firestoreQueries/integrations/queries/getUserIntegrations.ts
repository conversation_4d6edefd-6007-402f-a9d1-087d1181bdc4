import { db } from '@/configs/firebase'
import { getQueryConstraints } from '@/customUtils/getGroupsQueryConstraints'
import getUserGroups from '@/firestoreQueries/groups/queries/getUserGroups'
import { IntegrationTypeFromDB, IntegrationTypeWithRef } from '@/firestoreQueries/integrations/IntegrationTypes'
import pathStore from '@/firestoreQueries/utils/pathStore'
import * as Sentry from '@sentry/react'
import { getAuth } from 'firebase/auth'
import { collection, getDocs, query } from 'firebase/firestore'

type Args = {
  organizationId: string | undefined
  userId: string
}

export default async ({ organizationId }: Args) => {
  try {
    if (!organizationId) throw new Error('Missing OrganizationId')
    const userId = getAuth().currentUser?.uid
    if (!userId) throw new Error('Missing UserId')

    const path = pathStore.integrations({ organizationId })
    const integrationsRef = collection(db, path)
    const groupsIds = (await getUserGroups({ organizationId, userId })).map((g) => g.id)
    const constraints = getQueryConstraints(groupsIds, userId)
    const q = query(integrationsRef, constraints)
    const integrationsSnap = await getDocs(q)

    const integrations: IntegrationTypeWithRef[] = []

    integrationsSnap.forEach((doc) => {
      const integration: IntegrationTypeWithRef = {
        ...(doc.data() as IntegrationTypeFromDB),
        ref: doc.ref,
      }
      integrations.push(integration)
    })

    return integrations
  } catch (error) {
    Sentry.captureException(error)
    throw error
  }
}
