import { db } from '@/configs/firebase'
import {
    IntegrationTypeFromDB,
    IntegrationTypeWithRef,
} from '@/firestoreQueries/integrations/IntegrationTypes'
import pathStore from '@/firestoreQueries/utils/pathStore'
import { doc, getDoc } from 'firebase/firestore'
import * as Sentry from '@sentry/react'

type Args = {
    integrationId: string
    organizationId: string | undefined
}

export default async ({ integrationId, organizationId }: Args) => {
    try {
        if (!organizationId) {
            throw new Error('Missing OrganizationId')
        }

        const integrationPath = pathStore.oneIntegration({
            organizationId,
            integrationId,
        })

        const integrationRef = doc(db, integrationPath)
        const integration = (
            await getDoc(integrationRef)
        ).data() as IntegrationTypeFromDB

        return {
            ...integration,
            ref: integrationRef,
        } as IntegrationTypeWithRef
    } catch (error) {
        Sentry.captureException(error)
        throw error
    }
}
