import { db } from '@/configs/firebase'
import {
  AvailableIntegrations,
  IntegrationTypeFromDB,
  IntegrationTypeWithRef,
} from '@/firestoreQueries/integrations/IntegrationTypes'
import pathStore from '@/firestoreQueries/utils/pathStore'
import { collection, getDocs, query, where } from 'firebase/firestore'
import * as Sentry from '@sentry/react'

type Args = {
  organizationId: string | undefined
  filter?: Array<AvailableIntegrations>
}

export const getIntegrations = async ({ organizationId, filter }: Args) => {
  try {
    if (!organizationId) {
      throw new Error('Missing OrganizationId')
    }
    const path = pathStore.integrations({ organizationId })
    const integrationsRef = collection(db, path)
    let q
    if (filter) {
      q = query(integrationsRef, where('integrationType', 'not-in', filter))
    } else {
      q = query(integrationsRef)
    }
    const integrationsSnap = await getDocs(q)

    const integrations: IntegrationTypeWithRef[] = []

    integrationsSnap.forEach((doc) => {
      const integration: IntegrationTypeWithRef = {
        ...(doc.data() as IntegrationTypeFromDB),
        ref: doc.ref,
      }
      integrations.push(integration)
    })

    return integrations
  } catch (error) {
    Sentry.captureException(error)
    return []
  }
}
