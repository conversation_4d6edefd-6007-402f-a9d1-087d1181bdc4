import { DocumentReference, getDoc } from 'firebase/firestore'
import {
    IntegrationTypeFromDB,
    IntegrationTypeWithRef,
} from '../IntegrationTypes'
import * as Sentry from '@sentry/react'

type Args = {
    refsArray: Array<DocumentReference>
}

export default async ({ refsArray }: Args) => {
    try {
        const resourcesData: Array<IntegrationTypeWithRef> = []

        const ids: Set<string> = new Set()

        for (const ref of refsArray) {
            if (ids.has(ref.id)) continue
            const doc = await getDoc(ref)
            const resourceData = doc.data() as IntegrationTypeFromDB
            resourcesData.push({
                ...resourceData,
                ref: doc.ref,
            })
            ids.add(ref.id)
        }

        return resourcesData
    } catch (error) {
        Sentry.captureException(error)
        throw error
    }
}
