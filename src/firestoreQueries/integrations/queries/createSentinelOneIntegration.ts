import { captureException } from '@sentry/react'
import { IntegrationTypeFromDB } from '../IntegrationTypes'
import pathStore from '@/firestoreQueries/utils/pathStore'
import { addDoc, collection } from 'firebase/firestore'
import { db } from '@/configs/firebase'
import getOneIntegration from './getOneIntegration'

type Args = {
  organizationId: string | undefined
  integration: IntegrationTypeFromDB
}

export default async function createSentinelOneIntegration({ organizationId, integration }: Args) {
  try {
    if (!organizationId) throw new Error('organizationId is undefined')

    const path = pathStore.integrations({ organizationId })
    const ref = collection(db, path)
    const newDocRef = await addDoc(ref, integration)

    return await getOneIntegration({ organizationId, integrationId: newDocRef.id })
  } catch (error) {
    captureException(error)
    throw error
  }
}
