import {
    IntegrationTypeFromDB,
    IntegrationTypeWithRef,
} from '@/firestoreQueries/integrations/IntegrationTypes'
import { DocumentReference, setDoc } from 'firebase/firestore'
import * as Sentry from '@sentry/react'

type Args = {
    integration: IntegrationTypeFromDB
    organizationId: string | undefined
    integrationRef: DocumentReference
}

export default async ({
    integration,
    organizationId,
    integrationRef,
}: Args) => {
    try {
        if (!organizationId) throw new Error('Missing OrganizationId')

        await setDoc(integrationRef, integration)

        return {
            ...integration,
            ref: integrationRef,
        } as IntegrationTypeWithRef
    } catch (error) {
        Sentry.captureException(error)
        throw error
    }
}
