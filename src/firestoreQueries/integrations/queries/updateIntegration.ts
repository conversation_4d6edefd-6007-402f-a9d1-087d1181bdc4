import { DocumentReference, updateDoc } from 'firebase/firestore'
import { IntegrationTypeFromDB } from '../IntegrationTypes'
import * as Sentry from '@sentry/react'

type Args = {
    integrationRef: DocumentReference
    integration: IntegrationTypeFromDB
}

export default async ({ integration, integrationRef }: Args) => {
    try {
        await updateDoc(integrationRef, integration)
    } catch (error) {
        Sentry.captureException(error)
        throw error
    }
}
