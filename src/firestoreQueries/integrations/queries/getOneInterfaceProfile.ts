import { DocumentReference, getDoc } from 'firebase/firestore'
import { InterfaceProfile } from '../IntegrationTypes'
import * as Sentry from '@sentry/react'

type Args = {
    interfaceProfileRef: DocumentReference
}

export default async ({ interfaceProfileRef }: Args) => {
    try {
        return (await getDoc(interfaceProfileRef)).data() as InterfaceProfile
    } catch (error) {
        Sentry.captureException(error)
        throw error
    }
}
