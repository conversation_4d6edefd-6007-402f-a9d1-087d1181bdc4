import { DocumentReference } from 'firebase/firestore'

export type AvailableIntegrations = 'aws' | 'standalone' | 'gcp' | 'azure' | 'docker' | 'sentinelOne' | 'crowdstrike' | 'msDefender'

export type IntegrationNames =
  | 'Amazon Web Services (AWS)'
  | 'Standalone'
  | 'Azure'
  | 'Google Cloud Platform (GCP)'
  | 'Docker'
  | 'Datadog'
  | 'Sentinel One'
  | 'CrowdStrike'
  | 'Microsoft Defender'


export type MSDefenderIntegrationConfig = {
  tenantId: string
  clientId: string
  clientSecret: string
}

export type AWSIntegrationConfig = {
  roleArn: string
  externalId: string
}

export type CrowdStrikeIntegrationConfig = {
  secret: string
  clientId: string
  region: string
}

export type SentinelOneIntegrationConfig = {
  token: string
  endpoint: string
}

export type IntegrationTypeFromDB = {
  integrationType: AvailableIntegrations
  name: string
  description: string
  config: AWSIntegrationConfig | SentinelOneIntegrationConfig | CrowdStrikeIntegrationConfig | MSDefenderIntegrationConfig
}

export type IntegrationTypeWithRef = IntegrationTypeFromDB & {
  ref: DocumentReference
}

export type IntegrationResourceServerType = 'EC2'

export type AssociatedInstanceType = {
  id: string
  ip: string
  name: string
  type: 'EC2' | 'RDS'
}

export type IntegrationSecurityGroupType = {
  id: string
  name: string
  description: string
  ip: string | null
  options?: {
    associatedInstances: Array<AssociatedInstanceType>
    region?: string
    vpcId?: string
  }
  type: IntegrationResourceServerType
}

export type InterfaceProfile = {
  metadata: {
    availabilityZone: string
    interfaceId: string
    macAddress: string
    primaryPrivateIPAddr: string
    primaryPublicIPAddr?: string
    privateIPAdrrs: Array<string>
    publicIPAddrs: Array<string>
    region: string
    vpcId: string
  }
  resource: {
    arn: string
    region: string
    resourceId: string
    resourceType: string
    service: string
    tags: {
      Name: string
    }
  }
  securityGroups: Array<{
    description: string
    groupId: string
    groupName: string
    inboundRules: Array<{
      fromPort: number
      ipProtocol: string
      ipRanges: Array<string>
      ipv6Ranges: Array<string>
      prefixListIDs: Array<string>
      toPort: number
      userIDGroupPairs: Array<string>
    }>
    outboundRules: Array<{
      fromPort: number
      ipProtocol: string
      ipRanges: Array<string>
      ipv6Ranges: Array<string>
      prefixListIDs: Array<string>
      toPort: number
      userIDGroupPairs: Array<string>
      vpcId: string
    }>
  }>
}

export type InterfaceTrafficPattern = {
  direction: 'INGRESS' | 'EGRESS'
  localAddr: string
  port: number | string
  remote: RemoteObject
  remoteType: 'VPC_RESOURCE' | 'AGGREGATED' | 'INTERNET' | 'AWS_SERVICE' | 'UNKNOWN' | 'NONE'
  trafficStats: {
    commonEgressVolume: number
    commonIngressVolume: number
    commonSessionVolume: number
    sessionCount: number
  }
  isDangerous?: true
}
//
export type InternetRemote = {
  remoteAddr: string
  port: number
  geolocation: string
}

export type AWSServiceRemote = {
  remoteAddr: string
  port: number
  serviceName:
    | 'AMAZON'
    | 'AMAZON_APPFLOW'
    | 'AMAZON_CONNECT'
    | 'API_GATEWAY'
    | 'CHIME_MEETINGS'
    | 'CHIME_VOICECONNECTOR'
    | 'CLOUD9'
    | 'CLOUDFRONT'
    | 'CODEBUILD'
    | 'DYNAMODB'
    | 'EBS'
    | 'EC2'
    | 'EC2_INSTANCE_CONNECT'
    | 'GLOBALACCELERATOR'
    | 'KINESIS_VIDEO_STREAMS'
    | 'ROUTE53'
    | 'ROUTE53_HEALTHCHECKS'
    | 'ROUTE53_HEALTHCHECKS_PUBLISHING'
    | 'ROUTE53_RESOLVER'
    | 'S3'
    | 'WORKSPACES_GATEWAYS'
}

export type VPCResourceRemote = {
  remoteAddr: string
  port: number
  name: string
  resourceType: 'EC2' | 'RDS' | 'instance'
  resourceId: string
}

export type UnknownRemote = {
  remoteAddr: string
  port: number
}

export type AggregatedRemote = {
  aggregateType:
    | 'INTERNET_SCANNERS_INGRESS_AGGREGATION'
    | 'INTERNET_SCANNERS_EGRESS_AGGREGATION'
    | 'AWS_EGRESS_AGGREGATION'
  port: number
}

export type RemoteObject =
  | InternetRemote
  | AWSServiceRemote
  | VPCResourceRemote
  | UnknownRemote
  | AggregatedRemote
  | object
