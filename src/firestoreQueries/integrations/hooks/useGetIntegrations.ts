import { useQuery } from '@tanstack/react-query'
import { getIntegrations } from '../queries/getIntegrations'
import { AvailableIntegrations } from '@/firestoreQueries/integrations/IntegrationTypes'
import reactQueryKeyStore from '@/configs/reactQueryKeyStore'

type Args = {
  organizationId: string | undefined
  queryKey: string[]
  filter?: Array<AvailableIntegrations>
}

export default ({ organizationId, filter }: Args) => {
  const queryKey = reactQueryKeyStore.integrations(filter)
  return useQuery({
    queryKey,
    queryFn: async () => await getIntegrations({ organizationId, filter }),
    staleTime: 1000 * 60 * 5, // 5 minutes
  })
}
