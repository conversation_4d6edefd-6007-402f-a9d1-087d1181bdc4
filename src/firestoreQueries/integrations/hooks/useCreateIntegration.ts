import { queryClient } from '@/Providers/ReactQueryProvider'
import {
    IntegrationTypeFromDB,
    IntegrationTypeWithRef,
} from '@/firestoreQueries/integrations/IntegrationTypes'
import { useMutation } from '@tanstack/react-query'
import { DocumentReference } from 'firebase/firestore'
import createIntegration from '../queries/createIntegration'

type Args = {
    integration: IntegrationTypeFromDB
    organizationId: string | undefined
    queryKey: string[]
    integrationRef: DocumentReference
}

export default () => {
    return useMutation({
        mutationFn: async ({
            integration,
            organizationId,
            integrationRef,
        }: Args) => {
            return await createIntegration({
                integration,
                organizationId,
                integrationRef,
            })
        },
        onMutate: ({
            integration,
            queryKey,
            organizationId,
            integrationRef,
        }) => {
            if (!organizationId) throw new Error('Organization ID is required')

            const prevIntegrations: IntegrationTypeWithRef[] =
                queryClient.getQueryData(queryKey) || []

            const newIntegration: IntegrationTypeWithRef = {
                ...integration,
                ref: integrationRef,
            }

            queryClient.setQueryData(queryKey, [
                ...prevIntegrations,
                newIntegration,
            ])

            return {
                integrationRef,
                rollback: () =>
                    queryClient.setQueryData(queryKey, prevIntegrations),
            }
        },
        onSuccess: (createdIntegration, { queryKey }, { integrationRef }) => {
            const prevIntegrations: IntegrationTypeWithRef[] =
                queryClient.getQueryData(queryKey) || []

            const newIntegrations = prevIntegrations.map((integration) => {
                if (integration.ref.id === integrationRef.id)
                    return createdIntegration
                return integration
            })

            queryClient.setQueryData(queryKey, newIntegrations)
        },
        onError: (error, variables, context) => {
            if (context?.rollback) context.rollback()
        },
    })
}
