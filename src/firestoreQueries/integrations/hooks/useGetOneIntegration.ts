import { useQuery } from '@tanstack/react-query'
import getOneIntegration from '../queries/getOneIntegration'

type Args = {
    integrationId: string
    organizationId: string | undefined
    queryKey: string[]
}

export default ({ integrationId, organizationId, queryKey }: Args) => {
    return useQuery({
        queryKey,
        queryFn: async () =>
            await getOneIntegration({ integrationId, organizationId }),
        staleTime: 1000 * 60 * 5,
    })
}
