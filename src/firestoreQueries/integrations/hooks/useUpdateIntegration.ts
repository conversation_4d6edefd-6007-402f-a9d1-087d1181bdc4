import { DocumentReference } from 'firebase/firestore'
import { IntegrationTypeFromDB, IntegrationTypeWithRef } from '../IntegrationTypes'
import { useMutation } from '@tanstack/react-query'
import updateIntegration from '../queries/updateIntegration'
import { queryClient } from '@/Providers/ReactQueryProvider'

type Args = {
    integrationRef: DocumentReference
    integration: IntegrationTypeFromDB
    queryKey: string[]
}
export default () => {
    return useMutation({
        mutationFn: ({ integration, integrationRef }: Args) => updateIntegration({ integration, integrationRef }),
        onMutate: ({ integration, integrationRef, queryKey }) => {
            const oldIntegration: IntegrationTypeWithRef | undefined = queryClient.getQueryData(queryKey)

            if (!oldIntegration) {
                throw new Error('no integration found')
            }

            const newIntegration = {
                ...integration,
                ref: integrationRef,
            } as IntegrationTypeWithRef

            queryClient.setQueryData(queryKey, newIntegration)

            return {
                rollback: () => queryClient.setQueryData(queryKey, oldIntegration),
            }
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['integrations'] })
            queryClient.removeQueries({
                queryKey: ['documentActivities'],
                exact: false,
            })
        },
        onError: (error, _, context) => {
            if (context) {
                context.rollback()
            }
            return error
        },
    })
}
