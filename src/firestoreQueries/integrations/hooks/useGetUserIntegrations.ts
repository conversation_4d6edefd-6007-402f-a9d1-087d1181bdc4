import { useQuery } from '@tanstack/react-query'
import refetchOnMountFn from '@/firestoreQueries/utils/refetchOnMountFn'
import { IntegrationTypeWithRef } from '../IntegrationTypes'
import getUserIntegrations from '../queries/getUserIntegrations'

type Args = {
    organizationId: string | undefined
    userId: string
}

export default ({ organizationId, userId }: Args) => {
    const queryKey = ['userIntegrations', userId]
    return useQuery({
        queryKey,
        queryFn: async () => {
            return await getUserIntegrations({
                organizationId,
                userId,
            })
        },
        staleTime: 1000 * 60 * 5, // 5 minutes
        refetchOnMount: () => refetchOnMountFn<IntegrationTypeWithRef>(queryKey),
    })
}
