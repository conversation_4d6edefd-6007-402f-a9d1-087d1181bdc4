import { queryClient } from '@/Providers/ReactQueryProvider'
import { useMutation } from '@tanstack/react-query'
import { DocumentReference } from 'firebase/firestore'
import { IntegrationTypeWithRef } from '../IntegrationTypes'
import deleteIntegration from '../queries/deleteIntegration'
import reactQueryKeyStore from '@/configs/reactQueryKeyStore'

type Args = {
  integrationRef: DocumentReference
  queryKey: Array<string>
}

export default () => {
  return useMutation({
    mutationFn: async ({ integrationRef }: Args) => {
      await deleteIntegration({ integrationRef })
    },
    onMutate: async ({ integrationRef, queryKey }: Args) => {
      queryClient.removeQueries({
        queryKey: reactQueryKeyStore.oneIntegration({
          integrationId: integrationRef.id,
        }),
      })

      const prevIntegrations: IntegrationTypeWithRef[] = queryClient.getQueryData(queryKey) ?? []

      const newIntegrations = prevIntegrations.filter((integration) => integration.ref.id !== integrationRef.id)

      queryClient.setQueryData(queryKey, newIntegrations)

      return {
        rollback: () => queryClient.setQueryData(queryKey, prevIntegrations),
      }
    },
    onError: (error, variables, context) => {
      if (context) {
        context.rollback()
      }
    },
  })
}
