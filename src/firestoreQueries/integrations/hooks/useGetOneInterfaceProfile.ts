import reactQueryKeyStore from '@/configs/reactQueryKeyStore'
import { useQuery } from '@tanstack/react-query'
import { DocumentReference } from 'firebase/firestore'
import getOneInterfaceProfile from '../queries/getOneInterfaceProfile'

type Args = {
    interfaceProfileRef: DocumentReference
}

export default ({ interfaceProfileRef }: Args) => {
    const queryKey = reactQueryKeyStore.oneInterfacePorfile({
        interfaceId: interfaceProfileRef.id,
    })

    return useQuery({
        queryKey,
        queryFn: async () => getOneInterfaceProfile({ interfaceProfileRef }),
        staleTime: 1000 * 60 * 5,
    })
}
