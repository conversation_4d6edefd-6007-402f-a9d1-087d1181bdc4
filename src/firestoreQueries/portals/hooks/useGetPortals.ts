import reactQueryKeyStore from '@/configs/reactQueryKeyStore'
import { useQuery } from '@tanstack/react-query'
import getPortals from '../queries/getPortals'
import { PortalType } from '../portalTypes'
import refetchOnMountFn from '@/firestoreQueries/utils/refetchOnMountFn'

type Args = {
  organizationId: string | undefined
  isAdmin: boolean
}

export default ({ organizationId, isAdmin }: Args) => {
  const queryKey = reactQueryKeyStore.portals()
  return useQuery({
    queryKey,
    queryFn: async () => await getPortals({ organizationId, isAdmin }),
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnMount: () => refetchOnMountFn<PortalType>(queryKey),
  })
}
