import { queryClient } from '@/Providers/ReactQueryProvider'
import reactQueryKeyStore, { accessQueryKeyStore } from '@/configs/reactQueryKeyStore'
import { useMutation } from '@tanstack/react-query'
import { PortalType } from '../portalTypes'
import deletePortal from '../queries/deletePortal'

type Args = {
  portalId: string
  organizationId: string | undefined
}

export default () => {
  return useMutation({
    mutationFn: async ({ portalId, organizationId }: Args) => {
      await deletePortal({
        portalId,
        organizationId,
      })
    },
    onMutate: ({ portalId, organizationId }) => {
      const portalsQueryKey = reactQueryKeyStore.portals()
      const prevPortals: Array<PortalType> = queryClient.getQueryData(portalsQueryKey) || []

      const newPortals = prevPortals.filter((portal) => portal.id !== portalId)

      queryClient.setQueryData(portalsQueryKey, newPortals)
      queryClient.invalidateQueries({ queryKey: ['resourcePortals'], exact: false })
      const portalGroupsQueryKey = accessQueryKeyStore.portalGroups({ organizationId: organizationId! })
      queryClient.invalidateQueries({ queryKey: portalGroupsQueryKey })

      return {
        rollback: () => queryClient.setQueryData(portalsQueryKey, prevPortals),
      }
    },
    onError: (error, _, context) => {
      if (context) {
        context.rollback()
      }
      return error
    },
  })
}
