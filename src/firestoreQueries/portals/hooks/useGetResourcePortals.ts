import { useQuery } from '@tanstack/react-query'
import { DocumentReference } from 'firebase/firestore'
import getResourcePortals from '../queries/getResourcePortals'

type Args = {
    organizationId: string | undefined
    resourceRef: DocumentReference
    queryKey: Array<string>
}

export default ({ organizationId, resourceRef, queryKey }: Args) => {
    return useQuery({
        queryKey,
        queryFn: async () =>
            getResourcePortals({ organizationId, resourceRef }),
        staleTime: 1000 * 60 * 5, // 5 minutes
    })
}
