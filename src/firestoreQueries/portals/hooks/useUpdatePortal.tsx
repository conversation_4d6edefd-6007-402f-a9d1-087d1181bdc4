import { queryClient } from '@/Providers/ReactQueryProvider'
import reactQueryKeyStore from '@/configs/reactQueryKeyStore'
import { useMutation } from '@tanstack/react-query'
import { PortalType } from '../portalTypes'
import updatePortal from '../queries/updatePortal'

type Args = {
    queryKey: string[]
    portal: PortalType
    organizationId: string | undefined
}

export default () => {
    return useMutation({
        mutationFn: async ({ portal, organizationId }: Args) => {
            return await updatePortal({ portal, organizationId })
        },
        onMutate: ({ portal, queryKey }) => {
            const prevPortal: PortalType = queryClient.getQueryData(queryKey) || ({} as PortalType)

            queryClient.setQueryData(queryKey, portal)

            const portalsQueryKey = reactQueryKeyStore.portals()
            const prevPortals: PortalType[] = queryClient.getQueryData(portalsQueryKey) || []

            const newPortals = prevPortals.map((p) => {
                if (p.id === portal.id) {
                    return {
                        ...portal,
                    } as PortalType
                }
                return p
            })

            queryClient.setQueryData(portalsQueryKey, newPortals)

            const rollback = () => queryClient.setQueryData(queryKey, prevPortal)
            return { rollback }
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['resourcePortals'], exact: false })
            queryClient.removeQueries({
                queryKey: ['documentActivities'],
                exact: false,
            })
        },
        onError: (error, _, context) => {
            if (context) {
                context.rollback()
            }
            return error
        },
    })
}
