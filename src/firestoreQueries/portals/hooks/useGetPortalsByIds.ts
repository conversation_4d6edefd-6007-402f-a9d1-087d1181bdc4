import reactQueryKeyStore from '@/configs/reactQueryKeyStore'
import { useQuery } from '@tanstack/react-query'
import getPortalsByIds from '../queries/getPortalsByIds'
import { useAuth } from '@clerk/clerk-react'

type Args = {
  organizationId: string | undefined
  portalsIds: string[]
}

export default function ({ organizationId, portalsIds }: Args) {
  const isAdmin = useAuth().orgRole === 'admin'
  return useQuery({
    queryKey: reactQueryKeyStore.specificPortals({ organizationId: organizationId!, portalsIds }),
    queryFn: async () => await getPortalsByIds({ organizationId, portalsIds, isAdmin }),
  })
}
