import { queryClient } from '@/Providers/ReactQueryProvider'
import { RolesType } from '@/firestoreQueries/utils/generalTypes'
import { useMutation } from '@tanstack/react-query'
import { DocumentReference } from 'firebase/firestore'
import { PortalInfoType, PortalPortsAccessType, PortalType } from '../portalTypes'
import createNewPortal from '../queries/createNewPortal'

type Args = {
  organizationId: string | undefined
  portalInfo: PortalInfoType
  portRules: Array<PortalPortsAccessType>
  roles: RolesType
  resourceRef: DocumentReference
  accessDuration: number
  queryKey: Array<string>
}

export default () => {
  return useMutation({
    mutationFn: async ({ roles, organizationId, portRules, portalInfo, resourceRef, accessDuration }: Args) =>
      await createNewPortal({
        accessDuration,
        organizationId,
        portRules,
        portalInfo,
        resourceRef,
        roles,
      }),
    onSuccess: (newPortal, { queryKey }) => {
      const prevPortals: Array<PortalType> = queryClient.getQueryData(queryKey) ?? []

      queryClient.invalidateQueries({ queryKey: ['resourcePortals'], exact: false })

      queryClient.setQueryData(queryKey, [...prevPortals, newPortal])
    },
    onError: (error) => {
      return error
    },
  })
}
