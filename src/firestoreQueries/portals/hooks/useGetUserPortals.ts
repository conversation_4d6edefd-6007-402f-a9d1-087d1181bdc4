import { useQuery } from '@tanstack/react-query'
import getUserPortals from '../queries/getUserPortals'
import refetchOnMountFn from '@/firestoreQueries/utils/refetchOnMountFn'
import { PortalType } from '../portalTypes'

type Args = {
    organizationId: string | undefined
    userId: string
}

export default ({ organizationId, userId }: Args) => {
    const queryKey = ['userPortals', userId]
    return useQuery({
        queryKey,
        queryFn: async () => {
            return await getUserPortals({
                organizationId,
                userId,
            })
        },
        staleTime: 1000 * 60 * 5, // 5 minutes
        refetchOnMount: () => refetchOnMountFn<PortalType>(queryKey),
    })
}
