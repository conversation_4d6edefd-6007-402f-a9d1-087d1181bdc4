import reactQueryKeyStore from '@/configs/reactQueryKeyStore'
import { useQuery } from '@tanstack/react-query'
import getOnePortal from '../queries/getOnePortal'

type Args = {
    organizationId: string | undefined
    portalId: string
}

export default ({ organizationId, portalId }: Args) => {
    const queryKey = reactQueryKeyStore.onePortal({ portalId })
    return useQuery({
        queryKey,
        queryFn: async () => await getOnePortal({ organizationId, portalId }),
        staleTime: 1000 * 60 * 5, // 5 minutes
    })
}
