import { DocumentReference } from 'firebase/firestore'
import { AvailableIntegrations } from '../integrations/IntegrationTypes'
import { DefaultRoleType, OwnerType, RolesType } from '../utils/generalTypes'

export type PortalPortsAccessType = {
  protocol: 'tcp' | 'udp'
  portRanges: string[]
}

export type PortalInfoType = {
  portalName: string
  portalDescription: string
  portalEmoji: string
  resourceEmoji: string
  integrationType: AvailableIntegrations
  portalIPs: string[]
  integrationIP: string
  portalOwner: OwnerType
}

export type PortalTypeFromDB = {
  accessDuration: number
  portalInfo: PortalInfoType
  roles: RolesType
  portsAccess: Array<PortalPortsAccessType>
  resourceRef: DocumentReference
  defaultRole: DefaultRoleType
}

export type PortalType = PortalTypeFromDB & { id: string }
