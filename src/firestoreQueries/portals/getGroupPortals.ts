import { captureException } from '@sentry/react'
import pathStore from '../utils/pathStore'
import { collection, getDocs, or, query, where } from 'firebase/firestore'
import { db } from '@/configs/firebase'
import { PortalType } from './portalTypes'

type Args = {
  organizationId: string | undefined
  groupId: string
}

export default async function ({ organizationId, groupId }: Args) {
  try {
    if (!organizationId) throw new Error('organizationId is undefined')

    const path = pathStore.portals({ organizationId })
    const ref = collection(db, path)
    const q = query(
      ref,
      or(where('roles.Viewers', 'array-contains', groupId), where('roles.Editors', 'array-contains', groupId)),
    )

    const snap = await getDocs(q)
    const portals: PortalType[] = []
    snap.forEach((doc) => portals.push({ ...doc.data(), id: doc.id } as PortalType))

    return portals
  } catch (error) {
    captureException(error)
    throw error
  }
}
