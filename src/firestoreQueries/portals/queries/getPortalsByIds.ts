import { db } from '@/configs/firebase'
import pathStore from '@/firestoreQueries/utils/pathStore'
import { captureException } from '@sentry/react'
import { getAuth } from 'firebase/auth'
import { collection, getDocs, query, where } from 'firebase/firestore'
import { PortalType } from '../portalTypes'
import getPortals from './getPortals'

type Args = {
  organizationId: string | undefined
  portalsIds: string[]
  isAdmin: boolean
}

function splitIntoChunks(array: string[], chunkSize: number): string[][] {
  const chunks = []
  for (let i = 0; i < array.length; i += chunkSize) {
    chunks.push(array.slice(i, i + chunkSize))
  }
  return chunks
}

async function fetchByChunk(path: string, chunk: string[], isAdmin: boolean): Promise<PortalType[]> {
  const ref = collection(db, path)
  let q = query(ref, where('__name__', 'in', chunk))
  if (isAdmin) {
    q = query(ref)
  }
  const snap = await getDocs(q)
  return snap.docs.map((doc) => ({ ...doc.data(), id: doc.id }) as PortalType)
}

// Main function to fetch portals
export default async function ({ organizationId, portalsIds, isAdmin }: Args) {
  try {
    if (portalsIds.length === 0) return []
    if (!organizationId) throw new Error('organizationId is undefined')
    const userId = getAuth().currentUser?.uid
    if (!userId) throw new Error('userId is undefined')

    const path = pathStore.portals({ organizationId })
    const permittedPortalsIds = (await getPortals({ isAdmin, organizationId })).map((p) => p.id)

    const filteredPortals = portalsIds.filter((p) => permittedPortalsIds.includes(p))

    const chunkSize = 10
    const chunks = splitIntoChunks(filteredPortals, chunkSize)

    const portalsPromises = chunks.map((chunk) => fetchByChunk(path, chunk, isAdmin))
    const portalsArrays = await Promise.all(portalsPromises)

    return portalsArrays.flat()
  } catch (error) {
    captureException(error)
    throw error
  }
}
