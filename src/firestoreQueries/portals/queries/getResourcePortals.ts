import { db } from '@/configs/firebase'
import pathStore from '@/firestoreQueries/utils/pathStore'
import {
    DocumentReference,
    collection,
    getDocs,
    query,
    where,
} from 'firebase/firestore'
import { PortalType } from '../portalTypes'
import * as Sentry from '@sentry/react'

type Args = {
    organizationId: string | undefined
    resourceRef: DocumentReference
}

export default async ({ resourceRef, organizationId }: Args) => {
    try {
        if (!organizationId) throw new Error('Missing OrganizationId')

        const portalsCollection = collection(
            db,
            pathStore.portals({ organizationId })
        )
        const q = query(
            portalsCollection,
            where('resourceRef', '==', resourceRef)
        )

        const portals = await getDocs(q)

        if (portals.empty) return []

        return portals.docs.map((doc) => ({
            id: doc.id,
            ...doc.data(),
        })) as PortalType[]
    } catch (error) {
        Sentry.captureException(error)
        throw error
    }
}
