import { db } from '@/configs/firebase'
import pathStore from '@/firestoreQueries/utils/pathStore'
import { doc, updateDoc } from 'firebase/firestore'
import { PortalType, PortalTypeFromDB } from '../portalTypes'
import getOnePortal from './getOnePortal'
import * as Sentry from '@sentry/react'

type Args = {
    organizationId: string | undefined
    portal: PortalType
}

export default async ({ portal, organizationId }: Args) => {
    try {
        const { id, portalInfo, portsAccess: portRules, accessDuration } = portal

        if (!organizationId) {
            throw new Error('Missing OrganizationId')
        }

        const newPortal = {
            portalInfo,
            portsAccess: portRules,
            accessDuration,
        } as PortalTypeFromDB

        const docPath = pathStore.onePortal({ organizationId, portalId: id })
        const docRef = doc(db, docPath)
        await updateDoc(docRef, newPortal)

        const updatedDoc = await getOnePortal({
            organizationId: organizationId,
            portalId: id,
        })
        return updatedDoc
    } catch (error) {
        Sentry.captureException(error)
        throw error
    }
}
