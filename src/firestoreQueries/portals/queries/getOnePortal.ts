import { db } from '@/configs/firebase'
import pathStore from '@/firestoreQueries/utils/pathStore'
import * as Sentry from '@sentry/react'
import { getAuth } from 'firebase/auth'
import { doc, getDoc } from 'firebase/firestore'
import { PortalType, PortalTypeFromDB } from '../portalTypes'

type Args = {
  organizationId: string | undefined
  portalId: string
}

export default async ({ organizationId, portalId }: Args) => {
  try {
    if (!organizationId) throw new Error('Missing OrganizationId')
    const userId = getAuth().currentUser?.uid
    if (!userId) throw new Error('userId is undefined')

    const path = pathStore.onePortal({ organizationId, portalId })
    const ref = doc(db, path)

    const resourceSnap = await getDoc(ref)

    if (resourceSnap.exists()) {
      const data = resourceSnap.data() as PortalTypeFromDB
      return {
        ...data,
        id: resourceSnap.id,
      } as PortalType
    } else {
      throw new Error('No such document!')
    }
  } catch (error) {
    Sentry.captureException(error)
    throw error
  }
}
