import { db } from '@/configs/firebase'
import { getPortalQueryConstraints } from '@/customUtils/getGroupsQueryConstraints'
import getUserGroups from '@/firestoreQueries/groups/queries/getUserGroups'
import getUserResources from '@/firestoreQueries/resources/queries/getUserResources'
import * as Sentry from '@sentry/react'
import { getAuth } from 'firebase/auth'
import { collection, doc, getDocs, query } from 'firebase/firestore'
import pathStore from '../../utils/pathStore'
import { PortalType } from '../portalTypes'

type Args = {
  organizationId: string | undefined
  isAdmin: boolean
}

export default async ({ organizationId, isAdmin }: Args) => {
  try {
    if (!organizationId) throw new Error('Organization ID is required')
    const userId = getAuth().currentUser?.uid
    if (!userId) throw new Error('userId is required')

    const groupsIds = (await getUserGroups({ organizationId, userId })).map((g) => g.id)
    const resourceIds = (await getUserResources({ organizationId, userId })).map((r) => r.id)
    const resourcesRef = resourceIds.map((resourceId) => {
      const path = pathStore.oneResource({ organizationId, resourceId })
      return doc(db, path)
    })

    const path = pathStore.portals({ organizationId })
    const collectionRef = collection(db, path)

    const constraints = getPortalQueryConstraints({ groupsIds, userId, resourcesRef })

    let q = query(collectionRef, constraints)

    if (isAdmin) {
      q = query(collectionRef)
    }
    const querySnapshot = await getDocs(q)

    const portalsData: Array<PortalType> = []

    querySnapshot.forEach((doc) => {
      const resourceData = doc.data() as PortalType
      portalsData.push({
        ...resourceData,
        id: doc.id,
      })
    })

    return portalsData
  } catch (error) {
    Sentry.captureException(error)
    throw error
  }
}
