import { db } from '@/configs/firebase'
import { collection, getDocs, or, query, where } from 'firebase/firestore'
import pathStore from '../../utils/pathStore'
import { PortalType } from '../portalTypes'
import * as Sentry from '@sentry/react'

type Args = {
  organizationId: string | undefined
  userId: string
}

export default async ({ organizationId, userId }: Args) => {
  try {
    if (!organizationId) {
      throw new Error('Missing OrganizationId')
    }

    if (!userId) {
      throw new Error('userId is required')
    }

    const path = pathStore.portals({ organizationId })
    const collectionRef = collection(db, path)

    const q = query(
      collectionRef,
      or(
        where(`roles.Viewers`, 'array-contains', userId),
        where(`roles.Owners`, 'array-contains', userId),
        where(`roles.Editors`, 'array-contains', userId),
      ),
    )

    const querySnapshot = await getDocs(q)

    const portalsData: Array<PortalType> = []

    querySnapshot.forEach((doc) => {
      const resourceData = doc.data() as PortalType
      portalsData.push({
        ...resourceData,
        id: doc.id,
      })
    })

    return portalsData
  } catch (error) {
    Sentry.captureException(error)
    throw error
  }
}
