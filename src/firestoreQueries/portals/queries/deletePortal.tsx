import { db } from '@/configs/firebase'
import pathStore from '@/firestoreQueries/utils/pathStore'
import { deleteDoc, doc } from 'firebase/firestore'
import * as Sentry from '@sentry/react'
import getPortalGroups from '@/firestoreQueries/portalGroups/queries/getPortalGroups'
import { PortalGroupType } from '@/firestoreQueries/portalGroups/portalGroupsTypes'
import updatePortalGroup from '@/firestoreQueries/portalGroups/queries/updatePortalGroup'

type Args = {
    portalId: string
    organizationId: string | undefined
}

export default async ({ portalId, organizationId }: Args) => {
    try {
        if (!organizationId) throw new Error('Missing OrganizationId')
        const path = pathStore.onePortal({ organizationId, portalId })

        const portalGroups = await getPortalGroups({ organizationId, isAdmin: true })
        const portalGroupsWithPortal = portalGroups.filter((portalGroup) => portalGroup.portalIds.includes(portalId))
        const pormises = portalGroupsWithPortal.map(async (portalGroup) => {
            const portalIds = portalGroup.portalIds.filter((id) => id !== portalId)
            const newPortalGroup: PortalGroupType = {
                ...portalGroup,
                portalIds,
            }
            await updatePortalGroup({ portalGroup: newPortalGroup, organizationId })
        })

        await Promise.all(pormises)

        await deleteDoc(doc(db, path))
    } catch (error) {
        Sentry.captureException(error)
        throw error
    }
}
