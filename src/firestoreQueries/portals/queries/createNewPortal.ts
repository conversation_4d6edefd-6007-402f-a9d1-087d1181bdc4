import { db } from '@/configs/firebase'
import { RolesType } from '@/firestoreQueries/utils/generalTypes'
import pathStore from '@/firestoreQueries/utils/pathStore'
import * as Sentry from '@sentry/react'
import { DocumentReference, addDoc, collection } from 'firebase/firestore'
import { PortalInfoType, PortalPortsAccessType, PortalType, PortalTypeFromDB } from '../portalTypes'
import { DEFAULT_ROLE } from '@/GLOBALS/GLOBALS'

type Args = {
  organizationId: string | undefined
  portalInfo: PortalInfoType
  portRules: Array<PortalPortsAccessType>
  resourceRef: DocumentReference
  accessDuration: number
  roles: RolesType
}

export default async ({ organizationId, portRules, portalInfo, resourceRef, accessDuration, roles }: Args) => {
  try {
    if (!organizationId) throw new Error('Missing OrganizationId')

    const portalsPath = pathStore.portals({ organizationId })

    const portalsRef = collection(db, portalsPath)

    const newPortal: PortalTypeFromDB = {
      defaultRole: DEFAULT_ROLE,
      portalInfo,
      portsAccess: portRules,
      resourceRef,
      accessDuration,
      roles,
    }

    const ref = await addDoc(portalsRef, newPortal)

    return {
      ...newPortal,
      id: ref.id,
    } as PortalType
  } catch (error) {
    Sentry.captureException(error)
    throw error
  }
}
