import { db } from '@/configs/firebase'
import { ndrPathStore } from '@/firestoreQueries/utils/pathStore'
import * as Sentry from '@sentry/react'
import { collection, getDocs } from 'firebase/firestore'
import { IssueTypeWithId } from '../customIssuesTypes'

type Args = {
    organizationId: string | undefined
}

export default async function getNdrIssues({ organizationId }: Args) {
    try {
        if (!organizationId) throw new Error('Organization ID is required')

        const path = ndrPathStore.issues({ organizationId })
        const ref = collection(db, path)
        const snap = await getDocs(ref)

        const issues: Array<IssueTypeWithId> = []

        snap.forEach((doc) => {
            issues.push({ ...doc.data(), id: doc.id } as IssueTypeWithId)
        })
        return issues
    } catch (error) {
        Sentry.captureException(error)
        throw error
    }
}
