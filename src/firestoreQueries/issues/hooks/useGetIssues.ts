import { ndrQueryKeyStore } from '@/configs/reactQueryKeyStore'
import { useQuery } from '@tanstack/react-query'
import getNdrIssues from '../queries/getIssues'

type Args = {
    organizationId: string | undefined
}

export default function useGetNdrIssues({ organizationId }: Args) {
    return useQuery({
        queryKey: ndrQueryKeyStore.issues({ organizationId: organizationId! }),
        queryFn: async () => getNdrIssues({ organizationId }),
        staleTime: 1000 * 60 * 5,
    })
}
