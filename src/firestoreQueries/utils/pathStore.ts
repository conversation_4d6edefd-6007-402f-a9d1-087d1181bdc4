type ResourcesArgs = {
  organizationId: string
}

type PortalsArgs = {
  organizationId: string
}

type ResourceArgs = {
  organizationId: string
  resourceId: string
}

type MemberArgs = {
  organizationId: string
  resourceId: string
}

type IntegrationArgs = { organizationId: string }

type OneIntegrtaionArgs = {
  organizationId: string
  integrationId: string
}

type IntegrationVPCsArgs = {
  organizationId: string
  integrationId: string
}

type onePortalArgs = {
  organizationId: string
  portalId: string
}

type ActivitiesArgs = {
  organizationId: string
}

type UserArgs = {
  userId: string
}

type DocumentActivitiesArgs = {
  organizationId: string
  documentType: 'resources' | 'portals' | 'integrations'
  documentId: string
}

type ScanIssuesArgs = {
  organizationId: string
  scanId: string
}

export default {
  resources: ({ organizationId }: ResourcesArgs) => `organizations/${organizationId}/resources`,
  oneResource: ({ organizationId, resourceId }: ResourceArgs) =>
    `organizations/${organizationId}/resources/${resourceId}`,
  members: ({ organizationId, resourceId }: MemberArgs) =>
    `organizations/${organizationId}/resources/${resourceId}/members`,

  integrations: ({ organizationId }: IntegrationArgs) => `organizations/${organizationId}/integrations`,

  emailNotifications: ({ organizationId }: IntegrationArgs) => `organizations/${organizationId}/emailNotifications`,

  oneIntegration: ({ organizationId, integrationId }: OneIntegrtaionArgs) =>
    `organizations/${organizationId}/integrations/${integrationId}`,
  integrationVPCs: ({ integrationId, organizationId }: IntegrationVPCsArgs) =>
    `organizations/${organizationId}/integrations/${integrationId}/vpcs`,
  user: ({ userId }: UserArgs) => `users/${userId}`,
  admins: ({ organizationId }: IntegrationArgs) => `/organizations/${organizationId}/admins`,
  userDefinedSettingsEmail: ({ userId }: UserArgs) => `users/${userId}/userDefinedSettings/emailSettings`,
  portals: ({ organizationId }: PortalsArgs) => `organizations/${organizationId}/portals`,
  onePortal: ({ organizationId, portalId }: onePortalArgs) => `organizations/${organizationId}/portals/${portalId}`,
  activities: ({ organizationId }: ActivitiesArgs) => `organizations/${organizationId}/activities`,
  documentActivities: ({ documentType, documentId, organizationId }: DocumentActivitiesArgs) =>
    `organizations/${organizationId}/${documentType}/${documentId}/activities`,
  scanIssues: ({ organizationId, scanId }: ScanIssuesArgs) => `organizations/${organizationId}/scans/${scanId}/issues`,
  oneScan: ({ organizationId, scanId }: { organizationId: string; scanId: string }) =>
    `organizations/${organizationId}/scans/${scanId}`,
  trafficIssues: ({ organizationId }: { organizationId: string }) => `organizations/${organizationId}/trafficIssues`,
  oneTrafficIssue: ({ organizationId, trafficIssueId }: { organizationId: string; trafficIssueId: string }) =>
    `organizations/${organizationId}/trafficIssues/${trafficIssueId}`,
  interfaceProfiles: ({ organizationId, integrationId }: { organizationId: string; integrationId: string }) =>
    `organizations/${organizationId}/integrations/${integrationId}/interfaceProfiles`,

  resourceTrafficPatterns: ({ organizationId, integrationId }: { organizationId: string; integrationId: string }) =>
    `organizations/${organizationId}/integrations/${integrationId}/interfaceProfiles`,
  //
  groups: ({ organizationId }: { organizationId: string }) => `/organizations/${organizationId}/groups`,
  oneGroup: ({ organizationId, groupId }: { organizationId: string; groupId: string }) =>
    `/organizations/${organizationId}/groups/${groupId}`,

  //
  requests: (organizationId: string) => `/organizations/${organizationId}/requests`,
  oneRequest: ({ organizationId, requestId }: { organizationId: string; requestId: string }) =>
    `/organizations/${organizationId}/requests/${requestId}`,
}

export const ndrPathStore = {
  stats: (organziationId: string) => `organizations/${organziationId}/dashboards/NDR/stats`,
  notifications: (organizationId: string) => `organizations/${organizationId}/dashboards/NDR/notifications`,
  savedPortflowQueries: (organizationId: string) =>
    `organizations/${organizationId}/dashboards/NDR/savedPortflowQueries`,
  savedPortflowQuery: (organizationId: string, id: string) =>
    `organizations/${organizationId}/dashboards/NDR/savedPortflowQueries/${id}`,
  entities: ({ organizationId }: { organizationId: string }) =>
    `organizations/${organizationId}/dashboards/NDR/entities`,
  issues: ({ organizationId }: { organizationId: string }) => `organizations/${organizationId}/dashboards/NDR/issues`,
  detections: ({ organizationId }: { organizationId: string }) =>
    `organizations/${organizationId}/dashboards/NDR/detections`,
  oneDetection: ({ organizationId, detectionId }: { organizationId: string; detectionId: string }) =>
    `organizations/${organizationId}/dashboards/NDR/detections/${detectionId}`,
  detectionControls: ({ organizationId }: { organizationId: string }) =>
    `organizations/${organizationId}/dashboards/NDR/detectionControls`,
  oneDetectionControl: ({ organizationId, controlId }: { organizationId: string; controlId: string }) =>
    `organizations/${organizationId}/dashboards/NDR/detectionControls/${controlId}`,
  detectionControlMetrics: ({ organizationId }: { organizationId: string }) =>
    `organizations/${organizationId}/dashboards/NDR/detectionControlMetrics`,
  oneDetectionControlMetric: ({ organizationId, metricId }: { organizationId: string; metricId: string }) =>
    `organizations/${organizationId}/dashboards/NDR/detectionControlMetrics/${metricId}`,
  oneIssue: ({ organizationId, issueId }: { organizationId: string; issueId: string }) =>
    `organizations/${organizationId}/dashboards/NDR/issues/${issueId}`,
  oneIssueRecentTraffics: ({
    organizationId,
    issueId,
    entityId,
  }: {
    organizationId: string
    issueId: string
    entityId: string
  }) => `organizations/${organizationId}/dashboards/NDR/issues/${issueId}/relatedTraffic/${entityId}`,
  trafficPatterns: ({ organizationId }: { organizationId: string }) =>
    `organizations/${organizationId}/dashboards/NDR/trafficPatterns`,
  portflowShortcuts: (organizationId: string) => `organizations/${organizationId}/dashboards/NDR/portflowShortcuts`,
  portflowShortcut: (organizationId: string, id: string) =>
    `organizations/${organizationId}/dashboards/NDR/portflowShortcuts/${id}`,
  entitiesMetrics: ({ organizationId }: { organizationId: string }) =>
    `organizations/${organizationId}/dashboards/NDR/entityMetrics`,
  activities: ({ organizationId }: { organizationId: string }) =>
    `organizations/${organizationId}/dashboards/NDR/activity`,
  labels: ({ organizationId }: { organizationId: string }) => `organizations/${organizationId}/dashboards/NDR/labels`,
  oneLabel: ({ organizationId, labelId }: { organizationId: string; labelId: string }) =>
    `organizations/${organizationId}/dashboards/NDR/labels/${labelId}`,
  labelAssignments: ({ organizationId }: { organizationId: string }) =>
    `organizations/${organizationId}/dashboards/NDR/labelAssignments`,
  securityGroups: ({ organizationId }: { organizationId: string }) =>
    `organizations/${organizationId}/dashboards/NDR/securityGroups`,
  detectionCategories: ({ organizationId }: { organizationId: string }) =>
    `organizations/${organizationId}/dashboards/NDR/detectionCategories/summary`,
  comments: ({ organizationId }: { organizationId: string }) =>
    `organizations/${organizationId}/dashboards/NDR/comments`,
  oneComment: ({ organizationId, commentId }: { organizationId: string; commentId: string }) =>
    `organizations/${organizationId}/dashboards/NDR/comments/${commentId}`,
  groupViews: ({ organizationId }: { organizationId: string }) =>
    `organizations/${organizationId}/dashboards/NDR/groupViews`,
  oneGroupView: ({ organizationId, groupViewId }: { organizationId: string; groupViewId: string }) =>
    `organizations/${organizationId}/dashboards/NDR/groupViews/${groupViewId}`,
}

export const accessPathStore = {
  portalGroups: ({ organizationId }: { organizationId: string }) =>
    `organizations/${organizationId}/dashboards/access/portalGroups`,
  onePortalGroup: ({ organizationId, portalGroupId }: { organizationId: string; portalGroupId: string }) =>
    `organizations/${organizationId}/dashboards/access/portalGroups/${portalGroupId}`,
}
