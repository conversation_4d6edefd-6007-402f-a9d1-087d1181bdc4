import { IntegrationTypeWithRef } from '../integrations/IntegrationTypes'
import { DefaultRoleType, OwnerType, PortRulesObjectType, RolesType } from '../utils/generalTypes'

export type ResourceInfoType = {
  resourceId: string // for aws this is the security group id
  securityGroupName: string
  region: string
  instanceType: 'EC2' | 'RDS' | ''
  awsResourceId: string
  resourceOwner: OwnerType
  resourceEmoji: string
  resourceName: string
  resourceDescription: string
  resourceIPs: string[]
}

export type ResourceTypeFromDB = {
  resourceInfo: ResourceInfoType
  portRules: PortRulesObjectType
  integration: IntegrationTypeWithRef
  roles: RolesType
  defaultRole: DefaultRoleType
}

export type ResourceType = ResourceTypeFromDB & {
  id: string
}

export type AWSResourceType = {
  id: string
  ip: string
  name: string
  type: 'EC2' | 'RDS'
  secGroups: Array<{ id: string; name: string; region: string }>
}
