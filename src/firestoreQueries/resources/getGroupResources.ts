import { db } from '@/configs/firebase'
import pathStore from '../utils/pathStore'
import { collection, getDocs, or, query, where } from 'firebase/firestore'
import { ResourceType } from './resourcesTypes'
import { captureException } from '@sentry/react'

type Args = {
  organizationId: string | undefined
  groupId: string
}

export default async function ({ organizationId, groupId }: Args) {
  try {
    if (!organizationId) throw new Error('organizationId is undefined')

    const path = pathStore.resources({ organizationId })
    const ref = collection(db, path)
    const q = query(
      ref,
      or(where('roles.Viewers', 'array-contains', groupId), where('roles.Editors', 'array-contains', groupId)),
    )

    const snap = await getDocs(q)
    const resources: ResourceType[] = []
    snap.forEach((doc) => resources.push({ ...doc.data(), id: doc.id } as ResourceType))

    return resources
  } catch (error) {
    captureException(error)
    throw error
  }
}
