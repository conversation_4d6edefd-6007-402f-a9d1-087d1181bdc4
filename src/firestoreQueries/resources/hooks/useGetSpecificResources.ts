import reactQueryKeyStore from '@/configs/reactQueryKeyStore'
import { useQuery } from '@tanstack/react-query'
import { DocumentReference } from 'firebase/firestore'
import getSpecificResources from '../queries/getSpecificResources'

type Args = {
  refsArray: Array<DocumentReference>
  organizationId: string | undefined
}

export default ({ refsArray, organizationId }: Args) => {
  const queryKey = reactQueryKeyStore.specificResources({
    resourcesIds: refsArray.map((ref) => ref.id),
    organizationId: organizationId!,
  })

  return useQuery({
    queryKey,
    queryFn: async () => getSpecificResources({ refsArray, organizationId }),
    staleTime: 1000 * 60 * 5, // 5 minutes
    // refetchOnMount: refetchOnMountFn<ResourceType>(queryKey),
  })
}
