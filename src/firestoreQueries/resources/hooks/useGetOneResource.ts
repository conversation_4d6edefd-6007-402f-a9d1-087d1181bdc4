import reactQueryKeyStore from '@/configs/reactQueryKeyStore'
import { useQuery } from '@tanstack/react-query'
import getOneResource from '../queries/getOneResource'

type Args = {
    organizationId: string | undefined
    resourceId: string
}

export default ({ organizationId, resourceId }: Args) => {
    const queryKey = reactQueryKeyStore.oneResource({ resourceId })
    return useQuery({
        queryKey,
        queryFn: async () =>
            await getOneResource({ organizationId, resourceId }),
        staleTime: 1000 * 60 * 5, // 5 minutes
    })
}
