import { useQuery } from '@tanstack/react-query'
import { ResourceType } from '@/firestoreQueries/resources/resourcesTypes'
import { getInterfaceProfileId } from '../queries/getInterfaceProfileId'

export function useGetInterfaceProfileId({ organizationId, resource }: { organizationId: string; resource: ResourceType }) {
  return useQuery({
    queryKey: ['interfaceProfileId', organizationId, resource.id],
    queryFn: () => getInterfaceProfileId({ organizationId, resource }),
  })
} 