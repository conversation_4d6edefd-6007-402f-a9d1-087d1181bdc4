import { useQuery } from '@tanstack/react-query'
import refetchOnMountFn from '@/firestoreQueries/utils/refetchOnMountFn'
import getUserResources from '../queries/getUserResources'
import { ResourceType } from '../resourcesTypes'

type Args = {
    organizationId: string | undefined
    userId: string
}

export default ({ organizationId, userId }: Args) => {
    const queryKey = ['userResources', userId]
    return useQuery({
        queryKey,
        queryFn: async () => {
            return await getUserResources({
                organizationId,
                userId,
            })
        },
        staleTime: 1000 * 60 * 5, // 5 minutes
        refetchOnMount: () => refetchOnMountFn<ResourceType>(queryKey),
    })
}
