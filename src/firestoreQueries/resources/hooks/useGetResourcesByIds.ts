import reactQueryKeyStore from '@/configs/reactQueryKeyStore'
import { useQuery } from '@tanstack/react-query'
import getResourcesByIds from '../queries/getResourcesByIds'

type Args = {
  organizationId: string | undefined
  resourcesIds: string[]
}

export default function ({ organizationId, resourcesIds }: Args) {
  return useQuery({
    queryKey: reactQueryKeyStore.specificResources({ organizationId: organizationId!, resourcesIds }),
    queryFn: async () => await getResourcesByIds({ organizationId, resourcesIds }),
  })
}
