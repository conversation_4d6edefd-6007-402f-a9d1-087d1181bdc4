import { queryClient } from '@/Providers/ReactQueryProvider'
import { useMutation } from '@tanstack/react-query'
import createNewResource from '../queries/createNewResource'
import { ResourceType, ResourceTypeFromDB } from '../resourcesTypes'
import { IntegrationTypeWithRef } from '@/firestoreQueries/integrations/IntegrationTypes'

type Args = {
    queryKey: string[]
    organizationId: string | undefined
    resource: ResourceTypeFromDB
    resourceId?: string
    integration: IntegrationTypeWithRef
}

export default () => {
    return useMutation({
        mutationFn: async ({ organizationId, resource, resourceId, integration }: Args) => {
            return await createNewResource({
                organizationId,
                resource,
                resourceId,
                integration,
            })
        },
        onSuccess: (newResource, { queryKey }) => {
            const prevResources: Array<ResourceType> = queryClient.getQueryData(queryKey) || []

            const newResources = [...prevResources, newResource]

            queryClient.setQueryData(queryKey, newResources)
        },
        onError: (error) => {
            return error
        },
    })
}
