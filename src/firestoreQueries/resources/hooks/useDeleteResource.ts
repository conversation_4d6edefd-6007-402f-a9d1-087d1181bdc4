import { queryClient } from '@/Providers/ReactQueryProvider'
import reactQueryKeyStore from '@/configs/reactQueryKeyStore'
import { AvailableIntegrations } from '@/firestoreQueries/integrations/IntegrationTypes'
import { ResourceTypeFromDB } from '@/firestoreQueries/resources/resourcesTypes'
import { useMutation } from '@tanstack/react-query'
import { DocumentReference } from 'firebase/firestore'
import deleteResource from '../queries/deleteResource'

type Args = {
    resourceId: string
    organizationId: string | undefined
    integrationRef: DocumentReference
    integrationType: AvailableIntegrations
    queryKey: string[]
}

export default () => {
    return useMutation({
        mutationFn: async ({
            resourceId,
            organizationId,
            integrationRef,
            integrationType,
        }: Args) => {
            await deleteResource({
                resourceId,
                organizationId,
                integrationRef,
                integrationType,
            })
        },
        onMutate: ({ resourceId }) => {
            const resourcesQueryKey = reactQueryKeyStore.resources()
            const prevResources: Array<ResourceTypeFromDB & { id: string }> =
                queryClient.getQueryData(resourcesQueryKey) || []

            const newResources = prevResources.filter(
                (resource) => resource.id !== resourceId
            )

            queryClient.setQueryData(resourcesQueryKey, newResources)

            return {
                rollback: () =>
                    queryClient.setQueryData(resourcesQueryKey, prevResources),
            }
        },
        onError: (error, variables, context) => {
            if (context) {
                context.rollback()
            }
            return error
        },
    })
}
