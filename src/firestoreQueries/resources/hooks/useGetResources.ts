import reactQueryKeyStore from '@/configs/reactQueryKeyStore'
import refetchOnMountFn from '@/firestoreQueries/utils/refetchOnMountFn'
import { useQuery } from '@tanstack/react-query'
import getResources from '../queries/getResources'
import { ResourceType } from '../resourcesTypes'

type Args = {
    organizationId: string | undefined
    isAdmin: boolean
}

export default ({ organizationId, isAdmin }: Args) => {
    const queryKey = reactQueryKeyStore.resources()
    return useQuery({
        queryKey,
        queryFn: async () => await getResources({ organizationId, isAdmin }),
        staleTime: 1000 * 60 * 5, // 5 minutes
        refetchOnMount: () => refetchOnMountFn<ResourceType>(queryKey),
    })
}
