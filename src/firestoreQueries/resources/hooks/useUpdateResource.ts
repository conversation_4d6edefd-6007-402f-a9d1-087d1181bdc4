import { queryClient } from '@/Providers/ReactQueryProvider'
import reactQueryKeyStore from '@/configs/reactQueryKeyStore'
import { useMutation } from '@tanstack/react-query'
import updateResource from '../queries/updateResource'
import { ResourceType } from '../resourcesTypes'
import { PortalType } from '@/firestoreQueries/portals/portalTypes'

type Args = {
    queryKey: string[]
    resource: ResourceType
    organizationId: string | undefined
}

export default () => {
    return useMutation({
        mutationFn: async ({ resource, organizationId }: Args) => {
            return await updateResource({ resource, organizationId })
        },
        onMutate: ({ resource, queryKey }) => {
            // Update the resource in the single resource query
            const prevResource: ResourceType = queryClient.getQueryData(queryKey) || ({} as ResourceType)

            queryClient.setQueryData(queryKey, resource)

            // Update the resource in the resources array query
            const resouresQueryKey = reactQueryKeyStore.resources()
            const prevResources: ResourceType[] = queryClient.getQueryData(resouresQueryKey) || []

            const newResources = prevResources.map((r) => {
                if (r.id === resource.id) {
                    return resource
                }
                return r
            })

            queryClient.setQueryData(resouresQueryKey, newResources)

            const portalsQueryKey = reactQueryKeyStore.portals()
            const prevPortals: PortalType[] = queryClient.getQueryData(portalsQueryKey) || []

            const newPortals = prevPortals.map((portal) => {
                if (portal.resourceRef.id === resource.id) {
                    return {
                        ...portal,
                        portalInfo: {
                            ...portal.portalInfo,
                            resourceEmoji: resource.resourceInfo.resourceEmoji,
                        },
                    }
                }
                return portal
            })

            queryClient.setQueryData(portalsQueryKey, newPortals)

            const rollback = () => {
                queryClient.setQueryData(queryKey, prevResource)
                queryClient.setQueryData(resouresQueryKey, prevResources)
                queryClient.setQueryData(portalsQueryKey, prevPortals)
            }
            return { rollback }
        },
        onSuccess: () => {
            queryClient.removeQueries({
                queryKey: ['documentActivities'],
                exact: false,
            })
        },
        onError: (error, variables, context) => {
            if (context) {
                context.rollback()
            }
            return error
        },
    })
}
