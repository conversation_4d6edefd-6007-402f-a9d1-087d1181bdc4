import { db } from '@/configs/firebase'
import pathStore from '@/firestoreQueries/utils/pathStore'
import {
    collection,
    doc,
    getDocs,
    query,
    where,
    writeBatch,
} from 'firebase/firestore'
import { ResourceType, ResourceTypeFromDB } from '../resourcesTypes'
import getOneResource from './getOneResource'
import * as Sentry from '@sentry/react'

type Args = {
    organizationId: string | undefined
    resource: ResourceType
}

export default async ({ resource, organizationId }: Args) => {
    try {
        const { id, integration, portRules, resourceInfo } = resource

        if (!organizationId) {
            throw new Error('Missing organizationId')
        }

        const newResource = {
            resourceInfo,
            portRules,
            integration,
        } as ResourceTypeFromDB

        const resourcePath = `organizations/${organizationId}/resources/${id}`
        const resourceRef = doc(db, resourcePath)

        const batch = writeBatch(db)
        batch.update(resourceRef, newResource)

        const portalsPath = pathStore.portals({ organizationId })
        const portalsRef = collection(db, portalsPath)
        const portalsQ = query(
            portalsRef,
            where('resourceRef', '==', resourceRef)
        )
        const portalsSnap = await getDocs(portalsQ)

        portalsSnap.forEach((doc) => {
            batch.update(doc.ref, {
                resourceRef: resourceRef,
            })
        })

        await batch.commit()
        const updatedDoc = await getOneResource({
            organizationId: organizationId,
            resourceId: id,
        })
        return updatedDoc
    } catch (error) {
        Sentry.captureException(error)
        throw error
    }
}
