import { db } from '@/configs/firebase'
import { AvailableIntegrations } from '@/firestoreQueries/integrations/IntegrationTypes'
import { DocumentReference, deleteDoc, doc } from 'firebase/firestore'
import * as Sentry from '@sentry/react'

type Args = {
    resourceId: string
    organizationId: string | undefined
    integrationRef: DocumentReference
    integrationType: AvailableIntegrations
}

export default async ({
    resourceId,
    organizationId,
    integrationRef,
    integrationType,
}: Args) => {
    try {
        if (!organizationId) throw new Error('Missing organizationId')

        const path = `organizations/${organizationId}/resources/${resourceId}`
        await deleteDoc(doc(db, path))

        if (integrationType === 'standalone') {
            await deleteDoc(integrationRef)
        }
    } catch (error) {
        Sentry.captureException(error)
        throw error
    }
}
