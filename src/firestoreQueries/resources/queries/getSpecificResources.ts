import { db } from '@/configs/firebase'
import pathStore from '@/firestoreQueries/utils/pathStore'
import * as Sentry from '@sentry/react'
import { collection, DocumentReference, getDocs, query, where } from 'firebase/firestore'
import { ResourceType } from '../resourcesTypes'

type Args = {
  refsArray: Array<DocumentReference>
  organizationId: string | undefined
}

export default async ({ refsArray, organizationId }: Args) => {
  try {
    if (refsArray.length === 0) return []
    if (!organizationId) throw new Error('organizationId is undefined')

    const idsArray = refsArray.map((ref) => ref.id)

    const resourcesPath = pathStore.resources({ organizationId })
    const ref = collection(db, resourcesPath)
    const q = query(ref, where('__name__', 'in', idsArray))
    const snapshot = await getDocs(q)

    const resources: Array<ResourceType> = []

    snapshot.forEach((doc) => {
      resources.push({ ...doc.data(), id: doc.id } as ResourceType)
    })

    return resources
  } catch (error) {
    Sentry.captureException(error)
    throw error
  }
}
