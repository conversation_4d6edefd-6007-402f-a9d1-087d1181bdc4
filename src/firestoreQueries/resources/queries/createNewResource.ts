import { db } from '@/configs/firebase'
import { IntegrationTypeFromDB, IntegrationTypeWithRef } from '@/firestoreQueries/integrations/IntegrationTypes'
import pathStore from '@/firestoreQueries/utils/pathStore'
import * as Sentry from '@sentry/react'
import { getAuth } from 'firebase/auth'
import { addDoc, collection, doc, getDoc, setDoc } from 'firebase/firestore'
import { ResourceType, ResourceTypeFromDB } from '../resourcesTypes'

type Args = {
  organizationId: string | undefined
  resource: ResourceTypeFromDB
  integration: IntegrationTypeWithRef
  resourceId?: string
}

export default async function createNewResource({ resource, organizationId, resourceId, integration }: Args) {
  try {
    if (!organizationId) {
      throw new Error('Missing OrganizationId')
    }

    const userId = getAuth().currentUser?.uid

    if (!userId) {
      throw new Error('Failed getting userId')
    }

    const integrationObject: IntegrationTypeFromDB = {
      integrationType: integration.integrationType,
      name: integration.name,
      config: integration.config,
      description: '',
    }

    await setDoc(integration.ref, integrationObject)

    const path = pathStore.resources({ organizationId })
    const collectionRef = collection(db, path)

    if (!resourceId) {
      const resourceRef = doc(collectionRef)
      await addDoc(collectionRef, resource)

      const newResourceSnap = await getDoc(resourceRef)

      return {
        ...newResourceSnap.data(),
        id: newResourceSnap.id,
      } as ResourceType
    }

    const resourceRef = doc(collectionRef, resourceId)
    await setDoc(resourceRef, resource)
    return {
      ...resource,
      id: resourceId,
    } as ResourceType
  } catch (error) {
    Sentry.captureException(error)
    throw error
  }
}
