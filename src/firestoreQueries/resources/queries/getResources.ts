import { db } from '@/configs/firebase'
import { getResourceQueryConstraints } from '@/customUtils/getGroupsQueryConstraints'
import getUserGroups from '@/firestoreQueries/groups/queries/getUserGroups'
import * as Sentry from '@sentry/react'
import { getAuth } from 'firebase/auth'
import { collection, getDocs, query } from 'firebase/firestore'
import pathStore from '../../utils/pathStore'
import { ResourceType } from '../resourcesTypes'

type Args = {
  organizationId: string | undefined
  isAdmin: boolean
}

export default async ({ organizationId, isAdmin }: Args) => {
  try {
    if (!organizationId) throw new Error('Missing organizationId')
    const userId = getAuth().currentUser?.uid
    if (!userId) throw new Error('userId is required')
    const groupsIds = (await getUserGroups({ organizationId, userId })).map((g) => g.id)

    const path = pathStore.resources({ organizationId })
    const collectionRef = collection(db, path)

    const constraints = getResourceQueryConstraints({ groupsIds, userId })

    let q = query(collectionRef, constraints)

    if (isAdmin) {
      q = query(collectionRef)
    }

    const querySnapshot = await getDocs(q)

    const resourcesData: Array<ResourceType> = []

    querySnapshot.forEach((doc) => {
      const resourceData = doc.data() as ResourceType
      resourcesData.push({
        ...resourceData,
        id: doc.id,
      })
    })

    return resourcesData
  } catch (error) {
    Sentry.captureException(error)
    throw error
  }
}
