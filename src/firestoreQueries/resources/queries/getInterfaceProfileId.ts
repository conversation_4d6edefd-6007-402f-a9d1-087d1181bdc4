import { db } from '@/configs/firebase'
import { collection, getDocs } from 'firebase/firestore'
import { InterfaceProfile } from '@/firestoreQueries/integrations/IntegrationTypes'
import { ResourceType } from '@/firestoreQueries/resources/resourcesTypes'
import pathStore from '@/firestoreQueries/utils/pathStore'
import { connectAndGetAvailableResource } from '@/views/Access/Resources/utils'

export async function getInterfaceProfileId({ organizationId, resource }: { organizationId: string; resource: ResourceType }) {
  try {
    const availableResources = await connectAndGetAvailableResource({ organizationId, resource })

    const path = pathStore.interfaceProfiles({ integrationId: resource.integration.ref.id, organizationId })
    const ref = collection(db, path)
    const snap = await getDocs(ref)

    const profile = snap.docs.find((d) => {
      const data = d.data() as InterfaceProfile | undefined
      if (!data) throw new Error('No data')
      return data.resource.resourceId === availableResources?.associatedInstances[0].id
    })

    if (!profile) {
      throw new Error('No profile found')
    }
    return profile.id
  } catch (error) {
    return ''
  }
} 