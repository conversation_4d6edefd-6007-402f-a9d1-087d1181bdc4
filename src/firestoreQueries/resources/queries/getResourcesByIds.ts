import { db } from '@/configs/firebase'
import pathStore from '@/firestoreQueries/utils/pathStore'
import { captureException } from '@sentry/react'
import { collection, getDocs, query, where } from 'firebase/firestore'
import { ResourceType } from '../resourcesTypes'

type Args = {
  organizationId: string | undefined
  resourcesIds: string[]
}

// Function to split the resourcesIds array into chunks
function splitIntoChunks(array: string[], chunkSize: number): string[][] {
  const chunks = []
  for (let i = 0; i < array.length; i += chunkSize) {
    chunks.push(array.slice(i, i + chunkSize))
  }
  return chunks
}

// Function to fetch portals for a given chunk of IDs
async function fetchByChunk(path: string, chunk: string[]): Promise<ResourceType[]> {
  const ref = collection(db, path)
  const q = query(ref, where('__name__', 'in', chunk))
  const snap = await getDocs(q)
  return snap.docs.map((doc) => ({ ...doc.data(), id: doc.id }) as ResourceType)
}

// Main function to fetch portals
export default async function ({ organizationId, resourcesIds }: Args) {
  try {
    if (!organizationId) throw new Error('organizationId is undefined')
    if (resourcesIds.length === 0) return []

    const path = pathStore.resources({ organizationId })
    const chunkSize = 10
    const chunks = splitIntoChunks(resourcesIds, chunkSize)

    const promises = chunks.map((chunk) => fetchByChunk(path, chunk))
    const resolvedPromises = await Promise.all(promises)

    return resolvedPromises.flat()
  } catch (error) {
    captureException(error)
    throw error
  }
}
