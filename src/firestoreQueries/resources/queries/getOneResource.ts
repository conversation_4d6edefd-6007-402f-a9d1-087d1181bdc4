import { db } from '@/configs/firebase'
import pathStore from '@/firestoreQueries/utils/pathStore'
import { doc, getDoc } from 'firebase/firestore'
import { ResourceType, ResourceTypeFromDB } from '../resourcesTypes'
import * as Sentry from '@sentry/react'

type Args = {
    organizationId: string | undefined
    resourceId: string
}

export default async ({ organizationId, resourceId }: Args) => {
    try {
        if (!organizationId) {
            throw new Error('Missing organizationId')
        }

        const path = pathStore.oneResource({ organizationId, resourceId })

        const ref = doc(db, path)

        const resourceSnap = await getDoc(ref)

        if (resourceSnap.exists()) {
            const data = resourceSnap.data() as ResourceTypeFromDB

            return {
                ...data,
                id: resourceSnap.id,
            } as ResourceType
        } else {
            throw new Error('Document not found. getOneResource.ts')
        }
    } catch (error) {
        Sentry.captureException(error)
        throw error
    }
}
