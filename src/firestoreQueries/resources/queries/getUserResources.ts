import { db } from '@/configs/firebase'
import { getQueryConstraints } from '@/customUtils/getGroupsQueryConstraints'
import getUserGroups from '@/firestoreQueries/groups/queries/getUserGroups'
import * as Sentry from '@sentry/react'
import { collection, getDocs, query } from 'firebase/firestore'
import pathStore from '../../utils/pathStore'
import { ResourceType } from '../resourcesTypes'

type Args = {
  organizationId: string | undefined
  userId: string
}

export default async ({ organizationId, userId }: Args) => {
  try {
    if (!organizationId) throw new Error('Missing organizationId')
    if (!userId) throw new Error('Missing organizationId')

    const path = pathStore.resources({ organizationId })
    const collectionRef = collection(db, path)

    const groupsIds = (await getUserGroups({ organizationId, userId })).map((g) => g.id)
    const constraints = getQueryConstraints(groupsIds, userId)

    const q = query(collectionRef, constraints)

    const querySnapshot = await getDocs(q)

    const resourcesData: Array<ResourceType> = []

    querySnapshot.forEach((doc) => {
      const resourceData = doc.data() as ResourceType
      resourcesData.push({
        ...resourceData,
        id: doc.id,
      })
    })

    return resourcesData
  } catch (error) {
    Sentry.captureException(error)
    throw error
  }
}
