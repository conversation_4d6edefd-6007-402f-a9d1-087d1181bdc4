import { db } from '@/configs/firebase'
import pathStore from '@/firestoreQueries/utils/pathStore'
import { captureException } from '@sentry/react'
import { addDoc, collection, getDoc } from 'firebase/firestore'
import { AbstractRequestType, RequestTypeFromDB } from '../requestsTypes'

type Args = {
  organizationId: string | undefined
  request: RequestTypeFromDB
}

export default async function ({ organizationId, request }: Args) {
  try {
    if (!organizationId) throw new Error('organziationId is undefined')
    const path = pathStore.requests(organizationId)
    const ref = collection(db, path)

    const requsetRef = await addDoc(ref, request)

    const snap = await getDoc(requsetRef)
    if (!snap.exists()) throw new Error('newly created request could not be found!')
    const data = snap.data() as RequestTypeFromDB
    return {
      ...data,
      id: snap.id,
    } as AbstractRequestType
  } catch (error) {
    captureException(error)
    throw error
  }
}
