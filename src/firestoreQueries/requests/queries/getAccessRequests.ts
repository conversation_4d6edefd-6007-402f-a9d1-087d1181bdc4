import { db } from '@/configs/firebase'
import pathStore from '@/firestoreQueries/utils/pathStore'
import { captureException } from '@sentry/react'
import { getAuth } from 'firebase/auth'
import { collection, getDocs, query, where } from 'firebase/firestore'
import { AccessRequestType } from '../requestsTypes'

type Args = {
  organizationId: string | undefined
  isAdmin: boolean
}

export default async function getAccessRequests({ organizationId, isAdmin }: Args) {
  try {
    if (!organizationId) throw new Error('organizationId is undefiend')
    const userId = getAuth().currentUser?.uid
    if (!userId) throw new Error('userId is undefined')
    const path = pathStore.requests(organizationId)
    const ref = collection(db, path)

    let q = query(ref, where('type', '==', 'accessRequest'), where('userId', '==', userId))
    if (isAdmin) {
      q = query(ref, where('type', '==', 'accessRequest'))
    }

    const snap = await getDocs(q)
    const requests: Array<AccessRequestType> = []
    snap.forEach((doc) => requests.push({ ...doc.data(), id: doc.id } as AccessRequestType))

    return requests
  } catch (error) {
    captureException(error)
    throw error
  }
}
