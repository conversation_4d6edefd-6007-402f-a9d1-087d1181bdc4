import { db } from '@/configs/firebase'
import pathStore from '@/firestoreQueries/utils/pathStore'
import { doc, getDoc } from '@firebase/firestore'
import { captureException } from '@sentry/react'
import { AbstractRequestType, RequestTypeFromDB } from '../requestsTypes'

type Args = {
  organizationId: string | undefined
  requestId: string
}

export default async function getOneRequest({ organizationId, requestId }: Args) {
  try {
    if (!organizationId) throw new Error('organizationId is undefined')
    const path = pathStore.oneRequest({ organizationId, requestId })
    const ref = doc(db, path)

    const snap = await getDoc(ref)

    if (!snap.exists()) throw new Error('request not found')

    const data = snap.data() as RequestTypeFromDB
    return {
      ...data,
      id: snap.id,
    } as AbstractRequestType
  } catch (error) {
    captureException(error)
    throw error
  }
}
