import { captureException } from '@sentry/react'
import { RequestTypeFromDB } from '../requestsTypes'
import pathStore from '@/firestoreQueries/utils/pathStore'
import { doc, updateDoc } from 'firebase/firestore'
import { db } from '@/configs/firebase'
import getOneRequest from './getOneRequest'

type Args = {
  organizationId: string | undefined
  requestId: string
  request: RequestTypeFromDB
}

export default async function updateRequest({ organizationId, requestId, request }: Args) {
  try {
    if (!organizationId) throw new Error('organizationId is undefined')

    const path = pathStore.oneRequest({ organizationId, requestId })
    const ref = doc(db, path)
    await updateDoc(ref, request as any)

    return await getOneRequest({ organizationId, requestId })
  } catch (error) {
    captureException(error)
    throw error
  }
}
