import { DocumentReference, Timestamp } from 'firebase/firestore'

export type RequestType = 'accessRequest'
export type RequestStatus = 'Pending' | 'Approved' | 'Declined'

export interface RequestTypeFromDB {
  userId: string
  status: RequestStatus
  type: RequestType
  data: AccessRequestData // would be extended in the future
}

export interface AccessRequestData {
  startTime: Timestamp
  endTime: Timestamp
  entityType: 'resource' | 'portal' | 'portalGroup'
  entityRef: DocumentReference
}

export interface AccessRequestTypeFromDB extends RequestTypeFromDB {
  data: AccessRequestData
}

export interface AccessRequestType extends AccessRequestTypeFromDB {
  id: string
}
export interface AbstractRequestType extends RequestTypeFromDB {
  id: string
}
