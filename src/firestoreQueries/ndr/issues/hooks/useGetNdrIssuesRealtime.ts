import { db } from '@/configs/firebase'
import { ndrPathStore } from '@/firestoreQueries/utils/pathStore'
import { collection, onSnapshot, query, where, QueryConstraint, orderBy } from 'firebase/firestore'
import { useState } from 'react'
import _ from 'lodash'
import { IssueTypeWithId } from '../customIssuesTypes'
import { FirestoreTimeRange } from '@/customHooks/useFirestoreTimeRange'
import useDeepCompareEffect from '@/hooks/useDeepCompareEffect'

function generateHash(input: any): string {
  const str = typeof input === 'string' ? input : JSON.stringify(input)
  let hash = 5381
  
  for (let i = 0; i < str.length; i++) {
    hash = (hash * 33) ^ str.charCodeAt(i)
  }
  
  return (hash >>> 0).toString(36)
}

export type EntityId = string
export type IssueTypeWithIdWithRelatedEntitiesIds = IssueTypeWithId & { relatedEntitiesIds: EntityId[] }

export type QueryFilter = {
  fieldPath: string
  opStr: '==' | '>=' | '<='
  value: string
}

type Args = {
  organizationId: string | undefined

  timestamp?: FirestoreTimeRange
  entityId?: string
  filters?: QueryFilter[]
}

export default function useGetNdrIssuesRealtime({ organizationId, timestamp, entityId, filters }: Args) {
  const [issues, setIssues] = useState<IssueTypeWithIdWithRelatedEntitiesIds[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)
  const [queryCache, setQueryCache] = useState('')

  useDeepCompareEffect(() => {
    if (!organizationId) {
      setLoading(false)
      return
    }

    setLoading(true)
    const path = ndrPathStore.issues({ organizationId })
    const ref = collection(db, path)

    let compositeFilter: QueryConstraint[] = []

    if (filters && Array.isArray(filters)) {
      filters.forEach((filter) => {
        compositeFilter.push(where(filter.fieldPath, filter.opStr, filter.value))
      })
    }

    if (timestamp) {
      compositeFilter.push(where('createdTime', '>=', timestamp.from))
      compositeFilter.push(where('createdTime', '<=', timestamp.to))
    }

    compositeFilter.push(orderBy('createdTime', 'desc'))

    let q
    if (compositeFilter.length) {
      q = query(ref, ...compositeFilter)
    } else {
      q = query(ref)
    }

    const unsubscribe = onSnapshot(
      q,
      (snapshot) => {
        const documents: Array<IssueTypeWithIdWithRelatedEntitiesIds> = []
        snapshot.forEach((doc) => {
          documents.push({ ...doc.data(), id: doc.id } as IssueTypeWithIdWithRelatedEntitiesIds)
        })
        const filteredDocuments = entityId
          ? _.filter(documents, (doc) => {
            if(_.has(doc, 'entitiesIds')) {
              return doc.entitiesIds.includes(entityId)
            }

            return doc.entityId === entityId
          })
          : documents
        
        const hash = generateHash(JSON.stringify(_.map(filteredDocuments, 'id')))
        setQueryCache(hash)

        setIssues(filteredDocuments)
        setLoading(false)
        setError(null)
      },
      (err) => {
        console.error('Error in Firestore subscription:', err)
        setError(err as Error)
        setLoading(false)
      },
    )

    return () => unsubscribe()
  }, [organizationId, timestamp, entityId, filters])

  return { issues, loading, error, queryCache }
}
