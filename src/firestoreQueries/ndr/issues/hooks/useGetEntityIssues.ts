import { ndrQueryKeyStore } from '@/configs/reactQueryKeyStore'
import { useQuery } from '@tanstack/react-query'
import getEntityIssues from '../queries/getEntityIssues'

type Args = {
    organizationId: string | undefined
    entityId: string
}

export default function ({ organizationId, entityId }: Args) {
    return useQuery({
        queryKey: ndrQueryKeyStore.entityIssues({ organizationId: organizationId!, entityId }),
        queryFn: async () => await getEntityIssues({ entityId, organizationId }),
    })
}
