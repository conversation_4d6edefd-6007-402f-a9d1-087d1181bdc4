import { FirestoreTimeRange } from '@/customHooks/useFirestoreTimeRange'
import _ from 'lodash'
import { useRef, useState } from 'react'
import useGetEntities from '@/firestoreQueries/ndr/entities/hooks/useGetEntities'
import useUpdateEffect from '@/hooks/useUpdateEffect'
import useGetNdrIssuesRealtime, {
  EntityId,
  IssueTypeWithIdWithRelatedEntitiesIds, QueryFilter
} from '@/firestoreQueries/ndr/issues/hooks/useGetNdrIssuesRealtime'

type Args = {
  organizationId: string
  entityId?: string
  timestamp?: FirestoreTimeRange
  filters?: QueryFilter[]
}

function relatedEntitiesToIssueIdsMap(issues: Array<IssueTypeWithIdWithRelatedEntitiesIds>) {
  const result = new Map<string, EntityId[]>()

  _.forEach(issues, (issue) => {
    if ('relatedEntitiesIds' in issue) {
      result.set(issue.id, issue.relatedEntitiesIds)
    }
  })

  return result
}

function useFetchRealtimeNdrIssuesWithRelatedEntities({ organizationId, timestamp, entityId, filters }: Args) {
  const [entitiesIdsForFetching, setEntitiesIdsForFetching] = useState<EntityId[]>([])
  const [enrichedIssues, setEnrichedIssues] = useState<IssueTypeWithIdWithRelatedEntitiesIds[]>([])

  const loadedIssueIdsSnapshotRef = useRef<string[]>([])
  const entitiesEdsCollectionWithIssueIdRef = useRef<Map<string, EntityId[]>>(new Map())

  const {
    issues,
    loading: isIssueSnapshotInitialising,
    queryCache: issueQueryCache,
    error: isIssuesFailed,
  } = useGetNdrIssuesRealtime({
    organizationId,
    timestamp,
    entityId,
    filters
  })

  const {
    data: relatedEntitiesData,
    isLoading: isRelatedEntitiesLoading,
    isFetched,
    isError: isGetEntitiesError,
  } = useGetEntities({
    organizationId,
    entityIds: entitiesIdsForFetching,
    leanedEntities: true,
    enabled: entitiesIdsForFetching.length > 0,
    queryKey: issueQueryCache
  })

  // Note(pavlo): mapping entities IDs to toad from firestore;
  useUpdateEffect(() => {
    if (!issueQueryCache || !issues) return

    entitiesEdsCollectionWithIssueIdRef.current = relatedEntitiesToIssueIdsMap(issues)

    const filteredIssues = issues.filter((issue) => !loadedIssueIdsSnapshotRef.current.includes(issue.id))
    const allEntitiesIds: EntityId[] = _.uniq(_.map(filteredIssues, 'relatedEntitiesIds').filter(Boolean).flat())

    setEntitiesIdsForFetching(allEntitiesIds)

    loadedIssueIdsSnapshotRef.current = _.map(issues, 'id')
  }, [issueQueryCache, issues])

  // Note(pavlo): Enriching issues with entities;
  useUpdateEffect(() => {
    if (!isFetched || !relatedEntitiesData || !issues) return

    const clonedIssues = _.cloneDeep(issues)

    clonedIssues.forEach((issue) => {
      // Note(pavlo): Be sure that `relatedEntities` fields exist.
      Object.assign(issue, { relatedEntities: [] })
      
      if (entitiesEdsCollectionWithIssueIdRef.current.has(issue.id)) {
        const relatedEntities = relatedEntitiesData.filter((entity) => issue.relatedEntitiesIds.includes(entity.id))

        Object.assign(issue, { relatedEntities })
      }
    })

    setEntitiesIdsForFetching([])
    setEnrichedIssues(clonedIssues)
  }, [relatedEntitiesData, isFetched, issues])

  return {
    issues: enrichedIssues,
    isLoading: isRelatedEntitiesLoading || isIssueSnapshotInitialising || (enrichedIssues.length === 0 && !isFetched),
    isError: isGetEntitiesError || isIssuesFailed,
  }
}

export default useFetchRealtimeNdrIssuesWithRelatedEntities
