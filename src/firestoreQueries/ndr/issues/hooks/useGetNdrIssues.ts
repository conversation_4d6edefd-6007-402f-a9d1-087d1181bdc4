import { ndrQueryKeyStore } from '@/configs/reactQueryKeyStore'
import { useQuery } from '@tanstack/react-query'
import getNdrIssues from '../queries/getNdrIssues'
import { FirestoreTimeRange } from '@/customHooks/useFirestoreTimeRange'

type Args = {
  organizationId: string | undefined
  timestamp?: FirestoreTimeRange | undefined
}

export default function useGetNdrIssues({ organizationId, timestamp }: Args) {
  return useQuery({
    queryKey: ndrQueryKeyStore.issues({ organizationId: organizationId! }),
    queryFn: async () => getNdrIssues({ organizationId, timestamp }),
    staleTime: 1000 * 60 * 5,
  })
}
