import { IssueStatus } from '@globalTypes/ndrBenchmarkTypes/issueTypes'
import { useMutation } from '@tanstack/react-query'
import updateNdrIssueStatus from '../queries/updateNdrIssueStatus'
import { ndrQueryKeyStore } from '@/configs/reactQueryKeyStore'
import { IssueTypeWithId } from '../customIssuesTypes'
import { queryClient } from '@/Providers/ReactQueryProvider'
import { FirestoreTimeRange } from '@/customHooks/useFirestoreTimeRange'

type Args = {
  organizationId: string | undefined
  issueId: string
  status: IssueStatus
}

export default function ({ timestamp }: { timestamp: FirestoreTimeRange }) {
  return useMutation({
    mutationFn: async ({ organizationId, issueId, status }: Args) =>
      updateNdrIssueStatus({ organizationId, issueId, status }),
    onMutate: ({ organizationId, issueId, status }: Args) => {
      const queryKey = ndrQueryKeyStore.issues({ organizationId: organizationId!, timestamp })
      const prev: IssueTypeWithId[] = queryClient.getQueryData(queryKey) ?? []

      const updated = prev.map((issue) => {
        if (issue.id === issueId) {
          return { ...issue, status }
        }
        return issue
      })

      queryClient.setQueryData(queryKey, updated)

      return {
        rollback: () => queryClient.setQueryData(queryKey, prev),
      }
    },

    onError: (_, __, context) => {
      if (context) context.rollback()
    },
  })
}
