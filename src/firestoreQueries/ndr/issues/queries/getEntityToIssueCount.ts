import { doc, getDoc } from 'firebase/firestore'
import { db } from '@/configs/firebase'

interface EntityToIssueCount {
  [entityId: string]: number
}

export default async function getEntityToIssueCount(organizationId: string | undefined) {
  try {
    if (!organizationId) return {}

    const docRef = doc(db, 'organizations', organizationId, 'dashboards/NDR/entitiesIssuesCount', 'summary')
    const docSnap = await getDoc(docRef)

    if (!docSnap.exists()) {
      return {} as EntityToIssueCount
    }

    const data = docSnap.data() as EntityToIssueCount

    return data
  } catch (error) {
    console.error(error)
  }
}