import { db } from '@/configs/firebase'
import { ndrPathStore } from '@/firestoreQueries/utils/pathStore'
import { IssueStatus } from '@globalTypes/ndrBenchmarkTypes/issueTypes'
import * as Sentry from '@sentry/react'
import { doc, updateDoc } from 'firebase/firestore'

type Args = {
  issueId: string
  organizationId: string | undefined
  status: IssueStatus
}

export default async ({ issueId, organizationId, status }: Args) => {
  try {
    if (!organizationId) throw new Error('Missing organizationId')

    const path = ndrPathStore.oneIssue({
      organizationId,
      issueId,
    })

    const ref = doc(db, path)

    await updateDoc(ref, { status })
  } catch (error) {
    Sentry.captureException(error)
    throw error
  }
}
