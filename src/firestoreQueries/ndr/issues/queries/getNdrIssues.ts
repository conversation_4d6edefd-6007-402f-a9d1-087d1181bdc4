import { db } from '@/configs/firebase'
import { FirestoreTimeRange } from '@/customHooks/useFirestoreTimeRange'
import { ndrPathStore } from '@/firestoreQueries/utils/pathStore'
import * as Sentry from '@sentry/react'
import { collection, getDocs, query, where } from 'firebase/firestore'
import { IssueTypeWithId } from '../customIssuesTypes'

type Args = {
  organizationId: string | undefined
  timestamp?: FirestoreTimeRange
}

export default async function getNdrIssues({ organizationId, timestamp }: Args) {
  try {
    if (!organizationId) throw new Error('Organization ID is required')
    const path = ndrPathStore.issues({ organizationId })
    const ref = collection(db, path)

    let q = query(ref, where('status', '==', 'open'))

    if (timestamp) {
      q = query(ref, where('createdTime', '>=', timestamp.from), where('createdTime', '<=', timestamp.to))
    }

    const snap = await getDocs(q)

    const documents: Array<IssueTypeWithId> = []
    snap.forEach((doc) => {
      documents.push({ ...doc.data(), id: doc.id } as IssueTypeWithId)
    })
    return documents
  } catch (error) {
    Sentry.captureException({
      message: error,
      extra: { organizationId, timestamp },
    })
    throw error
  }
}
