import { db } from '@/configs/firebase'
import { ndrPathStore } from '@/firestoreQueries/utils/pathStore'
import * as Sentry from '@sentry/react'
import { collection, getDocs, query, where } from 'firebase/firestore'
import { IssueTypeWithId } from '../customIssuesTypes'

type Args = {
  organizationId: string | undefined
  entityId: string
}

export default async function ({ organizationId, entityId }: Args) {
  try {
    if (!organizationId) throw new Error('organizationId is undefined')
    if (!entityId) throw new Error('entityId is undefined')

    const path = ndrPathStore.issues({ organizationId })
    const ref = collection(db, path)
    const q = query(ref, where('entityId', '==', entityId))

    const snap = await getDocs(q)

    const issues: Array<IssueTypeWithId> = []

    snap.forEach((doc) => {
      issues.push({ ...doc.data(), id: doc.id } as IssueTypeWithId)
    })

    return issues
  } catch (error) {
    Sentry.captureException(error)
    throw error
  }
}
