import { ndrQueryKeyStore } from '@/configs/reactQueryKeyStore'
import { useQuery } from '@tanstack/react-query'
import { IssueTypeWithId } from '../../issues/customIssuesTypes'
import getRecentIssueTrafficPatterns from '@/firestoreQueries/ndr/trafficPatterns/queries/getRecentIssueTrafficPatterns'

type Args = {
  organizationId: string | undefined
  issue: IssueTypeWithId,
  entityId: string,
  enabled: boolean
}

export default function useGetRecentTrafficPatterns ({ organizationId, issue, entityId, enabled }: Args) {
  return useQuery({
    queryKey: ndrQueryKeyStore.issueTrafficPattern({ issueId: issue.id, organizationId: organizationId!, entityId }),
    queryFn: async () => await getRecentIssueTrafficPatterns({ organizationId, issue, entityId }),
    staleTime: 1000 * 60 * 5,
    enabled,
  })
}
