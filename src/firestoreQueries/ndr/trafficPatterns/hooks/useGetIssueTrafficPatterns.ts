import { ndrQueryKeyStore } from '@/configs/reactQueryKeyStore'
import { useQuery } from '@tanstack/react-query'
import getIssueTrafficPatterns from '../queries/getIssueTrafficPatterns'
import { IssueTypeWithId } from '../../issues/customIssuesTypes'

type Args = {
    organizationId: string | undefined
    issue: IssueTypeWithId,
    entityId: string
    enabled: boolean
}

export default function useGetIssueTrafficPatterns ({ organizationId, issue, entityId, enabled }: Args) {
    return useQuery({
        queryKey: ndrQueryKeyStore.issueTrafficPattern({ issueId: issue.id, organizationId: organizationId!, entityId }),
        queryFn: async () => await getIssueTrafficPatterns({ organizationId, issue, entityId }),
        staleTime: 1000 * 60 * 5,
        enabled
    })
}
