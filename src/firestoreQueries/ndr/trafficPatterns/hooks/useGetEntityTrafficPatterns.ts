import { ndrQueryKeyStore } from '@/configs/reactQueryKeyStore'
import { useQuery } from '@tanstack/react-query'
import getEntityTrafficPatterns from '../queries/getEntityTrafficPatterns'

type Args = {
    entityId: string
    organizationId: string | undefined
}

export default function ({ entityId, organizationId }: Args) {
    return useQuery({
        queryKey: ndrQueryKeyStore.entityTrafficPatterns({ entityId, organizationId: organizationId! }),
        queryFn: async () => await getEntityTrafficPatterns({ entityId, organizationId }),
        staleTime: 1000 * 60 * 5,
    })
}
