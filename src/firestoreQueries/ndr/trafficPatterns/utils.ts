import { BaseEntity, CloudEntity, ReferenceEntity } from '@globalTypes/ndrBenchmarkTypes/entityTypes'

export function getRemoteEntityFromTrafficPattern(remoteEntity: BaseEntity, allEntities: Array<CloudEntity>) {
  switch (remoteEntity.type) {
    case 'reference': {
      const fullEntity = allEntities.find((e) => e.id === (remoteEntity as ReferenceEntity).id)
      if (!fullEntity) throw Error(`Entity with id ${(remoteEntity as ReferenceEntity).id} does not exist`)
      return fullEntity
    }
    default:
      return remoteEntity
  }
}
