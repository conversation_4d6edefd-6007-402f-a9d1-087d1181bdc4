// Import necessary functions and modules
import { db } from '@/configs/firebase'
import { ndrPathStore } from '@/firestoreQueries/utils/pathStore'
import { collection, getCountFromServer, query, where} from "firebase/firestore";

export default async function countTrafficPatterns(organizationId: string, startTime: Date | null  = null): Promise<number> {
  const path = ndrPathStore.trafficPatterns({ organizationId })

  const collectionRef = collection(db, path)
  const q = startTime ? query(collectionRef, where('time', '>', startTime)) : query(collectionRef);
  const snapshot = await getCountFromServer(q);

  return snapshot.data().count;
}
