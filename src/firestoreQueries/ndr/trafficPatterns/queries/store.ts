import { captureException } from '@sentry/react'

const dbName = 'TrafficDB'
const storeName = 'trafficPatterns'

export function openDB(): Promise<IDBDatabase> {
  return new Promise((resolve, reject) => {
    const request: IDBOpenDBRequest = indexedDB.open(dbName, 1)

    request.onerror = (event) => {
      reject(new Error('Failed to open the database.'))
    }

    request.onsuccess = (event) => {
      const db = request.result
      resolve(db)
    }

    request.onupgradeneeded = (event) => {
      const db = request.result
      if (!db.objectStoreNames.contains(storeName)) {
        db.createObjectStore(storeName, { keyPath: 'id' })
      }
    }
  })
}

export async function writeAll(trafficPatterns: any[]): Promise<void> {
  const db = await openDB()
  const transaction = db.transaction([storeName], 'readwrite')
  const objectStore = transaction.objectStore(storeName)

  // Clear the object store before adding new data
  const clearRequest: IDBRequest = objectStore.clear()

  clearRequest.onsuccess = async () => {
    // Use Promise.all to wait for all put operations to complete
    try {
      const putPromises = trafficPatterns.map((pattern) => {
        return new Promise((resolve, reject) => {
          const request: IDBRequest = objectStore.put(pattern)

          request.onsuccess = () => {
            resolve(true)
          }

          request.onerror = (event) => {
            reject(event)
          }
        })
      })

      // Wait for all put operations to complete
      await Promise.all(putPromises)
    } catch (error) {
      captureException(error)
    }
  }

  clearRequest.onerror = (event) => {}
}

export async function getAll(): Promise<any[]> {
  const db = await openDB()
  const transaction = db.transaction([storeName], 'readonly')
  const objectStore = transaction.objectStore(storeName)

  return new Promise((resolve, reject) => {
    const request: IDBRequest = objectStore.getAll()

    request.onsuccess = (event) => {
      resolve(request.result as any[])
    }

    request.onerror = (event) => {
      reject(new Error('Failed to retrieve traffic patterns.'))
    }
  })
}
