// Import necessary functions and modules
import { db } from '@/configs/firebase'
import { ndrPathStore } from '@/firestoreQueries/utils/pathStore'
import { DocumentData, collection, query, orderBy, where, limit, startAfter, getDocs, QueryDocumentSnapshot } from "firebase/firestore"; 
import { TrafficPattern } from '@globalTypes/ndrBenchmarkTypes/trafficPatternTypes'

export type PaginateOptions = {  
  pageSize: number
  lastDoc?: QueryDocumentSnapshot<DocumentData, DocumentData> | null
  startTime: Date | null
}

export default async function fetchPaginatedTrafficPatterns(organizationId: string, options: PaginateOptions): Promise<{trafficPatterns: TrafficPattern[], lastDoc: QueryDocumentSnapshot<DocumentData, DocumentData> }> {
  const path = ndrPathStore.trafficPatterns({ organizationId })

  const collectionRef = collection(db, path)
  
  // Construct the query
  let q = query(collectionRef, orderBy("time"), limit(options.pageSize)); 
  if (options.lastDoc) {
    q = query(collectionRef, orderBy("time"), startAfter(options.lastDoc), limit(options.pageSize));
  } else if (options.startTime) {
    q = query(collectionRef, orderBy("time"), where('time', '>', options.startTime), limit(options.pageSize));
  }
  
  // Fetch the documents
  const querySnapshot = await getDocs(q);
  // Get the last document for the next page
  const newLastDoc = querySnapshot.docs[querySnapshot.docs.length - 1];

  // Map the data to an array
  const trafficPatterns = querySnapshot.docs.map(doc => doc.data()) as TrafficPattern[];

  return { trafficPatterns, lastDoc: newLastDoc };
}
