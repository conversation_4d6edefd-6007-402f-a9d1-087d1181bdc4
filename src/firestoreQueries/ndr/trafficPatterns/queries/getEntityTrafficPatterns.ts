import { db } from '@/configs/firebase'
import { ndrPathStore } from '@/firestoreQueries/utils/pathStore'
import { TrafficPattern } from '@globalTypes/ndrBenchmarkTypes/trafficPatternTypes'
import * as Sentry from '@sentry/react'
import { collection, getDocs, query, where } from 'firebase/firestore'
import { TrafficPatternWithId } from '../customTrafficPatternsTypes'

type Args = {
  entityId: string
  organizationId: string | undefined
}

export default async function ({ entityId, organizationId }: Args) {
  try {
    if (!organizationId) throw new Error('organizationId is undefined')

    const path = ndrPathStore.trafficPatterns({ organizationId })
    const ref = collection(db, path)
    const q = query(ref, where('entityId', '==', entityId))
    const snap = await getDocs(q)

    const trafficPatterns: TrafficPatternWithId[] = []

    snap.forEach((doc) => {
      const data = doc.data() as TrafficPattern
      const trafficPattern: TrafficPatternWithId = {
        ...data,
        id: doc.id,
      }
      trafficPatterns.push(trafficPattern)
    })

    return trafficPatterns
  } catch (error) {
    Sentry.captureException(error)
    throw error
  }
}
