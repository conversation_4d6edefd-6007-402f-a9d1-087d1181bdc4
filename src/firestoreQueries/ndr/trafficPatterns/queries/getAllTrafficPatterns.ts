// Import necessary functions and modules
import { db } from '@/configs/firebase'
import { ndrPathStore } from '@/firestoreQueries/utils/pathStore'
import { captureException } from '@sentry/react'
import { collection, getDocs, getDocsFromCache } from 'firebase/firestore'
import { TrafficPatternWithId } from '../customTrafficPatternsTypes'

type Args = {
  organizationId: string | undefined
}

export default async function getAllTrafficPatterns({ organizationId }: Args): Promise<TrafficPatternWithId[]> {
  try {
    if (!organizationId) throw new Error('organizationId is undefined')

    const path = ndrPathStore.trafficPatterns({ organizationId })
    const ref = collection(db, path)

    // Try to get data from the cache first
    let cachedData: TrafficPatternWithId[] = []
    try {
      const cacheSnapshot = await getDocsFromCache(ref)

      cachedData = cacheSnapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }) as TrafficPatternWithId)
      if (cachedData.length === 0) {
        throw new Error('No cached data available.')
      }
    } catch (error) {
      try {
        const snap = await getDocs(ref)

        return snap.docs.map((doc) => ({ id: doc.id, ...doc.data() }) as TrafficPatternWithId)
      } catch (error) {
        captureException(error)
        throw error
      }
    }

    // Return the cached data immediately
    return cachedData
  } catch (error) {
    captureException(error)
    throw error
  }
}
