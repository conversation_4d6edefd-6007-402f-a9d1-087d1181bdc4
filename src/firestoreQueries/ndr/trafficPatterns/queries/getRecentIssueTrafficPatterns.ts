import * as Sentry from '@sentry/react'
import { IssueTypeWithId } from '../../issues/customIssuesTypes'
import { ndrPathStore } from '@/firestoreQueries/utils/pathStore'
import { getDoc, doc } from 'firebase/firestore'
import { db } from '@/configs/firebase'

type Args = {
  organizationId: string | undefined
  issue: IssueTypeWithId
  entityId: string
}

export default async function getRecentIssueTrafficPatterns ({ organizationId, issue, entityId }: Args) {
  try {
    if (!organizationId) throw new Error('Missing organizationId')
    
    const path = ndrPathStore.oneIssueRecentTraffics({
      organizationId,
      issueId: issue.id,
      entityId,
    })
    
    const ref = doc(db, path)
    const snap = await getDoc(ref)
    
    const response =  snap.data()
    
    return response.trafficPatterns;
  } catch (error) {
    Sentry.captureException(error)
    throw error
  }
}
