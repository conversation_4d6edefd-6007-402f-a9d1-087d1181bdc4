import * as Sentry from '@sentry/react'
import getNdrEntities from '../../entities/queries/getEntities'
import { IssueTypeWithId } from '../../issues/customIssuesTypes'

type Args = {
  entityId: string
  organizationId: string | undefined
  issue: IssueTypeWithId
}

export default async function getIssueTrafficPatterns ({ entityId, organizationId, issue }: Args) {
  try {
    if (!organizationId) throw new Error('Organization ID is required')
    const allEntities = await getNdrEntities({ organizationId })
    
    const mainEntity = allEntities.find((e) => e.id === issue.entityId)
    if (!mainEntity) {
      throw new Error(`Entity was not found for issue entity. Issue -> ${issue.id} Entity -> ${issue.entityId}`)
    }
    
    return issue.trafficPatterns.map((pattern, index) => ({...pattern, id: String(index)}))
  } catch (error) {
    Sentry.captureException(error)
    throw error
  }
}
