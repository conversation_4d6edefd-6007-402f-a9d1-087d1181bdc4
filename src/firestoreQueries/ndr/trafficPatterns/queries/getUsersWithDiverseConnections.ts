/* eslint-disable import/named */
import { EntityTypeWithId } from '@/firestoreQueries/ndr/entities/customEntitiesTypes'
import getEntities from '@/firestoreQueries/ndr/entities/queries/getEntities'
import { CloudEntity } from '@globalTypes/ndrBenchmarkTypes/entityTypes'
import { MachineType, SentinelOneInfo } from '@globalTypes/ndrBenchmarkTypes/sentinelOneTypes'
import { captureException } from '@sentry/react'
import { TrafficPatternWithId } from '../customTrafficPatternsTypes'
import getAllTrafficPatterns from './getAllTrafficPatterns'

const USER_MACHINE_TYPES: MachineType[] = ['desktop', 'laptop']
const NO_OF_CRITICAL_ENTITIES = 5

export default async function getUsersWithDiverseConnections(organizationId: string | undefined) {
  try {
    if (!organizationId) throw new Error('organizationId is undefined')
    const [entities, trafficPatterns] = await Promise.all([
      getEntities({ organizationId }),
      getAllTrafficPatterns({ organizationId }),
    ])
  } catch (error) {
    captureException(error)
  }
}

export function createUsersWithDiverseConnection(
  entities: Array<EntityTypeWithId>,
  trafficPatterns: Array<TrafficPatternWithId>,
) {
  const countMap: Map<string, number> = new Map()

  trafficPatterns.forEach((tp) => {
    const localEntity = entities.find((e) => e.id === tp.entityId)
    if (!localEntity) return
    if (localEntity.type !== 'sentinelOne_agent') return
    const remoteEntity = tp.remoteEntity
    let sender: CloudEntity
    let reciever: CloudEntity

    if (tp.direction === 'ingress') {
      sender = remoteEntity as CloudEntity
      reciever = localEntity
    } else {
      sender = localEntity
      reciever = remoteEntity as CloudEntity
    }

    const recieverInfo = reciever.info as SentinelOneInfo
    const senderInfo = sender.info as SentinelOneInfo

    if (USER_MACHINE_TYPES.includes(recieverInfo.machineType)) return
    if (tp.remoteEntity.type !== 'sentinelOne_agent') return

    if (!USER_MACHINE_TYPES.includes(senderInfo.machineType)) return
    const curr = countMap.get(senderInfo.computerName) || 0
    countMap.set(senderInfo.computerName, curr + 1)
  })

  const barChartData = [...countMap.entries()]
    .sort((a, b) => b[1] - a[1])
    .slice(0, NO_OF_CRITICAL_ENTITIES)
    .map(([key, data]) => ({ key, data }))

  return barChartData
}
