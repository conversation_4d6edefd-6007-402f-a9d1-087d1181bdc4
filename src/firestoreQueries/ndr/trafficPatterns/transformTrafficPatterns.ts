import { BaseEntity, CloudEntity, ReferenceEntity } from '@globalTypes/ndrBenchmarkTypes/entityTypes'
import { SentinelOneTrafficPatternAdditionalInformation } from '@globalTypes/ndrBenchmarkTypes/sentinelOneTypes'
import { TrafficPattern } from '@globalTypes/ndrBenchmarkTypes/trafficPatternTypes'

export type UniqueTrafficPattern = {
  srcEntity: BaseEntity
  srcAddr: string
  srcProviderInformation?: SentinelOneTrafficPatternAdditionalInformation
  dstEntity: BaseEntity
  dstAddr: string
  dstProviderInformation?: SentinelOneTrafficPatternAdditionalInformation
  port: string
  time: Date
  isDanger?: boolean
  egressVolume: number
  ingressVolume: number
}

function transformTrafficPattern(
  oldPattern: TrafficPattern,
  allEntities: Array<CloudEntity>,
): UniqueTrafficPattern | null {
  // Find entity by ID or create one from partial information
  function findOrCreateEntity(entityId: string | null, fallbackInfo: Partial<BaseEntity> | null): BaseEntity | null {
    if (!entityId && !fallbackInfo?.name) return null

    const foundEntity = allEntities.find((entity) => entity.id === entityId)
    if (foundEntity) return foundEntity

    if (fallbackInfo?.name) {
      return { ...fallbackInfo, id: fallbackInfo.id || fallbackInfo.name }
    }

    return null
  }

  // Determine local and remote entities
  const localEntity = findOrCreateEntity(oldPattern.entityId, oldPattern.localEntity)
  const remoteEntity =
    oldPattern.remoteEntity.type === 'reference'
      ? findOrCreateEntity((oldPattern.remoteEntity as ReferenceEntity).id, null)
      : findOrCreateEntity(null, oldPattern.remoteEntity)

  // If either entity is missing, we can't create a valid traffic pattern
  if (!localEntity || !remoteEntity) return null

  // Common properties for all traffic patterns
  const commonProperties = {
    port: oldPattern.port,
    egressVolume: oldPattern.egressVolume,
    ingressVolume: oldPattern.ingressVolume,
    time: oldPattern.time,
    isDanger: oldPattern.isDanger,
  }

  // Determine source and destination based on traffic direction
  const isEgress = oldPattern.direction === 'egress'
  const sourceEntity = isEgress ? localEntity : remoteEntity
  const destinationEntity = isEgress ? remoteEntity : localEntity
  const sourceAddress = isEgress ? oldPattern.localAddr : oldPattern.remoteAddr
  const destinationAddress = isEgress ? oldPattern.remoteAddr : oldPattern.localAddr
  const sourceProviderInfo = isEgress ? oldPattern.providerInformation : oldPattern.remoteProviderInformation
  const destinationProviderInfo = isEgress ? oldPattern.remoteProviderInformation : oldPattern.providerInformation

  // Construct and return the new traffic pattern
  return {
    ...commonProperties,
    srcEntity: sourceEntity,
    dstEntity: destinationEntity,
    srcAddr: sourceAddress,
    dstAddr: destinationAddress,
    srcProviderInformation: sourceProviderInfo,
    dstProviderInformation: destinationProviderInfo,
  }
}

export function getTrafficPatternUniqueKey(trafficPattern: UniqueTrafficPattern): string {
  if (trafficPattern.dstProviderInformation && trafficPattern.srcProviderInformation) {
    return `${trafficPattern.srcAddr}-${trafficPattern.dstAddr}-${trafficPattern.port}-${JSON.stringify(trafficPattern.srcProviderInformation)}-${JSON.stringify(trafficPattern.dstProviderInformation)}`
  }
  return `${trafficPattern.srcAddr}-${trafficPattern.dstAddr}-${trafficPattern.port}`
}

export function getTransformedTrafficPatterns(
  trafficPatterns: TrafficPattern[],
  entities: Array<CloudEntity>,
): UniqueTrafficPattern[] {
  const uniqueMap = new Map<string, UniqueTrafficPattern>()

  trafficPatterns.forEach((trafficPattern) => {
    const transformedTrafficPattern = transformTrafficPattern(trafficPattern, entities)
    if (transformedTrafficPattern) {
      const key = getTrafficPatternUniqueKey(transformedTrafficPattern)
      uniqueMap.set(key, transformedTrafficPattern)
    }
  })

  const uniqueTrafficPatterns = Array.from(uniqueMap.values())
  return uniqueTrafficPatterns
}
