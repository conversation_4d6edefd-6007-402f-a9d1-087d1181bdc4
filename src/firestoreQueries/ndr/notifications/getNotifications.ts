import { db } from '@/configs/firebase'
import { ndrPathStore } from '@/firestoreQueries/utils/pathStore'
import { Notification } from '@globalTypes/ndrBenchmarkTypes/notificationTypes'
import { captureException } from '@sentry/react'
import {
  collection,
  query,
  orderBy,
  limit,
  startAfter,
  getDocs,
  // where,
  QueryDocumentSnapshot,
  Query,
} from 'firebase/firestore'

import { parseNotificationFormattingByKey } from '@/views/NDR/NotificationsPage/utils'
import _ from 'lodash'

async function getNotifications(
  organizationsId: string,
  pageSize: number = 10,
  lastDoc?: QueryDocumentSnapshot,
  // searchQuery?: string,
): Promise<{
  notifications: Notification[]
  lastDoc: QueryDocumentSnapshot | null
  hasMore: boolean
}> {
  try {
    if (!organizationsId) throw new Error('organizationsId is undefined')
    const path = ndrPathStore.notifications(organizationsId)
    const ref = collection(db, path)

    let q: Query = query(ref, orderBy('time', 'desc'))

    // if (searchQuery) {
    //   q = query(ref,
    //     where('title', '==', searchQuery)
    //   )
    // }

    q = query(q, limit(pageSize))

    if (lastDoc) {
      q = query(q, startAfter(lastDoc))
    }

    const snap = await getDocs(q)
    const notifications = snap.docs.map((doc) => doc.data() as Notification)

    const results = notifications
      .filter((notification) => !_.isNil(notification.message))
      .map((notification) => ({
        ...notification,
        title: parseNotificationFormattingByKey(notification, 'title'),
        message: parseNotificationFormattingByKey(notification, 'message'),
      }))

    return {
      notifications: results,
      lastDoc: snap.docs[snap.docs.length - 1] || null,
      hasMore: results.length >= pageSize,
    }
  } catch (error) {
    captureException(error)
    throw error
  }
}

export default getNotifications
