import { db } from '@/configs/firebase'
import * as Sentry from '@sentry/react'
import { doc, getDoc } from 'firebase/firestore'
import { BaseActivity } from '@/components/ActivityLogs/components/ActivityLogs'
import { ndrPathStore } from '@/firestoreQueries/utils/pathStore'

type Args = {
  organizationId: string | undefined
  activityId: string
}

export default async function getFirewallActivityDetails({ organizationId, activityId }: Args) {
  try {
    if (!organizationId) throw new Error('Missing organizationId')
    const path = ndrPathStore.activities({ organizationId })
    const docRef = doc(db, path, activityId)
    const docSnap = await getDoc(docRef)

    if (!docSnap.exists()) {
      throw new Error('Activity not found')
    }

    return {
      ...docSnap.data(),
      id: docSnap.id,
    } as BaseActivity
  } catch (error) {
    Sentry.captureException(error)
    throw error
  }
}
