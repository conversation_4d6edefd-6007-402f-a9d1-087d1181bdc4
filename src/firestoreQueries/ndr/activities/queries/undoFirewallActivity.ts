import * as Sentry from '@sentry/react'
import { ExtendedActivity } from '@/components/ActivityLogs/components/ActivityLogs'
import { applyFirewallRules } from '@/utils/cloudFunctions'
import { Rule } from '@/components/ActivityLogs/components/types'

type Args = {
  organizationId: string | undefined
  activity: ExtendedActivity
}

export default async function undoFirewallActivity({ organizationId, activity }: Args) {
  try {
    if (!organizationId) throw new Error('Missing organizationId')

    let rules = [] as Rule[]
    let rulesToDelete = [] as Rule[]

    switch (activity.additionalData?.activityType) {
      case 'firewallRuleCreated':
        rulesToDelete = [activity.additionalData.activityData.postState]
        break
      case 'firewallRuleDeleted':
      case 'firewallRuleUpdated':
        rules = [activity.additionalData.activityData.previousState]
        break
      default:
        throw new Error(`Unsupported activity type: ${activity.additionalData?.activityType}`)
    }

    await applyRules({
      rules,
      rulesToDelete,
      organizationId,
      entityId: activity.additionalData.entityId,
    })

    return true
  } catch (error) {
    Sentry.captureException(error)
    throw error
  }
}

async function applyRules({
  rules,
  rulesToDelete,
  organizationId,
  entityId,
}: {
  rules: any[]
  rulesToDelete: any[]
  organizationId: string
  entityId: string
}) {
  const validate = (rules: any[]) =>
    rules
      .filter((rule) => {
        return rule?.portRanges?.length > 0 && rule.remoteIdentifiers?.length > 0
      })
      .map(({ uuid, ...rules }) => ({ ...rules }))

  try {
    await applyFirewallRules({
      rules: validate(rules),
      rulesToDelete,
      organizationId,
      entityId,
    })
  } catch (error) {
    console.error('applyRules::submit_failed', { error })
    throw error
  }
}
