import { db } from '@/configs/firebase'
import * as Sentry from '@sentry/react'
import { collection, getDocs, limit, orderBy, query, startAfter } from 'firebase/firestore'
import { ndrPathStore } from '@/firestoreQueries/utils/pathStore'
import { ExtendedActivity } from '@/components/ActivityLogs/components/types'

type Args = {
  organizationId: string | undefined
  page: number
  pageSize: number
  lastDoc?: any
}

export default async function getActivities({ organizationId, pageSize = 10, lastDoc }: Args) {
  try {
    if (!organizationId) throw new Error('Missing organizationId')

    const path = ndrPathStore.activities({ organizationId })
    const ref = collection(db, path)

    let q = query(ref, orderBy('createdAt', 'desc'), limit(pageSize))

    if (lastDoc) {
      q = query(q, startAfter(lastDoc))
    }

    const snap = await getDocs(q)
    const activities: ExtendedActivity[] = []

    snap.forEach((doc) => {
      activities.push({ ...doc.data(), id: doc.id } as ExtendedActivity)
    })

    const lastVisible = snap.docs[snap.docs.length - 1]
    const hasMore = activities.length === pageSize

    return {
      activities,
      hasMore,
      lastDoc: lastVisible,
    }
  } catch (error) {
    console.error('Error fetching activities:', error)
    Sentry.captureException(error)
    throw error
  }
}
