import { useQuery } from '@tanstack/react-query'
import getActivities from '../queries/getActivities'

type Args = {
  organizationId: string | undefined
  page: number
  pageSize: number
}

export default function useGetActivities({ organizationId, page, pageSize }: Args) {
  return useQuery({
    queryKey: ['activities', organizationId],
    queryFn: async () => await getActivities({ organizationId, page, pageSize }),
  })
}
