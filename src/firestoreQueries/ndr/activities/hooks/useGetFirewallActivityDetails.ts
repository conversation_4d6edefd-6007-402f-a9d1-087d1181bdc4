import { useQuery } from '@tanstack/react-query'
import getFirewallActivityDetails from '../queries/getFirewallActivityDetails'

type Args = {
  organizationId: string | undefined
  activityId: string
  enabled?: boolean
}

export default function useGetFirewallActivityDetails({ organizationId, activityId, enabled }: Args) {
  return useQuery({
    queryKey: ['firewall-activity-details', organizationId, activityId],
    queryFn: async () => await getFirewallActivityDetails({ organizationId, activityId }),
    enabled: enabled && !!organizationId && !!activityId,
  })
} 