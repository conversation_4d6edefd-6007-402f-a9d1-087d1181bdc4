import { renderHook, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ReactNode } from 'react'
import { useGetUserGroupViews, useCreateGroupView } from '../useGroupViews'

// Mock the queries
jest.mock('../queries/groupViewQueries', () => ({
  getUserGroupViews: jest.fn(),
  createGroupView: jest.fn(),
  updateGroupView: jest.fn(),
  deleteGroupView: jest.fn(),
  getDefaultGroupView: jest.fn(),
}))

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  })

  return ({ children }: { children: ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )
}

describe('useGroupViews hooks', () => {
  const mockOrganizationId = 'org-123'
  const mockUserId = 'user-456'

  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('useGetUserGroupViews', () => {
    it('should fetch user group views when enabled', async () => {
      const mockGroupViews = [
        {
          id: 'view-1',
          name: 'Production View',
          primaryKey: 'environment',
          secondaryKey: 'application',
          isDefault: true,
          userId: mockUserId,
          organizationId: mockOrganizationId,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ]

      const { getUserGroupViews } = require('../queries/groupViewQueries')
      getUserGroupViews.mockResolvedValue(mockGroupViews)

      const { result } = renderHook(
        () => useGetUserGroupViews(mockOrganizationId, mockUserId),
        { wrapper: createWrapper() }
      )

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(mockGroupViews)
      expect(getUserGroupViews).toHaveBeenCalledWith(mockOrganizationId, mockUserId)
    })

    it('should not fetch when organizationId or userId is undefined', () => {
      const { result } = renderHook(
        () => useGetUserGroupViews(undefined, mockUserId),
        { wrapper: createWrapper() }
      )

      expect(result.current.isLoading).toBe(false)
      expect(result.current.data).toBeUndefined()
    })
  })

  describe('useCreateGroupView', () => {
    it('should create a new group view', async () => {
      const mockNewView = {
        id: 'view-2',
        name: 'Test View',
        primaryKey: 'team',
        isDefault: false,
        userId: mockUserId,
        organizationId: mockOrganizationId,
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      const { createGroupView } = require('../queries/groupViewQueries')
      createGroupView.mockResolvedValue(mockNewView)

      const { result } = renderHook(
        () => useCreateGroupView(mockOrganizationId, mockUserId),
        { wrapper: createWrapper() }
      )

      const createData = {
        name: 'Test View',
        primaryKey: 'team',
        isDefault: false,
      }

      result.current.mutate(createData)

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(createGroupView).toHaveBeenCalledWith(
        mockOrganizationId,
        mockUserId,
        createData
      )
    })

    it('should handle creation errors', async () => {
      const { createGroupView } = require('../queries/groupViewQueries')
      createGroupView.mockRejectedValue(new Error('Creation failed'))

      const { result } = renderHook(
        () => useCreateGroupView(mockOrganizationId, mockUserId),
        { wrapper: createWrapper() }
      )

      const createData = {
        name: 'Test View',
        primaryKey: 'team',
      }

      result.current.mutate(createData)

      await waitFor(() => {
        expect(result.current.isError).toBe(true)
      })

      expect(result.current.error).toEqual(new Error('Creation failed'))
    })
  })
})

// Integration test for the complete flow
describe('Group Views Integration', () => {
  it('should handle the complete group view lifecycle', async () => {
    const mockOrganizationId = 'org-123'
    const mockUserId = 'user-456'

    // Mock the complete flow
    const {
      getUserGroupViews,
      createGroupView,
      updateGroupView,
      deleteGroupView,
    } = require('../queries/groupViewQueries')

    // Initial empty state
    getUserGroupViews.mockResolvedValueOnce([])

    // Create a new view
    const newView = {
      id: 'view-1',
      name: 'Test View',
      primaryKey: 'environment',
      isDefault: false,
      userId: mockUserId,
      organizationId: mockOrganizationId,
      createdAt: new Date(),
      updatedAt: new Date(),
    }
    createGroupView.mockResolvedValueOnce(newView)

    // Update the view
    const updatedView = { ...newView, name: 'Updated Test View' }
    updateGroupView.mockResolvedValueOnce(updatedView)

    // Delete the view
    deleteGroupView.mockResolvedValueOnce(undefined)

    // Test the flow
    const wrapper = createWrapper()

    // 1. Get initial empty views
    const { result: getResult } = renderHook(
      () => useGetUserGroupViews(mockOrganizationId, mockUserId),
      { wrapper }
    )

    await waitFor(() => {
      expect(getResult.current.isSuccess).toBe(true)
    })
    expect(getResult.current.data).toEqual([])

    // 2. Create a new view
    const { result: createResult } = renderHook(
      () => useCreateGroupView(mockOrganizationId, mockUserId),
      { wrapper }
    )

    createResult.current.mutate({
      name: 'Test View',
      primaryKey: 'environment',
    })

    await waitFor(() => {
      expect(createResult.current.isSuccess).toBe(true)
    })

    expect(createGroupView).toHaveBeenCalledWith(
      mockOrganizationId,
      mockUserId,
      {
        name: 'Test View',
        primaryKey: 'environment',
      }
    )
  })
})
