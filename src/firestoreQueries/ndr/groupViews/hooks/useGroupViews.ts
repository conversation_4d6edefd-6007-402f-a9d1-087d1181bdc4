import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { ndrQueryKeyStore } from '@/configs/reactQueryKeyStore'
import { 
  getGroupViews, 
  getUserGroupViews, 
  getGroupView, 
  getDefaultGroupView,
  createGroupView, 
  updateGroupView, 
  deleteGroupView 
} from '../queries/groupViewQueries'
import { 
  GroupViewWithId, 
  CreateGroupViewRequest, 
  UpdateGroupViewRequest 
} from '@globalTypes/ndrBenchmarkTypes/groupViews/groupViewTypes'

// Get all group views for an organization
export function useGetGroupViews(organizationId: string | undefined) {
  return useQuery({
    queryKey: ndrQueryKeyStore.groupViews({ organizationId }),
    queryFn: () => {
      if (!organizationId) throw new Error('Organization ID is required')
      return getGroupViews(organizationId)
    },
    enabled: !!organizationId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

// Get group views for a specific user
export function useGetUserGroupViews(organizationId: string | undefined, userId: string | undefined) {
  return useQuery({
    queryKey: [...ndrQueryKeyStore.groupViews({ organizationId }), 'user', userId],
    queryFn: () => {
      if (!organizationId || !userId) throw new Error('Organization ID and User ID are required')
      return getUserGroupViews(organizationId, userId)
    },
    enabled: !!organizationId && !!userId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

// Get a single group view
export function useGetGroupView(organizationId: string | undefined, groupViewId: string | undefined) {
  return useQuery({
    queryKey: ndrQueryKeyStore.oneGroupView({ organizationId, groupViewId: groupViewId || '' }),
    queryFn: () => {
      if (!organizationId || !groupViewId) throw new Error('Organization ID and Group View ID are required')
      return getGroupView(organizationId, groupViewId)
    },
    enabled: !!organizationId && !!groupViewId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

// Get default group view for a user
export function useGetDefaultGroupView(organizationId: string | undefined, userId: string | undefined) {
  return useQuery({
    queryKey: [...ndrQueryKeyStore.groupViews({ organizationId }), 'default', userId],
    queryFn: () => {
      if (!organizationId || !userId) throw new Error('Organization ID and User ID are required')
      return getDefaultGroupView(organizationId, userId)
    },
    enabled: !!organizationId && !!userId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

// Create a new group view
export function useCreateGroupView(organizationId: string | undefined, userId: string | undefined) {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (groupViewData: CreateGroupViewRequest) => {
      if (!organizationId || !userId) throw new Error('Organization ID and User ID are required')
      return createGroupView(organizationId, userId, groupViewData)
    },
    onSuccess: (newGroupView) => {
      // Invalidate and refetch group views
      queryClient.invalidateQueries({
        queryKey: ndrQueryKeyStore.groupViews({ organizationId }),
      })
      
      // If this is set as default, invalidate default query
      if (newGroupView.isDefault) {
        queryClient.invalidateQueries({
          queryKey: [...ndrQueryKeyStore.groupViews({ organizationId }), 'default', userId],
        })
      }
    },
  })
}

// Update a group view
export function useUpdateGroupView(organizationId: string | undefined, userId: string | undefined) {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (updateData: UpdateGroupViewRequest) => {
      if (!organizationId || !userId) throw new Error('Organization ID and User ID are required')
      return updateGroupView(organizationId, userId, updateData)
    },
    onSuccess: (updatedGroupView) => {
      // Invalidate and refetch group views
      queryClient.invalidateQueries({
        queryKey: ndrQueryKeyStore.groupViews({ organizationId }),
      })
      
      // Update the specific group view in cache
      queryClient.setQueryData(
        ndrQueryKeyStore.oneGroupView({ organizationId, groupViewId: updatedGroupView.id }),
        updatedGroupView
      )
      
      // If this is set as default, invalidate default query
      if (updatedGroupView.isDefault) {
        queryClient.invalidateQueries({
          queryKey: [...ndrQueryKeyStore.groupViews({ organizationId }), 'default', userId],
        })
      }
    },
  })
}

// Delete a group view
export function useDeleteGroupView(organizationId: string | undefined) {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (groupViewId: string) => {
      if (!organizationId) throw new Error('Organization ID is required')
      return deleteGroupView(organizationId, groupViewId)
    },
    onSuccess: (_, groupViewId) => {
      // Invalidate and refetch group views
      queryClient.invalidateQueries({
        queryKey: ndrQueryKeyStore.groupViews({ organizationId }),
      })
      
      // Remove the specific group view from cache
      queryClient.removeQueries({
        queryKey: ndrQueryKeyStore.oneGroupView({ organizationId, groupViewId }),
      })
    },
  })
}

// Set a group view as default
export function useSetDefaultGroupView(organizationId: string | undefined, userId: string | undefined) {
  const updateGroupView = useUpdateGroupView(organizationId, userId)
  
  return useMutation({
    mutationFn: (groupViewId: string) => {
      return updateGroupView.mutateAsync({
        id: groupViewId,
        isDefault: true,
      })
    },
  })
}
