import { db } from '@/configs/firebase'
import { ndrPathStore } from '@/firestoreQueries/utils/pathStore'
import { captureException } from '@sentry/react'
import { 
  collection, 
  doc, 
  getDocs, 
  getDoc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy 
} from 'firebase/firestore'
import { 
  GroupView, 
  GroupViewWithId, 
  CreateGroupViewRequest, 
  UpdateGroupViewRequest 
} from '@globalTypes/ndrBenchmarkTypes/groupViews/groupViewTypes'

// Get all group views for an organization
export async function getGroupViews(organizationId: string): Promise<GroupViewWithId[]> {
  try {
    const path = ndrPathStore.groupViews({ organizationId })
    const ref = collection(db, path)
    const q = query(ref, orderBy('createdAt', 'desc'))
    const snapshot = await getDocs(q)
    
    return snapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as GroupViewWithId[]
  } catch (error) {
    captureException(error)
    throw error
  }
}

// Get group views for a specific user
export async function getUserGroupViews(organizationId: string, userId: string): Promise<GroupViewWithId[]> {
  try {
    const path = ndrPathStore.groupViews({ organizationId })
    const ref = collection(db, path)
    const q = query(ref, where('userId', '==', userId), orderBy('createdAt', 'desc'))
    const snapshot = await getDocs(q)
    
    return snapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as GroupViewWithId[]
  } catch (error) {
    captureException(error)
    throw error
  }
}

// Get a single group view
export async function getGroupView(organizationId: string, groupViewId: string): Promise<GroupViewWithId | null> {
  try {
    const path = ndrPathStore.oneGroupView({ organizationId, groupViewId })
    const ref = doc(db, path)
    const snapshot = await getDoc(ref)
    
    if (!snapshot.exists()) {
      return null
    }
    
    return {
      id: snapshot.id,
      ...snapshot.data(),
    } as GroupViewWithId
  } catch (error) {
    captureException(error)
    throw error
  }
}

// Get default group view for a user
export async function getDefaultGroupView(organizationId: string, userId: string): Promise<GroupViewWithId | null> {
  try {
    const path = ndrPathStore.groupViews({ organizationId })
    const ref = collection(db, path)
    const q = query(ref, where('userId', '==', userId), where('isDefault', '==', true))
    const snapshot = await getDocs(q)
    
    if (snapshot.empty) {
      return null
    }
    
    const doc = snapshot.docs[0]
    return {
      id: doc.id,
      ...doc.data(),
    } as GroupViewWithId
  } catch (error) {
    captureException(error)
    throw error
  }
}

// Create a new group view
export async function createGroupView(
  organizationId: string, 
  userId: string, 
  groupViewData: CreateGroupViewRequest
): Promise<GroupViewWithId> {
  try {
    const path = ndrPathStore.groupViews({ organizationId })
    const ref = collection(db, path)
    
    // If this is set as default, unset other defaults for this user
    if (groupViewData.isDefault) {
      await unsetDefaultGroupViews(organizationId, userId)
    }
    
    const newGroupView: Omit<GroupView, 'id'> = {
      name: groupViewData.name,
      primaryKey: groupViewData.primaryKey,
      ...(groupViewData.secondaryKey && { secondaryKey: groupViewData.secondaryKey }),
      ...(groupViewData.additionalKeys && groupViewData.additionalKeys.length > 0 && { additionalKeys: groupViewData.additionalKeys }),
      userId,
      organizationId,
      isDefault: groupViewData.isDefault || false,
      createdAt: new Date(),
      updatedAt: new Date(),
    }
    
    const docRef = await addDoc(ref, newGroupView)
    
    return {
      id: docRef.id,
      ...newGroupView,
    } as GroupViewWithId
  } catch (error) {
    console.log(error)
    captureException(error)
    throw error
  }
}

// Update a group view
export async function updateGroupView(
  organizationId: string, 
  userId: string, 
  updateData: UpdateGroupViewRequest
): Promise<GroupViewWithId> {
  try {
    const { id, ...updateFields } = updateData
    const path = ndrPathStore.oneGroupView({ organizationId, groupViewId: id })
    const ref = doc(db, path)
    
    // If this is set as default, unset other defaults for this user
    if (updateFields.isDefault) {
      await unsetDefaultGroupViews(organizationId, userId)
    }
    
    // Filter out undefined values and empty strings to prevent Firestore errors
    const cleanedUpdateFields = Object.fromEntries(
      Object.entries(updateFields).filter(([key, value]) => {
        if (key === 'id') return false // Don't include id in update
        return value !== undefined && value !== ''
      })
    )

    const updatedData = {
      ...cleanedUpdateFields,
      updatedAt: new Date(),
    }
    
    await updateDoc(ref, updatedData)
    
    // Get the updated document
    const updatedDoc = await getDoc(ref)
    return {
      id: updatedDoc.id,
      ...updatedDoc.data(),
    } as GroupViewWithId
  } catch (error) {
    captureException(error)
    throw error
  }
}

// Delete a group view
export async function deleteGroupView(organizationId: string, groupViewId: string): Promise<void> {
  try {
    const path = ndrPathStore.oneGroupView({ organizationId, groupViewId })
    const ref = doc(db, path)
    await deleteDoc(ref)
  } catch (error) {
    captureException(error)
    throw error
  }
}

// Helper function to unset default group views for a user
async function unsetDefaultGroupViews(organizationId: string, userId: string): Promise<void> {
  try {
    const path = ndrPathStore.groupViews({ organizationId })
    const ref = collection(db, path)
    const q = query(ref, where('userId', '==', userId), where('isDefault', '==', true))
    const snapshot = await getDocs(q)
    
    const updatePromises = snapshot.docs.map((doc) => 
      updateDoc(doc.ref, { isDefault: false, updatedAt: new Date() })
    )
    
    await Promise.all(updatePromises)
  } catch (error) {
    captureException(error)
    throw error
  }
}
