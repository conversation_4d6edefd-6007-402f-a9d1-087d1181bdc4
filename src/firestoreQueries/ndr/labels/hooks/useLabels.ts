import { db } from '@/configs/firebase'
import { collection, doc, getDocs, setDoc, deleteDoc, updateDoc, query, where } from 'firebase/firestore'
import { useMutation, useQuery } from '@tanstack/react-query'
import { queryClient } from '@/Providers/ReactQueryProvider'
import { ndrQueryKeyStore } from '@/configs/reactQueryKeyStore'
import { ndrPathStore } from '@/firestoreQueries/utils/pathStore'
import { Condition, LabelType, UserLabel } from '@/types/label'
import { getFunctions, httpsCallable } from '@firebase/functions'

const functions = getFunctions()

export interface Label {
  id: string
  key: string
  values: string[]
  type: LabelType
  description?: string
  conditions?: Condition[]
  createdAt: Date
  updatedAt: Date
  icon: string
  color: string
  withoutCalculate?: boolean
  dryRun?: boolean
  originalValues?: string[]
  name?: string
  issueCount?: number
  
}

export interface LabelAssignment {
  id?: string
  labelIds: string[]
  entityId: string
  createdAt: Date
}

interface ResponseEntityLabels {
  id: string
  entityId: string
  labels: Label[]
}

async function getSecurityGroups(organizationId: string) {
  const labelsRef = collection(db, ndrPathStore.securityGroups({ organizationId }))
  const snapshot = await getDocs(labelsRef)
  return snapshot.docs.map((doc) => ({
    id: doc.id,
    ...doc.data(),
  })) as SecurityGroup[]
}

// Query functions
async function getLabels(organizationId: string) {
  const labelsRef = collection(db, ndrPathStore.labels({ organizationId }))
  const snapshot = await getDocs(labelsRef)
  return snapshot.docs.map((doc) => ({
    id: doc.id,
    ...doc.data(),
  })) as Label[]
}

async function getEntityLabels(organizationId: string, entityId: string) {
  try {
    const assignmentsPath = ndrPathStore.labelAssignments({ organizationId })
    const assignmentsRef = collection(db, assignmentsPath)
    const assignmentsQuery = query(assignmentsRef, where('entityId', '==', entityId))
    const assignmentsSnapshot = await getDocs(assignmentsQuery)
    const labelsPath = ndrPathStore.labels({ organizationId })
    const labelsRef = collection(db, labelsPath)
    const labelsSnapshot = await getDocs(labelsRef)

    const labelsMap = new Map()
    labelsSnapshot.docs.forEach((doc) => {
      labelsMap.set(doc.id, {
        id: doc.id,
        ...doc.data(),
      })
    })

    // Process assignments and map to labels
    let result: ResponseEntityLabels | null = null
    for (const assignmentDoc of assignmentsSnapshot.docs) {
      const assignment = assignmentDoc.data()
      const labelIds = assignment.labelIds || []

      // Map each labelId to its corresponding label data
      const labels = labelIds
        .map((labelId: string) => {
          const label = labelsMap.get(labelId)
          if (label) {
            return label
          }
          return null
        })
        .filter(Boolean) as Label[] // Remove any null values

      result = {
        id: assignmentDoc.id,
        entityId: assignment.entityId,
        labels,
      }
    }

    return result
  } catch (e) {
    return null
  }
}

async function getLabelHostnames(organizationId: string) {
  // Get all label assignments
  const assignmentsRef = collection(db, ndrPathStore.labelAssignments({ organizationId }))
  const assignmentsSnapshot = await getDocs(assignmentsRef)

  // Get all entities
  const entitiesRef = collection(db, ndrPathStore.entities({ organizationId }))
  const entitiesSnapshot = await getDocs(entitiesRef)

  // Create a map of entity IDs to their names
  const entityNameMap = new Map()
  entitiesSnapshot.docs.forEach((doc) => {
    const data = doc.data()
    entityNameMap.set(doc.id, data.name || data.info?.computerName || data.info?.hostname || 'Unknown')
  })

  // Create a map of label IDs to hostnames
  const labelHostnamesMap = new Map()
  assignmentsSnapshot.docs.forEach((doc) => {
    const assignment = doc.data()
    const labelIds = assignment.labelIds || []
    const entityName = entityNameMap.get(assignment.entityId)

    if (entityName) {
      labelIds.forEach((labelId: string) => {
        if (!labelHostnamesMap.has(labelId)) {
          labelHostnamesMap.set(labelId, new Set())
        }
        labelHostnamesMap.get(labelId).add(entityName)
      })
    }
  })

  // Convert Sets to Arrays
  const result = new Map()
  labelHostnamesMap.forEach((hostnames, labelId) => {
    result.set(labelId, Array.from(hostnames))
  })

  return result
}

const submitLabelFunc = httpsCallable<
  {
    label: UserLabel
    organizationId: string
    entityId?: string
    dryRun?: boolean
  },
  { data: any[]; error: any }
>(functions, 'calculateAndAssignLabel')

// Mutation functions
async function createLabel(
  organizationId: string,
  label: Omit<Label, 'id' | 'createdAt' | 'updatedAt'>,
  userId: string,
) {
  const path = ndrPathStore.labels({ organizationId })
  const ref = collection(db, path)
  const newLabelRef = doc(ref)

  const { dryRun, withoutCalculate, ...labelData } = label

  const newLabel: Label = {
    ...labelData,
    id: newLabelRef.id,
    createdAt: new Date(),
    updatedAt: new Date(),
    userId,
  }

  if (!dryRun) {
    await setDoc(newLabelRef, newLabel)
  }

  if (!withoutCalculate) {
    const result = await submitLabelFunc({
      label: { ...labelData, id: newLabelRef.id } as UserLabel,
      organizationId,
      dryRun: Boolean(dryRun),
    })

    if (result?.data?.error) {
      throw new Error(result?.data?.error)
    }

    return { newLabel, founded: result?.data?.length }
  }

  return { newLabel }
}

async function updateLabel(organizationId: string, label: Label, userId: string) {
  const labelRef = doc(db, ndrPathStore.oneLabel({ organizationId, labelId: label.id }))
  const { withoutCalculate, ...labelData } = label

  const updatedLabel = {
    ...labelData,
    userId,
    updatedAt: new Date(),
  }

  await updateDoc(labelRef, updatedLabel)
  if (!withoutCalculate) {
    const result = await submitLabelFunc({
      label: labelData,
      organizationId,
    })

    if (result?.data?.error) {
      throw new Error(result?.data?.error)
    }
  }
  return updatedLabel
}

async function assignLabel(
  organizationId: string,
  { entityId, labelId }: { entityId: string; labelId: string },
  userId: string,
) {
  const assignmentsRef = collection(db, ndrPathStore.labelAssignments({ organizationId }))
  const newAssignmentRef = doc(assignmentsRef)

  // Get existing assignments for this entity
  const existingAssignmentsQuery = query(assignmentsRef, where('entityId', '==', entityId))
  const existingAssignmentsSnapshot = await getDocs(existingAssignmentsQuery)

  let labelIds: string[] = []

  if (!existingAssignmentsSnapshot.empty) {
    // If there's an existing assignment, update it
    const existingAssignment = existingAssignmentsSnapshot.docs[0]
    const existingData = existingAssignment.data()
    labelIds = [...(existingData.labelIds || []), labelId]

    await updateDoc(existingAssignment.ref, {
      labelIds,
      updatedAt: new Date(),
    })

    return {
      entityId,
      labelIds,
      createdAt: existingData.createdAt,
      labelId,
      userId,
    }
  } else {
    // Create new assignment
    const newAssignment: LabelAssignment = {
      entityId,
      labelIds: [labelId],
      createdAt: new Date(),
      userId,
    }

    await setDoc(newAssignmentRef, newAssignment)
    return newAssignment
  }
}

async function removeLabel(organizationId: string, { entityId, labelId }: { entityId: string; labelId: string }) {
  const assignmentsRef = collection(db, ndrPathStore.labelAssignments({ organizationId }))
  const q = query(assignmentsRef, where('entityId', '==', entityId))
  const snapshot = await getDocs(q)

  if (snapshot.empty) {
    throw new Error('Label assignment not found')
  }

  const assignmentDoc = snapshot.docs[0]
  const assignment = assignmentDoc.data()
  const labelIds = assignment.labelIds || []

  // Remove the labelId from the array
  const updatedLabelIds = labelIds.filter((id: string) => id !== labelId)

  if (updatedLabelIds.length === 0) {
    // If no labels left, delete the assignment
    await deleteDoc(assignmentDoc.ref)
  } else {
    // Update the assignment with the new labelIds array
    await updateDoc(assignmentDoc.ref, {
      labelIds: updatedLabelIds,
      updatedAt: new Date(),
    })
  }

  return { entityId, labelId }
}

async function deleteLabel(organizationId: string, labelId: string) {
  const labelRef = doc(db, ndrPathStore.oneLabel({ organizationId, labelId }))
  await deleteDoc(labelRef)

  // Also delete all assignments for this label
  const assignmentsRef = collection(db, ndrPathStore.labelAssignments({ organizationId }))
  const q = query(assignmentsRef, where('labelIds', 'array-contains', labelId))
  const snapshot = await getDocs(q)

  const deletePromises = snapshot.docs.map(async (doc) => {
    const assignment = doc.data()
    const updatedLabelIds = assignment.labelIds.filter((id: string) => id !== labelId)

    if (updatedLabelIds.length === 0) {
      await deleteDoc(doc.ref)
    } else {
      await updateDoc(doc.ref, {
        labelIds: updatedLabelIds,
        updatedAt: new Date(),
      })
    }
  })

  await Promise.all(deletePromises)
  return labelId
}

// Updated hooks
export function useGetLabels(organizationId: string | undefined) {
  return useQuery({
    queryKey: ndrQueryKeyStore.labels(organizationId),
    queryFn: () => {
      if (!organizationId) throw new Error('Organization ID is required')
      return getLabels(organizationId)
    },
  })
}

export interface SecurityGroup {
  id: string
  region: string
  rules: Rule[]
  name: string
  account_id: string
  description: string
}

export interface Rule {
  created_at?: string
  remote_type?: string
  description?: string
  to_port?: number
  remote?: string
  protocol?: string
  id?: string
  direction?: string
  from_port?: number
}

export function useGetSecurityGroups(organizationId: string | undefined) {
  return useQuery({
    queryKey: ndrQueryKeyStore.securityGroups({ organizationId }),
    queryFn: () => {
      if (!organizationId) throw new Error('Organization ID is required')
      return getSecurityGroups(organizationId)
    },
  })
}

export function useGetEntityLabels(organizationId: string | undefined, entityId: string) {
  return useQuery({
    queryKey: ndrQueryKeyStore.entityLabels({ organizationId, entityId }),
    queryFn: () => {
      if (!organizationId || !entityId) throw new Error('Organization ID and Entity ID are required')
      return getEntityLabels(organizationId, entityId)
    },
  })
}

export function useGetLabelHostnames(organizationId: string | undefined) {
  return useQuery({
    queryKey: ndrQueryKeyStore.labelHostnames({ organizationId }),
    queryFn: () => {
      if (!organizationId) throw new Error('Organization ID is required')
      return getLabelHostnames(organizationId)
    },
  })
}

export function useCreateLabel(organizationId: string | undefined, userId: string) {
  return useMutation({
    mutationFn: (label: Omit<Label, 'id' | 'createdAt' | 'updatedAt'> & { withoutCalculate?: boolean }) => {
      if (!organizationId) throw new Error('Organization ID is required')
      return createLabel(organizationId, label, userId)
    },
    onMutate: async (newLabel) => {
      if (!newLabel.dryRun) {
        // Cancel any outgoing refetches
        await queryClient.cancelQueries({
          queryKey: ndrQueryKeyStore.labels(organizationId),
        })

        // Snapshot the previous value
        const previousLabels = queryClient.getQueryData(ndrQueryKeyStore.labels(organizationId))

        // Optimistically update the cache with a temporary ID
        queryClient.setQueryData(ndrQueryKeyStore.labels(organizationId), (oldData: any) => {
          if (!oldData) return oldData
          const tempLabel = {
            ...newLabel,
            id: 'temp-id', // Temporary ID that will be replaced
            createdAt: new Date(),
            updatedAt: new Date(),
          }
          return [...oldData, tempLabel]
        })

        // Return a context object with the snapshotted value
        return { previousLabels }
      }
    },
    onSuccess: (data) => {
      // Update the cache with the actual Firestore ID
      queryClient.setQueryData(ndrQueryKeyStore.labels(organizationId), (oldData: any) => {
        if (!oldData) return oldData
        return oldData.map((label: Label) => (label.id === 'temp-id' ? data.newLabel : label))
      })
    },
    onError: (_, __, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousLabels) {
        queryClient.setQueryData(ndrQueryKeyStore.labels(organizationId), context.previousLabels)
      }
    },
  })
}

export function useUpdateLabel(organizationId: string | undefined, userId: string | undefined, entityId?: string) {
  return useMutation({
    mutationFn: (label: Label) => {
      if (!organizationId) throw new Error('Organization ID is required')
      return updateLabel(organizationId, label, userId)
    },
    onMutate: async (updatedLabel) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({
        queryKey: ndrQueryKeyStore.labels(organizationId),
      })
      await queryClient.cancelQueries({
        queryKey: ndrQueryKeyStore.entities({ organizationId }),
      })

      // Snapshot the previous values
      const previousLabels = queryClient.getQueryData(ndrQueryKeyStore.labels(organizationId))
      const previousEntities = queryClient.getQueryData(ndrQueryKeyStore.entities({ organizationId }))

      // Optimistically update labels cache
      queryClient.setQueryData(ndrQueryKeyStore.labels(organizationId), (oldData: any) => {
        if (!oldData) return oldData
        return oldData.map((label: Label) => (label.id === updatedLabel.id ? updatedLabel : label))
      })

      // Optimistically update entities cache
      queryClient.setQueryData(ndrQueryKeyStore.entities({ organizationId }), (oldData: any) => {
        if (!oldData) return oldData
        return oldData.map((entity: any) => {
          if (entity.labels) {
            return {
              ...entity,
              labels: entity.labels.map((label: Label) => (label.id === updatedLabel.id ? updatedLabel : label)),
            }
          }
          return entity
        })
      })

      // Optimistically update entities cache
      queryClient.setQueryData(ndrQueryKeyStore.labels(organizationId), (oldData: any) => {
        if (!oldData) return oldData
        const updated = oldData.map((label: Label) => (label.id === updatedLabel.id ? updatedLabel : label))
        return updated
      })

      if (entityId) {
        const previousEntityLabels = queryClient.getQueryData(
          ndrQueryKeyStore.entityLabels({ organizationId, entityId }),
        )

        queryClient.setQueryData(ndrQueryKeyStore.entityLabels({ organizationId, entityId }), (oldData: any) => {
          if (!oldData) return oldData
          const updated = oldData.labels.map((label: Label) => (label.id === updatedLabel.id ? updatedLabel : label))
          return {
            ...oldData,
            labels: updated,
          }
        })
        return { previousLabels, previousEntities, previousEntityLabels }
      }

      return { previousLabels, previousEntities }
    },
    onError: (_, __, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousLabels) {
        queryClient.setQueryData(ndrQueryKeyStore.labels(organizationId), context.previousLabels)
      }
      if (context?.previousEntities) {
        queryClient.setQueryData(ndrQueryKeyStore.entities({ organizationId }), context.previousEntities)
      }
    },
  })
}

export function useAssignLabel(organizationId: string | undefined, userId: string) {
  return useMutation({
    mutationFn: ({ entityId, labelId, labelData }: { entityId: string; labelId: string; labelData: Label }) => {
      if (!organizationId) throw new Error('Organization ID is required')
      return assignLabel(organizationId, { entityId, labelId }, userId)
    },
    onMutate: async ({ entityId, labelId, labelData }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({
        queryKey: ndrQueryKeyStore.entityLabels({ organizationId, entityId }),
      })
      await queryClient.cancelQueries({
        queryKey: ndrQueryKeyStore.entities({ organizationId }),
      })

      // Snapshot the previous values
      const previousLabels = queryClient.getQueryData(ndrQueryKeyStore.entityLabels({ organizationId, entityId }))
      const previousEntities = queryClient.getQueryData(ndrQueryKeyStore.entities({ organizationId }))

      // Optimistically update entity labels cache
      queryClient.setQueryData(ndrQueryKeyStore.entityLabels({ organizationId, entityId }), (oldData: any) => {
        if (!oldData) return oldData
        return {
          ...oldData,
          labels: [...(oldData.labels || []), labelData],
        }
      })

      // Optimistically update entities cache
      queryClient.setQueryData(ndrQueryKeyStore.entities({ organizationId }), (oldData: any) => {
        if (!oldData) return oldData
        return oldData.map((entity: any) => {
          if (entity.id === entityId) {
            return {
              ...entity,
              labels: [...(entity.labels || []), labelData],
            }
          }
          return entity
        })
      })

      // Return a context object with the snapshotted values
      return { previousLabels, previousEntities }
    },
    onError: (_, __, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousLabels) {
        queryClient.setQueryData(ndrQueryKeyStore.entityLabels({ organizationId, entityId }), context.previousLabels)
      }
      if (context?.previousEntities) {
        queryClient.setQueryData(ndrQueryKeyStore.entities({ organizationId }), context.previousEntities)
      }
    },
  })
}

export function useRemoveLabel(organizationId: string | undefined) {
  return useMutation({
    mutationFn: ({ entityId, labelId, labelData }: { entityId: string; labelId: string; labelData: Label }) => {
      if (!organizationId) throw new Error('Organization ID is required')
      return removeLabel(organizationId, { entityId, labelId })
    },
    onMutate: async ({ entityId, labelId, labelData }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({
        queryKey: ndrQueryKeyStore.entities({ organizationId }),
      })

      // Snapshot the previous value
      const previousEntities = queryClient.getQueryData(ndrQueryKeyStore.entities({ organizationId }))

      // Optimistically update the cache
      queryClient.setQueryData(ndrQueryKeyStore.entities({ organizationId }), (oldData: any) => {
        if (!oldData) return oldData

        return oldData.map((entity: any) => {
          if (entity.id === entityId) {
            return {
              ...entity,
              labels: (entity.labels || []).filter((label: Label) => label.id !== labelId),
            }
          }
          return entity
        })
      })

      // Return a context object with the snapshotted value
      return { previousEntities }
    },
    onError: (_, __, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousEntities) {
        queryClient.setQueryData(ndrQueryKeyStore.entities({ organizationId }), context.previousEntities)
      }
    },
    onSuccess: (_, { entityId }) => {
      if (organizationId) {
        queryClient.invalidateQueries({
          queryKey: ndrQueryKeyStore.entityLabels({ organizationId, entityId }),
        })
      }
    },
  })
}

export function useDeleteLabel(organizationId: string | undefined) {
  return useMutation({
    mutationFn: (labelId: string) => {
      if (!organizationId) throw new Error('Organization ID is required')
      return deleteLabel(organizationId, labelId)
    },
    onSuccess: (labelId) => {
      invalidateLabelQueries(organizationId, labelId)
    },
  })
}

// Handler function for query invalidation
function invalidateLabelQueries(organizationId: string | undefined, labelId?: string) {
  if (!organizationId) return

  // Invalidate labels query
  queryClient.invalidateQueries({
    queryKey: ndrQueryKeyStore.labels(organizationId),
  })

  // Invalidate label hostnames query if labelId is provided
  if (labelId) {
    queryClient.invalidateQueries({
      queryKey: ndrQueryKeyStore.labelHostnames({ organizationId, labelId }),
    })
  }
}
