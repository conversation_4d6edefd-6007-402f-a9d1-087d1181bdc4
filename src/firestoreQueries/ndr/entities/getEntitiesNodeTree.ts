import { EntityTypeWithId } from '@/firestoreQueries/ndr/entities/customEntitiesTypes'

import { SuperNode } from './nodeTreeUtils/generalTypes'
import { findProcessorByEntityType } from './entities-processors'

export const serializeEntitiesToNodes = (
  entities: EntityTypeWithId[],
): SuperNode[] => {
  // soo we're here now
  const entityProcessor = findProcessorByEntityType(entities[0].type)
  return entityProcessor.serializeEntitiesToNodes(entities)
}


export const nodeTypeToNameMap: Record<string, string> = {
  account: 'Account',
  region: 'Region',
  vpc: 'VPC',
  instance: 'Instance',
  site: 'Site',
  'internet-parent': 'Internet',
  'internet-child': 'Internet',
  'so-group': 'Group',
  activeDirectoryDomain: 'Domain',
  activeDirectoryOU: 'Organizational Unit',
  activeDirectoryComputer: 'Computer',
  availabilityZone: 'Availability Zone',
  tenantNode: 'Tenant',
  domainNode: 'Domain',
}
