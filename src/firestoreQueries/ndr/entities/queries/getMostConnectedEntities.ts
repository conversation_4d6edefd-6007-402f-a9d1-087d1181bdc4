import { EntityTypeWithId } from '@/firestoreQueries/ndr/entities/customEntitiesTypes'
import { TrafficPatternWithId } from '../../trafficPatterns/customTrafficPatternsTypes'

export function createMostConnectedEntities(
  entities: Array<EntityTypeWithId>,
  trafficPatterns: Array<TrafficPatternWithId>,
) {
  const countMap = new Map<string, number>()
  trafficPatterns.forEach((trafficPattern) => {
    if (trafficPattern.direction === 'egress') return
    const curr = countMap.get(trafficPattern.entityId) || 0
    countMap.set(trafficPattern.entityId, curr + 1)
  })

  return Array.from(countMap.entries())
    .sort((a, b) => b[1] - a[1])
    .slice(0, 10)
    .map(([entityId, count]) => {
      const entity = entities.find((entity) => entity.id === entityId)
      if (!entity) return null
      return {
        entity: entity,
        count,
      }
    })
    .filter((entity) => entity !== null)
}
