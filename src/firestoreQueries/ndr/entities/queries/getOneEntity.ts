import { db } from '@/configs/firebase'
import { ndrPathStore } from '@/firestoreQueries/utils/pathStore'
import * as Sentry from '@sentry/react'
import { collection, getDocs, query, where } from 'firebase/firestore'
import { EntityTypeWithId } from '../customEntitiesTypes'

type Args = {
    organizationId: string | undefined
    entityId: string
}

export default async function getOneEntity({ organizationId, entityId }: Args) {
    try {
        if (!organizationId) throw new Error('Organization ID is required')

        const path = ndrPathStore.entities({ organizationId })
        const ref = collection(db, path)
        const q = query(ref, where('id', '==', entityId))
        const snap = await getDocs(q)

        const entity: EntityTypeWithId = snap.docs[0].data() as EntityTypeWithId

        return entity
    } catch (error) {
        Sentry.captureException(error)
        throw error
    }
}
