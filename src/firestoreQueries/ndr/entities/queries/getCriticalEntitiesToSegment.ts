import { db } from '@/configs/firebase'
import { EntityTypeWithId } from '@/firestoreQueries/ndr/entities/customEntitiesTypes'
import { ndrPathStore } from '@/firestoreQueries/utils/pathStore'
import { CloudEntity } from '@globalTypes/ndrBenchmarkTypes/entityTypes'
import { captureException } from '@sentry/react'
import { collection, getDocs, query } from 'firebase/firestore'
// eslint-disable-next-line import/named
import { ChartDataShape } from 'reaviz'
import getAllTrafficPatterns from '../../trafficPatterns/queries/getAllTrafficPatterns'
import { SentinelOneInfo } from '@globalTypes/ndrBenchmarkTypes/sentinelOneTypes'
import { TrafficPatternWithId } from '../../trafficPatterns/customTrafficPatternsTypes'

const NO_OF_CRITICAL_ENTITIES = 5

async function getServerEntities(organizationId: string) {
  const ref = collection(db, ndrPathStore.entities({ organizationId }))
  const q = query(ref)
  const snap = await getDocs(q)
  const entities: Array<EntityTypeWithId> = []
  snap.forEach((doc) => {
    const data = doc.data() as CloudEntity
    const entity = { ...data, id: doc.id } as EntityTypeWithId
    entities.push(entity)
  })

  return entities
}

export default async function getCriticalEntitiesToSegment(organizationId: string | undefined) {
  try {
    if (!organizationId) throw new Error('organizationId is undefined')
    const [entities, trafficPatterns] = await Promise.all([
      getServerEntities(organizationId),
      getAllTrafficPatterns({ organizationId }),
    ])
    return createCriticalEntitiesToSegment(entities, trafficPatterns)
  } catch (error) {
    captureException(error)
    throw error
  }
}

export function createCriticalEntitiesToSegment(
  entities: Array<EntityTypeWithId>,
  trafficPatterns: Array<TrafficPatternWithId>,
) {
  const criticalPorts = [
    22, 1422, 1433, 1434, 3306, 33060, 5432, 5433, 1521, 1526, 27017, 27018, 27019, 28017, 6379, 6380, 9042, 9160, 7000,
    7001, 5984, 6000, 50000, 50001, 2483, 2484, 3036, 3050, 1717, 4711, 19812, 8086, 8192, 8193, 9200, 9300, 7474, 7687,
  ]
  const map: Map<string, number> = new Map()
  entities.forEach((entity) => {
    map.set((entity.info as SentinelOneInfo).computerName, 0)
  })
  trafficPatterns.forEach((trafficPattern) => {
    const entity = entities.find((entity) => entity.id === trafficPattern.entityId)
    if (!entity) return
    if (criticalPorts.includes(Number(trafficPattern.port))) {
      const curr = map.get((entity.info as SentinelOneInfo).computerName) ?? 0
      map.set((entity.info as SentinelOneInfo).computerName, curr + 1)
    }
  })

  const barChartData: ChartDataShape[] = Array.from(map.entries())
    .sort((a, b) => b[1] - a[1])
    .slice(0, NO_OF_CRITICAL_ENTITIES)
    .map(([key, data]) => {
      return {
        key,
        data,
      }
    })
    .filter((entity) => entity !== undefined)
    .filter((e) => e.data !== 0)

  return barChartData
}
