// import { db } from '@/configs/firebase'
// import { ndrPathStore } from '@/firestoreQueries/utils/pathStore'
// import { CloudEntity } from '@globalTypes/ndrBenchmarkTypes/entityTypes'
// import { SentinelOneInfo } from '@globalTypes/ndrBenchmarkTypes/sentinelOneTypes'
// import { captureException } from '@sentry/react'
// import { collection, getDocs, query, where } from 'firebase/firestore'

export type Instance = 'aws' | 'gcp' | 'activeDirectory' | 'sentinelOne' | 'exsi' | 'crowdstrike'

// export default async function getInstancesCount(organizationId: string | undefined) {
//   try {
//     if (!organizationId) throw new Error('organizationId is undefined')
//     const path = ndrPathStore.entities({ organizationId })
//     const ref = collection(db, path)
//     const q = query(ref, where('type', '==', 'sentinelOne_agent'))
//     const snap = await getDocs(q)
//     const entities: Array<CloudEntity> = []
//     snap.forEach((doc) => {
//       entities.push(doc.data() as CloudEntity)
//     })

//     return createInstancesCount(entities)
//   } catch (error) {
//     captureException(error)
//     throw error
//   }
// }

// export function createInstancesCount(entities: Array<CloudEntity>) {
//   const countMap: { [K in Instance | string]: number } = {
//     gcp: 0,
//     aws: 0,
//     activeDir: 0,
//     crowdstrike: 0,
//     sentinelOne: 0,
//     esxi: 0,
//   }

//   entities.forEach((entity) => {
//     const info = entity.info as SentinelOneInfo
//     if (info.cloudProviders['AWS']) countMap.aws++
//     if (info.cloudProviders['GCP']) countMap.gcp++
//     if (info.cloudProviders['ESXI']) countMap.esxi++
//     if (info.activeDirectory.computerDistinguishedName) {
//       countMap.activeDir++
//     }
//     countMap.sentinelOne++
//   })

//   return Object.entries(countMap).map(([cloud, count]) => ({ cloud, count })) as Array<{
//     cloud: Instance
//     count: number
//   }>
// }
