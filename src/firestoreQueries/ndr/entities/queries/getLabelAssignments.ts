import { ndrPathStore } from '@/firestoreQueries/utils/pathStore'
import { collection, getDocs, query, where } from 'firebase/firestore'
import { db } from '@/configs/firebase'

type Args = {
  organizationId: string,
  entityIds: string[]
}

async function getLabelAssignments({ organizationId, entityIds }: Args) {
  const path = ndrPathStore.labelAssignments({ organizationId })
  const ref = collection(db, path)
  const snapshot = await getDocs(query(ref, where('entityId', 'in', entityIds)))
  
  const resultMap = new Map()
  snapshot.docs.forEach((doc) => {
    const data = doc.data()
    if (data.entityId && data.labelIds) {
      resultMap.set(data.entityId, data.labelIds)
    }
  })
  
  return resultMap
}

export default getLabelAssignments;
