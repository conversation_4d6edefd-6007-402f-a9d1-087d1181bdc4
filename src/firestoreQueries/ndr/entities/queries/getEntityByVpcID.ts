import { db } from '@/configs/firebase'
import { ndrPathStore } from '@/firestoreQueries/utils/pathStore'
import * as Sentry from '@sentry/react'
import { collection, getDocs, query, where } from 'firebase/firestore'
import { EntityTypeWithId } from '../customEntitiesTypes'

type Args = {
  organizationId: string | undefined
  vpcID: string
}

export default async function getEntityByVpcID({ organizationId, vpcID }: Args) {
  try {
    if (!organizationId) throw new Error('Organization ID is required')

    const path = ndrPathStore.entities({ organizationId })
    const ref = collection(db, path)
    const q = query(ref, where('info.vpcId', '==', vpcID))
    const querySnapshot = await getDocs(q)

    const entities = querySnapshot.docs.map(doc => doc.data());

    if (entities.length === 0) {
      return [];
    }

    return entities;
  } catch (error) {
    Sentry.captureException(error)
    throw error
  }
}
