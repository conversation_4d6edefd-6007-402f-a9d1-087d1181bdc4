import { db } from '@/configs/firebase'
import { ndrPathStore } from '@/firestoreQueries/utils/pathStore'
import * as Sentry from '@sentry/react'
import { collection, documentId, getCountFromServer, getDocs, query, where } from 'firebase/firestore'
import { EntityTypeWithId } from '../customEntitiesTypes'

type Args = {
  organizationId: string
  labelIds?: string[]
  enabled?: boolean
}

type WithEntityId = Args & {
  entityId: string
  entityIds?: never
}

type WithEntityIds = Args & {
  entityIds: string[]
  entityId?: never
}

async function getEntities({ organizationId, isActiveOnly }: Args & { isActiveOnly: boolean }) {
  const allEntitiesPath = ndrPathStore.entities({ organizationId })
  const allEntitiesRef = collection(db, allEntitiesPath)
  const entitiesQuery = isActiveOnly ? query(allEntitiesRef, where('isActive', '==', true)) : allEntitiesRef
  const allEntitiesSnap = await getDocs(entitiesQuery)
  const allEntities = allEntitiesSnap.docs.map(
    (doc) =>
      ({
        id: doc.id,
        ...doc.data(),
      }) as EntityTypeWithId,
  )

  return allEntities
}

async function getEntitiesWithAllData({ organizationId, isActiveOnly = false }: Args & { isActiveOnly?: boolean }) {
  try {
    // const results = await Promise.allSettled([
    //   getEntities({ organizationId, isActiveOnly }),
    //   // getRiskScore({ organizationId }),
    //   // getLabelAssignments({ organizationId }),
    //   // getLabel({ organizationId }),
    // ])

    // const { entities } = await getListOfEntities({
    //   table: 'entities',
    //   queryRules: {
    //     condition: 'and',
    //     rules: [],
    //   },
    //   organizationId,
    // })

    // const [entitiesResult
    //   // , riskScoresResult, labelAssignmentsResult, labelsResult
    // ] = results
    //
    // if (entitiesResult.status === 'rejected') {
    //   throw entitiesResult.reason
    // }

    // const entities = entitiesResult.value
    // const riskDataMap = riskScoresResult.status === 'fulfilled' ? riskScoresResult.value : {}
    // const labelAssignmentsMap = labelAssignmentsResult.status === 'fulfilled' ? labelAssignmentsResult.value : new Map()
    // const labelsMap = labelsResult.status === 'fulfilled' ? labelsResult.value : new Map()
    const entities = []
    // Combine all data
    const response = entities.map((entity) => {
      const labelIds = labelAssignmentsMap.get(entity.id) || []
      const labels = labelIds
        .map((labelId: string) => {
          const label = labelsMap.get(labelId)
          return label
        })
        .filter(Boolean)

      return {
        ...entity,
        riskScore: riskDataMap[entity.id]?.riskScore?.value ?? 0,
        firewallRulesCount: riskDataMap[entity.id]?.firewallRulesCount ?? 0,
        labels,
        riskScore: 0,
        firewallRulesCount: 0,
        labels: [],
      }
    })

    return response
  } catch (error) {
    Sentry.captureException(error)
    throw error
  }
}

export default async function getNdrEntities(args: Args) {
  return getEntitiesWithAllData(args)
}

export const getNumberOfEntities = async (organizationId: string): Promise<number> => {
  const allEntitiesPath = ndrPathStore.entities({ organizationId })

  const ref = collection(db, allEntitiesPath)
  const snapshot = await getCountFromServer(ref)

  return snapshot.data().count
}

function chunkArray<T>(arr: T[], size: number): T[][] {
  const result: T[][] = []
  for (let i = 0; i < arr.length; i += size) {
    result.push(arr.slice(i, i + size))
  }
  return result
}

export const fetchEntities = async (organizationId: string, entityIds?: string[]): Promise<EntityTypeWithId[]> => {
  if (!entityIds || entityIds.length === 0) {
    return []
  }

  const MAX_BATCH_SIZE = 10
  const ref = collection(db, ndrPathStore.entities({ organizationId }))

  const chunks = chunkArray(entityIds, MAX_BATCH_SIZE)

  const results = await Promise.all(
    chunks.map(async (chunk) => {
      const q = query(ref, where(documentId(), 'in', chunk))
      const snapshot = await getDocs(q)
      return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }))
    }),
  )

  return results.flat() as EntityTypeWithId[]
}
