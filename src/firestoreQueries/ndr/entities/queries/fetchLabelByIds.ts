import { ndrPathStore } from '@/firestoreQueries/utils/pathStore'
import { collection, documentId, getDocs, query, where } from 'firebase/firestore'
import { db } from '@/configs/firebase'

type Args = {
  organizationId: string
  labelIds: string[]
}

async function fetchLabelByIds({ organizationId, labelIds }: Args) {
  const path = ndrPathStore.labels({ organizationId })
  const ref = collection(db, path)
  const labelsQuery = query(ref, where(documentId(), 'in', labelIds))

  const snapshot = await getDocs(labelsQuery)

  const labelsMap = new Map()
  snapshot.docs.forEach((doc) => {
    labelsMap.set(doc.id, {
      id: doc.id,
      ...doc.data(),
    })
  })

  return labelsMap
}

export default fetchLabelByIds
