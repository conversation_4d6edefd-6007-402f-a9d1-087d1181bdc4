import { ndrPathStore } from '@/firestoreQueries/utils/pathStore'
import { collection, getDocs, query, limit } from 'firebase/firestore'
import { db } from '@/configs/firebase'

type Args = {
  organizationId: string
}

async function getFirstEntity({ organizationId }: Args) {
  if(!organizationId) return;
  
  const allEntitiesPath = ndrPathStore.entities({ organizationId })
  const allEntitiesRef = collection(db, allEntitiesPath)
  
  const q = query(allEntitiesRef, limit(1))
  const querySnapshot = await getDocs(q)
  
  const firstDoc = querySnapshot.docs[0]
  
  return firstDoc ? { id: firstDoc.id, ...firstDoc.data() } : null
}

export default getFirstEntity;
