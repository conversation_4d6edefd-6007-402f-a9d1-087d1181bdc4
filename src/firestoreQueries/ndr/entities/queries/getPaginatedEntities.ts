import { db } from '@/configs/firebase'
import { ndrPathStore } from '@/firestoreQueries/utils/pathStore'
import * as Sentry from '@sentry/react'
import { collection, getDocs, query, where } from 'firebase/firestore'
import { EntityTypeWithId } from '../customEntitiesTypes'
import { getFunctions, httpsCallable } from 'firebase/functions'
import { RiskData } from './getRiskDataMap'
import { SQLQueryBuilder } from '@/services/SQLQueryBuilder'
import { fetchEntities } from './getEntities'
import { Label } from '@/firestoreQueries/ndr/labels/hooks/useLabels'

type Args = {
  organizationId: string | undefined
  pageSize: number
  lastDocId?: string
  searchQuery: string
  offset?: number
  showInactiveItems?: boolean
  labels: Label[]
  riskDataMap: RiskData
}

type PaginatedResult = {
  entities: EntityTypeWithId[]
  hasMore: boolean
  lastDocumentId?: string
  total: number
}

// Function to get sorted and filtered entity IDs
async function getSortedAndFilteredEntityIds({
  organizationId,
  searchQuery,
  showInactiveItems,
  offset,
  pageSize,
  riskData,
}: {
  organizationId: string
  searchQuery: string
  showInactiveItems: boolean
  offset: number
  pageSize: number
  riskData: RiskData
}): Promise<{ searchMatchedIds: string[]; total: number }> {
  // Get sorted entity IDs based on risk scores
  const sortedEntityIds = riskData?.map((e) => e[0]) as string[]

  // If no search query, return sorted IDs
  if (!searchQuery) {
    return { searchMatchedIds: sortedEntityIds, total: sortedEntityIds.length }
  }

  // If we need to filter by search query
  const functions = getFunctions()
  const queryBigQuery = httpsCallable<any, any>(functions, 'query0')

  const queryBuilder = new SQLQueryBuilder({
    organizationId,
    table: 'entities',
  })
    .select(['id'])
    .from('entities')
    .as('t')
    .where(`REGEXP_CONTAINS(TO_JSON_STRING(t), r'(?i)${searchQuery}')`)
    .where(!showInactiveItems ? 'JSON_VALUE(TO_JSON_STRING(t), "$.is_active") = "true"' : 'TRUE')

  const { query, params } = queryBuilder.build()

  const response = await queryBigQuery({
    query,
    params,
    organizationId,
    pageSize,
    offset,
  })

  const searchMatchedIds = response.data.rows.map((row: { id: string }) => row.id)
  return {
    searchMatchedIds: sortedEntityIds.filter((id) => searchMatchedIds.includes(id)),
    total: response.data.pagination.total,
  }
}

export async function getPaginatedEntities({
  organizationId,
  pageSize,
  searchQuery,
  offset = 0,
  showInactiveItems = false,
  labels,
  riskDataMap,
}: Args): Promise<PaginatedResult> {
  if (!organizationId) throw new Error('Organization ID is required')
  try {
    const { searchMatchedIds, total } = await getSortedAndFilteredEntityIds({
      organizationId,
      searchQuery,
      showInactiveItems,
      offset,
      pageSize,
      riskData: riskDataMap!,
    })

    // Apply pagination
    const paginatedIds = searchMatchedIds.slice(offset, offset + pageSize + 1)
    const hasMore = paginatedIds.length > pageSize
    const finalIds = paginatedIds

    // If no entities found, return empty result
    if (finalIds.length === 0) {
      return {
        entities: [],
        hasMore: false,
        lastDocumentId: undefined,
        total: 0,
      }
    }

    // Fetch the actual entities using fetchEntities, it fetches only entity for current page
    const entities = await fetchEntities(organizationId, finalIds)
    // Get label assignments for the fetched entities
    const assignmentsPath = ndrPathStore.labelAssignments({ organizationId })
    const assignmentsRef = collection(db, assignmentsPath)
    const assignmentsQuery = query(
      assignmentsRef,
      where(
        'entityId',
        'in',
        entities.map((e) => e.id),
      ),
    )
    const assignmentsSnap = await getDocs(assignmentsQuery)

    // Create a map of entity IDs to their label assignments
    const labelAssignmentsMap = new Map<string, string[]>()
    assignmentsSnap.docs.forEach((doc) => {
      const data = doc.data()
      if (data.entityId && data.labelIds) {
        labelAssignmentsMap.set(data.entityId, data.labelIds)
      }
    })
    // Create a map from the passed labels array for efficient lookup
    const labelsMap = new Map()
    labels.forEach((label) => {
      labelsMap.set(label.id, label)
    })

    // Combine all data
    const entitiesWithMetadata = entities.map((entity) => {
      const labelIds = labelAssignmentsMap.get(entity.id) || []
      const entityLabels = labelIds.reduce((acc, next) => {
        const label = labelsMap.get(next)
        if (label) {
          acc.push(label)
        }
        return acc
      }, [] as Label[])

      const currentRiskData = riskDataMap?.find((e) => e[0] === entity.id)![1]
      return {
        ...entity,
        riskScore: currentRiskData?.riskScore ?? 0,
        potentialRiskScore: currentRiskData?.potentialRiskScore ?? 0,
        firewallRulesCount: currentRiskData?.firewallRulesCount ?? 0,
        labels: entityLabels,
      }
    })

    const metadataLength = entitiesWithMetadata.length
    const lastDocumentId = hasMore && metadataLength > 0 ? entitiesWithMetadata[metadataLength - 1].id : undefined

    return {
      entities: entitiesWithMetadata,
      hasMore,
      lastDocumentId,
      total,
    }
  } catch (error) {
    console.log({ error })
    Sentry.captureException(error)
    throw error
  }
}
