import { db } from '@/configs/firebase'
import { ndrPathStore } from '@/firestoreQueries/utils/pathStore'
import { collection, getDocs, orderBy, query } from 'firebase/firestore'

export type RiskDataMap = Map<
  string,
  {
    riskScore: number
    potentialRiskScore: number
    firewallRulesCount: number
  }
>

export type RiskData = [string, {
  riskScore: number;
  potentialRiskScore: number;
  firewallRulesCount: number;
}][] | undefined


export async function getRiskDataMap(organizationId: string) {
  const riskDataMap: RiskDataMap = new Map()
  const riskPath = ndrPathStore.entitiesMetrics({ organizationId })
  const riskRef = collection(db, riskPath)
  const riskQuery = query(riskRef, orderBy('riskScore.value', 'desc'))
  const riskSnap = await getDocs(riskQuery)

  riskSnap.docs.forEach((doc) => {
    const data = doc.data()
    riskDataMap.set(doc.id, {
      riskScore: data.riskScore?.value ?? 0,
      potentialRiskScore: data.potentialRiskScore?.value ?? 0,
      firewallRulesCount: data.firewallRulesCount ?? 0,
    })
  })

  return [...riskDataMap]
}
