import { useOrganization } from '@clerk/clerk-react'
import { useAppConfig } from '@/zustandStores/useAppConfig'
import { useQuery } from '@tanstack/react-query'
import { EntitiesIntegrationsTypes, getListOfEntities } from '@/services/BigQueryService'
import { ndrQueryKeyStore } from '@/configs/reactQueryKeyStore'
import { QueryRules } from '@/trafficPatternsQuery/evaluateQueryRules'

const defaultArgs: { queryRules: QueryRules } = {
  queryRules: { condition: 'and' as const, rules: [] },
}

function useBigQueryEntities({ queryRules }: { queryRules?: QueryRules } = defaultArgs) {
  const organizationId = useOrganization().organization?.id as string
  const { integrationType } = useAppConfig()

  return useQuery({
    queryKey: [...ndrQueryKeyStore.entities({ organizationId }), queryRules],
    queryFn: () =>
      getListOfEntities({
        organizationId,
        integrationType: integrationType as EntitiesIntegrationsTypes,
        queryRules,
      }),
    enabled: <PERSON><PERSON><PERSON>(integrationType),
  })
}

export default useBigQueryEntities
