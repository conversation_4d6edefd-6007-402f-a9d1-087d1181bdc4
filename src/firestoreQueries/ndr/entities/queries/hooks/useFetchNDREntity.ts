import useGetEntities from '@/firestoreQueries/ndr/entities/hooks/useGetEntities'
import useFetchMetricsForEntities from '@/firestoreQueries/ndr/entities/queries/hooks/useFetchMetricsForEntities'
import useFetchLabelAssigmentForEntities from '@/firestoreQueries/ndr/entities/queries/hooks/useFetchLabelAssigmentForEntities'
import { useMemo } from 'react'
import useFetchLabelsByIds from '@/firestoreQueries/ndr/entities/queries/hooks/useFetchLabelForEntities'
import _ from 'lodash'

type Args = {
  organizationId: string
  entityId: string
}

function useFetchNDREntity({ organizationId, entityId }: Args) {
  const entitiesQuery = useGetEntities({
    entityIds: [entityId],
    organizationId,
    enabled: Boolean(entityId),
    leanedEntities: true,
  })

  const riskScore = useFetchMetricsForEntities({ organizationId, entityIds: [entityId] })

  const assignedLabels = useFetchLabelAssigmentForEntities({
    organizationId,
    entityIds: [entityId],
    enabled: Boolean(entityId),
  })

  const computedLabelIds = useMemo(() => {
    if (!assignedLabels || !assignedLabels.data) return []

    return [entityId].map((id) => assignedLabels.data?.get(id) ?? []).flat()
  }, [entityId, assignedLabels])

  const allLabels = useFetchLabelsByIds({
    organizationId,
    labelIds: computedLabelIds,
    enabled: computedLabelIds.length > 0,
  })

  const computedData = useMemo(() => {
    const result = new Map()

    if (!allLabels.data || !entitiesQuery.data || !riskScore.data || !assignedLabels.data) return result

    entitiesQuery.data.forEach((entity: any) => {
      const score = _.get(riskScore.data, `${entity.id}.riskScore.value`, 0)
      const potentialScore = _.get(riskScore.data, `${entity.id}.potentialRiskScore.value`, 0)
      
      const firewallRulesCount = _.get(riskScore.data, `${entity.id}.firewallRulesCount`, 0)
      const labels = assignedLabels.data
        .get(entity.id)
        .map((labelId: string) => allLabels.data.get(labelId))
        .filter(Boolean)

      result.set(entity.id, {
        ...entity,
        riskScore: score,
        potentialScore,
        firewallRulesCount: firewallRulesCount,
        labels,
      })
    })

    return result
  }, [computedLabelIds, allLabels, entitiesQuery, riskScore])

  const isSomeLoading = _.map([entitiesQuery, assignedLabels, allLabels], 'isLoading')
  const isLoading = isSomeLoading.filter(Boolean).length > 0

  return useMemo(
    () => ({
      isLoading,
      data: computedData.get(entityId),
      isDataReady: computedData.size > 0 && !isLoading,
    }),
    [isLoading, computedData],
  )
}

export default useFetchNDREntity
