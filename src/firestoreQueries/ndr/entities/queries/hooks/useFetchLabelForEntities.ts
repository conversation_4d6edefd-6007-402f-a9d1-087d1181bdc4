import { useQuery } from '@tanstack/react-query'
import fetchLabelByIds from '@/firestoreQueries/ndr/entities/queries/fetchLabelByIds'

type Args = {
  labelIds: string[]
  organizationId: string
  enabled: boolean
}

function useFetchLabelsByIds({ labelIds, organizationId, enabled = true }: Args) {
  return useQuery({
    queryKey: ['useFetchLabelForEntities', organizationId, ...labelIds],
    queryFn: () => fetchLabelByIds({ organizationId, labelIds }),
    enabled,
  })
}

export default useFetchLabelsByIds
