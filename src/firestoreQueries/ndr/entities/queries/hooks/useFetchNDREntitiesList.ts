import useGetEntities from '@/firestoreQueries/ndr/entities/hooks/useGetEntities'
import useFetchMetricsForEntities from '@/firestoreQueries/ndr/entities/queries/hooks/useFetchMetricsForEntities'
import useFetchLabelAssigmentForEntities from '@/firestoreQueries/ndr/entities/queries/hooks/useFetchLabelAssigmentForEntities'
import { useMemo } from 'react'
import useFetchLabelsByIds from '@/firestoreQueries/ndr/entities/queries/hooks/useFetchLabelForEntities'
import _ from 'lodash'

type Args = {
  organizationId: string
  entityIds: string[]
  enabled?: boolean
}

function useFetchNDREntitiesList({ organizationId, entityIds, enabled = true }: Args) {
  const entitiesQuery = useGetEntities({
    entityIds,
    organizationId,
    enabled: enabled && entityIds.length > 0,
    leanedEntities: true,
  })

  const riskScore = useFetchMetricsForEntities({ organizationId, entityIds, enabled })

  const assignedLabels = useFetchLabelAssigmentForEntities({
    organizationId,
    entityIds,
    enabled: enabled && entityIds.length > 0,
  })

  const computedLabelIds = useMemo(() => {
    if (!assignedLabels || !assignedLabels.data) return []

    return entityIds.map((id) => assignedLabels.data?.get(id) ?? []).flat()
  }, [entityIds, assignedLabels])

  const allLabels = useFetchLabelsByIds({
    organizationId,
    labelIds: computedLabelIds,
    enabled: enabled && assignedLabels.isFetched,
  })

  const computedData = useMemo(() => {
    const result = new Map()

    if (!allLabels.data || !entitiesQuery.data || !riskScore.data || !assignedLabels.data) return result

    entitiesQuery.data.forEach((entity: any) => {
      const score = _.get(riskScore.data, `${entity.id}.value`, 0)
      const firewallRulesCount = _.get(riskScore.data, `${entity.id}.firewallRulesCount`, 0)
      const labels = assignedLabels.data
        .get(entity.id)
        .map((labelId: string) => allLabels.data.get(labelId))
        .filter(Boolean)

      result.set(entity.id, {
        ...entity,
        riskScore: score,
        firewallRulesCount: firewallRulesCount,
        labels,
      })
    })

    return result
  }, [computedLabelIds, allLabels, entitiesQuery, riskScore])

  const isSomeLoading = _.map([entitiesQuery, assignedLabels, allLabels], 'isLoading')
  const isLoading = isSomeLoading.filter(Boolean).length > 0

  return useMemo(
    () => ({
      isLoading,
      data: Array.from(computedData.values()),
      isDataReady: computedData.size > 0 && !isLoading,
    }),
    [isLoading, computedData],
  )
}

export default useFetchNDREntitiesList
