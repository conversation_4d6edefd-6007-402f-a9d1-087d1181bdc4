import { useQuery } from '@tanstack/react-query'
import getLabelAssignments from '@/firestoreQueries/ndr/entities/queries/getLabelAssignments'

type Args = {
  entityIds: string[]
  enabled?: boolean
  organizationId: string
}

function useFetchLabelAssigmentForEntities({ entityIds, organizationId, enabled }: Args) {
  return useQuery({
    queryKey: ['getLabelAssignments', organizationId, ...entityIds],
    queryFn: () => getLabelAssignments({ organizationId, entityIds }),
    enabled,
  })
}

export default useFetchLabelAssigmentForEntities
