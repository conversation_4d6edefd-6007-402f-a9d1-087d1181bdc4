import { useQuery } from '@tanstack/react-query'
import fetchMetricsForEntities from '@/firestoreQueries/ndr/entities/queries/fetchRiskScoreForEntities'

type Args = {
  organizationId: string,
  entityIds: string[],
  enabled?: boolean,
}

function useFetchMetricsForEntities({ organizationId, entityIds, enabled = true }: Args) {
  return useQuery({
    queryKey: ['risk-score', organizationId, ...entityIds],
    queryFn: () => fetchMetricsForEntities({ organizationId, entityIds }),
    enabled,
  })
}

export default useFetchMetricsForEntities;
