import { ndrPathStore } from '@/firestoreQueries/utils/pathStore'
import { collection, getDocs, query, where } from 'firebase/firestore'
import { db } from '@/configs/firebase'

type Args = {
  organizationId: string,
  entityIds: string[]
}

type EntityMetrics = {
  firewallRulesCount: number
  riskScore: number
}

async function fetchMetricsForEntities({ organizationId, entityIds }: Args): Promise<Record<string, EntityMetrics>> {
  const path = ndrPathStore.entitiesMetrics({ organizationId })
  const ref = collection(db, path)
  const q = query(ref, where('__name__', 'in', entityIds))
  const snapshot = await getDocs(q)
  
  return Object.fromEntries(snapshot.docs.map((doc) => [doc.id, doc.data() as EntityMetrics]))
}

export default fetchMetricsForEntities
