import { EntityTypeWithId } from '@/firestoreQueries/ndr/entities/customEntitiesTypes'

import { SuperNode } from '../nodeTreeUtils/generalTypes'
import { INTERNET_NODE } from '../nodeTreeUtils/constants'
import {
  CrowdstrikeProcessor,
  CrowdstrikeActiveDirectoryProcessor,
  CrowdstrikeGenericProcessor,
} from '../nodeTreeUtils/processors'
import { EntityProcessor, SerializeEntitiesToNodes } from './types'

const CROWDSTRIKE_PROCESSORS = [
  new CrowdstrikeProcessor(),
  new CrowdstrikeActiveDirectoryProcessor(),
  new CrowdstrikeGenericProcessor(),
]

const serializeEntitiesToNodes: SerializeEntitiesToNodes = (entities) => {
  const crowdstrikeNodes = proccessCrowdstrikeEntities(entities)

  return [...crowdstrikeNodes, INTERNET_NODE]
}

function proccessCrowdstrikeEntities(entities: EntityTypeWithId[]): SuperNode[] {
  const processedEntityIds = new Set<string>()
  let remainingEntities = [...entities]
  const processedNodes: SuperNode[] = []

  CROWDSTRIKE_PROCESSORS.forEach((processor) => {
    const filtered = processor.filter(remainingEntities)
    filtered.forEach((entity) => processedEntityIds.add(entity.id))

    const processedEntities = processor.process(filtered)
    processedNodes.push(...processedEntities)

    remainingEntities = remainingEntities.filter((entity) => !processedEntityIds.has(entity.id))
  })

  if (remainingEntities.length > 0) {
    console.warn('Found unprocessed Crowdstrike entities:')
  }

  const { isValid, missingEntities } = validateAllEntitiesAreInTree(processedNodes, entities)

  if (!isValid) {
    console.log('Not all entities are in the tree', missingEntities)
  }

  return processedNodes
}

type ValidatorResult = {
  isValid: boolean
  missingEntities: string[]
}

const validateAllEntitiesAreInTree = (nodes: SuperNode[], entities: EntityTypeWithId[]): ValidatorResult => {
  try {
    const entitiesIds = getEntityIdsFromNodes(nodes, new Set())
    const entitiesIdsSet = new Set(entitiesIds)
    const entitiesIdsSetFromEntities = new Set(entities.map((entity) => entity.id))

    return {
      isValid: entitiesIdsSet.size === entitiesIdsSetFromEntities.size,
      missingEntities: Array.from(entitiesIdsSetFromEntities).filter((id) => !entitiesIdsSet.has(id)),
    }
  } catch (error) {
    console.log('validator failed', error)

    return {
      isValid: false,
      missingEntities: [],
    }
  }
}

const getEntityIdsFromNodes = (nodes: SuperNode[], entitiesIds: Set<string>): Set<string> => {
  nodes.forEach((node) => {
    if (node.type === 'machine') {
      entitiesIds.add(node.id)
    } else {
      node.data.children?.forEach((child) => getEntityIdsFromNodes([child], entitiesIds))
    }
  })

  return entitiesIds
}

export const crowdstrikeEntityProcessor: EntityProcessor = {
  serializeEntitiesToNodes,
}
