import { EntityTypeWithId } from "../customEntitiesTypes"
import { INTERNET_NODE } from "../nodeTreeUtils/constants"
import { SuperNode } from "../nodeTreeUtils/generalTypes"
import { SentinelOneAWSProcessor } from "../nodeTreeUtils/processors/sentinelOne/sentinalOneAWSProcessor"
import {
  SentinelOneActiveDirectoryProcessor,
  SentinelOneCloudProcessor,
  SentinelOneStandaloneProcessor
} from "../nodeTreeUtils/processors"
import { EntityProcessor, SerializeEntitiesToNodes } from "./types"

const SENTINEL_ONE_PROCESSORS = [
  new SentinelOneCloudProcessor(),
  new SentinelOneActiveDirectoryProcessor(),
  new SentinelOneStandaloneProcessor(),
]

const serializeEntitiesToNodes: SerializeEntitiesToNodes = (
  entities,
) => {
  const awsNodes = new SentinelOneAWSProcessor().process(entities)
  const sentinelOneNodes = proccessSentinelOneEntities(entities)
  return [...awsNodes, ...sentinelOneNodes, INTERNET_NODE]
}

function proccessSentinelOneEntities(
  entities: Array<EntityTypeWithId>,
): Array<SuperNode> {
  let filteredEntities = [...entities]
  const superNodes: SuperNode[] = []

  const requiredProcessors = SENTINEL_ONE_PROCESSORS.filter(processor =>
    processor.filter(entities).length > 0
  )

  requiredProcessors.forEach((processor) => {
    const entitiesToProcess = processor.filter(filteredEntities)
    const filteredEntitiesSet = new Set(entitiesToProcess.map((entity) => entity.id))

    filteredEntities = filteredEntities.filter((entity) => !filteredEntitiesSet.has(entity.id))
    const nodes = processor.process(entitiesToProcess)
    superNodes.push(...nodes)
  })

  return superNodes
}

export const sentinelOneEntityProcessor: EntityProcessor = {
  serializeEntitiesToNodes,
}
