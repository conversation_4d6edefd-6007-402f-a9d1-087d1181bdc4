import { BaseEntityType } from '@globalTypes/ndrBenchmarkTypes/entityTypes'
import { awsEntityProcessor } from './awsEntityProcessor'
import { crowdstrikeEntityProcessor } from './crowdstrikeEntityProcessor'
import { sentinelOneEntityProcessor } from './sentinelOneEntityProcessor'
import { msDefenderEntityProcessor } from './msDefenderEntityProcessor'
import { EntityProcessor } from './types'

export const findProcessorByEntityType = (type: BaseEntityType): EntityProcessor => {
  if (type =='aws_endpoint') {
    return awsEntityProcessor
  }
  if (type =='crowdstrike_agent') {
    return crowdstrikeEntityProcessor
  }
  if (type =='sentinelOne_agent') {
    return sentinelOneEntityProcessor
  }
  if (type =='ms_defender_endpoint') {
    return msDefenderEntityProcessor
  }
  throw new Error(`No processor found for entity type: ${type}`)
}

export { 
  awsEntityProcessor,
  crowdstrikeEntityProcessor,
  sentinelOneEntityProcessor,
  msDefenderEntityProcessor
}