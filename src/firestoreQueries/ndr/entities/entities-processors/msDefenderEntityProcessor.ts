import { EntityTypeWithId } from '@/firestoreQueries/ndr/entities/customEntitiesTypes'

import { SuperNode } from '../nodeTreeUtils/generalTypes'
import { INTERNET_NODE } from '../nodeTreeUtils/constants'
import {
    MSDefenderCloudProcessor,
    OUProcessor,
    DefenderTenantProcessor
} from '../nodeTreeUtils/processors'
import { EntityProcessor, SerializeEntitiesToNodes } from './types'

const MSDEFENDER_PROCESSORS = [
  new MSDefenderCloudProcessor(),
  new OUProcessor(),
]

const serializeEntitiesToNodes: SerializeEntitiesToNodes = (entities) => {
  const defenderNodes = proccessDefenderEntities(entities)

  return [...defenderNodes, INTERNET_NODE]
}

function proccessDefenderEntities(entities: EntityTypeWithId[]): SuperNode[] {
  const processedEntityIds = new Set<string>()
  let remainingEntities = [...entities]
  const processedNodes: SuperNode[] = []
  
  const search = window.location.search;
  const params = new URLSearchParams(search);
  
  if(params.get('operator') && Array.from(params.keys()).length > 2){
    MSDEFENDER_PROCESSORS.push(new DefenderTenantProcessor())
  }
  
  MSDEFENDER_PROCESSORS.forEach((processor) => {
    const filtered = processor.filter(remainingEntities)
    filtered.forEach((entity) => processedEntityIds.add(entity.id))

    const processedEntities = processor.process(filtered)
    processedNodes.push(...processedEntities)

    remainingEntities = remainingEntities.filter((entity) => !processedEntityIds.has(entity.id))
  })

  if (remainingEntities.length > 0) {
    console.warn('Found unprocessed MS Defender entities')
  }

  const { isValid, missingEntities } = validateAllEntitiesAreInTree(processedNodes, entities)

  return processedNodes
}

type ValidatorResult = {
  isValid: boolean
  missingEntities: string[]
}

const validateAllEntitiesAreInTree = (nodes: SuperNode[], entities: EntityTypeWithId[]): ValidatorResult => {
  try {
    const entitiesIds = getEntityIdsFromNodes(nodes, new Set())
    const entitiesIdsSet = new Set(entitiesIds)
    const entitiesIdsSetFromEntities = new Set(entities.map((entity) => entity.id))

    return {
      isValid: entitiesIdsSet.size === entitiesIdsSetFromEntities.size,
      missingEntities: Array.from(entitiesIdsSetFromEntities).filter((id) => !entitiesIdsSet.has(id)),
    }
  } catch (error) {
    console.log('validator failed', error)

    return {
      isValid: false,
      missingEntities: [],
    }
  }
}

const getEntityIdsFromNodes = (nodes: SuperNode[], entitiesIds: Set<string>): Set<string> => {
  nodes.forEach((node) => {
    if (node.type === 'machine') {
      entitiesIds.add(node.id)
    } else {
      node.data.children?.forEach((child) => getEntityIdsFromNodes([child], entitiesIds))
    }
  })

  return entitiesIds
}

export const msDefenderEntityProcessor: EntityProcessor = {
  serializeEntitiesToNodes,
}
