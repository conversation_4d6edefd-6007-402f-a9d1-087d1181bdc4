import { INTERNET_NODE } from '../nodeTreeUtils/constants'
import { AWSProcessor } from '../nodeTreeUtils/processors/aws/awsProcessor'
import { EntityProcessor, SerializeEntitiesToNodes } from './types'

import generateAWSServiceChildNodes from '@/firestoreQueries/ndr/entities/nodeTreeUtils/processors/aws/generateAWSServiceChildNodes'

const serializeEntitiesToNodes: SerializeEntitiesToNodes = (entities) => {
  const awsProcessor = new AWSProcessor()
  const awsNodes = awsProcessor.process(entities)
  const staticChildren = generateAWSServiceChildNodes('AWS_Services')
  
  INTERNET_NODE.data.children.push(...staticChildren)
  
  return [...awsNodes, INTERNET_NODE]
}

export const awsEntityProcessor: EntityProcessor = {
  serializeEntitiesToNodes,
}
