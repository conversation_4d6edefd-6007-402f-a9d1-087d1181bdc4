import { ReferenceEntity } from '@globalTypes/ndrBenchmarkTypes/entityTypes'
import { TrafficPatternWithId } from '../../trafficPatterns/customTrafficPatternsTypes'
import { EdgeMap, TrafficPatternMap } from './generalTypes'

export function getTrafficPatternMaps(trafficPatterns: Array<TrafficPatternWithId>): {
  targetsMap: TrafficPatternMap
  sourcesMap: TrafficPatternMap
} {
  //convet map and set to standalone type
  // map is nodeID -> target/source nodeID -> set of ports
  const targetsMap: TrafficPatternMap = new Map()
  const sourcesMap: TrafficPatternMap = new Map()

  trafficPatterns.forEach((tp) => {
    const isWithInternet = tp.remoteEntity.type === 'internet'
    if (isWithInternet) {
      const remoteEntityId = 'internet'
      const localEntityId = tp.entityId
      const allTargets = targetsMap.get(remoteEntityId) ?? (new Map() as EdgeMap)
      const targetPorts = allTargets.get(localEntityId)?.ports ?? new Set<string>()
      const newPorts = new Set([...targetPorts, tp.port])
      const targetTrafficPatterns =
        allTargets.get(localEntityId)?.trafficPatterns ?? new Map<string, TrafficPatternWithId>()
      targetTrafficPatterns.set(tp.id, tp)
      allTargets.set(localEntityId, { ports: newPorts, trafficPatterns: targetTrafficPatterns })
      targetsMap.set(remoteEntityId, allTargets)

      const allSources = sourcesMap.get(localEntityId) ?? (new Map() as EdgeMap)
      const sourcePorts = allSources.get(remoteEntityId)?.ports ?? new Set<string>()
      const newSourcePorts = new Set([...sourcePorts, tp.port])
      const sourceTrafficPatterns =
        allSources.get(remoteEntityId)?.trafficPatterns ?? new Map<string, TrafficPatternWithId>()
      sourceTrafficPatterns.set(tp.id, tp)
      allSources.set(remoteEntityId, { ports: newSourcePorts, trafficPatterns: sourceTrafficPatterns })
      sourcesMap.set(localEntityId, allSources)
      return
    }

    const remoteEntityId = (tp.remoteEntity as ReferenceEntity).id
    const localEntityId = tp.entityId
    if (tp.direction === 'ingress') {
      const allTargets = targetsMap.get(remoteEntityId) ?? (new Map() as EdgeMap)
      const targetPorts = allTargets.get(localEntityId)?.ports ?? new Set<string>()
      const newPorts = new Set([...targetPorts, tp.port])
      const targetTrafficPatterns =
        allTargets.get(localEntityId)?.trafficPatterns ?? new Map<string, TrafficPatternWithId>()
      targetTrafficPatterns.set(tp.id, tp)
      allTargets.set(localEntityId, { ports: newPorts, trafficPatterns: targetTrafficPatterns })
      targetsMap.set(remoteEntityId, allTargets)

      const allSources = sourcesMap.get(localEntityId) ?? (new Map() as EdgeMap)
      const sourcePorts = allSources.get(remoteEntityId)?.ports ?? new Set<string>()
      const newSourcePorts = new Set([...sourcePorts, tp.port])
      const sourceTrafficPatterns =
        allSources.get(remoteEntityId)?.trafficPatterns ?? new Map<string, TrafficPatternWithId>()
      sourceTrafficPatterns.set(tp.id, tp)
      allSources.set(remoteEntityId, { ports: newSourcePorts, trafficPatterns: sourceTrafficPatterns })
      sourcesMap.set(localEntityId, allSources)

      return
    }
    if (tp.direction === 'egress') {
      const allTargets = targetsMap.get(localEntityId) ?? (new Map() as EdgeMap)
      const targetPorts = allTargets.get(remoteEntityId)?.ports ?? new Set<string>()
      const newPorts = new Set([...targetPorts, tp.port])
      const targetTrafficPatterns =
        allTargets.get(remoteEntityId)?.trafficPatterns ?? new Map<string, TrafficPatternWithId>()
      targetTrafficPatterns.set(tp.id, tp)
      allTargets.set(remoteEntityId, { ports: newPorts, trafficPatterns: targetTrafficPatterns })
      targetsMap.set(localEntityId, allTargets)

      const allSources = sourcesMap.get(remoteEntityId) ?? (new Map() as EdgeMap)
      const sourcePorts = allSources.get(localEntityId)?.ports ?? new Set<string>()
      const newSourcePorts = new Set([...sourcePorts, tp.port])
      const sourceTrafficPatterns =
        allSources.get(localEntityId)?.trafficPatterns ?? new Map<string, TrafficPatternWithId>()
      sourceTrafficPatterns.set(tp.id, tp)
      allSources.set(localEntityId, { ports: newSourcePorts, trafficPatterns: sourceTrafficPatterns })
      sourcesMap.set(remoteEntityId, allSources)

      return
    }
  })
  return { targetsMap, sourcesMap }
}

export function formatTrafficPatternMap(map: TrafficPatternMap, nodeId: string) {
  const edges = map.get(nodeId) ?? (new Map() as EdgeMap)

  const edgesArray = Array.from(edges.entries()).map(([id, { ports, trafficPatterns }]) => {
    return {
      nodeId: id,
      ports: Array.from(ports),
      trafficPatterns: Array.from(trafficPatterns.values()),
    }
  })

  return edgesArray
}
