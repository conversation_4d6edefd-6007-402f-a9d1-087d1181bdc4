import { IssueTypeWithId } from '@/firestoreQueries/issues/customIssuesTypes'
import { AWSEndpointInfo, AWSSubtypes } from '@globalTypes/ndrBenchmarkTypes/entityTypes'
import { MachineType, SentinelOneInfo } from '@globalTypes/ndrBenchmarkTypes/sentinelOneTypes'
import { Node } from 'reactflow'
import { UniqueTrafficPattern } from '../../trafficPatterns/transformTrafficPatterns'

export type EdgeMapData = { ports: Set<string>; trafficPatterns: Map<string, UniqueTrafficPattern> }
//map of nodeId -> set of the ports they connect with
export type EdgeMap = Map<string, EdgeMapData>

//map of nodeId -> map of target/source NODEID -> set of ports
export type TrafficPatternMap = Map<string, EdgeMap>

export type NodeSubtype = 'sentinelOne'
  | 'aws'
  | 'gcp'
  | 'internet'
  | 'activeDirectory'
  | 'esxi'
  | 'unrecognized'
  | 'crowdstrike'
  | 'msdefender'
  | AWSSubtypes

export type NodeEdgesArr = Array<{ nodeId: string; ports: Array<string>; trafficPatterns: Array<UniqueTrafficPattern> }>
export type NodeTreeCloudProvider =
  | 'aws'
  | 'gcp'
  | 'azure'
  | 'sentinelOne'
  | 'crowdStrike'
  | 'internet'
  | 'unrecognized'
  | 'crowdstrike'
  | 'msdefender'
export type SuperNode = Node<NodeData>
export type NodeData = {
  parentId?: string
  label: string
  children?: Array<SuperNode>
  edgesTargets?: NodeEdgesArr
  edgesSources?: NodeEdgesArr
  subType: NodeSubtype
  cloudProvider: NodeTreeCloudProvider
  instanceType?:
  | 'windows'
  | 'linux'
  | 'macOS'
  | 'esxi-windows_legacy'
  | 'esxi-windows'
  | 'esxi-macos'
  | 'esxi-linux'
  | MachineType
  treeType: 'aws' | 'sentinelOne' | 'gcp' | 'internet' | 'activeDirectory' | 'unrecognized' | 'crowdstrike' | 'aws_service' | 'msdefender'
  info?: SentinelOneInfo | AWSEndpointInfo
  issues?: Array<IssueTypeWithId>
  riskScore: number
  labels: string[]
}
