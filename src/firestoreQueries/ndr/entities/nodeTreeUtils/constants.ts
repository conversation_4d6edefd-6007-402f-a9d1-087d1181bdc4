import { RawTypes } from "@/views/NDR/PortflowPage/utils/helpers"
import { SuperNode } from "./generalTypes"

export const position = { x: 0, y: 0 }

export const INTERNET_NODE = {
  type: RawTypes.INTERNET_PARENT,
  id: 'internet-parent',
  data: {
    label: 'internet',
    subType: 'internet',
    treeType: 'internet',
    children: [
      {
        id: 'internet',
        type: RawTypes.INTERNET_CHILD,
        data: {
          label: 'internet',
          subType: 'internet',
          treeType: 'internet',
          edgesTargets: [],
          edgesSources: [],
          cloudProvider: 'internet',
        },
        position,
      },
    ],
    cloudProvider: 'internet',
  },
  position,
} as SuperNode
