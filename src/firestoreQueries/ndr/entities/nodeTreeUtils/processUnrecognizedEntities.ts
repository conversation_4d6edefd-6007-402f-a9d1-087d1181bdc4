import { TrafficPatternWithId } from '../../trafficPatterns/customTrafficPatternsTypes'
import { position } from './constants'
import { SuperNode, TrafficPatternMap } from './generalTypes'
import { formatTrafficPatternMap } from './trafficPatternsMap'
import { EntityTypeWithId } from '@/firestoreQueries/ndr/entities/customEntitiesTypes'

class UnrecognizedEntityProcessor {
  private isValidIpAddress(ip: string): boolean {
    const ipv4Regex = /^(\d{1,3}\.){3}\d{1,3}$/
    if (!ipv4Regex.test(ip)) return false
    const parts = ip.split('.')
    return parts.every((part) => parseInt(part, 10) >= 0 && parseInt(part, 10) <= 255)
  }

  private calculateSubnet(ip: string): string {
    const parts = ip.split('.')
    return `${parts[0]}.${parts[1]}.${parts[2]}.0/24`
  }

  filterFunction = (trafficPattern: TrafficPatternWithId): boolean => {
    return (
      trafficPattern.remoteEntity.type === 'unrecognized' && this.isValidIpAddress(trafficPattern.remoteEntity.name)
    )
  }

  filter(trafficPatterns: Array<TrafficPatternWithId>): Array<TrafficPatternWithId> {
    return trafficPatterns.filter(this.filterFunction)
  }

  process(
    trafficPatterns: Array<TrafficPatternWithId>,
    targetsMap: TrafficPatternMap,
    sourcesMap: TrafficPatternMap,
  ): Array<SuperNode> {
    const parentNode: SuperNode = {
      type: 'unrecognized-parent',
      id: 'unrecognized-parent',
      data: {
        label: 'Unrecognized',
        subType: 'unrecognized',
        treeType: 'unrecognized',
        children: [],
        cloudProvider: 'unrecognized',
      },
      position,
    }

    const subnetMap = new Map<string, SuperNode>()
    const entityMap = new Map<string, SuperNode>()

    trafficPatterns.forEach((trafficPattern) => {
      const entity = trafficPattern.remoteEntity
      const ip = entity.name

      if (!entityMap.has(ip)) {
        const subnet = this.calculateSubnet(ip)
        const targets = formatTrafficPatternMap(targetsMap, ip)
        const sources = formatTrafficPatternMap(sourcesMap, ip)

        const childNode: SuperNode = {
          id: ip,
          type: 'unrecognized-child',
          data: {
            cloudProvider: 'unrecognized',
            label: ip,
            subType: 'unrecognized',
            treeType: 'unrecognized',
            edgesTargets: targets,
            edgesSources: sources,
            parentId: subnet,
          },
          position,
        }

        entityMap.set(ip, childNode)

        if (!subnetMap.has(subnet)) {
          subnetMap.set(subnet, {
            id: subnet,
            type: 'unrecognized-subnet',
            data: {
              label: subnet,
              subType: 'unrecognized',
              treeType: 'unrecognized',
              children: [],
              cloudProvider: 'unrecognized',
              parentId: parentNode.id,
            },
            position,
          })
        }

        subnetMap.get(subnet)!.data.children!.push(childNode)
      } else {
        // If the IP already exists, we might want to update or merge some information
        // For example, we could merge the targets and sources
        const existingNode = entityMap.get(ip)!
        existingNode.data.edgesTargets = {
          ...existingNode.data.edgesTargets,
          ...formatTrafficPatternMap(targetsMap, ip),
        }
        existingNode.data.edgesSources = {
          ...existingNode.data.edgesSources,
          ...formatTrafficPatternMap(sourcesMap, ip),
        }
      }
    })

    // Sort subnet groups by the number of children (unrecognized IPs) in descending order
    parentNode.data.children = Array.from(subnetMap.values()).sort((a, b) => {
      return (b.data.children?.length || 0) - (a.data.children?.length || 0)
    })

    return [parentNode]
  }
}

const calculateSubnet = (ip: string): string => {
  const parts = ip.split('.')
  return `${parts[0]}.${parts[1]}.${parts[2]}.0/24`
}
export default function processUnrecognizedEntities(
  unrecognizedNodeIds: string[]
): SuperNode[] {
  const subnetMap = new Map<string, SuperNode>();

  const parentNode: SuperNode = {
    type: 'unrecognized-parent',
    id: 'unrecognized-parent',
    data: {
      label: 'Unrecognized',
      subType: 'unrecognized',
      treeType: 'unrecognized',
      children: [],
      cloudProvider: 'unrecognized',
    },
    position,
  };

  for (const unrecognizedNodeId of unrecognizedNodeIds) {
    const subnet = calculateSubnet(unrecognizedNodeId)
    const childNode: SuperNode = {
      id: unrecognizedNodeId,
      type: 'unrecognized-child',
      data: {
        cloudProvider: 'unrecognized',
        label: unrecognizedNodeId,
        subType: 'unrecognized',
        treeType: 'unrecognized',
        edgesTargets: [],
        edgesSources: [],
        parentId: subnet,
      },
      position,
    }


    if (!subnetMap.has(subnet)) {
      subnetMap.set(subnet, {
        id: subnet,
        type: 'unrecognized-subnet',
        data: {
          label: subnet,
          subType: 'unrecognized',
          treeType: 'unrecognized',
          children: [],
          cloudProvider: 'unrecognized',
          parentId: parentNode.id,
        },
        position,
      });
    }

    subnetMap.get(subnet)!.data.children!.push(childNode)
  }

  const finalChildren: SuperNode[] = [];
  subnetMap.forEach((subnetNode) => {
    if (subnetNode.data.children && subnetNode.data.children.length === 1) {
      finalChildren.push(subnetNode.data.children[0]!); // Add the single child directly
    } else {
      finalChildren.push(subnetNode); // Keep the subnet node as parent
    }
  });


  parentNode.data.children = finalChildren.sort((a, b) => {
    const countA = a.data.children?.length || 0;
    const countB = b.data.children?.length || 0;
    return countB - countA; // Sort by children count desc
  });

  return [parentNode]
}
