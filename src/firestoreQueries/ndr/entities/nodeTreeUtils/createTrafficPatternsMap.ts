import { BaseEntity, CloudEntity } from '@globalTypes/ndrBenchmarkTypes/entityTypes'
import { TrafficPatternWithId } from '../../trafficPatterns/customTrafficPatternsTypes'
import {
  getTrafficPatternUniqueKey,
  getTransformedTrafficPatterns,
  UniqueTrafficPattern,
} from '../../trafficPatterns/transformTrafficPatterns'
import { EdgeMap, EdgeMapData, TrafficPatternMap } from './generalTypes'

export function getUniqueTrafficPatternMaps(
  trafficPatterns: Array<TrafficPatternWithId>,
  entities: Array<CloudEntity>,
): {
  targetsMap: TrafficPatternMap
  sourcesMap: TrafficPatternMap
} {
  //convet map and set to standalone type
  // map is nodeID -> target/source nodeID -> set of ports

  const transformed = getTransformedTrafficPatterns(trafficPatterns, entities)

  const targetsMap: TrafficPatternMap = new Map()
  const sourcesMap: TrafficPatternMap = new Map()

  transformed.forEach((tp) => {
    const srcEntityKey = getEntityKey(tp.srcEntity)
    const dstEntityKey = getEntityKey(tp.dstEntity)

    //these are all of the targets of the srcEntity
    //each target contains a set of ports through which the communication is happening and a map of relevant traffic patterns
    updateTrafficPatternMap(targetsMap, srcEntityKey, dstEntityKey, tp.port, tp)
    //these are all of the sources of the dstEntity
    //each source contains a set of ports through which the communication is happening and a map of relevant traffic patterns
    updateTrafficPatternMap(sourcesMap, dstEntityKey, srcEntityKey, tp.port, tp)
  })

  return { targetsMap, sourcesMap }
}

function updateTrafficPatternMap(
  map: TrafficPatternMap,
  entityKey: string,
  relatedEntityKey: string,
  port: string,
  trafficPattern: UniqueTrafficPattern,
) {
  const edges = map.get(entityKey) ?? (new Map() as EdgeMap)
  const { ports, trafficPatterns } =
    edges.get(relatedEntityKey) ??
    ({
      ports: new Set<string>(),
      trafficPatterns: new Map(),
    } as EdgeMapData)
  const newPorts = new Set([...ports, port])
  const trafficPatternKey = getTrafficPatternUniqueKey(trafficPattern)
  trafficPatterns.set(trafficPatternKey, trafficPattern)
  edges.set(relatedEntityKey, { ports: newPorts, trafficPatterns })
  map.set(entityKey, edges)
}

export function getEntityKey(node: BaseEntity) {
  switch (node.type) {
    case 'unrecognized':
      return node.name
    case 'internet':
    case 'internet_scanner':
      return 'internet'
    default:
      return (node as CloudEntity).id
  }
}

export function formatTrafficPatternMap(map: TrafficPatternMap, nodeId: string) {
  const edges = map.get(nodeId) ?? (new Map() as EdgeMap)

  const edgesArray = Array.from(edges.entries()).map(([id, { ports, trafficPatterns }]) => {
    return {
      nodeId: id,
      ports: Array.from(ports),
      trafficPatterns: Array.from(trafficPatterns.values()),
    }
  })

  return edgesArray
}
