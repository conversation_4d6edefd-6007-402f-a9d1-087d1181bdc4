import { EntityTypeWithId } from '../../../customEntitiesTypes'
import { position } from '../../constants'
import { NodeTreeCloudProvider, SuperNode } from '../../generalTypes'
import { EntityProcessor } from '../../processorInterface'
import { CrowdStrikeInfo } from '@globalTypes/ndrBenchmarkTypes/entities/crowdstrikeTypes'

export class CrowdstrikeProcessor implements EntityProcessor {
  private cloudProvider: NodeTreeCloudProvider = 'crowdstrike'

  filterFunction(entity: EntityTypeWithId): boolean {
    const subTypes = entity.subTypes ?? []

    if (!entity.info.serviceProviderAccountId) {
      return false
    }

    return (
      entity.type === 'crowdstrike_agent' &&
      subTypes.length > 0 &&
      !subTypes.includes('activeDirectory') &&
      (subTypes.includes('aws') || subTypes.includes('gcp'))
    )
  }

  filter(entites: EntityTypeWithId[]): EntityTypeWithId[] {
    return entites.filter(this.filterFunction)
  }

  process(entities: EntityTypeWithId[]): SuperNode[] {
    const sitesMap = new Map<string, SuperNode>()
    const groupsMap = new Map<string, SuperNode>()
    const instancesMap = new Map<string, SuperNode>()

    entities.forEach((entity) => this.classify(entity, sitesMap, groupsMap, instancesMap))

    instancesMap.forEach((instance) => {
      const groupId = (instance.data as { parentId: string }).parentId
      const groupNode = groupsMap.get(groupId)
      if (!groupNode) return
      if (!groupNode.data.children) groupNode.data.children = []
      groupNode.data.children.push(instance)
    })

    groupsMap.forEach((group) => {
      const siteId = (group.data as { parentId: string }).parentId
      const siteNode = sitesMap.get(siteId)
      if (!siteNode) return
      if (!siteNode.data.children) siteNode.data.children = []
      siteNode.data.children.push(group)
    })

    return Array.from(sitesMap.values())
  }

  classify(
    entity: EntityTypeWithId,
    sitesMap: Map<string, SuperNode>,
    groupsMap: Map<string, SuperNode>,
    instancesMap: Map<string, SuperNode>,
  ) {
    const info = entity.info as CrowdStrikeInfo
    const subType = entity.subTypes?.find((type) => type !== 'activeDirectory') || entity.subTypes?.[0]
    const instanceId = entity.id
    const { projectId, zoneId } = getProjectIdAndZoneId(entity)

    // Create a unique zone identifier by combining project and zone
    const uniqueZoneId = `${projectId}:${zoneId}`

    const availabilityZoneNode: SuperNode = {
      position,
      id: projectId,
      type: 'account',
      data: {
        label: projectId,
        children: [],
        subType: subType as any,
        treeType: subType as any,
        cloudProvider: this.cloudProvider,
        riskScore: entity.riskScore,
        labels: entity.labels,
      },
    }

    sitesMap.set(projectId, availabilityZoneNode)

    const regionNode: SuperNode = {
      position,
      id: uniqueZoneId,
      type: 'region',
      data: {
        label: zoneId,
        parentId: projectId,
        children: [],
        subType: 'crowdstrike',
        treeType: 'crowdstrike',
        cloudProvider: this.cloudProvider,
        riskScore: entity.riskScore,
        labels: entity.labels,
      },
    }

    groupsMap.set(uniqueZoneId, regionNode)

    const instanceNode: SuperNode = {
      position,
      id: instanceId,
      type: 'machine',
      data: {
        label: entity.name,
        parentId: uniqueZoneId,
        subType: 'crowdstrike',
        treeType: 'crowdstrike',
        instanceType: (info.osVersion as 'windows' | 'linux' | 'macOS') || 'unknown',
        info: info,
        issues: [],
        cloudProvider: this.cloudProvider,
        riskScore: entity.riskScore,
        labels: entity.labels,
      },
    }
    instancesMap.set(instanceId, instanceNode)
  }
}

interface ProjectAndZoneIds {
  projectId: string
  zoneId: string
}

export const getProjectIdAndZoneId = (entity: EntityTypeWithId): ProjectAndZoneIds => {
  const info = entity.info as CrowdStrikeInfo
  const subType = entity.subTypes?.find((type) => type !== 'activeDirectory') || entity.subTypes?.[0]

  if (subType === 'gcp') {
    const [_, projectId, __, zoneId] = info.zoneGroup?.split('/') ?? []

    return {
      projectId: projectId ?? '',
      zoneId: zoneId ?? '',
    }
  } else {
    // aws case
    const [continent, geoDirection, regionAndAvailabilityZone] = info.zoneGroup?.split('-') ?? []
    const [region, availabilityZone] = regionAndAvailabilityZone?.split('') ?? []

    return {
      projectId: info.serviceProviderAccountId ?? '',
      zoneId: `${continent}-${geoDirection}-${region}`,
    }
  }
}
