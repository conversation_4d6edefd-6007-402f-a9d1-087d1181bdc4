import { CrowdStrikeInfo } from '@globalTypes/ndrBenchmarkTypes/entities/crowdstrikeTypes'
import { EntityTypeWithId } from '../../../customEntitiesTypes'
import { position } from '../../constants'
import { NodeTreeCloudProvider, SuperNode } from '../../generalTypes'
import { EntityProcessor } from '../../processorInterface'

export class CrowdstrikeActiveDirectoryProcessor implements EntityProcessor {
  private cloudProvider: NodeTreeCloudProvider = 'crowdstrike'

  filterFunction(entity: EntityTypeWithId): boolean {
    const subTypes = entity.subTypes ?? []

    return (
      (entity.type === 'crowdstrike_agent' && subTypes.length > 0 && subTypes.includes('activeDirectory')) ||
      !entity.info.serviceProviderAccountId
    )
  }

  filter(entites: EntityTypeWithId[]): EntityTypeWithId[] {
    return entites.filter(this.filterFunction)
  }

  process(entities: EntityTypeWithId[]): SuperNode[] {
    const sitesMap = new Map<string, SuperNode>()
    const groupsMap = new Map<string, SuperNode>()
    const instancesMap = new Map<string, SuperNode>()

    entities.forEach((entity) => this.classify(entity, sitesMap, groupsMap, instancesMap))

    instancesMap.forEach((instance) => {
      const parentId = (instance.data as { parentId: string }).parentId
      const parentNode = groupsMap.get(parentId) || sitesMap.get(parentId)

      if (!parentNode) return
      if (!parentNode.data.children) parentNode.data.children = []
      parentNode.data.children.push(instance)
    })

    // Then build the OU hierarchy by attaching child OUs to parent OUs
    const processedGroups = new Set<string>()

    groupsMap.forEach((group, groupId) => {
      if (processedGroups.has(groupId)) return

      const parentId = (group.data as { parentId: string }).parentId
      const parentNode = groupsMap.get(parentId) || sitesMap.get(parentId)

      if (!parentNode) return
      if (!parentNode.data.children) parentNode.data.children = []
      parentNode.data.children.push(group)
      processedGroups.add(groupId)
    })

    return Array.from(sitesMap.values())
  }

  classify(
    entity: EntityTypeWithId,
    sitesMap: Map<string, SuperNode>,
    groupsMap: Map<string, SuperNode>,
    instancesMap: Map<string, SuperNode>,
  ) {
    const info = entity.info as CrowdStrikeInfo
    const machineDomain = info.machineDomain || 'no-machine-domain'
    const instanceId = entity.id

    const siteNode: SuperNode = {
      position,
      id: machineDomain,
      type: 'activeDirectoryDomain',
      data: {
        label: machineDomain,
        children: [],
        subType: 'crowdstrike',
        treeType: 'crowdstrike',
        cloudProvider: this.cloudProvider,
        riskScore: entity.riskScore,
        labels: entity.labels,
      },
    }

    sitesMap.set(machineDomain, siteNode)

    // this field has data like ['orgA', 'orgB'], which said orgB is a child of orgA
    const organizationalUnits = (info.ou as unknown as string[]) || []

    organizationalUnits.reverse().forEach((orgUnit, index) => {
      // Create a unique ID by combining domain and full OU path
      const ouPath = organizationalUnits.slice(0, index + 1).join('/')
      const uniqueGroupId = `${machineDomain}/${ouPath}`

      if (groupsMap.has(uniqueGroupId)) {
        return
      }

      const unitNode: SuperNode = {
        position,
        id: uniqueGroupId,
        type: 'activeDirectoryOU',
        data: {
          label: orgUnit,
          children: [],
          subType: 'crowdstrike',
          treeType: 'crowdstrike',
          parentId: index > 0 ? `${machineDomain}/${organizationalUnits.slice(0, index).join('/')}` : machineDomain,
          cloudProvider: this.cloudProvider,
          riskScore: entity.riskScore,
          labels: entity.labels,
        },
      }

      groupsMap.set(uniqueGroupId, unitNode)
    })

    const instanceNode = {
      position,
      id: instanceId,
      type: 'machine',
      data: {
        label: entity.name,
        parentId: organizationalUnits.length > 0 ? `${machineDomain}/${organizationalUnits.join('/')}` : machineDomain,
        subType: 'crowdstrike',
        treeType: 'crowdstrike',
        instanceType: (info.platformName as 'windows' | 'linux' | 'macOS') || 'unknown',
        info: info,
        issues: [],
        isWorkload: entity.isWorkload,
        cloudProvider: this.cloudProvider,
        riskScore: entity.riskScore,
        labels: entity.labels,
      } as unknown as SuperNode,
    }
    instancesMap.set(instanceId, instanceNode as unknown as SuperNode)
  }
}
