import { CrowdStrikeInfo } from '@globalTypes/ndrBenchmarkTypes/entities/crowdstrikeTypes'
import { EntityTypeWithId } from '../../../customEntitiesTypes'
import { position } from '../../constants'
import { NodeTreeCloudProvider, SuperNode } from '../../generalTypes'
import { EntityProcessor } from '../../processorInterface'

export class CrowdstrikeGenericProcessor implements EntityProcessor {
  private cloudProvider: NodeTreeCloudProvider = 'crowdstrike'

  filterFunction(entity: EntityTypeWithId): boolean {
    const subTypes = entity.subTypes ?? []

    const hasNoSubTypes = subTypes.length === 0
    const hasOnlyEsxiSubType = subTypes.length === 1 && subTypes[0] === 'esxi'

    return entity.type === 'crowdstrike_agent' && (hasNoSubTypes || hasOnlyEsxiSubType)
  }

  filter(entites: Array<EntityTypeWithId>): Array<EntityTypeWithId> {
    return entites.filter(this.filterFunction)
  }

  process(entities: Array<EntityTypeWithId>): Array<SuperNode> {
    const sitesMap = new Map<string, SuperNode>()
    const groupsMap = new Map<string, SuperNode>()
    const instancesMap = new Map<string, SuperNode>()

    entities.forEach((entity) => this.classify(entity, sitesMap, groupsMap, instancesMap))

    instancesMap.forEach((instance) => {
      const parentId = (instance.data as { parentId: string }).parentId
      // Try to find parent in groupsMap first, if not found try sitesMap
      const parentNode = groupsMap.get(parentId) || sitesMap.get(parentId)
      if (!parentNode) return
      if (!parentNode.data.children) parentNode.data.children = []
      parentNode.data.children.push(instance)
    })

    groupsMap.forEach((group) => {
      const parentId = (group.data as { parentId: string }).parentId
      const parentNode = sitesMap.get(parentId)
      if (!parentNode) return
      if (!parentNode.data.children) parentNode.data.children = []
      parentNode.data.children.push(group)
    })

    return Array.from(sitesMap.values())
  }

  classify(
    entity: EntityTypeWithId,
    sitesMap: Map<string, SuperNode>,
    groupsMap: Map<string, SuperNode>,
    instancesMap: Map<string, SuperNode>,
  ) {
    const info = entity.info as CrowdStrikeInfo
    const crowdstrikeCustomerId = info.cid || 'no-machine-domain'
    const instanceId = entity.id

    const siteNode: SuperNode = {
      position,
      id: crowdstrikeCustomerId,
      type: 'tenantNode',
      data: {
        label: crowdstrikeCustomerId,
        children: [],
        subType: 'crowdstrike',
        treeType: 'crowdstrike',
        parentId: crowdstrikeCustomerId,
        cloudProvider: this.cloudProvider,
        riskScore: entity.riskScore,
        labels: entity.labels,
      },
    }

    sitesMap.set(crowdstrikeCustomerId, siteNode)

    if (entity?.subTypes?.length == 1 && entity?.subTypes[0] == 'esxi') {
      const groupNode: SuperNode = {
        position,
        id: 'VMs',
        type: 'so-group',
        data: {
          label: 'VMs',
          children: [],
          subType: 'crowdstrike',
          treeType: 'crowdstrike',
          parentId: crowdstrikeCustomerId,
          cloudProvider: this.cloudProvider,
          riskScore: entity.riskScore,
          labels: entity.labels,
        },
      }

      groupsMap.set('VMs', groupNode)
    }

    const instanceNode: SuperNode = {
      position,
      id: instanceId,
      type: 'machine',
      data: {
        label: entity.name,
        parentId: entity?.subTypes?.length == 1 && entity?.subTypes[0] == 'esxi' ? 'VMs' : crowdstrikeCustomerId,
        subType: 'crowdstrike',
        treeType: 'crowdstrike',
        instanceType: (info.platformName as 'windows' | 'linux' | 'macOS') || 'unknown',
        info: info,
        issues: [],
        cloudProvider: this.cloudProvider,
        riskScore: entity.riskScore,
        labels: entity.labels,
      },
    }
    instancesMap.set(instanceId, instanceNode)
  }
}
