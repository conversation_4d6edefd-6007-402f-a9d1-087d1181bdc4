import { IssueTypeWithId } from '@/firestoreQueries/issues/customIssuesTypes'
import { CrowdStrikeInfo } from '@globalTypes/ndrBenchmarkTypes/entities/crowdstrikeTypes'
import { EntityTypeWithId } from '../../../customEntitiesTypes'
import { position } from '../../constants'
import { NodeSubtype, NodeTreeCloudProvider, SuperNode } from '../../generalTypes'
import { EntityProcessor } from '../../processorInterface'

export class OUProcessor implements EntityProcessor {
  filterFunction(entity: EntityTypeWithId): boolean {
    return entity.common.activeDirectoryDomain !== '' && entity.common?.activeDirectoryOu?.length > 0
  }

  filter(entites: EntityTypeWithId[]): EntityTypeWithId[] {
    return entites.filter(this.filterFunction)
  }

  process(entities: EntityTypeWithId[]): SuperNode[] {
    const sitesMap = new Map<string, SuperNode>()
    const groupsMap = new Map<string, SuperNode>()
    const instancesMap = new Map<string, SuperNode>()

    entities.forEach((entity) => this.classify(entity, sitesMap, groupsMap, instancesMap))

    instancesMap.forEach((instance) => {
      const parentId = (instance.data as { parentId: string }).parentId
      const parentNode = groupsMap.get(parentId) || sitesMap.get(parentId)

      if (!parentNode) return
      if (!parentNode.data.children) parentNode.data.children = []
      parentNode.data.children.push(instance)
    })

    // Then build the OU hierarchy by attaching child OUs to parent OUs
    const processedGroups = new Set<string>()

    groupsMap.forEach((group, groupId) => {
      if (processedGroups.has(groupId)) return

      const parentId = (group.data as { parentId: string }).parentId
      const parentNode = groupsMap.get(parentId) || sitesMap.get(parentId)

      if (!parentNode) return
      if (!parentNode.data.children) parentNode.data.children = []
      parentNode.data.children.push(group)
      processedGroups.add(groupId)
    })

    return Array.from(sitesMap.values())
  }

  classify(
    entity: EntityTypeWithId,
    sitesMap: Map<string, SuperNode>,
    groupsMap: Map<string, SuperNode>,
    instancesMap: Map<string, SuperNode>,
  ) {
    const info = entity.info as CrowdStrikeInfo
    const domainName = entity.common.activeDirectoryDomain || 'no-machine-domain'
    const instanceId = entity.id

    const siteNode: SuperNode = {
      position,
      id: domainName,
      type: 'activeDirectoryDomain',
      data: {
        label: domainName,
        children: [],
        subType: entity.integrationType as unknown as NodeSubtype,
        treeType: entity.integrationType as unknown as any,
        cloudProvider: entity.integrationType as unknown as NodeTreeCloudProvider,
        riskScore: entity.riskScore || 0,
        labels: entity.labels || [],
      },
    }

    sitesMap.set(domainName, siteNode)

    // this field has data like ['orgA', 'orgB'], which said orgB is a child of orgA
    const organizationalUnits = entity.common?.activeDirectoryOu || []

    organizationalUnits.reverse().forEach((orgUnit, index) => {
      // Create a unique ID by combining domain and full OU path
      const ouPath = organizationalUnits.slice(0, index + 1).join('/')
      const uniqueGroupId = `${domainName}/${ouPath}`

      if (groupsMap.has(uniqueGroupId)) {
        return
      }

      const unitNode: SuperNode = {
        position,
        id: uniqueGroupId,
        type: 'activeDirectoryOU',
        data: {
          label: orgUnit,
          children: [],
          subType: entity.integrationType as unknown as NodeSubtype,
          treeType: entity.integrationType as unknown as any,
          parentId: index > 0 ? `${domainName}/${organizationalUnits.slice(0, index).join('/')}` : domainName,
          cloudProvider: entity.integrationType as unknown as NodeTreeCloudProvider,
          riskScore: entity.riskScore || 0,
          labels: entity.labels || [],
        },
      }

      groupsMap.set(uniqueGroupId, unitNode)
    })

    const instanceNode = {
      position,
      id: instanceId,
      type: 'machine',
      data: {
        label: entity.name,
        parentId: organizationalUnits.length > 0 ? `${domainName}/${organizationalUnits.join('/')}` : domainName,
        subType: entity.integrationType,
        treeType: entity.integrationType,
        instanceType: (info.platformName as 'windows' | 'linux' | 'macOS') || 'unknown',
        info: info,
        issues: [],
        isWorkload: entity.isWorkload,
        cloudProvider: entity.integrationType,
        riskScore: entity.riskScore || 0,
        labels: entity.labels || [],
      } as unknown as SuperNode,
    }
    instancesMap.set(instanceId, instanceNode as unknown as SuperNode)
  }
}
