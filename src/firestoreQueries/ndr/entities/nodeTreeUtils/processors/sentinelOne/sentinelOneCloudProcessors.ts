import { EntityTypeWithId } from '../../../customEntitiesTypes'
import { position } from '../../constants'
import { NodeTreeCloudProvider, SuperNode } from '../../generalTypes'
import { EntityProcessor } from '../../processorInterface'
import { SentinelOneInfo } from '@globalTypes/ndrBenchmarkTypes'

type AllMap = {
  accountsMap: Map<string, SuperNode>
  regionsMap: Map<string, SuperNode>
  vpcsMap: Map<string, SuperNode>
  instancesMap: Map<string, SuperNode>
}

export class SentinelOneCloudProcessor implements EntityProcessor {
  private knownCloudProviders = ['aws', 'gcp']
  private cloudProvider: NodeTreeCloudProvider = 'sentinelOne'

  filterFunction = (entity: EntityTypeWithId) => {
    if (entity.type !== 'sentinelOne_agent') return false
    const keys = Object.keys((entity.info as SentinelOneInfo).cloudProviders).map((key) => key.toLowerCase())
    if (keys.length === 0) return false
    return keys.some((key) => this.knownCloudProviders.includes(key))
  }

  isAwsInSentinelOne(entity: EntityTypeWithId): boolean {
    const info = entity.info as SentinelOneInfo
    return Object.hasOwn(info.cloudProviders, 'AWS')
  }

  isGcpInSentinelOne(entity: EntityTypeWithId): boolean {
    const info = entity.info as SentinelOneInfo
    return Object.hasOwn(info.cloudProviders, 'GCP')
  }

  filter(entities: Array<EntityTypeWithId>): Array<EntityTypeWithId> {
    return entities.filter(this.filterFunction)
  }

  classify(entity: EntityTypeWithId, cloud: 'aws' | 'gcp', allMaps: AllMap) {
    const info = entity.info as SentinelOneInfo
    const cloudProvider =
      cloud === 'aws' ? info.cloudProviders['AWS'] : cloud === 'gcp' ? info.cloudProviders['GCP'] : undefined
    if (!cloudProvider) return

    const entityId = entity.id
    const accountId = cloudProvider.cloudAccount!
    const region = cloudProvider.cloudLocation!
    const vpcId = cloudProvider.cloudNetwork!
    const instanceId = entity.id

    const accountNode: SuperNode = {
      position,
      id: accountId,
      type: 'account',
      data: {
        cloudProvider: this.cloudProvider,
        label: accountId,
        children: [],
        subType: 'sentinelOne',
        treeType: cloud,
      },
    }
    allMaps.accountsMap.set(accountId, accountNode)

    const regionNode: SuperNode = {
      position,
      id: region,
      type: 'region',
      data: {
        cloudProvider: this.cloudProvider,
        label: region,
        parentId: accountId,
        children: [],
        subType: 'sentinelOne',
        treeType: cloud,
      },
    }
    allMaps.regionsMap.set(region, regionNode)

    const vpcNode: SuperNode = {
      position,
      id: vpcId,
      type: 'vpc',
      data: {
        cloudProvider: this.cloudProvider,
        label: vpcId,
        parentId: region,
        children: [],
        subType: 'sentinelOne',
        treeType: cloud,
      },
    }
    allMaps.vpcsMap.set(vpcId, vpcNode)

    const instanceNode: SuperNode = {
      position,
      id: entityId,
      type: 'instance',
      data: {
        cloudProvider: this.cloudProvider,
        label: info.computerName,
        parentId: vpcId,
        subType: 'sentinelOne',
        treeType: cloud,
        instanceType: info.osType as 'windows' | 'linux' | 'macOS',
        info: info,
        issues: [],
        riskScore: entity.riskScore,
        labels: entity.labels,
      },
    }
    allMaps.instancesMap.set(instanceId, instanceNode)
  }

  process(entities: Array<EntityTypeWithId>): Array<SuperNode> {
    const awsInSentinalOnes = entities.filter(this.isAwsInSentinelOne)
    const gcpInSentinalOnes = entities.filter(this.isGcpInSentinelOne)

    const accountsMap: Map<string, SuperNode> = new Map()
    const regionsMap: Map<string, SuperNode> = new Map()
    const vpcsMap: Map<string, SuperNode> = new Map()
    const instancesMap: Map<string, SuperNode> = new Map()

    const allMap: AllMap = {
      accountsMap,
      regionsMap,
      vpcsMap,
      instancesMap,
    }

    awsInSentinalOnes.forEach((entity) => this.classify(entity, 'aws', allMap))
    gcpInSentinalOnes.forEach((entity) => this.classify(entity, 'gcp', allMap))

    instancesMap.forEach((instance) => {
      const vpcId = (instance.data as { parentId: string }).parentId
      const vpcNode = vpcsMap.get(vpcId)
      if (!vpcNode) return
      if (!vpcNode.data.children) vpcNode.data.children = []
      vpcNode.data.children.push(instance)
    })

    vpcsMap.forEach((vpc) => {
      const regionId = (vpc.data as { parentId: string }).parentId
      const regionNode = regionsMap.get(regionId)
      if (!regionNode) return
      if (!regionNode.data.children) regionNode.data.children = []
      regionNode.data.children.push(vpc)
    })

    regionsMap.forEach((region) => {
      const accountId = (region.data as { parentId: string }).parentId
      const accountNode = accountsMap.get(accountId)
      if (!accountNode) return
      if (!accountNode.data.children) accountNode.data.children = []
      accountNode.data.children.push(region)
    })
    return Array.from(accountsMap.values())
  }
}
