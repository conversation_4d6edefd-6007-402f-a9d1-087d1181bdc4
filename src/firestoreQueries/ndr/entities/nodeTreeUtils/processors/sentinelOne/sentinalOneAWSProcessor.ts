import { AWSEndpointInfo, AWSEndpointType, CloudEntity } from '@globalTypes/ndrBenchmarkTypes/entityTypes'
import { NodeTreeCloudProvider, SuperNode } from '../../generalTypes'
import { position } from '../../constants'
import { EntityProcessor } from '../../processorInterface'
import { EntityTypeWithId } from '../../../customEntitiesTypes'

export const awsTypes = [
  'aws_ec2_instance',
  'aws_rds_db_cluster',
  'aws_lambda_function',
  'aws_ec2_application_load_balancer',
  'aws_vpc',
  'aws_subnet',
  'aws_region',
  'aws_account',
]

const awsInstanceTypes = [
  'aws_ec2_instance',
  'aws_rds_db_cluster',
  'aws_lambda_function',
  'aws_ec2_application_load_balancer',
]

export class SentinelOneAWSProcessor implements EntityProcessor {
  private cloudProvider: NodeTreeCloudProvider = 'aws'

  filterFunction(entity: EntityTypeWithId) {
    return awsTypes.includes(entity.type)
  }

  filter(entities: Array<EntityTypeWithId>): Array<EntityTypeWithId> {
    return entities.filter(this.filterFunction)
  }

  process(entities: Array<EntityTypeWithId>): Array<SuperNode> {
    const accounts: Array<CloudEntity> = []
    const regions: Array<CloudEntity> = []
    const vpcs: Array<CloudEntity> = []
    const instances: Array<CloudEntity> = []
    entities.forEach((entity) => {
      switch (entity.type as string) {
        case 'aws_account':
          accounts.push(entity)
          break
        case 'aws_region':
          regions.push(entity)
          break
        case 'aws_vpc':
          vpcs.push(entity)
          break
        default:
          if (awsInstanceTypes.includes(entity.type)) {
            instances.push(entity)
          }
      }
    })

    const instancesNodes: Array<SuperNode> = instances.map((i) => {
      const length = i.id.split('/').length
      const instanceId = i.id.split('/')[length - 1]

      return {
        id: instanceId,
        type: 'instance',
        data: {
          cloudProvider: this.cloudProvider,
          label: i.name,
          parentId: (i.info as AWSEndpointInfo).vpcId,
          subType: 'aws',
          treeType: 'aws',
          instanceType: i.type as AWSEndpointType,
          issues: [],
          riskScore: i.riskScore,
          labels: i.labels,
        },
        position,
      }
    })

    const vpcNodes: Array<SuperNode> = vpcs.map((v) => {
      const length = v.id.split('/').length
      const vpcId = v.id.split('/')[length - 1]
      return {
        id: v.id,
        type: 'vpc',
        data: {
          cloudProvider: this.cloudProvider,
          label: v.name,
          subType: 'aws',
          parentId: (v.info as AWSEndpointInfo).region,
          children: instancesNodes.filter((s) => s.data.parentId === vpcId),
          treeType: 'aws',
          riskScore: entity.riskScore,
          labels: entity.labels,
        },
        position,
      }
    })

    const regionNodes: Array<SuperNode> = regions.map((r) => {
      return {
        id: (r.info as AWSEndpointInfo).region,
        type: 'region',
        data: {
          cloudProvider: this.cloudProvider,
          subType: 'aws',
          label: r.name,
          parentId: (r.info as AWSEndpointInfo).accountId,
          children: vpcNodes.filter((v) => v.data.parentId === (r.info as AWSEndpointInfo).region),
          treeType: 'aws',
          riskScore: entity.riskScore,
          labels: entity.labels,
        },
        position,
      }
    })

    const accountNodes: Array<SuperNode> = accounts.map((a) => {
      const length = a.id.split(':').length
      const accountId = a.id.split(':')[length - 1]
      return {
        id: a.id,
        type: 'account',
        data: {
          cloudProvider: this.cloudProvider,
          label: a.name,
          children: regionNodes.filter((r) => r.data.parentId === accountId),
          subType: 'aws',
          treeType: 'aws',
          riskScore: entity.riskScore,
          labels: entity.labels,
        },
        position,
      }
    })

    return accountNodes
  }
}
