import { EntityTypeWithId } from '@/firestoreQueries/ndr/entities/customEntitiesTypes'
import { position } from '../../constants'
import { NodeTreeCloudProvider, SuperNode } from '../../generalTypes'
import { EntityProcessor } from '../../processorInterface'
import { SentinelOneInfo } from '@globalTypes/ndrBenchmarkTypes'

export class SentinelOneActiveDirectoryProcessor implements EntityProcessor {
  private cloudProvider: NodeTreeCloudProvider = 'sentinelOne'

  filterFunction(entity: EntityTypeWithId): boolean {
    const info = entity.info as SentinelOneInfo

    return !!info?.activeDirectory?.computerDistinguishedName
  }

  isESXIInSentinelOne(entity: EntityTypeWithId): boolean {
    const info = entity.info as SentinelOneInfo
    return Object.hasOwn(info.cloudProviders, 'ESXI')
  }

  filter(entites: Array<EntityTypeWithId>): Array<EntityTypeWithId> {
    return entites.filter(this.filterFunction)
  }

  classify(
    entity: EntityTypeWithId,
    maps: {
      activeDirectoryDomains: Map<string, SuperNode>
      activeDirectoryOUs: Map<string, SuperNode>
      activeDirectoryComputers: Map<string, SuperNode>
    },
  ) {
    const info = entity.info as SentinelOneInfo

    const domainParts = info.activeDirectory.computerDistinguishedName
      .split(',')
      .filter((part) => part.startsWith('DC='))
    const activeDomainId = info.activeDirectory.computerDistinguishedName ? domainParts.join(',') : info.domain
    const domainNode: SuperNode = {
      id: activeDomainId,
      type: 'activeDirectoryDomain',
      data: {
        label: info.activeDirectory.computerDistinguishedName
          ? domainParts.map((part) => part.split('=')[1]).join('.')
          : info.domain,
        subType: 'activeDirectory',
        treeType: 'activeDirectory',
        children: [],
        cloudProvider: this.cloudProvider,
        riskScore: entity.riskScore,
        labels: entity.labels,
      },
      position,
    }
    maps.activeDirectoryDomains.set(activeDomainId, domainNode)

    let activeDirectoryOUs = info.activeDirectory.computerDistinguishedName
      .split(',')
      .filter((part) => part.startsWith('OU='))
      .map((part) => part.split('OU=')[1])

    if (activeDirectoryOUs.length > 1) {
      activeDirectoryOUs = activeDirectoryOUs.slice(0, -1)
    }

    const activeDirectoryOUKey = activeDirectoryOUs.reverse().join(' / ')

    const ouNode: SuperNode = {
      id: activeDirectoryOUKey,
      type: 'activeDirectoryOU',
      data: {
        label: activeDirectoryOUKey,
        parentId: activeDomainId,
        subType: 'activeDirectory',
        treeType: 'activeDirectory',
        children: [],
        cloudProvider: this.cloudProvider,
        riskScore: entity.riskScore,
        labels: entity.labels,
      },
      position,
    }
    if (activeDirectoryOUKey) maps.activeDirectoryOUs.set(activeDirectoryOUKey, ouNode)

    const isESXI = this.isESXIInSentinelOne(entity)

    const computerNode: SuperNode = {
      id: entity.id,
      type: 'activeDirectoryComputer',
      data: {
        label: entity.name,
        parentId: activeDirectoryOUKey === '' ? activeDomainId : activeDirectoryOUKey,
        subType: 'activeDirectory',
        treeType: 'activeDirectory',
        instanceType: isESXI ? `esxi-${info.osType}` : info.machineType,
        issues: [],
        cloudProvider: this.cloudProvider,
        riskScore: entity.riskScore,
        labels: entity.labels,
      },
      position,
    }
    maps.activeDirectoryComputers.set(entity.id, computerNode)
  }

  process(entities: Array<EntityTypeWithId>): Array<SuperNode> {
    const domainsMap: Map<string, SuperNode> = new Map()
    const ouGroupMap: Map<string, SuperNode> = new Map()
    const computersMap: Map<string, SuperNode> = new Map()

    entities.forEach((entity) =>
      this.classify(
        entity,
        {
          activeDirectoryDomains: domainsMap,
          activeDirectoryOUs: ouGroupMap,
          activeDirectoryComputers: computersMap,
        },
      ),
    )

    computersMap.forEach((computer) => {
      const ouId = (computer.data as { parentId: string }).parentId
      const ouNode = ouGroupMap.get(ouId)
      if (!ouNode) {
        const domainId = (computer.data as { parentId: string }).parentId
        const domainNode = domainsMap.get(domainId)
        if (!domainNode) return

        if (!domainNode.data.children) domainNode.data.children = []
        domainNode.data.children.push(computer)
        return
      }
      if (!ouNode.data.children) ouNode.data.children = []
      ouNode.data.children.push(computer)
    })

    ouGroupMap.forEach((ou) => {
      const domainId = (ou.data as { parentId: string }).parentId
      const domainNode = domainsMap.get(domainId)
      if (!domainNode) return
      if (!domainNode.data.children) domainNode.data.children = []
      domainNode.data.children.push(ou)
    })

    return Array.from(domainsMap.values())
  }
}
