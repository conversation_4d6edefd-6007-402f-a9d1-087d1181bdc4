import { EntityTypeWithId } from '../../../customEntitiesTypes'
import { position } from '../../constants'
import { NodeTreeCloudProvider, SuperNode } from '../../generalTypes'
import { EntityProcessor } from '../../processorInterface'
import { SentinelOneInfo } from '@globalTypes/ndrBenchmarkTypes'

export class SentinelOneStandaloneProcessor implements EntityProcessor {
  private cloudProvider: NodeTreeCloudProvider = 'sentinelOne'

  filterFunction(entity: EntityTypeWithId): boolean {
    return entity.type === 'sentinelOne_agent'
  }
  filter(entites: Array<EntityTypeWithId>): Array<EntityTypeWithId> {
    return entites.filter(this.filterFunction)
  }

  process(entities: Array<EntityTypeWithId>): Array<SuperNode> {
    const sitesMap = new Map<string, SuperNode>()
    const groupsMap = new Map<string, SuperNode>()
    const instancesMap = new Map<string, SuperNode>()
    entities.forEach((entity) => this.classify(entity, sitesMap, groupsMap, instancesMap))

    instancesMap.forEach((instance) => {
      const groupId = (instance.data as { parentId: string }).parentId
      const groupNode = groupsMap.get(groupId)
      if (!groupNode) return
      if (!groupNode.data.children) groupNode.data.children = []
      groupNode.data.children.push(instance)
    })

    groupsMap.forEach((group) => {
      const siteId = (group.data as { parentId: string }).parentId
      const siteNode = sitesMap.get(siteId)
      if (!siteNode) return
      if (!siteNode.data.children) siteNode.data.children = []
      siteNode.data.children.push(group)
    })

    return Array.from(sitesMap.values())
  }

  classify(
    entity: EntityTypeWithId,
    sitesMap: Map<string, SuperNode>,
    groupsMap: Map<string, SuperNode>,
    instancesMap: Map<string, SuperNode>,
  ) {
    const info = entity.info as SentinelOneInfo
    const siteId = info.siteName
    const groupId = info.groupId.toString()
    const instanceId = entity.id

    const siteNode: SuperNode = {
      position,
      id: siteId,
      type: 'site',
      data: {
        label: siteId,
        children: [],
        subType: 'sentinelOne',
        treeType: 'sentinelOne',
        cloudProvider: this.cloudProvider,
        riskScore: entity.riskScore,
        labels: entity.labels,
      },
    }

    sitesMap.set(siteId, siteNode)

    const groupNode: SuperNode = {
      position,
      id: groupId,
      type: 'so-group',
      data: {
        label: groupId,
        parentId: siteId,
        children: [],
        subType: 'sentinelOne',
        treeType: 'sentinelOne',
        cloudProvider: this.cloudProvider,
        riskScore: entity.riskScore,
        labels: entity.labels,
      },
    }

    groupsMap.set(groupId, groupNode)

    const instanceNode: SuperNode = {
      position,
      id: instanceId,
      type: 'machine',
      data: {
        label: info.computerName,
        parentId: groupId,
        subType: 'sentinelOne',
        treeType: 'sentinelOne',
        instanceType: info.osType as 'windows' | 'linux' | 'macOS',
        info: info,
        issues: [],
        cloudProvider: this.cloudProvider,
        riskScore: entity.riskScore,
        labels: entity.labels,
      },
    }
    instancesMap.set(instanceId, instanceNode)
  }
}
