import { EntityTypeWithId } from '../../../customEntitiesTypes'
import { position } from '../../constants'
import { NodeTreeCloudProvider, SuperNode } from '../../generalTypes'
import { EntityProcessor } from '../../processorInterface'
import { GroupedEntity } from './GroupingProcessor'

export class ZoomedGroupProcessor implements EntityProcessor {
  private cloudProvider: NodeTreeCloudProvider = 'sentinelOne'
  private targetGroup: GroupedEntity
  private allEntities: EntityTypeWithId[]

  constructor(targetGroup: GroupedEntity, allEntities: EntityTypeWithId[]) {
    this.targetGroup = targetGroup
    this.allEntities = allEntities
  }

  filterFunction(entity: EntityTypeWithId): boolean {
    // Only include entities that are part of the target group
    return this.targetGroup.entities.includes(entity.id)
  }

  filter(entities: Array<EntityTypeWithId>): Array<EntityTypeWithId> {
    return entities.filter(this.filterFunction)
  }

  process(entities: Array<EntityTypeWithId>): Array<SuperNode> {
    const groupContainerMap = new Map<string, SuperNode>()
    const machineNodesMap = new Map<string, SuperNode>()

    // Create the so-group container for this specific group
    const groupContainerId = `so-group-${this.targetGroup.groupId}`
    const groupContainer: SuperNode = {
      position,
      id: groupContainerId,
      type: 'so-group',
      data: {
        label: `← ${this.targetGroup.groupName}`, // Back arrow indicates clickable
        children: [],
        subType: 'sentinelOne',
        treeType: 'sentinelOne',
        cloudProvider: 'sentinelOne' as NodeTreeCloudProvider,
        riskScore: 0,
        labels: [],
        // Store group metadata (cast as any to bypass type checking)
        groupKeys: this.targetGroup.groupKeys,
        aggregatedLabels: this.targetGroup.aggregatedLabels,
        entityCount: this.targetGroup.entityCount,
        isBackButton: true, // Mark for back functionality
      } as any,
    }
    groupContainerMap.set(groupContainerId, groupContainer)

    // Create machine nodes for each entity in the group
    entities.forEach((entity) => {
      const machineNode: SuperNode = {
        position,
        id: entity.id,
        type: 'machine',
        data: {
          ...entity,
          label: entity.name || entity.id,
          parentId: groupContainerId,
          subType: (entity as any).subType || entity.type || 'sentinelOne',
          treeType: 'sentinelOne',
          cloudProvider: 'sentinelOne' as NodeTreeCloudProvider,
          instanceType: (entity as any).instanceType || 'unknown',
          info: entity.info || {},
          issues: (entity as any).issues || [],
          riskScore: (entity as any).riskScore || 0,
          labels: (entity as any).labels || [],
          isMachine: true,
        } as any,
      }
      machineNodesMap.set(entity.id, machineNode)
    })

    // Build the tree structure by adding machine nodes to the group container
    machineNodesMap.forEach((machineNode) => {
      const parentId = (machineNode.data as { parentId: string }).parentId
      const parentContainer = groupContainerMap.get(parentId)
      if (parentContainer) {
        if (!parentContainer.data.children) parentContainer.data.children = []
        parentContainer.data.children.push(machineNode)
      }
    })

    // Return only the root container - machine nodes are in its children array
    return Array.from(groupContainerMap.values())
  }
}
