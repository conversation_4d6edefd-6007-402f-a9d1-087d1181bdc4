import { EntityTypeWithId } from '../../../customEntitiesTypes'
import { SuperNode, NodeSubtype, NodeTreeCloudProvider } from '../../generalTypes'
import { EntityProcessor } from '../../processorInterface'
import { position } from '../../constants'
import { GroupedEntity } from '@globalTypes/ndrBenchmarkTypes/groupViews/groupViewTypes'

export class GroupingProcessor implements EntityProcessor {
  private groupedEntities: GroupedEntity[]
  private entityToGroupMap: Map<string, string>

  constructor(groupedEntities: GroupedEntity[] = []) {
    this.groupedEntities = groupedEntities
    this.entityToGroupMap = this.createEntityToGroupMap()
  }

  filterFunction(entity: EntityTypeWithId): boolean {
    // Include all entities that are part of a group
    return this.entityToGroupMap.has(entity.id)
  }

  filter(entities: EntityTypeWithId[]): EntityTypeWithId[] {
    return entities.filter(this.filterFunction.bind(this))
  }

  process(entities: EntityTypeWithId[]): SuperNode[] {
    if (!this.groupedEntities.length) {
      return []
    }

    const groupNodes: SuperNode[] = []

    this.groupedEntities.forEach(group => {
      // Get entities that belong to this group
      const groupEntities = entities.filter(entity => 
        group.entities.includes(entity.id)
      )

      if (groupEntities.length === 0) return

      // Calculate aggregated risk score
      const avgRiskScore = groupEntities.reduce((sum, entity) => 
        sum + (entity.riskScore || 0), 0
      ) / groupEntities.length

      // Create group node
      const groupNode: SuperNode = {
        position,
        id: group.groupId,
        type: 'groupNode',
        data: {
          label: group.groupName,
          children: groupEntities.map(entity => this.createEntityChildNode(entity, group.groupId)),
          subType: 'group' as NodeSubtype,
          treeType: 'group' as any,
          cloudProvider: 'group' as NodeTreeCloudProvider,
          riskScore: avgRiskScore,
          labels: this.aggregateLabels(groupEntities),
          entityCount: group.entityCount,
          groupKeys: group.groupKeys,
          aggregatedLabels: group.aggregatedLabels,
          isGroup: true,
          isExpanded: false,
        },
      }

      groupNodes.push(groupNode)
    })

    return groupNodes
  }

  private createEntityToGroupMap(): Map<string, string> {
    const map = new Map<string, string>()
    this.groupedEntities.forEach(group => {
      group.entities.forEach(entityId => {
        map.set(entityId, group.groupId)
      })
    })
    return map
  }

  private createEntityChildNode(entity: EntityTypeWithId, parentGroupId: string): SuperNode {
    return {
      position,
      id: entity.id,
      type: entity.integrationType || 'unknown',
      data: {
        label: entity.name || entity.id,
        children: [],
        subType: entity.integrationType as unknown as NodeSubtype,
        treeType: entity.integrationType as unknown as any,
        cloudProvider: entity.integrationType as unknown as NodeTreeCloudProvider,
        riskScore: entity.riskScore || 0,
        labels: entity.labels || [],
        parentId: parentGroupId,
        isEntityInGroup: true,
        originalEntity: entity,
      },
    }
  }

  private aggregateLabels(entities: EntityTypeWithId[]): any[] {
    const labelMap = new Map<string, any>()
    
    entities.forEach(entity => {
      if (entity.labels) {
        entity.labels.forEach(label => {
          if (!labelMap.has(label.key || label.id)) {
            labelMap.set(label.key || label.id, label)
          }
        })
      }
    })

    return Array.from(labelMap.values())
  }

  // Method to update grouped entities
  updateGroupedEntities(groupedEntities: GroupedEntity[]): void {
    this.groupedEntities = groupedEntities
    this.entityToGroupMap = this.createEntityToGroupMap()
  }

  // Method to expand a group (show individual entities)
  expandGroup(groupId: string): SuperNode[] {
    const group = this.groupedEntities.find(g => g.groupId === groupId)
    if (!group) return []

    // This would return the individual entity nodes for the expanded group
    // Implementation depends on how the tree manipulation system works
    return []
  }

  // Method to collapse a group (hide individual entities)
  collapseGroup(groupId: string): void {
    // Implementation for collapsing a group
    // This would hide the individual entities and show only the group node
  }

  // Check if an entity is part of a group
  isEntityGrouped(entityId: string): boolean {
    return this.entityToGroupMap.has(entityId)
  }

  // Get the group ID for an entity
  getGroupForEntity(entityId: string): string | undefined {
    return this.entityToGroupMap.get(entityId)
  }

  // Get all entities in a group
  getEntitiesInGroup(groupId: string): string[] {
    const group = this.groupedEntities.find(g => g.groupId === groupId)
    return group ? group.entities : []
  }
}
