import { EntityTypeWithId } from '../../../customEntitiesTypes'
import { position } from '../../constants'
import { NodeTreeCloudProvider, SuperNode } from '../../generalTypes'
import { EntityProcessor } from '../../processorInterface'

export interface GroupedEntity {
  groupId: string
  groupName: string
  entities: string[]
  groupKeys: Record<string, string>
  aggregatedLabels: Record<string, string[]>
  entityCount: number
}

export class GroupingProcessor implements EntityProcessor {
  private cloudProvider: NodeTreeCloudProvider = 'sentinelOne' // Default, can be overridden
  private groupedEntities: GroupedEntity[]
  private allEntities: EntityTypeWithId[]

  constructor(groupedEntities: GroupedEntity[], allEntities: EntityTypeWithId[]) {
    this.groupedEntities = groupedEntities
    this.allEntities = allEntities
  }

  filterFunction(entity: EntityTypeWithId): boolean {
    // Include all entities that are part of any group
    const allGroupEntityIds = new Set(
      this.groupedEntities.flatMap(group => group.entities)
    )
    return allGroupEntityIds.has(entity.id)
  }

  filter(entities: Array<EntityTypeWithId>): Array<EntityTypeWithId> {
    return entities.filter(this.filterFunction)
  }

  process(entities: Array<EntityTypeWithId>): Array<SuperNode> {
    const rootContainerMap = new Map<string, SuperNode>()
    const groupNodesMap = new Map<string, SuperNode>()
    const machineNodesMap = new Map<string, SuperNode>()

    // Create root container
    const rootContainerId = 'groups-root-container'
    const rootContainer: SuperNode = {
      position,
      id: rootContainerId,
      type: 'so-group',
      data: {
        label: `Dynamic Groups (${this.groupedEntities.length} groups)`,
        children: [],
        subType: 'sentinelOne',
        treeType: 'sentinelOne',
        cloudProvider: 'sentinelOne' as NodeTreeCloudProvider,
        riskScore: 0,
        labels: [],
      },
    }
    rootContainerMap.set(rootContainerId, rootContainer)

    // Create group nodes (so-group type)
    this.groupedEntities.forEach((group) => {
      const groupNode: SuperNode = {
        position,
        id: group.groupId,
        type: 'so-group',
        data: {
          label: group.groupName,
          parentId: rootContainerId,
          children: [],
          subType: 'sentinelOne',
          treeType: 'sentinelOne',
          cloudProvider: 'sentinelOne' as NodeTreeCloudProvider,
          riskScore: 0,
          labels: [],
          // Store group metadata (cast as any to bypass type checking)
          groupKeys: group.groupKeys,
          aggregatedLabels: group.aggregatedLabels,
          entityCount: group.entityCount,
          isGroup: true,
        } as any,
      }
      groupNodesMap.set(group.groupId, groupNode)
    })

    // Create machine nodes for each entity
    entities.forEach((entity) => {
      // Find which group this entity belongs to
      const parentGroup = this.groupedEntities.find(group => 
        group.entities.includes(entity.id)
      )
      
      if (parentGroup) {
        const machineNode: SuperNode = {
          position,
          id: entity.id,
          type: 'machine',
          data: {
            ...entity,
            label: entity.name || entity.id,
            parentId: parentGroup.groupId,
            subType: (entity as any).subType || entity.type || 'sentinelOne',
            treeType: 'sentinelOne',
            cloudProvider: 'sentinelOne' as NodeTreeCloudProvider,
            instanceType: (entity as any).instanceType || 'unknown',
            info: entity.info || {},
            issues: (entity as any).issues || [],
            riskScore: (entity as any).riskScore || 0,
            labels: (entity as any).labels || [],
            isMachine: true,
          } as any,
        }
        machineNodesMap.set(entity.id, machineNode)
      }
    })

    // Build the tree structure by adding children to parents
    // Add machine nodes to their group parents
    machineNodesMap.forEach((machineNode) => {
      const parentGroupId = (machineNode.data as { parentId: string }).parentId
      const parentGroup = groupNodesMap.get(parentGroupId)
      if (parentGroup) {
        if (!parentGroup.data.children) parentGroup.data.children = []
        parentGroup.data.children.push(machineNode)
      }
    })

    // Add group nodes to root container
    groupNodesMap.forEach((groupNode) => {
      const parentId = (groupNode.data as { parentId: string }).parentId
      const rootContainer = rootContainerMap.get(parentId)
      if (rootContainer) {
        if (!rootContainer.data.children) rootContainer.data.children = []
        rootContainer.data.children.push(groupNode)
      }
    })

    // Return only root nodes - the tree structure is built through children arrays
    return Array.from(rootContainerMap.values())
  }
}
