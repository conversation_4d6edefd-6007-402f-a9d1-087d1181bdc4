import { AWSEndpointInfo } from '@globalTypes/ndrBenchmarkTypes/entities'
import { NodeTreeCloudProvider, SuperNode } from '@/firestoreQueries/ndr/entities/nodeTreeUtils/generalTypes'
import { position } from '@/firestoreQueries/ndr/entities/nodeTreeUtils/constants'
import { EntityProcessor } from '@/firestoreQueries/ndr/entities/nodeTreeUtils/processorInterface'
import { EntityTypeWithId } from '@/firestoreQueries/ndr/entities/customEntitiesTypes'
import { subTypeToNameMapper } from '@/views/NDR/Dashboard/Layout/Instances/Instances'

export class AWSProcessor implements EntityProcessor {
  private cloudProvider: NodeTreeCloudProvider = 'aws'

  filterFunction(entity: EntityTypeWithId) {
    return entity.type === 'aws_endpoint'
  }

  filter(entities: Array<EntityTypeWithId>): Array<EntityTypeWithId> {
    return entities.filter(this.filterFunction)
  }

  process(entities: Array<EntityTypeWithId>): Array<SuperNode> {
    const accountsMap = new Map<string, SuperNode>()
    const regionsMap = new Map<string, SuperNode>()
    const vpcsMap = new Map<string, SuperNode>()
    const resourcesMap = new Map<string, SuperNode>()
    const subTypeMap = new Map<string, SuperNode>()

    // First pass: Create all nodes
    entities.forEach((entity) => {
      const info = entity.info as AWSEndpointInfo
      const accountId = info.accountId || 'unknown-account'
      const region = info.region || 'unknown-region'
      const vpcId = info.vpcId || 'unknown-vpc'
      const uniqueRegionId = `${accountId}:${region}`
      const uniqueVpcId = `${uniqueRegionId}:${vpcId}`
      const subType = entity.subTypes[0] || 'unknown'
      const uniqueSubTypeId = `${uniqueVpcId}:${subType}`

      // Create account node if it doesn't exist
      if (!accountsMap.has(accountId)) {
        accountsMap.set(accountId, {
          position,
          id: accountId,
          type: 'account',
          data: {
            label: accountId,
            children: [],
            subType: 'aws',
            treeType: 'aws',
            cloudProvider: this.cloudProvider,
            riskScore: entity.riskScore,
            labels: entity.labels,
          },
        })
      }

      // Create region node if it doesn't exist
      if (!regionsMap.has(uniqueRegionId)) {
        regionsMap.set(uniqueRegionId, {
          position,
          id: uniqueRegionId,
          type: 'region',
          data: {
            label: region,
            parentId: accountId,
            children: [],
            subType: 'aws',
            treeType: 'aws',
            cloudProvider: this.cloudProvider,
            riskScore: entity.riskScore,
            labels: entity.labels,
          },
        })
      }

      // Create VPC node if it doesn't exist
      if (!vpcsMap.has(uniqueVpcId)) {
        vpcsMap.set(uniqueVpcId, {
          position,
          id: uniqueVpcId,
          type: 'vpc',
          data: {
            label: vpcId,
            parentId: uniqueRegionId,
            children: [],
            subType: 'aws',
            treeType: 'aws',
            cloudProvider: this.cloudProvider,
            riskScore: entity.riskScore,
            labels: entity.labels,
          },
        })
      }

      // Create subType container node if it doesn't exist
      if (!subTypeMap.has(uniqueSubTypeId)) {
        subTypeMap.set(uniqueSubTypeId, {
          position,
          id: uniqueSubTypeId,
          type: 'aws-service-group',
          data: {
            label: this.getServiceLabel(subType),
            parentId: uniqueVpcId,
            children: [],
            subType: subType,
            treeType: 'aws',
            cloudProvider: this.cloudProvider,
            riskScore: entity.riskScore,
            labels: entity.labels,
          },
        })
      }

      // Create subType container node if it doesn't exist
      if (!subTypeMap.has(uniqueSubTypeId)) {
        subTypeMap.set(uniqueSubTypeId, {
          position,
          id: uniqueSubTypeId,
          type: 'aws-service-group',
          data: {
            label: this.getServiceLabel(subType),
            parentId: uniqueVpcId,
            children: [],
            subType: subType,
            treeType: 'aws',
            cloudProvider: this.cloudProvider,
            riskScore: entity.riskScore,
            labels: entity.labels,
          },
        })
      }

      // Create resource node
      const resourceNode: SuperNode = {
        position,
        id: entity.id,
        type: 'machine',
        data: {
          label: entity.name,
          parentId: uniqueSubTypeId,
          subType: info.endpointType,
          treeType: 'aws',
          instanceType: info.endpointType,
          info: info,
          issues: [],
          cloudProvider: this.cloudProvider,
          riskScore: entity.riskScore,
          labels: entity.labels,
        },
      }
      resourcesMap.set(entity.id, resourceNode)
    })

    // Second pass: Build the tree structure bottom-up
    resourcesMap.forEach((resource) => {
      const subTypeId = resource.data.parentId as string
      const subTypeNode = subTypeMap.get(subTypeId)
      if (!subTypeNode) return
      if (!subTypeNode.data.children) subTypeNode.data.children = []
      subTypeNode.data.children.push(resource)
    })

    subTypeMap.forEach((subType) => {
      const vpcId = subType.data.parentId as string
      const vpcNode = vpcsMap.get(vpcId)
      if (!vpcNode) return
      if (!vpcNode.data.children) vpcNode.data.children = []
      vpcNode.data.children.push(subType)
    })

    vpcsMap.forEach((vpc) => {
      const regionId = vpc.data.parentId as string
      const regionNode = regionsMap.get(regionId)
      if (!regionNode) return
      if (!regionNode.data.children) regionNode.data.children = []
      regionNode.data.children.push(vpc)
    })

    regionsMap.forEach((region) => {
      const accountId = region.data.parentId as string
      const accountNode = accountsMap.get(accountId)
      if (!accountNode) return
      if (!accountNode.data.children) accountNode.data.children = []
      accountNode.data.children.push(region)
    })

    return Array.from(accountsMap.values())
  }

  private getServiceLabel(subType: string): string {
    return subTypeToNameMapper[subType] || subType
  }
}
