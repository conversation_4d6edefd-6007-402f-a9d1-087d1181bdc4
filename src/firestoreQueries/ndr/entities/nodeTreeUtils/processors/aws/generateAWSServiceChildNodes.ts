import { position } from '@/firestoreQueries/ndr/entities/nodeTreeUtils/constants'

const AWS_SERVICES = [
  'AMAZON',
  'AMAZON_APPFLOW',
  'AMAZON_CONNECT',
  'API_GATEWAY',
  'CHIME_MEETINGS',
  'CHIME_VOICECONNECTOR',
  '<PERSON><PERSON>OUD9',
  'CLOUDFRONT',
  'C<PERSON><PERSON>UDFRONT_ORIGIN_FACING',
  'CODEBUILD',
  'DYNAMODB',
  'EBS',
  'EC2',
  'EC2_INSTANCE_CONNECT',
  'GLOBALACCELERATOR',
  'IVS_REALTIME',
  'KINESIS_VIDEO_STREAMS',
  'MEDIA_PACKAGE_V2',
  'ROUTE53',
  'ROUTE53_HEALTHCHECKS',
  'ROUTE53_HEALTHCHECKS_PUBLISHING',
  'ROUTE53_RESOLVER',
  'S3',
  'WORKSPACES_GATEWAYS',
]

const generateLabel = (key: string) => key.split('_').join(' ')

function generateAWSServiceChildNodes(accountId: string) {
  const children = AWS_SERVICES.map((serviceName) => ({
    position,
    id: serviceName,
    type: 'aws-service',
    data: {
      label: generateLabel(serviceName),
      parentId: accountId,
      children: [],
      subType: 'aws',
      treeType: 'aws',
      cloudProvider: '',
      riskScore: 0,
      addr: '',
      port: 0
    },
  }))
  
  
  return children
}

export default generateAWSServiceChildNodes
