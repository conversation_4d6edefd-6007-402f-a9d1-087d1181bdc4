import { EntityTypeWithId } from '../../../customEntitiesTypes'
import { position } from '../../constants'
import { NodeTreeCloudProvider, SuperNode } from '../../generalTypes'
import { EntityProcessor } from '../../processorInterface'
import { MSDefenderInfoSqlProperties } from '@globalTypes/ndrBenchmarkTypes/entities-sql-info/msDefender'

export class DefenderTenantProcessor implements EntityProcessor {
  private cloudProvider: NodeTreeCloudProvider = 'msdefender'

  filterFunction(entity: EntityTypeWithId): boolean {
    return true
  }

  filter(entites: Array<EntityTypeWithId>): Array<EntityTypeWithId> {
    return entites.filter(this.filterFunction)
  }

  process(entities: Array<EntityTypeWithId>): Array<SuperNode> {
    const sitesMap = new Map<string, SuperNode>()
    const groupsMap = new Map<string, SuperNode>()
    const instancesMap = new Map<string, SuperNode>()

    entities.forEach((entity) => this.classify(entity, sitesMap, groupsMap, instancesMap))

    instancesMap.forEach((instance) => {
      const parentId = (instance.data as { parentId: string }).parentId
      // Try to find parent in groupsMap first, if not found try sitesMap
      const parentNode = groupsMap.get(parentId) || sitesMap.get(parentId)
      if (!parentNode) return
      if (!parentNode.data.children) parentNode.data.children = []
      parentNode.data.children.push(instance)
    })

    groupsMap.forEach((group) => {
      const parentId = (group.data as { parentId: string }).parentId
      const parentNode = sitesMap.get(parentId)
      if (!parentNode) return
      if (!parentNode.data.children) parentNode.data.children = []
      parentNode.data.children.push(group)
    })

    return Array.from(sitesMap.values())
  }

  classify(
    entity: EntityTypeWithId,
    sitesMap: Map<string, SuperNode>,
    groupsMap: Map<string, SuperNode>,
    instancesMap: Map<string, SuperNode>
  ) {
    const tenantId = (entity.info as MSDefenderInfoSqlProperties).tenantId
    const instanceId = entity.id

    const siteNode: SuperNode = {
      position,
      id: tenantId,
      type: 'tenantNode',
      data: {
        label: tenantId,
        children: [],
        subType: 'msdefender',
        treeType: 'msdefender',
        parentId: tenantId,
        cloudProvider: this.cloudProvider,
        riskScore: entity.riskScore || 0,
        labels: entity.labels || [],
      },
    }

    sitesMap.set(tenantId, siteNode)

    const instanceNode: SuperNode = {
      position,
      id: instanceId,
      type: 'machine',
      data: {
        label: entity.name,
        parentId: tenantId,
        subType: 'msdefender',
        treeType: 'msdefender',
        info: entity.info,
        issues: [],
        cloudProvider: this.cloudProvider,
        riskScore: entity.riskScore || 0,
        labels: entity.labels || [],
      },
    }
    instancesMap.set(instanceId, instanceNode)
  }
}
