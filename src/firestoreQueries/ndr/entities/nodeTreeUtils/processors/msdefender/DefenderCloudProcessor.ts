import { EntityTypeWithId } from '../../../customEntitiesTypes'
import { position } from '../../constants'
import { NodeTreeCloudProvider, SuperNode } from '../../generalTypes'
import { EntityProcessor } from '../../processorInterface'

export class MSDefenderCloudProcessor implements EntityProcessor {
  private cloudProvider: NodeTreeCloudProvider = 'msdefender'

  filterFunction(entity: any): boolean {
    return (
      entity.type === 'ms_defender_endpoint' && entity.common.cloudProvider && entity.common.cloudProvider === 'aws'
    )
  }

  filter(entites: EntityTypeWithId[]): EntityTypeWithId[] {
    return entites.filter(this.filterFunction)
  }

  process(entities: EntityTypeWithId[]): SuperNode[] {
    const sitesMap = new Map<string, SuperNode>()
    const groupsMap = new Map<string, SuperNode>()
    const instancesMap = new Map<string, SuperNode>()

    entities.forEach((entity) => this.classify(entity, sitesMap, groupsMap, instancesMap))

    instancesMap.forEach((instance) => {
      const groupId = (instance.data as { parentId: string }).parentId
      const groupNode = groupsMap.get(groupId)
      if (!groupNode) return
      if (!groupNode.data.children) groupNode.data.children = []
      groupNode.data.children.push(instance)
    })

    groupsMap.forEach((group) => {
      const siteId = (group.data as { parentId: string }).parentId
      const siteNode = sitesMap.get(siteId)
      if (!siteNode) return
      if (!siteNode.data.children) siteNode.data.children = []
      siteNode.data.children.push(group)
    })

    return Array.from(sitesMap.values())
  }

  classify(
    entity: any,
    sitesMap: Map<string, SuperNode>,
    groupsMap: Map<string, SuperNode>,
    instancesMap: Map<string, SuperNode>,
  ) {
    const info = entity.info as any
    const [_, __, instanceType, region, accountId, instanceId] = entity.info.awsResourceName.split(':')

    // Create a unique zone identifier by combining project and zone
    const uniqueZoneId = `${accountId}:${region}`

    const availabilityZoneNode: SuperNode = {
      position,
      id: accountId,
      type: 'account',
      data: {
        label: accountId,
        children: [],
        subType: 'aws',
        treeType: 'aws',
        cloudProvider: this.cloudProvider,
        riskScore: entity.riskScore,
        labels: entity.labels,
      },
    }

    sitesMap.set(accountId, availabilityZoneNode)

    const regionNode: SuperNode = {
      position,
      id: uniqueZoneId,
      type: 'region',
      data: {
        label: region,
        parentId: accountId,
        children: [],
        subType: 'msdefender',
        treeType: 'msdefender',
        cloudProvider: this.cloudProvider,
        riskScore: entity.riskScore,
        labels: entity.labels,
      },
    }

    groupsMap.set(uniqueZoneId, regionNode)

    const instanceNode: SuperNode = {
      position,
      id: entity.id,
      type: 'machine',
      data: {
        label: entity.name,
        parentId: uniqueZoneId,
        subType: 'msdefender',
        treeType: 'msdefender',
        instanceType: (info.osVersion as 'windows' | 'linux' | 'macOS') || 'unknown',
        info: info,
        issues: [],
        cloudProvider: this.cloudProvider,
        riskScore: entity.riskScore,
        labels: entity.labels,
      },
    }
    instancesMap.set(instanceId, instanceNode)
  }
}
