import { IssueTypeWithId } from '@/firestoreQueries/issues/customIssuesTypes'
import { EntityTypeWithId } from '../customEntitiesTypes'
import { SuperNode, TrafficPatternMap } from './generalTypes'

export interface EntityProcessor {
  filter(entities: EntityTypeWithId[]): EntityTypeWithId[]
  process(
    entities: EntityTypeWithId[],
    issues: IssueTypeWithId[],
  ): SuperNode[]
  filterFunction: (entity: EntityTypeWithId) => boolean
}
