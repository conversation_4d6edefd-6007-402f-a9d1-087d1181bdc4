import { ndrQueryKeyStore } from '@/configs/reactQueryKeyStore'
import { useQuery } from '@tanstack/react-query'
import getOneEntity from '../queries/getOneEntity'

type Args = {
    organizationId: string | undefined
    entityId: string
    enabled: boolean
}

export default function useGetOneEntity({ entityId, organizationId, enabled = true }: Args) {
    return useQuery({
        queryKey: ndrQueryKeyStore.oneEntity({ entityId, organizationId: organizationId! }),
        queryFn: async () => await getOneEntity({ entityId, organizationId }),
        staleTime: 1000 * 60 * 5,
        enabled
    })
}
