import { ndrQueryKeyStore } from '@/configs/reactQueryKeyStore'
import { useQuery } from '@tanstack/react-query'
import getNdrEntities, { fetchEntities, getNumberOfEntities } from '../queries/getEntities'

type Args = {
  organizationId: string,
  leanedEntities?: boolean,
  entityIds?: string[],
  enabled?: boolean
  queryKey?: string
}

export default function useGetEntities({ organizationId, leanedEntities, entityIds, enabled = true, queryKey }: Args) {
  return useQuery({
    queryKey: [
      ...ndrQueryKeyStore.dashboardEntities({ organizationId: organizationId! }),
      leanedEntities ? 'leanedEntities' : 'extended',
      entityIds ? [...entityIds] : '',
      queryKey ?? ''
    ],
    queryFn: async () => {
      if (leanedEntities) {
        return await fetchEntities(organizationId!, entityIds)
      }
      return await getNdrEntities({ organizationId })
    },
    staleTime: 15 * 60 * 1000,
    enabled: enabled && !!organizationId,
  })
}


export const useEntitiesCount = ({ organizationId }: { organizationId: string }) => {
  return useQuery({
    queryKey: [organizationId, 'ndr-entities-count'],
    queryFn: async () => {
      const numberOfEntities = await getNumberOfEntities(organizationId as string)
      return numberOfEntities
    },
    staleTime: 15 * 60 * 1000,
  })
}
