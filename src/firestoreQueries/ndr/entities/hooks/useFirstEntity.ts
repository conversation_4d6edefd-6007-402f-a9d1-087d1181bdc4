import {useQuery} from '@tanstack/react-query'
import getFirstEntity from '@/firestoreQueries/ndr/entities/queries/getFirstEntity'

type Args = {
  organizationId: string;
}

function useFirstEntity({ organizationId }: Args) {
  return useQuery({
    queryKey: ['fist-entity', organizationId],
    queryFn: async () => await getFirstEntity({ organizationId }),
    enabled: <PERSON><PERSON><PERSON>(organizationId)
  })
}

export default useFirstEntity
