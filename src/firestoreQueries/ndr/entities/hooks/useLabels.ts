import { db } from '@/configs/firebase'
import { ndrPathStore } from '@/firestoreQueries/utils/pathStore'
import { ndrQueryKeyStore } from '@/configs/reactQueryKeyStore'
import { collection, getDocs, query } from 'firebase/firestore'
import { useQuery } from '@tanstack/react-query'

export function useLabels(organizationId: string | undefined) {
  return useQuery({
    queryKey: [...ndrQueryKeyStore.labels({ organizationId: organizationId! })],
    queryFn: async () => {
      const labelsPath = ndrPathStore.labels({ organizationId: organizationId! })
      const labelsRef = collection(db, labelsPath)
      const labelsSnap = await getDocs(labelsRef)
      const labelsMap = new Map()
      labelsSnap.docs.forEach((doc) => {
        labelsMap.set(doc.id, {
          id: doc.id,
          ...doc.data(),
        })
      })
      return labelsMap
    },
    enabled: !!organizationId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
} 