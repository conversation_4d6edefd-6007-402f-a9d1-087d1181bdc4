import { ndrQueryKeyStore } from '@/configs/reactQueryKeyStore'
import { useInfiniteQuery } from '@tanstack/react-query'
import { getPaginatedEntities } from '../queries/getPaginatedEntities'
import { useRiskDataMap } from './useRiskDataMap'
import { useGetLabels } from '../../labels/hooks/useLabels'
import { PAGE_SIZE } from '@/views/NDR/Inventory/InventoryLayout'

type useGetEntitiesPaginatedArgs = {
  organizationId: string | undefined
  pageSize?: number
  showInactiveItems?: boolean
  searchQuery: string
}

export default function useGetEntitiesPaginated({
  organizationId,
  pageSize = PAGE_SIZE,
  showInactiveItems = false,
  searchQuery,
}: useGetEntitiesPaginatedArgs) {
  const { data: riskDataMap, isLoading: riskDataMapIsLoading } = useRiskDataMap(organizationId)
  const { data: labels, isLoading: isLabelsLoading } = useGetLabels(organizationId)
  const entitiesQuery = useInfiniteQuery({
    queryKey: ndrQueryKeyStore.entitiesPaginated({
      organizationId: organizationId!,
      pageSize,
      searchQuery,
    }),
    queryFn: async ({ pageParam = 0 }) => {
      const result = await getPaginatedEntities({
        organizationId,
        pageSize,
        offset: pageParam,
        searchQuery,
        showInactiveItems,
        riskDataMap: riskDataMap!,
        labels: labels!,
      })
      return result
    },
    enabled: !isLabelsLoading && !riskDataMapIsLoading,
    initialPageParam: 0,
    getNextPageParam: (lastPage, allPages) => {
      if (!lastPage.hasMore) return undefined
      return allPages.length * pageSize
    },
  })

  return {
    ...entitiesQuery,
    isLoading: entitiesQuery.isLoading || riskDataMapIsLoading || isLabelsLoading,
  }
}
