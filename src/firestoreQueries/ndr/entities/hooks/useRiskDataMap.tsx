import { ndrQueryKeyStore } from '@/configs/reactQueryKeyStore'
import { useQuery } from '@tanstack/react-query'
import { getRiskDataMap } from '../queries/getRiskDataMap'

export function useRiskDataMap(organizationId: string | undefined) {
  return useQuery({
    queryKey: ndrQueryKeyStore.riskDataMap({ organizationId: organizationId! }),
    queryFn: () => getRiskDataMap(organizationId!),
    enabled: !!organizationId,
  })
}
