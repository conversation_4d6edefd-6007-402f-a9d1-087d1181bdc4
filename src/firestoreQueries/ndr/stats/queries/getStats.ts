import { db } from '@/configs/firebase'
import { ndrPathStore } from '@/firestoreQueries/utils/pathStore'
import { captureException } from '@sentry/react'
import { collection, getDocs } from 'firebase/firestore'
// eslint-disable-next-line import/named
import { ChartDataShape } from 'reaviz'
import { InstancesObjectType, StatDocumentType } from '../statsTypes'

export type Stats = {
  critical_entities_to_segment?: ChartDataShape[]
  instances?: InstancesObjectType[]
  employee_diverse_connections?: ChartDataShape[]
  most_connected_entities?: ChartDataShape[]
  notifications_bar_chart: any
  notifications_total_count: any
  notifications_pie_chart: any
  notifications_line_graph: any
  notifications_count_last_week: any
  notifications_count_last_24h: any
}

export default async function(organizationId: string | undefined) {
  try {
    if (!organizationId) throw new Error('organizationId is undefined')
    
    const path = ndrPathStore.stats(organizationId)
    const ref = collection(db, path)
    const snap = await getDocs(ref)
    
    const stats = snap.docs.reduce((stats, doc) => {
      const stat = doc.data() as StatDocumentType
      switch (stat.type) {
        case 'critical_entities_to_segment':
          stats.critical_entities_to_segment = stat.data as ChartDataShape[]
          break
        case 'employee_diverse_connections':
          stats.employee_diverse_connections = stat.data as ChartDataShape[]
          break
        case 'instances':
          stats.instances = stat.data as InstancesObjectType[]
          break
        case 'most_connected_entities':
          stats.most_connected_entities = stat.data as ChartDataShape[]
          break;
        case 'notifications_bar_chart':
          stats.notifications_bar_chart = stat.data
          break;
        case 'notifications_total_count':
          stats.notifications_total_count = stat.data
          break;
        case 'notifications_pie_chart':
          stats.notifications_pie_chart = stat.data
          break;
        case 'notifications_line_graph':
          stats.notifications_line_graph = stat.data
          break;
        case 'notifications_count_last_week':
          stats.notifications_count_last_week = stat.data
          break;
        case 'notifications_count_last_24h':
          stats.notifications_count_last_24h = stat.data
          break;
      }
      
      return stats
    }, {} as Stats)
    return stats
  } catch (error) {
    captureException(error)
    throw error
  }
}
