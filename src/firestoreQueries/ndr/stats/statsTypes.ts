// eslint-disable-next-line import/named
import { ChartDataShape } from 'reaviz'

export type StatType =
  | 'instances'
  | 'critical_entities_to_segment'
  | 'employee_diverse_connections'
  | 'most_connected_entities'
  | 'notifications_bar_chart'
  | 'notifications_total_count'
  | 'notifications_pie_chart'
  | 'notifications_line_graph'
  | 'notifications_count_last_week'
  | 'notifications_count_last_24h'


export type InstanceOption = 'aws' | 'gcp' | 'esxi' | 'activeDirectory' | 'azure'

export type InstancesObjectType = {
  count: number;
  subType: InstanceOption | null,
  type: string
}

export type StatDocumentType = {
  chartType: 'bar' | 'instances'
  title: string
  type: StatType
  data: ChartDataShape[] | InstancesObjectType[],
}
