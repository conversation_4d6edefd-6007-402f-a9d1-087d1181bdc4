import { queryClient } from '@/Providers/ReactQueryProvider'
import { useMutation } from '@tanstack/react-query'
import { editComment } from '../queries/editComment'
import { IComment } from '@/components/Comments/Comments'

export function useEditComment(organizationId: string | undefined, entityId: string | undefined) {
  return useMutation({
    mutationFn: ({
      commentId,
      text,
      emailText,
      mentioned,
      authorId,
      insightId,
    }: {
      commentId: string
      text: string
      emailText: string
      mentioned: IComment['mentioned']
      authorId: string
      insightId?: string
    }) => {
      if (!organizationId || !entityId) throw new Error('Organization ID and Entity ID are required')
      return editComment({ organizationId, commentId, text, emailText, mentioned, authorId, insightId })
    },
    onSuccess: () => {
      if (organizationId && entityId) {
        queryClient.invalidateQueries({ queryKey: ['comments', organizationId, entityId] })
        queryClient.invalidateQueries({ queryKey: ['comments-infinite', organizationId, entityId] })
      }
    },
  })
}
