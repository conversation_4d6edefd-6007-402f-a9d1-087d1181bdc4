import { useMutation } from '@tanstack/react-query'
import { queryClient } from '@/Providers/ReactQueryProvider'
import { deleteComment } from '../queries/deleteComment'

export function useDeleteComment(organizationId: string | undefined, entityId: string | undefined) {
  return useMutation({
    mutationFn: ({ commentId }: { commentId: string }) => {
      if (!organizationId) throw new Error('Organization ID is required')
      return deleteComment({ organizationId, commentId })
    },
    onSuccess: () => {
      if (organizationId && entityId) {
        queryClient.invalidateQueries({ queryKey: ['comments', organizationId, entityId] })
        queryClient.invalidateQueries({ queryKey: ['comments-infinite', organizationId, entityId] })
      }
    },
  })
}
