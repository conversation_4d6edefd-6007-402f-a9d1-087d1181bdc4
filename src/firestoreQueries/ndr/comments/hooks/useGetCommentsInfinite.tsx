import { useInfiniteQuery } from '@tanstack/react-query'
import { QueryDocumentSnapshot } from 'firebase/firestore'
import { fetchComments } from '../queries/fetchComments'

export function useGetCommentsInfinite(
  organizationId: string | undefined,
  entityId: string | undefined,
  pageSize: number = 20,
) {
  return useInfiniteQuery({
    queryKey: ['comments-infinite', organizationId, entityId, pageSize],
    queryFn: async ({ pageParam }: { pageParam: QueryDocumentSnapshot | undefined }) => {
      if (!organizationId || !entityId) throw new Error('Organization ID and Entity ID are required')
      return fetchComments({
        organizationId,
        entityId,
        pageSize,
        lastDoc: pageParam,
      })
    },
    initialPageParam: undefined as QueryDocumentSnapshot | undefined,
    getNextPageParam: (lastPage) => (lastPage.hasMore ? lastPage.lastDoc : undefined),
    enabled: !!organizationId && !!entityId,
  })
}
