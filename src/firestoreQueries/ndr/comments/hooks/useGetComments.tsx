import { useQuery } from '@tanstack/react-query'
import { fetchComments } from '../queries/fetchComments'

export function useGetComments(organizationId: string | undefined, entityId: string | undefined) {
  return useQuery({
    queryKey: ['comments', organizationId, entityId],
    queryFn: () => {
      if (!organizationId || !entityId) throw new Error('Organization ID and Entity ID are required')
      return fetchComments({ organizationId, entityId })
    },
    enabled: !!organizationId && !!entityId,
  })
}
