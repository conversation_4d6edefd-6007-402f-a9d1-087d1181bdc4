import { queryClient } from '@/Providers/ReactQueryProvider'
import { useMutation } from '@tanstack/react-query'
import { addComment } from '../queries/addComment'
import { IComment, ResourceType } from '@/components/Comments/Comments'

export function useAddComment(organizationId: string | undefined, entityId: string | undefined) {
  return useMutation({
    mutationFn: ({
      text,
      emailText,
      mentioned,
      parentId,
      authorId,
      resourceType,
      insightId,
    }: {
      text: string
      emailText: string
      mentioned: IComment['mentioned']
      parentId?: string | null
      authorId: string
      resourceType: ResourceType
      insightId?: string
    }) => {
      if (!organizationId || !entityId) throw new Error('Organization ID and Entity ID are required')
      return addComment({ organizationId, entityId, text, emailText, mentioned, parentId, authorId, resourceType, insightId })
    },
    onSuccess: () => {
      if (organizationId && entityId) {
        queryClient.invalidateQueries({
          queryKey: ['comments', organizationId, entityId],
        })
        queryClient.invalidateQueries({
          queryKey: ['comments-infinite', organizationId, entityId],
        })
      }
    },
  })
}
