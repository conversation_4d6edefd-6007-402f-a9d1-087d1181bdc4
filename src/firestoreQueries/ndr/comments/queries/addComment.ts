import { collection, Timestamp, setDoc, doc } from 'firebase/firestore'
import { ndrPathStore } from '@/firestoreQueries/utils/pathStore'
import { db } from '@/configs/firebase'
import { IComment, ResourceType } from '@/components/Comments/Comments'

type AddArgs = {
  organizationId: string
  entityId: string
  text: string
  emailText: string
  mentioned: IComment['mentioned']
  authorId: string
  parentId?: string | null
  resourceType: ResourceType
  insightId?: string | null
}

export async function addComment({
  organizationId,
  entityId,
  text,
  emailText,
  mentioned,
  authorId,
  parentId = null,
  resourceType,
  insightId = null,
}: AddArgs): Promise<IComment> {
  const path = ndrPathStore.comments({ organizationId })
  const ref = collection(db, path)
  const newDoc = doc(ref)
  const now = Timestamp.now()
  const comment: IComment = {
    id: newDoc.id,
    parentId,
    mentioned,
    text,
    emailText,
    entityId,
    authorId,
    updatedAt: now,
    createdAt: now,
    resourceType,
    insightId,
  }
  await setDoc(newDoc, comment)
  return comment
}
