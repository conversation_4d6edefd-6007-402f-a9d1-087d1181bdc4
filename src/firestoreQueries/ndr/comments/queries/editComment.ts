import { IComment } from '@/components/Comments/Comments'
import { db } from '@/configs/firebase'
import { ndrPathStore } from '@/firestoreQueries/utils/pathStore'
import { updateDoc, Timestamp, doc } from 'firebase/firestore'

type EditArgs = {
  organizationId: string
  commentId: string
  text: string
  emailText: string
  mentioned: IComment['mentioned']
  authorId: string
  insightId?: string
}

export async function editComment({
  organizationId,
  commentId,
  text,
  emailText,
  mentioned,
  authorId,
  insightId,
}: EditArgs): Promise<void> {
  const path = ndrPathStore.oneComment({ organizationId, commentId })
  const commentRef = doc(db, path)
  await updateDoc(commentRef, {
    text,
    emailText,
    mentioned,
    authorId,
    updatedAt: Timestamp.now(),
    insightId: insightId || null,
  })
}
