import { db } from '@/configs/firebase'
import { ndrPathStore } from '@/firestoreQueries/utils/pathStore'
import { deleteDoc, doc } from 'firebase/firestore'

type DeleteArgs = {
  organizationId: string
  commentId: string
}

export async function deleteComment({ organizationId, commentId }: DeleteArgs): Promise<void> {
  const path = ndrPathStore.oneComment({ organizationId, commentId })
  const commentRef = doc(db, path)
  await deleteDoc(commentRef)
}
