import { ndrPathStore } from '@/firestoreQueries/utils/pathStore'
import {
  QueryDocumentSnapshot,
  collection,
  Query,
  query,
  limit,
  startAfter,
  getDocs,
  orderBy,
} from 'firebase/firestore'
import { db } from '@/configs/firebase'
import { IComment } from '@/components/Comments/Comments'

type FetchArgs = {
  organizationId: string
  entityId: string
  pageSize?: number
  lastDoc?: QueryDocumentSnapshot
}

export async function fetchComments({ organizationId, entityId, pageSize = 20, lastDoc }: FetchArgs): Promise<{
  comments: IComment[]
  lastDoc: QueryDocumentSnapshot | null
  hasMore: boolean
}> {
  const path = ndrPathStore.comments({ organizationId })
  const ref = collection(db, path)

  let q: Query = query(ref, orderBy('createdAt', 'desc'))
  q = query(q, limit(pageSize + 1))

  if (lastDoc) {
    q = query(q, startAfter(lastDoc))
  }

  const snap = await getDocs(q)
  const docs = snap.docs
  const hasMore = docs.length > pageSize

  const paginatedDocs = hasMore ? docs.slice(0, pageSize) : docs

  const comments = paginatedDocs
    .map((doc) => ({ ...doc.data(), id: doc.id }) as IComment)
    .filter((comment) => comment.entityId === entityId)

  return {
    comments,
    lastDoc: paginatedDocs[paginatedDocs.length - 1] || null,
    hasMore,
  }
}
