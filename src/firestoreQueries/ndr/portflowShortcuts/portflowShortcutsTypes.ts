import { Timestamp } from 'firebase/firestore'

export type PortflowQueryType = {
  key: string
  values: Array<string>
}

export enum PortflowShortcutCategory {
  SAVED_QUERIES = 'Saved Queries',
  LAST_QUERIES = 'Last Queries',
  SUGGESTED_QUERIES = 'Suggested Queries',
  APPLICATIONS = 'Applications',
}

export type PortflowShortcutType = {
  userID: string
  category: PortflowShortcutCategory
  title: string
  subTitile: string
  image: string
  filter: PortflowQueryType[]
  lastPerformed?: Timestamp
}

export type PortflowShortcutsDict = {
  [K in PortflowShortcutCategory]: Array<PortflowShortcutType & { id: string }>
}
