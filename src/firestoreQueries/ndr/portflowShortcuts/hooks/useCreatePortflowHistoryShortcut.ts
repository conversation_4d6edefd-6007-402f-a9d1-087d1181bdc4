import { queryClient } from '@/Providers/ReactQueryProvider'
import { ndrQueryKeyStore } from '@/configs/reactQueryKeyStore'
import { useMutation } from '@tanstack/react-query'
import { PortflowShortcutCategory, PortflowShortcutsDict, PortflowShortcutType } from '../portflowShortcutsTypes'
import createPortflowHistoryShrotcut from '../queries/createPortflowHistoryShortcut'
import { getDateString } from '@/utils/timeConvertors'

type Args = {
  organizationId: string | undefined
  shortcut: PortflowShortcutType
}

export default function useCreatePortflowHistoryShortcut() {
  return useMutation({
    mutationFn: async ({ organizationId, shortcut }: Args) =>
      await createPortflowHistoryShrotcut({ organizationId, shortcut }),
    onSuccess: (shortcut, { organizationId }) => {
      const queryKey = ndrQueryKeyStore.portflowShortcuts(organizationId)
      const prev: PortflowShortcutsDict = queryClient.getQueryData(queryKey) ?? ({} as PortflowShortcutsDict)
      const updated = { ...prev }
      if (!updated[shortcut.category]) {
        updated[shortcut.category] = []
      }
      shortcut.title = getDateString(shortcut.lastPerformed!.toDate())
      updated[shortcut.category].push(shortcut)
      updated[PortflowShortcutCategory.LAST_QUERIES]
        .sort((a, b) => b.lastPerformed!.seconds - a.lastPerformed!.seconds)
        .slice(0, 15)
      queryClient.setQueryData(queryKey, updated)
    },
  })
}
