import { ndrQueryKeyStore } from '@/configs/reactQueryKeyStore'
import { queryClient } from '@/Providers/ReactQueryProvider'
import { useMutation } from '@tanstack/react-query'
import { PortflowShortcutsDict, PortflowShortcutType } from '../portflowShortcutsTypes'
import updatePortflowHistoryShortcut from '../queries/updatePortflowHistoryShortcut'
import cloneDeep from 'lodash/cloneDeep'
import { getDateString } from '@/utils/timeConvertors'

type Args = {
  organizationId: string | undefined
  shortcut: PortflowShortcutType & { id: string }
}

export default function useUpdatePortflowHistoryShortcut() {
  return useMutation({
    mutationFn: async (args: Args) => await updatePortflowHistoryShortcut(args),
    onMutate: ({ organizationId, shortcut }) => {
      const queryKey = ndrQueryKeyStore.portflowShortcuts(organizationId)
      const prev: PortflowShortcutsDict = queryClient.getQueryData(queryKey) ?? ({} as PortflowShortcutsDict)
      const updated = cloneDeep(prev)
      if (!updated[shortcut.category]) return

      shortcut.title = getDateString(shortcut.lastPerformed!.toDate())
      updated[shortcut.category] = updated[shortcut.category].map((sh) => {
        if (sh.id === shortcut.id) {
          return shortcut
        }
        return sh
      })

      if (updated['Last Queries']) {
        updated['Last Queries'].sort((a, b) => b.lastPerformed!.seconds - a.lastPerformed!.seconds)
      }

      queryClient.setQueryData(queryKey, updated)
      return {
        rollback: () => queryClient.setQueryData(queryKey, prev),
      }
    },
    onError: (_, __, context) => {
      if (context) context.rollback()
    },
  })
}
