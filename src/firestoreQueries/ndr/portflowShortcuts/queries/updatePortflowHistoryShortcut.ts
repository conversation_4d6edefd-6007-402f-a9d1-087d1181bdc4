import { db } from '@/configs/firebase'
import { ndrPathStore } from '@/firestoreQueries/utils/pathStore'
import { captureException } from '@sentry/react'
import { doc, setDoc } from 'firebase/firestore'
import { PortflowShortcutType } from '../portflowShortcutsTypes'

type Args = {
  organizationId: string | undefined
  shortcut: PortflowShortcutType & { id: string }
}

export default async function updatePortflowHistoryShortcut({ organizationId, shortcut }: Args) {
  try {
    if (!organizationId) throw new Error('organizationId is undefined')
    const path = ndrPathStore.portflowShortcut(organizationId, shortcut.id)
    const ref = doc(db, path)
    await setDoc(ref, shortcut)
    return shortcut
  } catch (error) {
    captureException(error)
    throw error
  }
}
