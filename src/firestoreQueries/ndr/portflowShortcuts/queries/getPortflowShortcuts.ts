import { db } from '@/configs/firebase'
import { ndrPathStore } from '@/firestoreQueries/utils/pathStore'
import { getDateString } from '@/utils/timeConvertors'
import { captureException } from '@sentry/react'
import { collection, getDocs, limit, orderBy, query, where } from 'firebase/firestore'
import { PortflowShortcutCategory, PortflowShortcutsDict, PortflowShortcutType } from '../portflowShortcutsTypes'

export default async function getPortflowShortcuts(organizationId: string | undefined, userID: string): Promise<void> {
  try {
    if (!organizationId) throw new Error('organizationId is undefined')
    const path = ndrPathStore.portflowShortcuts(organizationId)
    const ref = collection(db, path)

    const savedQueries = query(
      ref,
      where('category', '==', PortflowShortcutCategory.SAVED_QUERIES),
      where('userID', '==', userID),
    )

    const lastQueries = query(
      ref,
      where('category', '==', PortflowShortcutCategory.LAST_QUERIES),
      where('userID', '==', userID),
      limit(15),
    )

    const [noHistorySnap, historySnap] = await Promise.all([getDocs(savedQueries), getDocs(lastQueries)])

    const shortcustDocs = [...noHistorySnap.docs, ...historySnap.docs].map(
      (doc) => ({ ...doc.data(), id: doc.id }) as PortflowShortcutType & { id: string },
    )

    const dict = shortcustDocs.reduce((dict, doc) => {
      if (!dict[doc.category]) {
        dict[doc.category] = []
      }
      if (doc.category === PortflowShortcutCategory.LAST_QUERIES && doc.lastPerformed) {
        doc.title = getDateString(doc.lastPerformed.toDate())
      }
      dict[doc.category].push(doc)

      return dict
    }, {} as PortflowShortcutsDict)

    if (dict['Last Queries']) {
      dict['Last Queries'].sort((a, b) => b.lastPerformed!.seconds - a.lastPerformed!.seconds)
    }

    return dict
  } catch (error) {
    captureException(error)
    throw error
  }
}
