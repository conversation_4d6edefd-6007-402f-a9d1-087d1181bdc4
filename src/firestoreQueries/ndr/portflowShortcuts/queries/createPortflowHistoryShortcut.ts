import { db } from '@/configs/firebase'
import { ndrPathStore } from '@/firestoreQueries/utils/pathStore'
import { captureException } from '@sentry/react'
import { addDoc, collection } from 'firebase/firestore'
import { PortflowShortcutType } from '../portflowShortcutsTypes'

type Args = {
  organizationId: string | undefined
  shortcut: PortflowShortcutType
}

export default async function createPortflowHistoryShrotcut({ organizationId, shortcut }: Args) {
  try {
    if (!organizationId) throw new Error('organizationId is undefined')
    const path = ndrPathStore.portflowShortcuts(organizationId)
    const ref = collection(db, path)
    const docRef = await addDoc(ref, shortcut)
    return { ...shortcut, id: docRef.id } as PortflowShortcutType & { id: string }
  } catch (error) {
    captureException(error)
    throw error
  }
}
