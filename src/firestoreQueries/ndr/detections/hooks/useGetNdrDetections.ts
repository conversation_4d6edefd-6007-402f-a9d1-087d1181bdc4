import { db } from '@/configs/firebase'
import { ndrPathStore } from '@/firestoreQueries/utils/pathStore'
import * as Sentry from '@sentry/react'
import { collection, getDocs, Timestamp, query, where, limit, orderBy } from 'firebase/firestore'
import { FirestoreTimeRange } from '@/customHooks/useFirestoreTimeRange'
import { ndrQueryKeyStore } from '@/configs/reactQueryKeyStore'
import { useQuery } from '@tanstack/react-query'

type Args = {
  organizationId: string | undefined
  timestamp?: FirestoreTimeRange
  page?: number
  pageSize?: number
  category?: string
}

export interface Time {
  seconds: number
  nanoseconds: number
}

export interface DetectionItem {
  updatedTime: Timestamp
  controlId: string
  subTitle: string
  title: string
  createdTime: Timestamp
  category: string
  status: string
  severity: string
  controlResults: Record<string, string>[]
  explanation: string
  id: string
}

export interface DetectionsResult {
  detections: DetectionItem[]
  hasMore: boolean
}

export default function useGetNdrDetections({ organizationId, timestamp, page = 1, pageSize = 10, category }: Args) {
  return useQuery({
    queryKey: ndrQueryKeyStore.detections({ organizationId: organizationId!, category }),
    queryFn: async () => getNdrDetections({ organizationId, timestamp, page, pageSize, category }),
    placeholderData: (prev) => prev,
  })
}

export async function getNdrDetections({
  organizationId,
  timestamp,
  page = 1,
  pageSize = 10,
  category,
}: Args): Promise<DetectionsResult> {
  try {
    if (!organizationId) throw new Error('Organization ID is required')

    const path = ndrPathStore.detections({ organizationId })
    const ref = collection(db, path)

    // Request one more item than needed to check if there are more items to load
    const checkPageSize = pageSize + 1

    // TEMPORARY: Try a much simpler query to isolate the issue
    let q
    if (category && category !== 'all') {
      q = query(
        ref,
        where('category', '==', category),
        limit(checkPageSize * page)
      )
    } else {
      q = query(
        ref,
        where('createdTime', '>=', timestamp?.from),
        where('createdTime', '<=', timestamp?.to),
        orderBy('createdTime', 'desc'),
        limit(checkPageSize * page)
      )
    }

    const snap = await getDocs(q)

    const detections: DetectionItem[] = []

    snap.forEach((doc) => {
      const docData = doc.data()
      detections.push({ ...docData, id: doc.id } as DetectionItem)
    })

    // Calculate if there are more items to load
    const hasMore = detections.length > pageSize * page

    return {
      detections: detections.slice(0, pageSize * page),
      hasMore,
    }
  } catch (error) {
    Sentry.captureException(error)
    throw error
  }
}
