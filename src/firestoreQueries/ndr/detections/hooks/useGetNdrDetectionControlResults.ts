import { doc, getDoc } from 'firebase/firestore'
import { db } from '@/configs/firebase'
import { useEffect, useState } from 'react'

interface UseGetNdrDetectionControlResultsProps {
  organizationId: string
  detectionId: string
}

export interface ControlResultsData {
  data: Record<string, any>
}

export const useGetNdrDetectionControlResults = ({
  organizationId,
  detectionId,
}: UseGetNdrDetectionControlResultsProps) => {
  const [data, setData] = useState<ControlResultsData | undefined>()
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    const fetchControlResults = async () => {
      if (!organizationId || !detectionId) {
        setData(undefined)
        setIsLoading(false)
        return
      }

      try {
        setIsLoading(true)
        const controlResultsRef = doc(
          db,
          'organizations',
          organizationId,
          'dashboards',
          'NDR',
          'detections',
          detectionId,
          'controlResults',
          'data'
        )

        const docSnap = await getDoc(controlResultsRef)
        if (docSnap.exists()) {
          setData(docSnap.data() as ControlResultsData)
        } else {
          setData(undefined)
        }
      } catch (err) {
        setError(err as Error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchControlResults()
  }, [organizationId, detectionId])

  return {
    data,
    isLoading,
    error
  }
} 