import { IssueStatus } from '@globalTypes/ndrBenchmarkTypes/issueTypes'
import { useMutation } from '@tanstack/react-query'
import updateNdrIssueStatus from '../queries/updateDetectionStatus'
import { ndrQueryKeyStore } from '@/configs/reactQueryKeyStore'
import { queryClient } from '@/Providers/ReactQueryProvider'
import { FirestoreTimeRange } from '@/customHooks/useFirestoreTimeRange'
import { IssueTypeWithId } from '../../issues/customIssuesTypes'

type Args = {
  organizationId: string | undefined
  detectionId: string
  status: IssueStatus
}

type QueryData = {
  pages: {
    detections: IssueTypeWithId[]
    hasMore: boolean
  }[]
  pageParams: number[]
}

type MutationContext = {
  rollback: () => void
}

export default function ({ timestamp, categoryFilter }: { timestamp: FirestoreTimeRange; categoryFilter: string }) {
  return useMutation<void, Error, Args, MutationContext>({
    mutationFn: async ({ organizationId, detectionId, status }: Args) => {
      updateNdrIssueStatus({ organizationId, detectionId, status })
    },
    onMutate: ({ organizationId, detectionId, status }: Args) => {
      const queryKey = ndrQueryKeyStore.detections({
        organizationId: organizationId!,
        timestamp,
        category: categoryFilter,
      })
      // Get all pages from the cache
      const pages = queryClient.getQueryData<QueryData>(queryKey)

      if (!pages) return { rollback: () => {} }

      // Update detection status in all pages
      const updatedPages: QueryData = {
        ...pages,
        pages: pages.pages.map((page) => ({
          ...page,
          detections: page.detections.map((detection) =>
            detection.id === detectionId ? { ...detection, status } : detection,
          ),
        })),
      }

      // Update the cache with new data
      queryClient.setQueryData<QueryData>(queryKey, updatedPages)

      return {
        rollback: () => queryClient.setQueryData<QueryData>(queryKey, pages),
      }
    },
    onError: (_, __, context) => {
      if (context) context.rollback()
    },
  })
}
