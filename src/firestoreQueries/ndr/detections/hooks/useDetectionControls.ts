import { db } from '@/configs/firebase'
import { collection, doc, getDocs, setDoc, deleteDoc, updateDoc, writeBatch, query, where } from 'firebase/firestore'
import { useMutation, useQuery } from '@tanstack/react-query'
import { queryClient } from '@/Providers/ReactQueryProvider'
import { ndrQueryKeyStore } from '@/configs/reactQueryKeyStore'
import { ndrPathStore } from '@/firestoreQueries/utils/pathStore'
import * as Sentry from '@sentry/react'
import { DetectionControl } from '@globalTypes/ndrBenchmarkTypes'
import { toast } from '@/components/ui'
import ToastComponent from '@/generalComponents/ToastComponent'
import { FirestoreTimeRange } from '@/customHooks/useFirestoreTimeRange'
import { getFirestoreTimestamp } from '@/views/HunterX/DetectionControls/utils/utils'

export function useGetDetectionControls({
  organizationId,
  timestamp,
}: {
  organizationId: string
  timestamp?: FirestoreTimeRange
}) {
  return useQuery({
    queryKey: ndrQueryKeyStore.detectionControls({ organizationId, timestamp }),
    queryFn: async () => getNdrDetectionControls({ organizationId, timestamp }),
  })
}

// Hook to get all detection controls
export async function getNdrDetectionControls({
  organizationId,
  timestamp,
}: {
  organizationId: string
  timestamp?: FirestoreTimeRange
}) {
  try {
    const path = ndrPathStore.detectionControls({ organizationId })
    const ref = collection(db, path)

    let querySnapshot
    if (timestamp && (timestamp.from || timestamp.to)) {
      const queryFilters = []
      if (timestamp.from) {
        queryFilters.push(where('createdAt', '>=', timestamp.from))
      }
      if (timestamp.to) {
        queryFilters.push(where('createdAt', '<=', timestamp.to))
      }

      const queryRef = query(ref, ...queryFilters)
      querySnapshot = await getDocs(queryRef)
    } else {
      querySnapshot = await getDocs(ref)
    }

    const detections: DetectionControl[] = []

    querySnapshot.forEach((doc) => {
      detections.push({ ...doc.data(), id: doc.id } as DetectionControl)
    })
    return detections
  } catch (error) {
    Sentry.captureException(error)
  }
}

// Hook to create a new detection control
export function useCreateDetectionControl(organizationId: string | undefined) {
  return useMutation({
    mutationFn: async (control: Omit<DetectionControl, 'id' | 'createdAt' | 'updatedAt'>) => {
      if (!organizationId) throw new Error('Organization ID is required')

      const path = ndrPathStore.detectionControls({ organizationId })
      const ref = collection(db, path)
      const newControlRef = doc(ref)

      const newControl: DetectionControl = {
        ...control,
        id: newControlRef.id,
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      await setDoc(newControlRef, newControl)
      return newControl
    },
    onMutate: async (newControl) => {
      if (!organizationId) return

      // Cancel any outgoing refetches
      await queryClient.cancelQueries({
        queryKey: ndrQueryKeyStore.detectionControls({ organizationId }),
      })

      // Get all active queries that match the detection controls pattern
      const queries = queryClient.getQueriesData<DetectionControl[]>({
        queryKey: [ndrQueryKeyStore.detectionControls({ organizationId })[0]],
      })

      // Store previous data for all queries
      const previousQueriesData = new Map(queries)

      // Create optimistic control with temporary ID
      const optimisticControl: DetectionControl = {
        ...newControl,
        id: `temp-${Date.now()}`,
        createdAt: new Date(),
        updatedAt: new Date(),
      } as DetectionControl

      // Update all matching queries with optimistic data
      for (const [queryKey, oldData] of queries) {
        if (!oldData) continue
        queryClient.setQueryData(queryKey, [...oldData, optimisticControl])
      }

      return { previousQueriesData }
    },
    onError: (error, newControl, context) => {
      if (!organizationId || !context?.previousQueriesData) return

      // Restore all queries to their previous state
      for (const [queryKey, data] of context.previousQueriesData.entries()) {
        queryClient.setQueryData(queryKey, data)
      }

      toast.push(
        ToastComponent({
          title: 'Error Creating Control',
          message: error instanceof Error ? error.message : 'An error occurred while creating the detection control',
          type: 'danger',
        }),
      )
    },
    onSuccess: (newControl) => {
      if (!organizationId) return

      // Get all active queries that match the detection controls pattern
      const queries = queryClient.getQueriesData<DetectionControl[]>({
        queryKey: [ndrQueryKeyStore.detectionControls({ organizationId })[0]],
      })

      // Update all matching queries with the real control data
      for (const [queryKey, oldData] of queries) {
        if (!oldData) continue
        
        // Replace the temporary control with the real one
        const newData = oldData.map(control =>
          control.id.startsWith('temp-') ? newControl : control
        )
        
        queryClient.setQueryData(queryKey, newData)
      }

      toast.push(
        ToastComponent({
          title: 'Control created successfully',
          type: 'success',
        }),
      )
    },
    onSettled: () => {
      if (!organizationId) return

      // Always refetch after error or success to ensure we have the correct data
      queryClient.invalidateQueries({
        queryKey: ndrQueryKeyStore.detectionControls({ organizationId }),
      })
    },
  })
}

// Hook to update an existing detection control
export function useUpdateDetectionControl({
  organizationId,
  onError,
}: {
  organizationId: string | undefined
  onError: (data: DetectionControl) => void
}) {
  return useMutation({
    mutationFn: async (control: DetectionControl) => {
      try {
        if (!organizationId) throw new Error('Organization ID is required')

        const controlRef = doc(db, ndrPathStore.oneDetectionControl({ organizationId, controlId: control.id }))

        const updatedControl: DetectionControl = {
          ...control,
          updatedAt: getFirestoreTimestamp(new Date()),
        }

        await updateDoc(controlRef, updatedControl)
        return updatedControl
      } catch (e) {
        Sentry.captureException(e)
        throw e
      }
    },
    onSuccess: (updatedControl) => {
      toast.push(
        ToastComponent({
          title: 'Control updated successfully',
          type: 'success',
        }),
      )
    },
    onMutate: async (newControl) => {
      if (!organizationId) return

      // Cancel any outgoing refetches
      await queryClient.cancelQueries({
        queryKey: ndrQueryKeyStore.detectionControls({ organizationId }),
      })

      // Get all active queries to update them all with the optimistic change
      // This handles different timestamp filters that might be used in useGetDetectionControls
      const queries = queryClient.getQueriesData<DetectionControl[]>({
        queryKey: [ndrQueryKeyStore.detectionControls({ organizationId })[0]],
      })

      const previousQueriesData = new Map(queries)

      // Create optimistically updated control
      const optimisticControl: DetectionControl = {
        ...newControl,
        updatedAt: getFirestoreTimestamp(new Date()),
      }

      // Update all related queries in the cache
      for (const [queryKey, oldData] of queries) {
        if (!oldData) continue

        const newData = oldData.map((control) => (control.id === optimisticControl.id ? optimisticControl : control))

        queryClient.setQueryData(queryKey, newData)
      }

      return { previousQueriesData }
    },
    onError: (error: Error, newControl, context) => {
      if (!organizationId) return

      // Restore all queries that were optimistically updated
      if (context?.previousQueriesData) {
        for (const [queryKey, data] of context.previousQueriesData.entries()) {
          queryClient.setQueryData(queryKey, data)
        }
      }

      onError(newControl)

      toast.push(
        ToastComponent({
          title: 'Error Updating Control',
          message: error.message ?? 'An error occurred while updating the detection control',
          type: 'danger',
        }),
      )
    },
    onSettled: () => {
      if (!organizationId) return

      // Always refetch after error or success to ensure we have the correct data
      queryClient.invalidateQueries({
        queryKey: ndrQueryKeyStore.detectionControls({ organizationId }),
      })
    },
  })
}

export function useBulkToggleDetectionControls(organizationId: string | undefined) {
  return useMutation({
    mutationFn: async ({
      controlIds,
      update,
    }: {
      controlIds: string[]
      update: Record<keyof DetectionControl, boolean>
    }) => {
      if (!organizationId) throw new Error('Organization ID is required')

      const batch = writeBatch(db)

      controlIds.forEach((controlId) => {
        const controlRef = doc(db, ndrPathStore.oneDetectionControl({ organizationId, controlId }))
        batch.update(controlRef, {
          ...update,
          updatedAt: new Date(),
        })
      })

      await batch.commit()

      return { controlIds, update }
    },
    onSuccess: () => {
      if (organizationId) {
        queryClient.invalidateQueries({
          queryKey: ndrQueryKeyStore.detectionControls({ organizationId }),
        })
      }

      toast.push(
        ToastComponent({
          title: 'Controls Updated',
          type: 'success',
        }),
      )
    },
    onMutate: async ({ controlIds, update }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({
        queryKey: ndrQueryKeyStore.detectionControls({ organizationId: organizationId! }),
      })

      // Snapshot the previous value
      const previousControls = queryClient.getQueryData(
        ndrQueryKeyStore.detectionControls({ organizationId: organizationId! }),
      )

      // Get all active queries to update them all with the optimistic change
      // This handles different timestamp filters that might be used in useGetDetectionControls
      const queries = queryClient.getQueriesData<DetectionControl[]>({
        queryKey: [ndrQueryKeyStore.detectionControls({ organizationId: organizationId! })[0]],
      })

      // Update all related queries in the cache
      for (const [queryKey, data] of queries) {
        if (!data) continue

        queryClient.setQueryData(
          queryKey,
          data.map((control) =>
            controlIds.includes(control.id) ? { ...control, ...update, updatedAt: new Date() } : control,
          ),
        )
      }

      // Return a context object with the snapshotted value
      return { previousControls, queries }
    },
    onError: (error, variables, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousControls) {
        queryClient.setQueryData(
          ndrQueryKeyStore.detectionControls({ organizationId: organizationId! }),
          context.previousControls,
        )

        // Restore all queries that were optimistically updated
        if (context.queries) {
          for (const [queryKey, data] of context.queries) {
            queryClient.setQueryData(queryKey, data)
          }
        }
      }

      toast.push(
        ToastComponent({
          title: 'Error',
          message: error instanceof Error ? error.message : 'Failed to update detection control',
          type: 'danger',
        }),
      )
    },
  })
}

// Hook to delete a detection control
export function useDeleteDetectionControl(organizationId: string | undefined) {
  return useMutation({
    mutationFn: async (controlId: string) => {
      try {
        if (!organizationId) throw new Error('Organization ID is required')

        const controlRef = doc(db, ndrPathStore.oneDetectionControl({ organizationId, controlId }))
        const metricRef = doc(db, ndrPathStore.oneDetectionControlMetric({ organizationId, metricId: controlId }))

        // Delete both the control and its metric
        await Promise.all([deleteDoc(controlRef), deleteDoc(metricRef)])

        return controlId
      } catch (e) {
        Sentry.captureException(e)
        throw e
      }
    },
    onSuccess: () => {
      toast.push(
        ToastComponent({
          title: 'Control deleted successfully',
          type: 'success',
        }),
      )
    },
    onMutate: async (controlId) => {
      if (!organizationId) return

      // Cancel any outgoing refetches
      await Promise.all([
        queryClient.cancelQueries({
          queryKey: ndrQueryKeyStore.detectionControls({ organizationId }),
        }),
        queryClient.cancelQueries({
          queryKey: ndrQueryKeyStore.detectionControlMetrics({ organizationId }),
        }),
      ])

      // Get all active queries to update them all with the optimistic change
      const controlQueries = queryClient.getQueriesData<DetectionControl[]>({
        queryKey: [ndrQueryKeyStore.detectionControls({ organizationId })[0]],
      })

      const metricQueries = queryClient.getQueriesData<Record<string, number>>({
        queryKey: [ndrQueryKeyStore.detectionControlMetrics({ organizationId })[0]],
      })

      const previousData = {
        controls: new Map(controlQueries),
        metrics: new Map(metricQueries),
      }

      // Update all related control queries in the cache
      for (const [queryKey, oldData] of controlQueries) {
        if (!oldData) continue

        const newData = oldData.filter(control => control.id !== controlId)
        queryClient.setQueryData(queryKey, newData)
      }

      // Update all related metric queries in the cache
      for (const [queryKey, oldData] of metricQueries) {
        if (!oldData) continue

        const newData = { ...oldData }
        delete newData[controlId]
        queryClient.setQueryData(queryKey, newData)
      }

      return previousData
    },
    onError: (error: Error, controlId, context) => {
      if (!organizationId) return

      // Restore all queries that were optimistically updated
      if (context) {
        // Restore control queries
        for (const [queryKey, data] of context.controls.entries()) {
          queryClient.setQueryData(queryKey, data)
        }

        // Restore metric queries
        for (const [queryKey, data] of context.metrics.entries()) {
          queryClient.setQueryData(queryKey, data)
        }
      }

      toast.push(
        ToastComponent({
          title: 'Error Deleting Control',
          message: error.message ?? 'An error occurred while deleting the detection control',
          type: 'danger',
        }),
      )
    },
    onSettled: () => {
      if (!organizationId) return

      // Always refetch after error or success to ensure we have the correct data
      queryClient.invalidateQueries({
        queryKey: ndrQueryKeyStore.detectionControls({ organizationId }),
      })
      queryClient.invalidateQueries({
        queryKey: ndrQueryKeyStore.detectionControlMetrics({ organizationId }),
      })
    },
  })
}

// Hook to get detection control metrics
export function useGetDetectionControlMetrics(organizationId: string | undefined) {
  return useQuery({
    queryKey: ndrQueryKeyStore.detectionControlMetrics({ organizationId: organizationId! }),
    queryFn: async () => {
      if (!organizationId) throw new Error('Organization ID is required')
      const path = ndrPathStore.detectionControlMetrics({ organizationId })
      const ref = collection(db, path)
      const snap = await getDocs(ref)

      const metrics: Record<string, number> = {}
      snap.forEach((doc) => {
        metrics[doc.id] = doc.data().hitCount || 0
      })
      return metrics
    },
  })
}
