import { useQuery } from '@tanstack/react-query'
import { ndrQueryKeyStore } from '@/configs/reactQueryKeyStore'
import { db } from '@/configs/firebase'
import { doc, getDoc } from 'firebase/firestore'
import { ndrPathStore } from '@/firestoreQueries/utils/pathStore'
import * as Sentry from '@sentry/react'

type Args = {
  organizationId: string | undefined
}

export interface CategoryCount {
  name: string
  count: number
}

export default function useGetNdrDetectionCategories({ organizationId }: Args) {
  return useQuery({
    queryKey: ndrQueryKeyStore.detectionCategories({ organizationId: organizationId! }),
    queryFn: async () => {
      try {
        if (!organizationId) throw new Error('Organization ID is required')

        const path = ndrPathStore.detectionCategories({ organizationId })
        const ref = doc(db, path)
        const span = await getDoc(ref)
        const data = span.data() as Record<string, number>
        // Convert to array and sort by count descending, then by name
        const categories = Object.entries(data)
          .map(([name, count]): CategoryCount => ({ name, count }))
          .sort((a, b) => b.count - a.count || a.name.localeCompare(b.name))

        return categories
      } catch (error) {
        Sentry.captureException(error)
        return []
      }
    },
    staleTime: 1000 * 60 * 5, // Cache for 5 minutes
  })
}
