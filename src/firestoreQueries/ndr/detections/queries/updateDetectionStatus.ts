import { db } from '@/configs/firebase'
import { ndrPathStore } from '@/firestoreQueries/utils/pathStore'
import { IssueStatus } from '@globalTypes/ndrBenchmarkTypes/issueTypes'
import * as Sentry from '@sentry/react'
import { doc, updateDoc } from 'firebase/firestore'

type Args = {
  detectionId: string
  organizationId: string | undefined
  status: IssueStatus
}

export default async ({ detectionId, organizationId, status }: Args) => {
  try {
    if (!organizationId) throw new Error('Missing organizationId')
    const path = ndrPathStore.oneDetection({
      organizationId,
      detectionId,
    })

    const ref = doc(db, path)

    await updateDoc(ref, { status })
  } catch (error) {
    Sentry.captureException(error)
    throw error
  }
}
