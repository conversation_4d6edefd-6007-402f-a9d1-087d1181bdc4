import { ndrQueryKeyStore } from '@/configs/reactQueryKeyStore'
import { queryClient } from '@/Providers/ReactQueryProvider'
import { useMutation } from '@tanstack/react-query'
import cloneDeep from 'lodash/cloneDeep'
import { PortflowShortcutsDict, PortflowShortcutType } from '../../portflowShortcuts/portflowShortcutsTypes'
import createSavedPortflowQueries from '../queries/createSavedPortflowQueries'

type Args = {
  organizationId: string | undefined
  savedQuery: PortflowShortcutType
}

export default function useCreateSavedPortflowQuery() {
  return useMutation({
    mutationFn: async ({ organizationId, savedQuery }: Args) =>
      await createSavedPortflowQueries({ organizationId, shortcut: savedQuery }),
    onSuccess: (query, { organizationId }) => {
      const queryKey = ndrQueryKeyStore.portflowShortcuts(organizationId)
      const prev: PortflowShortcutsDict = queryClient.getQueryData(queryKey) ?? ({} as PortflowShortcutsDict)

      const updated = cloneDeep(prev)
      if (!updated[query.category]) {
        updated[query.category] = []
      }

      updated[query.category].push(query)

      queryClient.setQueryData(queryKey, updated)
    },
  })
}
