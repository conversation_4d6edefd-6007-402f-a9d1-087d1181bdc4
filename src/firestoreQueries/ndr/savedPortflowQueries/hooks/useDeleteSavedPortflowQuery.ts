import { ndrQueryKeyStore } from '@/configs/reactQueryKeyStore'
import { queryClient } from '@/Providers/ReactQueryProvider'
import { useMutation } from '@tanstack/react-query'
import { PortflowShortcutCategory, PortflowShortcutsDict } from '../../portflowShortcuts/portflowShortcutsTypes'
import deleteSavedQuery from '../queries/deleteSavedQuery'

type Args = {
  organizationId: string | undefined
  category: PortflowShortcutCategory
  id: string
}

export default function useDeleteSavedPortflowQuery() {
  return useMutation({
    mutationFn: async ({ id, organizationId }: Args) => await deleteSavedQuery({ id, organizationId }),
    onMutate: ({ id, organizationId, category }) => {
      const queryKey = ndrQueryKeyStore.portflowShortcuts(organizationId)
      const prev: PortflowShortcutsDict = queryClient.getQueryData(queryKey) ?? ({} as PortflowShortcutsDict)
      const updated = { ...prev }
      updated[category] = updated[category].filter((query) => query.id !== id)
      queryClient.setQueryData(queryKey, updated)

      function rollback() {
        queryClient.setQueryData(queryKey, prev)
      }

      return { rollback }
    },
    onError: (_, __, context) => {
      if (context) context.rollback()
    },
  })
}
