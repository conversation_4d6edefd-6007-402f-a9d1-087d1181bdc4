import { queryClient } from '@/Providers/ReactQueryProvider'
import { ndrQueryKeyStore } from '@/configs/reactQueryKeyStore'
import { useMutation } from '@tanstack/react-query'
import { PortflowShortcutsDict, PortflowShortcutType } from '../../portflowShortcuts/portflowShortcutsTypes'
import updateSavedQuery from '../queries/updateSavedQuery'

type Args = {
  organizationId: string | undefined
  queryId: string
  query: PortflowShortcutType
}

export default function useUpdateSavedQuery() {
  return useMutation({
    mutationFn: async ({ organizationId, queryId, query }: Args) =>
      await updateSavedQuery({ queryId, query, organizationId }),
    onMutate: ({ organizationId, query, queryId }) => {
      const queryKey = ndrQueryKeyStore.portflowShortcuts(organizationId)
      const prev: PortflowShortcutsDict = queryClient.getQueryData(queryKey) ?? ({} as PortflowShortcutsDict)

      const updated = { ...prev }
      updated[query.category] = updated[query.category].map((item) => {
        if (item.id === queryId) {
          return { ...query, id: queryId }
        }
        return item
      })

      queryClient.setQueryData(queryKey, updated)

      return {
        rollback: () => queryClient.setQueryData(queryKey, prev),
      }
    },
    onError: (_, __, context) => {
      if (context) context.rollback()
    },
  })
}
