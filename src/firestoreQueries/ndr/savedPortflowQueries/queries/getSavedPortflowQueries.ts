import { db } from '@/configs/firebase'
import { ndrPathStore } from '@/firestoreQueries/utils/pathStore'
import { captureException } from '@sentry/react'
import { collection, getDocs } from 'firebase/firestore'
import { SavedPortflowQueryWithId } from '../savedPortflowQueriesTypes'

export default async function getSavedPortflowQueries(organizationId: string | undefined) {
  try {
    if (!organizationId) throw new Error('organizationId is undefined')
    const path = ndrPathStore.savedPortflowQueries(organizationId)
    const ref = collection(db, path)
    const snap = await getDocs(ref)
    return snap.docs.map((doc) => ({ ...doc.data(), id: doc.id }) as SavedPortflowQueryWithId)
  } catch (error) {
    captureException(error)
    throw error
  }
}
