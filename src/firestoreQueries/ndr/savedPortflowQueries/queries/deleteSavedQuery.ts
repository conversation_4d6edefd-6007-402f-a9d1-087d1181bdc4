import { db } from '@/configs/firebase'
import { ndrPathStore } from '@/firestoreQueries/utils/pathStore'
import { captureException } from '@sentry/react'
import { deleteDoc, doc } from 'firebase/firestore'

type Args = {
  organizationId: string | undefined
  id: string
}

export default async function deleteSavedQuery({ id, organizationId }: Args) {
  try {
    if (!organizationId) throw new Error('organizationId is undefined')
    // TODO we have permission issue for this firebase method -> https://github.com/orgs/port0-io/projects/21/views/2?filterQuery=qa&pane=issue&itemId=84374164
    const path = ndrPathStore.portflowShortcut(organizationId, id)
    const ref = doc(db, path)
    await deleteDoc(ref)
  } catch (error) {
    captureException(error)
    throw error
  }
}
