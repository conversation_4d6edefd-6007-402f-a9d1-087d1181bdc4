import { db } from '@/configs/firebase'
import { ndrPathStore } from '@/firestoreQueries/utils/pathStore'
import { captureException } from '@sentry/react'
import { doc, updateDoc } from 'firebase/firestore'
import { PortflowShortcutType } from '../../portflowShortcuts/portflowShortcutsTypes'

type Args = {
  organizationId: string | undefined
  queryId: string
  query: PortflowShortcutType
}

export default async function updateSavedQuery({ query, queryId, organizationId }: Args) {
  try {
    if (!organizationId) throw new Error('organizationId is undefined')
    const path = ndrPathStore.portflowShortcut(organizationId, queryId)
    const ref = doc(db, path)
    await updateDoc(ref, query)
    return { ...query, id: queryId } as PortflowShortcutType & { id: string }
  } catch (error) {
    captureException(error)
    throw error
  }
}
