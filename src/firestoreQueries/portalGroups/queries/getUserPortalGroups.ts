import { db } from '@/configs/firebase'
import { getQueryConstraints } from '@/customUtils/getGroupsQueryConstraints'
import getUserGroups from '@/firestoreQueries/groups/queries/getUserGroups'
import * as Sentry from '@sentry/react'
import { collection, getDocs, query } from 'firebase/firestore'
import { accessPathStore } from '../../utils/pathStore'
import { PortalGroupType } from '../portalGroupsTypes'

type Args = {
  organizationId: string | undefined
  userId: string
}

export default async ({ organizationId, userId }: Args) => {
  try {
    if (!organizationId) throw new Error('Missing OrganizationId')

    if (!userId) throw new Error('userId is required')

    const groupsIds = (await getUserGroups({ organizationId, userId })).map((group) => group.id)

    const path = accessPathStore.portalGroups({ organizationId })
    const collectionRef = collection(db, path)

    const constraints = getQueryConstraints(groupsIds, userId)

    const q = query(collectionRef, constraints)

    const querySnapshot = await getDocs(q)

    const portalGroups: Array<PortalGroupType> = []

    querySnapshot.forEach((doc) => {
      const resourceData = doc.data() as PortalGroupType
      portalGroups.push({
        ...resourceData,
        id: doc.id,
      })
    })

    return portalGroups
  } catch (error) {
    Sentry.captureException(error)
    throw error
  }
}
