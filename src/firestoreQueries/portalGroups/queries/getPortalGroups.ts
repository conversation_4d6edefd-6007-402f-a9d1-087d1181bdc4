import { db } from '@/configs/firebase'
import { getQueryConstraints } from '@/customUtils/getGroupsQueryConstraints'
import getGroups from '@/firestoreQueries/groups/queries/getGroups'
import { accessPathStore } from '@/firestoreQueries/utils/pathStore'
import * as Sentry from '@sentry/react'
import { getAuth } from 'firebase/auth'
import { collection, getDocs, query } from 'firebase/firestore'
import { PortalGroupType } from '../portalGroupsTypes'

type Args = {
  organizationId: string | undefined
  isAdmin: boolean
}

export default async function getPortalGroups({ isAdmin, organizationId }: Args) {
  try {
    if (!organizationId) throw new Error('Organization ID is required')

    const userId = getAuth().currentUser?.uid
    if (!userId) throw new Error('userId is required')
    const groupsIds = (await getGroups({ organizationId })).map((g) => g.id)

    const path = accessPathStore.portalGroups({ organizationId })
    const collectionRef = collection(db, path)

    const constraints = getQueryConstraints(groupsIds, userId)
    let q = query(collectionRef, constraints)

    if (isAdmin) {
      q = query(collectionRef)
    }

    const querySnapshot = await getDocs(q)

    const portalGroupsData: Array<PortalGroupType> = []

    querySnapshot.forEach((doc) => {
      const resourceData = doc.data() as PortalGroupType
      portalGroupsData.push({
        ...resourceData,
        id: doc.id,
      })
    })

    return portalGroupsData
  } catch (error) {
    Sentry.captureException(error)
    throw error
  }
}
