import { db } from '@/configs/firebase'
import { accessPathStore } from '@/firestoreQueries/utils/pathStore'
import * as Sentry from '@sentry/react'
import { addDoc, collection } from 'firebase/firestore'
import { PortalGroupFromDB, PortalGroupType } from '../portalGroupsTypes'

type Args = {
  organizationId: string | undefined
  portalGroup: PortalGroupFromDB
}

export default async function createPortalGroup({ organizationId, portalGroup }: Args) {
  try {
    if (!organizationId) throw new Error('Organization ID is required')

    const path = accessPathStore.portalGroups({ organizationId })
    const groupsRef = collection(db, path)

    const ref = await addDoc(groupsRef, portalGroup)

    return {
      ...portalGroup,
      id: ref.id,
    } as PortalGroupType
  } catch (error) {
    Sentry.captureException(error)
    throw error
  }
}
