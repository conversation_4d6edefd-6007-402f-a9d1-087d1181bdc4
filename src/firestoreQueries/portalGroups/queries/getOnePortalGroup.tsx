import { db } from '@/configs/firebase'
import { accessPathStore } from '@/firestoreQueries/utils/pathStore'
import { doc, getDoc } from 'firebase/firestore'
import { PortalGroupFromDB, PortalGroupType } from '../portalGroupsTypes'
// import { UndefinedValueError } from '@/utils/customErrors/UndefiendValueError'
import * as Sentry from '@sentry/react'
type Args = {
    portalGroupId: string
    organizationId: string | undefined
}

export default async function ({ organizationId, portalGroupId }: Args) {
    try {
        // if (!organizationId) throw new UndefinedValueError('organizationId')
        if (!organizationId) throw new Error('Missing OrganizationId')

        const path = accessPathStore.onePortalGroup({ organizationId, portalGroupId })
        const ref = doc(db, path)
        const snap = await getDoc(ref)
        const data = snap.data() as PortalGroupFromDB

        return {
            ...data,
            id: snap.id,
        } as PortalGroupType
    } catch (error) {
        Sentry.captureException(error)
        throw error
    }
}
