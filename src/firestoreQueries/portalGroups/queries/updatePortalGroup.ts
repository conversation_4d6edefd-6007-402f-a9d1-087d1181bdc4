import { db } from '@/configs/firebase'
import { accessPathStore } from '@/firestoreQueries/utils/pathStore'
import * as Sentry from '@sentry/react'
import { doc, updateDoc } from 'firebase/firestore'
import { PortalGroupFromDB, PortalGroupType } from '../portalGroupsTypes'
import getOnePortalGroup from './getOnePortalGroup'

type Args = {
  organizationId: string | undefined
  portalGroup: PortalGroupType
}

export default async function ({ organizationId, portalGroup }: Args) {
  try {
    // if (!organizationId) throw new UndefinedValueError('organizationId')
    if (!organizationId) throw new Error('Missing OrganizationId')

    const { id, portalGroupInfo, roles, portalIds } = portalGroup

    const newPortalGroup = {
      roles,
      portalGroupInfo,
      portalIds,
    } as PortalGroupFromDB

    const path = accessPathStore.onePortalGroup({ organizationId, portalGroupId: id })
    const ref = doc(db, path)
    await updateDoc(ref, newPortalGroup as any)

    const updatedDoc = await getOnePortalGroup({ organizationId, portalGroupId: id })

    return updatedDoc
  } catch (error) {
    Sentry.captureException(error)
    throw error
  }
}
