import { db } from '@/configs/firebase'
import { accessPathStore } from '@/firestoreQueries/utils/pathStore'
// import { UndefinedValueError } from '@/utils/customErrors/UndefiendValueError'
import { deleteDoc, doc } from 'firebase/firestore'
import * as Sentry from '@sentry/react'

type Args = {
    organizationId: string | undefined
    portalGroupId: string
}

export default async function ({ organizationId, portalGroupId }: Args) {
    try {
        if (!organizationId) throw new Error('Missing OrganizationId')
        const path = accessPathStore.onePortalGroup({ organizationId, portalGroupId })
        await deleteDoc(doc(db, path))
    } catch (error) {
        Sentry.captureException(error)
        throw error
    }
}
