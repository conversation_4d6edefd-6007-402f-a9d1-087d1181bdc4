import { DefaultRoleType, OwnerType, RolesType } from '../utils/generalTypes'

export interface PortalGroupFromDB {
  portalGroupInfo: PortalGroupInfoType
  portalIds: Array<string>
  roles: RolesType
  defaultRole: DefaultRoleType
}

export interface PortalGroupInfoType {
  portalGroupName: string
  portalGroupDescription: string
  portalGroupEmoji: string
  portalGroupOwner: OwnerType
}
export interface PortalGroupType extends PortalGroupFromDB {
  id: string
}
