import { db } from '@/configs/firebase'
import { accessPathStore } from '../utils/pathStore'
import { collection, getDocs, or, query, where } from 'firebase/firestore'
import { PortalGroupType } from './portalGroupsTypes'
import { captureException } from '@sentry/react'

type Args = {
  organizationId: string | undefined
  groupId: string
}

export default async function ({ organizationId, groupId }: Args) {
  try {
    if (!organizationId) throw new Error('organizationId is undefined')

    const path = accessPathStore.portalGroups({ organizationId })
    const ref = collection(db, path)
    const q = query(
      ref,
      or(where('roles.Viewers', 'array-contains', groupId), where('roles.Editors', 'array-contains', groupId)),
    )

    const snap = await getDocs(q)
    const portalGroups: PortalGroupType[] = []
    snap.forEach((doc) => portalGroups.push({ ...doc.data(), id: doc.id } as PortalGroupType))

    return portalGroups
  } catch (error) {
    captureException(error)
    throw error
  }
}
