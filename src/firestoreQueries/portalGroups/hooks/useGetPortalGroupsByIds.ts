import { accessQueryKeyStore } from '@/configs/reactQueryKeyStore'
import { useQuery } from '@tanstack/react-query'
import getPortalGroupsByIds from '../queries/getPortalGroupsByIds'

type Args = {
  organizationId: string | undefined
  portalGroupsIds: string[]
}

export default function ({ organizationId, portalGroupsIds }: Args) {
  return useQuery({
    queryKey: accessQueryKeyStore.specificPortalGroups({ organizationId: organizationId!, portalGroupsIds }),
    queryFn: async () => await getPortalGroupsByIds({ organizationId, portalGroupsIds }),
  })
}
