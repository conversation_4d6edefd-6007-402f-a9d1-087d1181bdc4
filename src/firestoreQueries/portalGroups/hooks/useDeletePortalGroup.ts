import { useMutation } from '@tanstack/react-query'
import deletePortalGroup from '../queries/deletePortalGroup'
import { accessQueryKeyStore } from '@/configs/reactQueryKeyStore'
import { queryClient } from '@/Providers/ReactQueryProvider'
import { PortalGroupType } from '../portalGroupsTypes'

type Args = {
  organizationId: string | undefined
  portalGroupId: string
}

export default function () {
  return useMutation({
    mutationFn: async ({ organizationId, portalGroupId }: Args) =>
      await deletePortalGroup({ organizationId, portalGroupId }),

    onMutate: ({ organizationId, portalGroupId }) => {
      const allPortalGroupsQueryKey = accessQueryKeyStore.portalGroups({ organizationId: organizationId! })
      const portalGroupQueryKey = accessQueryKeyStore.onePortalGroup({
        organizationId: organizationId!,
        portalGroupId,
      })

      const portalGroup: PortalGroupType = queryClient.getQueryData(portalGroupQueryKey) || ({} as PortalGroupType)

      const prevPortalGroups: PortalGroupType[] = queryClient.getQueryData(allPortalGroupsQueryKey) || []
      const newPortalGroups = prevPortalGroups.filter((portalGroup) => portalGroup.id !== portalGroupId)
      queryClient.setQueryData(allPortalGroupsQueryKey, newPortalGroups)

      function rollback() {
        queryClient.setQueryData(allPortalGroupsQueryKey, prevPortalGroups)
        queryClient.setQueryData(portalGroupQueryKey, portalGroup)
      }
      return {
        rollback,
      }
    },
    onError: (error, _, context) => {
      if (context) context.rollback()
      return error
    },
  })
}
