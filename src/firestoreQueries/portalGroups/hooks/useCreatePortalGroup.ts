import { useMutation } from '@tanstack/react-query'
import { PortalGroupFromDB, PortalGroupType } from '../portalGroupsTypes'
import createPortalGroup from '../queries/createPortalGroup'
import { accessQueryKeyStore } from '@/configs/reactQueryKeyStore'
import { queryClient } from '@/Providers/ReactQueryProvider'

type Args = {
    organizationId: string | undefined
    portalGroup: PortalGroupFromDB
}

export default function () {
    return useMutation({
        mutationFn: async ({ organizationId, portalGroup }: Args) =>
            await createPortalGroup({ organizationId, portalGroup }),
        onSuccess: (portalGroup, { organizationId }) => {
            const queryKey = accessQueryKeyStore.portalGroups({ organizationId: organizationId! })
            const prev: PortalGroupType[] = queryClient.getQueryData(queryKey) || []

            queryClient.setQueryData(queryKey, [...prev, portalGroup])
        },
        onError: (error) => {
            return error
        },
    })
}
