import { accessQueryKeyStore } from '@/configs/reactQueryKeyStore'
import { useQuery } from '@tanstack/react-query'
import getOnePortalGroup from '../queries/getOnePortalGroup'
import refetchOnMountFn from '@/firestoreQueries/utils/refetchOnMountFn'

type Args = {
    organizationId: string | undefined
    portalGroupId: string
}

export default function ({ organizationId, portalGroupId }: Args) {
    return useQuery({
        queryKey: accessQueryKeyStore.onePortalGroup({ organizationId: organizationId!, portalGroupId }),
        queryFn: async () => await getOnePortalGroup({ organizationId, portalGroupId }),
        refetchOnMount: () =>
            refetchOnMountFn(accessQueryKeyStore.onePortalGroup({ organizationId: organizationId!, portalGroupId })),
    })
}
