import { accessQueryKeyStore } from '@/configs/reactQueryKeyStore'
import { useQuery } from '@tanstack/react-query'
import getPortalGroups from '../queries/getPortalGroups'

type Args = {
    organizationId: string | undefined
    isAdmin: boolean
}

export default ({ organizationId, isAdmin }: Args) => {
    return useQuery({
        queryKey: accessQueryKeyStore.portalGroups({ organizationId: organizationId! }),
        queryFn: async () => await getPortalGroups({ organizationId, isAdmin }),
    })
}
