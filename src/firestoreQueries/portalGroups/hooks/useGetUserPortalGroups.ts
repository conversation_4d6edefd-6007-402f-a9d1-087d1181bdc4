import refetchOnMountFn from '@/firestoreQueries/utils/refetchOnMountFn'
import { useQuery } from '@tanstack/react-query'
import getUserPortalGroups from '../queries/getUserPortalGroups'
import { PortalGroupType } from '../portalGroupsTypes'

type Args = {
  organizationId: string | undefined
  userId: string
}

export default function ({ organizationId, userId }: Args) {
  const queryKey = ['userPortalGroups', userId]
  return useQuery({
    queryKey,
    queryFn: async () => {
      return await getUserPortalGroups({
        organizationId,
        userId,
      })
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnMount: () => refetchOnMountFn<PortalGroupType>(queryKey),
  })
}
