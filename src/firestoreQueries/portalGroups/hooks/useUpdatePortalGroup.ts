import { useMutation } from '@tanstack/react-query'
import { PortalGroupType } from '../portalGroupsTypes'
import updatePortalGroup from '../queries/updatePortalGroup'
import { accessQueryKeyStore } from '@/configs/reactQueryKeyStore'
import { queryClient } from '@/Providers/ReactQueryProvider'

type Args = {
  portalGroup: PortalGroupType
  organizationId: string | undefined
}

export default function () {
  return useMutation({
    mutationFn: async ({ portalGroup, organizationId }: Args) =>
      await updatePortalGroup({ portalGroup, organizationId }),
    onMutate: ({ portalGroup, organizationId }) => {
      const portalGroupQueryKey = accessQueryKeyStore.onePortalGroup({
        portalGroupId: portalGroup.id,
        organizationId: organizationId!,
      })

      const prevPortalGroup: PortalGroupType = queryClient.getQueryData(portalGroupQueryKey) || ({} as PortalGroupType)

      queryClient.setQueryData(portalGroupQueryKey, portalGroup)

      const allPortalGroupsQueryKey = accessQueryKeyStore.portalGroups({ organizationId: organizationId! })
      const prevPortalGroups: PortalGroupType[] = queryClient.getQueryData(allPortalGroupsQueryKey) || []

      const newPortalGroups = prevPortalGroups.map((p) => {
        if (p.id === portalGroup.id) {
          return portalGroup
        }
        return p
      })

      queryClient.setQueryData(allPortalGroupsQueryKey, newPortalGroups)

      function rollback() {
        queryClient.setQueryData(portalGroupQueryKey, prevPortalGroup)
        queryClient.setQueryData(allPortalGroupsQueryKey, prevPortalGroups)
      }

      return { rollback, portalGroupQueryKey, allPortalGroupsQueryKey }
    },
    onSuccess: (_, __, { allPortalGroupsQueryKey, portalGroupQueryKey }) => {
      queryClient.invalidateQueries({ queryKey: allPortalGroupsQueryKey })
      queryClient.invalidateQueries({ queryKey: portalGroupQueryKey })
    },
    onError: (error, _, context) => {
      if (context) context.rollback()
      return error
    },
  })
}
