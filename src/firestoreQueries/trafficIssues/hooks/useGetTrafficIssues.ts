import { useQuery } from '@tanstack/react-query'
import getTrafficIssues from '../queries/getTrafficIssues'
import { Timestamp } from 'firebase/firestore'

type Args = {
  organizationId: string | undefined
  timestamp: {
    from: Timestamp
    to: Timestamp
  }
}

export default ({ organizationId, timestamp }: Args) => {
  return useQuery({
    queryKey: ['trafficIssues', organizationId],
    queryFn: async () => getTrafficIssues({ organizationId, timestamp }),
    staleTime: 1000 * 60 * 5,
    gcTime: 1000 * 60 * 1,
    initialData: [],
  })
}
