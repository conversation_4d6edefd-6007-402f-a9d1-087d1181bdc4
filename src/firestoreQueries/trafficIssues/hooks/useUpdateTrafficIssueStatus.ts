import { queryClient } from '@/Providers/ReactQueryProvider'
import { useMutation } from '@tanstack/react-query'
import updateTrafficIssueStatus from '../queries/updateTrafficIssueStatus'
import { TrafficIssue, TrafficIssueStatus } from '../trafficIssuesTypes'

type Args = {
    organizationId: string | undefined
    trafficIssueId: string
    status: TrafficIssueStatus
}

export default () => {
    return useMutation({
        mutationFn: async ({ organizationId, trafficIssueId, status }: Args) =>
            updateTrafficIssueStatus({
                organizationId,
                trafficIssueId,
                status,
            }),
        onMutate: ({ organizationId, trafficIssueId, status }: Args) => {
            const queryKey = ['trafficIssues', organizationId]
            const prev: TrafficIssue[] = queryClient.getQueryData(queryKey) ?? []

            const updated = prev.map((issue) => {
                if (issue.id === trafficIssueId) {
                    return { ...issue, status }
                }
                return issue
            })

            queryClient.setQueryData(queryKey, updated)

            return {
                rollback: () => queryClient.setQueryData(queryKey, prev),
            }
        },

        onError: (error, __, context) => {
            if (context) {
                context.rollback()
            }
        },
    })
}
