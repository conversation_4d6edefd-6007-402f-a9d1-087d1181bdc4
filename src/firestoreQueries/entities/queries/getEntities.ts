import { db } from '@/configs/firebase'
import { EntityTypeWithId } from '@/firestoreQueries/ndr/entities/customEntitiesTypes'
import * as Sentry from '@sentry/react'
import { collection, getDocs, getDocsFromCache } from 'firebase/firestore'
import { ndrPathStore } from '../../utils/pathStore'

type Args = {
  organizationId: string | undefined
}

export default async function getEntities({ organizationId }: Args): Promise<EntityTypeWithId[]> {
  try {
    if (!organizationId) {
      throw new Error('Missing Organization')
    }

    const path = ndrPathStore.entities({ organizationId })
    const collectionRef = collection(db, path)

    // Try to get data from the cache first
    let cachedData: EntityTypeWithId[] = []
    try {
      const cacheSnapshot = await getDocsFromCache(collectionRef)

      cachedData = cacheSnapshot.docs.map((doc) => ({ ...doc.data(), firestoreId: doc.id }) as EntityTypeWithId)
      if (cachedData.length === 0) {
        throw new Error('No cached data available.')
      }
    } catch (error) {
      try {
        const querySnapshot = await getDocs(collectionRef)
        const entityData: Array<EntityTypeWithId> = []
        querySnapshot.forEach((doc) => {
          entityData.push({ ...doc.data(), firestoreId: doc.id } as EntityTypeWithId)
        })

        return entityData
      } catch (error) {
        Sentry.captureException(error)
        throw error
      }
    }

    // Return the cached data immediately
    return cachedData
  } catch (error) {
    Sentry.captureException(error)
    throw error
  }
}
