import { Download } from 'lucide-react'
import { useOrganization } from '@clerk/clerk-react'
import { getRecentTraffics } from '@/services/BigQueryService'
import { generateQueryRules } from '../../views/HunterX/Dashboard/utils'
import * as Sentry from '@sentry/react'
import { createProgressTracker } from '../../views/HunterX/Dashboard/utils/progress'
import ToastComponent from '@/generalComponents/ToastComponent'
import formatNumber from '@/utils/formatNumber'
import useGraphConfig from '@/views/NDR/PortflowPage/hooks/useGraphConfig'
import Tooltip from '@/components/ui/Tooltip'
import { useExportProgress } from '../../views/HunterX/Dashboard/store/useExportProgress'
import ExportProgress from '../ExportProgress/ExportProgress'
import { useRef } from 'react'
import { convertToCSV, downloadCSV, generateFileName } from '@/customUtils/exportCSVUtils'

const PAGE_SIZE = 20000
const MAX_EXPORT_ROWS = 1000000
const CONCURRENT_REQUESTS = 6

interface ExportButtonProps {
  columns: string[]
  data: any[]
  totalItems: number
  tableId: 'traffic' | 'traffic_patterns'
}

export interface TrafficData {
  [key: string]: any
}

const ExportCSVButton = ({ columns, data, totalItems, tableId }: ExportButtonProps) => {
  const { organization } = useOrganization()
  const organizationId = organization?.id as string
  const { dateRange } = useGraphConfig()
  const {
    isExporting,
    setIsExporting,
    setProgress,
    updateProgress,
    resetProgress,
    toastId,
    setToastId,
    exportToast: toast,
    resetToastState,
  } = useExportProgress()
  const fileName = `${organization?.name} Traffic_Patterns`
  const cancelExportRef = useRef(false)

  const handleInMemoryExport = (data: TrafficData[]) => {
    const limitedData = data.slice(0, MAX_EXPORT_ROWS)
    const csvContent = convertToCSV(limitedData, columns)
    downloadCSV(csvContent, generateFileName(fileName))
    toast.push(
      ToastComponent({
        title: 'Export Successful',
        message: 'CSV file has been downloaded successfully',
        type: 'success',
      }),
    )
  }

  const fetchTrafficData = async (offset: number) => {
    const requests = Array.from({ length: totalItems <= PAGE_SIZE ? 1 : CONCURRENT_REQUESTS }, (_, i) => {
      const currentOffset = offset + i * PAGE_SIZE
      if (currentOffset >= totalItems || currentOffset >= MAX_EXPORT_ROWS) return null

      const remainingItems = Math.min(PAGE_SIZE, totalItems - currentOffset)
      if (remainingItems <= 0) return null

      return getRecentTraffics({
        organizationId,
        dateRange: dateRange!,
        tableId,
        offset: currentOffset,
        pageSize: remainingItems,
        queryRules: generateQueryRules({}),
        fields: ['src_id', 'dst_id', 'src_name', 'dst_name', 'src_process', 'port', 'src_addr', 'dst_addr', 'time'],
      })
    }).filter(Boolean)

    const responses = await Promise.all(requests)
    const rows = responses.flatMap((response) => response?.rows || [])
    return { rows, total: responses[0].pagination.total }
  }

  const processBatch = async (
    offset: number,
  ): Promise<{
    rows: TrafficData[]
    processed: number
    duration: number
    total: number
  }> => {
    const startTime = Date.now()
    const { rows, total } = await fetchTrafficData(offset)
    const endTime = Date.now()
    const duration = (endTime - startTime) / 1000

    return {
      rows,
      processed: rows.length,
      duration,
      total,
    }
  }

  const handleExportError = (error: any) => {
    Sentry.captureException(error)
    toast.push(
      ToastComponent({
        title: 'Export Failed',
        message: 'Failed to export CSV file',
        type: 'danger',
      }),
    )
  }

  const handleExport = async () => {
    try {
      if (isExporting) return
      setIsExporting(true)
      setProgress(createProgressTracker(Math.min(totalItems, MAX_EXPORT_ROWS)))
      cancelExportRef.current = false

      const handleDiscardExport = () => {
        cancelExportRef.current = true
        setIsExporting(false)
        resetProgress()
        resetToastState()
      }

      if (!toastId) {
        const id = await toast.push(<ExportProgress onDiscard={handleDiscardExport} />)
        setToastId(id!)
      }

      if (data.length === totalItems) {
        handleInMemoryExport(data)
        resetToastState()
        return
      }

      let allTrafficData: TrafficData[] = []
      let offset = 0
      let totalProcessed = 0
      let hasMoreData = true

      while (totalProcessed < Math.min(totalItems, MAX_EXPORT_ROWS) && hasMoreData) {
        if (cancelExportRef.current) break
        const { rows, duration, total } = await processBatch(offset)

        if (!rows.length) {
          hasMoreData = false
          break
        }

        const remainingItems = Math.min(totalItems, MAX_EXPORT_ROWS) - totalProcessed
        const rowsToAdd = rows.slice(0, remainingItems)

        if (allTrafficData.length + rowsToAdd.length > MAX_EXPORT_ROWS) {
          const remainingSpace = MAX_EXPORT_ROWS - allTrafficData.length
          allTrafficData.push(...rowsToAdd.slice(0, remainingSpace))
          totalProcessed += remainingSpace
          hasMoreData = false
          break
        }

        allTrafficData.push(...rowsToAdd)
        totalProcessed += rowsToAdd.length
        updateProgress(totalProcessed, rowsToAdd.length, duration, Math.min(total, MAX_EXPORT_ROWS))

        if (totalProcessed >= totalItems || totalProcessed >= MAX_EXPORT_ROWS) {
          hasMoreData = false
        }

        offset += totalItems <= PAGE_SIZE ? PAGE_SIZE : PAGE_SIZE * CONCURRENT_REQUESTS
      }

      if (cancelExportRef.current) {
        resetToastState()
        return
      }

      if (!allTrafficData.length) {
        toast.push(
          ToastComponent({
            title: 'No Data',
            message: 'No data available to export',
            type: 'info',
          }),
        )
        return
      }

      if (allTrafficData.length > MAX_EXPORT_ROWS) {
        allTrafficData = allTrafficData.slice(0, MAX_EXPORT_ROWS)
        totalProcessed = MAX_EXPORT_ROWS
      }

      const finalProgress = createProgressTracker(Math.min(totalItems, MAX_EXPORT_ROWS))
      finalProgress.current = Math.min(totalProcessed, MAX_EXPORT_ROWS)
      finalProgress.total = Math.min(totalItems, MAX_EXPORT_ROWS)
      finalProgress.percentage = 100
      finalProgress.speed = 0
      finalProgress.estimatedTimeRemaining = 0
      setProgress(finalProgress)

      const csvContent = convertToCSV(allTrafficData, columns)
      downloadCSV(csvContent, generateFileName(fileName))

      resetToastState()
      setTimeout(() => {
        toast.push(
          ToastComponent({
            title: 'Export Successful',
            message: 'CSV file has been downloaded successfully',
            type: 'success',
          }),
        )
      }, 100)
    } catch (error) {
      handleExportError(error)
      resetToastState()
    } finally {
      setIsExporting(false)
      resetProgress()
    }
  }

  const limitOverride = totalItems > MAX_EXPORT_ROWS

  const getlimitOverrideContent = () => {
    return `Export limited to ${formatNumber(MAX_EXPORT_ROWS)} rows out of ${formatNumber(totalItems)} total rows`
  }

  const ButtonComponent = () => (
    <button
      onClick={handleExport}
      disabled={isExporting || !data.length}
      type="button"
      className="py-1 flex items-center gap-2 h-8"
    >
      <div className="flex items-center gap-1.5 px-2 py-1.5 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-gray-200/50 dark:border-gray-700/50 rounded-md hover:bg-white dark:hover:bg-gray-800 transition-all duration-200 shadow-sm hover:shadow-md">
        <Download
          size={18}
          className={`text-gray-600 dark:text-gray-400 transition-none ${isExporting ? 'animate-pulse' : ''}`}
        />
        <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
          {isExporting ? 'Exporting...' : 'Export CSV'}
        </span>
      </div>
    </button>
  )

  return (
    <>
      <div className="relative inline-block">
        {limitOverride ? (
          <Tooltip title={getlimitOverrideContent()} placement="top">
            <ButtonComponent />
          </Tooltip>
        ) : (
          <ButtonComponent />
        )}
      </div>
    </>
  )
}

export default ExportCSVButton
