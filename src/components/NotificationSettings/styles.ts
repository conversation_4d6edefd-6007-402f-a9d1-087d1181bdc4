export const commonTextStyles = {
  boxSizing: 'border-box',
  fontSize: '0.8125rem',
  fontFamily: 'inherit',
  fontWeight: 500,
  lineHeight: 1.38462,
  WebkitFontSmoothing: 'auto',
  opacity: 0.8,
} as const

export const headingTextStyles = {
  ...commonTextStyles,
  fontSize: '1.0625rem',
  fontWeight: '700',
  lineHeight: '1.41176',
  margin: '0px 0px 1rem',
  color: 'rgb(33, 33, 38)',
} as const

export const labelTextStyles = {
  ...commonTextStyles,
  color: 'rgb(33, 33, 38)',
  lineHeight: '1.41176',
} as const

export const comingSoonBadgeStyles = {
  boxSizing: 'border-box',
  fontSize: '0.6125rem',
  fontFamily: 'inherit',
  fontWeight: 500,
  lineHeight: 1.38462,
  WebkitFontSmoothing: 'auto',
  boxShadow: 'rgba(0, 0, 0, 0.11) 0px 0px 0px 1px, rgba(0, 0, 0, 0.04) 0px 2px 0px -1px',
} as const 