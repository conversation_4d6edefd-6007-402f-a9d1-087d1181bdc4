import { useState, useCallback, useEffect, useRef } from 'react'
import { Card, Slider } from '@nextui-org/react'

interface DateFilterProps {
  onChange: (value: number) => void
  defaultValue?: number
  isDisabled?: boolean
}

function ProgressBar({ onChange, defaultValue = 1, isDisabled = false }: DateFilterProps) {
  const [rangeValue, setRangeValue] = useState<number>(defaultValue)
  const onChangeRef = useRef(onChange)

  // Update the ref when onChange changes
  useEffect(() => {
    onChangeRef.current = onChange
  }, [onChange])

  const getSliderConfig = () => ({
    min: 1,
    max: 5,
    defaultValue: 1,
    step: 1,
  })

  const handleRangeChange = useCallback((value: number | number[]) => {
    const rangeVal = Array.isArray(value) ? value[0] : value
    setRangeValue(rangeVal)
  }, [])

  const handleRangeChangeEnd = useCallback((value: number | number[]) => {
    const rangeVal = Array.isArray(value) ? value[0] : value
    setRangeValue(rangeVal)
    onChangeRef.current(rangeVal)
  }, [])

  const sliderConfig = getSliderConfig()

  return (
    <Card className="px-8 py-3 shadow-none overflow-visible">
      <div className="flex flex-col gap-3">
        {/* Range Controls */}
        <div className="flex items-center gap-8 rounded-xl p-2">
          {/* Range Slider */}
          <div className="flex-1">
            <Slider
              size="md"
              step={sliderConfig.step}
              maxValue={sliderConfig.max}
              minValue={sliderConfig.min}
              value={rangeValue}
              onChange={handleRangeChange}
              onChangeEnd={handleRangeChangeEnd}
              className="h-8"
              isDisabled={isDisabled}
              classNames={{
                base: 'gap-3',
                track: [
                  'bg-primary/20',
                  'relative',
                  '!h-2',
                  'my-2',
                  '!border-none',
                  'rounded-full',
                  'shadow-none',
                ].join(' '),
                filler: ['bg-primary', 'relative', '!h-2', 'rounded-full', 'shadow-none'].join(' '),
                thumb: [
                  'bg-white',
                  'rounded-lg',
                  'h-5 w-5',
                  'relative',
                  'border border-white/40',
                  'group-data-[focused=true]:ring-2',
                  'group-data-[focused=true]:ring-primary-500/30',
                  'shadow-none',
                ].join(' '),
                mark: [
                  'mt-3',
                  'text-xs',
                  'font-medium',
                  'whitespace-nowrap',
                  'text-primary dark:text-primary',
                  'px-2',
                  'shadow-none',
                  'first:ml-0 last:mr-0',
                ].join(' '),
              }}
              renderThumb={({ thumbProps, thumbStyles, ...props }) => (
                <>
                  <div {...thumbProps} style={thumbStyles} />
                  <div style={props.style} className={props.className} />
                </>
              )}
              aria-label="Range value"
              marks={[
                { value: 1, label: 'Info' },
                { value: 2, label: 'Low' },
                { value: 3, label: 'Medium' },
                { value: 4, label: 'High' },
                { value: 5, label: 'Critical' },
              ]}
            />
          </div>
        </div>
      </div>
    </Card>
  )
}

export default ProgressBar
