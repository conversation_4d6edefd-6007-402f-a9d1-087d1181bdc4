import { <PERSON><PERSON>, <PERSON><PERSON>, Spinner } from '@nextui-org/react'
import { memo, useEffect, useState } from 'react'
import { BellRing, Mail, Slack, MessageSquare } from 'lucide-react'
import ProgressBar from './ProgressBar'
import { useForm } from 'react-hook-form'
import ToastComponent from '@/generalComponents/ToastComponent'
import { toast } from '@/components/ui'
import { numberType, setUserDefinedSettingsEmail } from '@/firestoreQueries/user/queries/userDefinedSettingsEmail'
import useUserSettingsEmail from '@/firestoreQueries/user/hooks/useGetUserSettingsEmail'
import { headingTextStyles, labelTextStyles, commonTextStyles, comingSoonBadgeStyles } from './styles'

type NotificationSettingsType = {
  allowNotifications: boolean
  dailyReport: boolean
  detectionAlerts: {
    value: numberType
    isEnabled: boolean
  }
}

interface NotificationSettingsProps {
  userId: string
}

export const NotificationCustomIcon = () => <BellRing className="w-4 h-4" fill="black" stroke="black" />
export const NotificationSettings = memo(({ userId }: NotificationSettingsProps) => {
  const { data, refetch, isLoading } = useUserSettingsEmail({ userId })
  const [isFormReady, setIsFormReady] = useState(false)

  const { register, handleSubmit, setValue, watch } = useForm<NotificationSettingsType>({
    defaultValues: {
      allowNotifications: data?.allowNotifications ?? true,
      dailyReport: data?.dailyReport ?? true,
      detectionAlerts: {
        value: data?.detectionAlerts?.value || 1,
        isEnabled: data?.detectionAlerts?.isEnabled ?? true,
      },
    },
  })

  useEffect(() => {
    // set data when it was loaded
    if (!isLoading && data) {
      setValue('allowNotifications', data.allowNotifications)
      setValue('dailyReport', data.dailyReport)
      setValue('detectionAlerts.value', data.detectionAlerts?.value || 1)
      setValue('detectionAlerts.isEnabled', data.detectionAlerts?.isEnabled ?? true)
      setIsFormReady(true)
    }
  }, [data, isLoading, setValue])

  const formValues = watch()

  const onSubmit = async (values: NotificationSettingsType) => {
    try {
      await setUserDefinedSettingsEmail({
        userId,
        ...values,
      })
      toast.push(
        ToastComponent({
          title: 'Success',
          message: 'Notification settings updated successfully',
          type: 'success',
        }),
      )
    } catch (error) {
      toast.push(
        ToastComponent({
          title: 'Error',
          message: 'Failed to update notification settings',
          type: 'danger',
        }),
      )
    } finally {
      refetch()
    }
  }

  const handleSwitchChange = (field: keyof NotificationSettingsType | 'detectionAlerts.isEnabled', value: boolean) => {
    setValue(field, value, {
      shouldDirty: true,
      shouldTouch: true,
      shouldValidate: true,
    })
    handleSubmit(onSubmit)()
  }

  const handleDateFilterChange = (field: keyof NotificationSettingsType['detectionAlerts'], value: numberType) => {
    setValue(`detectionAlerts.${field}`, value, {
      shouldDirty: true,
      shouldTouch: true,
      shouldValidate: true,
    })
    handleSubmit(onSubmit)()
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="max-w-2xl mx-auto relative min-h-[600px]">
      {!isFormReady ? (
        <div className="flex items-center justify-center h-[600px]">
          <Spinner size="lg" label="Loading settings..." />
        </div>
      ) : (
        <>
          <div className="mb-6" id="cl-section-notifications">
            <h1 style={headingTextStyles}>Notifications</h1>
            <div style={{ borderTop: '1px solid rgba(0, 0, 0, 0.07)' }} />
            <p style={{ ...commonTextStyles, marginTop: '.7rem', padding: '1rem 0' }}>
              Manage your notification preferences
            </p>
          </div>

          <div className="flex gap-2 mb-6">
            <Button
              className="h-9 px-4 min-w-[120px] bg-primary/10 text-primary"
              radius="sm"
              size="sm"
              startContent={<Mail className="w-3.5 h-3.5" />}
            >
              Email
            </Button>
            <div className="relative">
              <Button
                className="h-9 px-4 min-w-[120px] text-default-500 bg-default-100/50"
                radius="sm"
                size="sm"
                startContent={<Slack className="w-3.5 h-3.5" />}
                isDisabled
              >
                Slack
              </Button>
              <div
                style={comingSoonBadgeStyles}
                className="absolute -top-2 -right-2 bg-default-100 text-default-500 px-1.5 py-0.5 rounded-full font-medium"
              >
                Coming Soon
              </div>
            </div>
            <div className="relative">
              <Button
                className="h-9 px-4 min-w-[120px] text-default-500 bg-default-100/50"
                radius="sm"
                size="sm"
                startContent={<MessageSquare className="w-3.5 h-3.5" />}
                isDisabled
              >
                Discord
              </Button>
              <div
                style={comingSoonBadgeStyles}
                className="absolute -top-2 -right-2 bg-default-100 text-default-500 px-1.5 py-0.5 rounded-full font-medium"
              >
                Coming Soon
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <div className="cl-profileSection border-b-[1px] border-b-[rgba(0,0,0,0.06)]">
              <div className="flex items-center justify-between" style={{ padding: '1rem 0' }}>
                <div>
                  <p style={labelTextStyles}>Allow Email Notifications</p>
                  <p style={commonTextStyles}>Enable or disable all notifications</p>
                </div>
                <Switch
                  {...register('allowNotifications')}
                  defaultSelected={formValues.allowNotifications}
                  size="sm"
                  onValueChange={(value) => handleSwitchChange('allowNotifications', value)}
                />
              </div>
            </div>

            {formValues.allowNotifications && (
              <>
                <div className="cl-profileSection" style={{ padding: '1rem 0' }}>
                  <div className="mb-2">
                    <div className="flex items-center justify-between">
                      <div>
                        <p style={labelTextStyles}>Detection Alerts</p>
                        <p style={commonTextStyles}>Real-time security detection notifications</p>
                      </div>
                      <Switch
                        {...register('detectionAlerts.isEnabled')}
                        defaultSelected={formValues.detectionAlerts.isEnabled}
                        size="sm"
                        onValueChange={(value) => handleSwitchChange('detectionAlerts.isEnabled', value)}
                      />
                    </div>
                  </div>
                  <ProgressBar
                    defaultValue={formValues.detectionAlerts.value}
                    onChange={(value) => handleDateFilterChange('value', value as numberType)}
                    isDisabled={!formValues.detectionAlerts.isEnabled}
                  />
                </div>

                <div className="cl-profileSection">
                  <div className="flex items-center justify-between py-1">
                    <div>
                      <p style={labelTextStyles}>Daily Report</p>
                      <p style={commonTextStyles}>Comprehensive security summary</p>
                    </div>
                    <Switch
                      {...register('dailyReport')}
                      defaultSelected={formValues.dailyReport}
                      size="sm"
                      onValueChange={(value) => handleSwitchChange('dailyReport', value)}
                    />
                  </div>
                </div>

                <div className="cl-profileSection">
                  <div className="flex items-center justify-between py-1">
                    <div>
                      <p style={labelTextStyles}>Weekly Report</p>
                      <p style={commonTextStyles}>Comprehensive security summary</p>
                    </div>
                    <div
                      style={comingSoonBadgeStyles}
                      className="bg-default-100 text-default-500 px-1.5 py-0.5 rounded-full font-medium"
                    >
                      Coming Soon
                    </div>
                  </div>
                </div>
              </>
            )}
          </div>
        </>
      )}
    </form>
  )
})
