import { queryClient } from '@/Providers/ReactQueryProvider'
import reactQueryKeyStore from '@/configs/reactQueryKeyStore'
import getUserGroups from '@/firestoreQueries/groups/queries/getUserGroups'
import useGetPortals from '@/firestoreQueries/portals/hooks/useGetPortals'
import { setupWebRTCConnection } from '@/utils/webRTCClient'
import View from '@/views'
import { useMembershipsStore, User } from '@/zustandStores/useMemberships'
import { useGroupsStore } from '@/zustandStores/useUserGroups'
import useWRTCStore from '@/zustandStores/useWRTCStore'
import { useAuth, useOrganization, useUser } from '@clerk/clerk-react'
import { useQuery } from '@tanstack/react-query'
import { motion } from 'framer-motion'
import { useEffect } from 'react'
import { useLocation } from 'react-router-dom'
import TopNav from '../template/TopNav'
import useWindowSize from '../ui/hooks/useWindowSize'

const localTheme = localStorage.getItem('theme')

if (!localTheme) {
  localStorage.setItem('theme', 'light')
}

const ModernLayout = () => {
  const location = useLocation()
  const isAdmin = useAuth().orgRole === 'admin'
  const userId = useAuth().userId
  const organizationId = useOrganization().organization?.id
  const groupsStore = useGroupsStore()
  const { width } = useWindowSize()

  const { data: portals } = useGetPortals({
    organizationId,
    isAdmin,
  })
  const wRTCStore = useWRTCStore()

  useEffect(() => {
    queryClient.clear()
  }, [organizationId])

  const { data: groups } = useQuery({
    queryKey: reactQueryKeyStore.groups({ organizationId: organizationId! }),
    queryFn: async () => await getUserGroups({ organizationId, userId: userId! }),
    enabled: !!userId,
  })

  useEffect(() => {
    if (groups) {
      groupsStore.setGroups(groups)
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [groups])

  useEffect(() => {
    if (portals) {
      portals.forEach((portal) => {
        if (!wRTCStore.channelsMap[portal.portalInfo.integrationIP]) {
          setupWebRTCConnection({
            ip: portal.portalInfo.integrationIP,
          })
            .then((dataChannel) => {
              wRTCStore.updateChannelsMap({
                channel: dataChannel,
                integrationIp: portal.portalInfo.integrationIP,
              })
            })
            .catch(() => {
              // TODO: handle error
            })
        }
        wRTCStore.updatePortalsIpMap({
          portalId: portal.id,
          ip: portal.portalInfo.integrationIP,
          accessDuration: portal.accessDuration,
        })
      })
    }
    // eslint-disable-next-line react-compiler/react-compiler
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [portals])

  const updateMembership = useMembershipsStore((state) => state.updateMemberships)
  const getMemberships = useOrganization().organization!.getMemberships
  const user = useUser().user
  useEffect(() => {
    getMemberships().then((memberships) => {
      const users: User[] = []
      memberships.data.forEach((m) =>
        users.push({
          firstName: m.publicUserData.firstName,
          lastName: m.publicUserData.lastName,
          imageUrl: m.publicUserData.imageUrl,
          userId: m.publicUserData.userId!,
        } as User),
      )

      if (process.env.NODE_ENV === 'production') {
        // Initialize FullStory with user identity
        ;(window as any).FS?.('setIdentity', {
          uid: user?.id,
          properties: {
            displayName: `${user?.firstName} ${user?.lastName}`,
            email: user?.emailAddresses[0].emailAddress,
          },
        })
      }
      updateMembership(users)
    })
    // eslint-disable-next-line react-compiler/react-compiler
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  return (
    <>
      {width && width > 600 ? (
        <div className="app-layout-modern flex flex-auto h-screen flex-col">
          <TopNav />
          <div className="flex flex-auto min-w-0">
            <div className="flex flex-col flex-auto min-w-0 relative w-full border-l border-gray-200 dark:border-gray-700">
              <View
                pageContainerType={
                  location.pathname === '/shortcuts' || location.pathname === '/hunterx-dashboard'
                    ? 'gutterless'
                    : 'default'
                }
              />
            </div>
          </div>
        </div>
      ) : (
        <div className="app-layout-modern flex justify-center items-center flex-col h-screen">
          <motion.h1
            className="text-2xl font-bold relative text-gray-800"
            initial={{ opacity: 0, top: -100 }}
            animate={{ opacity: 1, top: 0 }}
            transition={{ duration: 1.2 }}
          >
            We currently only support desktops
          </motion.h1>
        </div>
      )}
    </>
  )
}

export default ModernLayout
