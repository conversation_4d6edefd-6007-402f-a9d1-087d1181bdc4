import '@/configs/firebase'
import useDirection from '@/utils/hooks/useDirection'
import useLocale from '@/utils/hooks/useLocale'
import { useOrganizationSync } from '@/utils/hooks/useOrganizationSync'
import { useAuth } from '@clerk/clerk-react'
import { getAuth, signInWithCustomToken } from 'firebase/auth'
import { useLDClient } from 'launchdarkly-react-client-sdk'
import { Suspense, lazy, useEffect, useMemo } from 'react'
import ChooseOrgPage from './ChooseOrgPage'
import useFirstEntity from '@/firestoreQueries/ndr/entities/hooks/useFirstEntity'
import { useAppConfig } from '@/zustandStores/useAppConfig'
import useUpdateEffect from '@/hooks/useUpdateEffect'

const Layout = () => {
  const { organization, organizationId } = useOrganizationSync()
  const isOrg = !!organization
  const authData = useAuth()
  const useId = authData.userId
  
  const ldClient = useLDClient()
  
  const { data: firstEntityData } = useFirstEntity({ organizationId })
  
  const { setIntegrationType } = useAppConfig()
  
  useUpdateEffect(() => {
    setIntegrationType('')
    if (firstEntityData) {
      setIntegrationType(firstEntityData.integrationType)
    }
  }, [firstEntityData, organizationId])
  
  useEffect(() => {
    if (organizationId && ldClient && useId) {
      ldClient.identify({
        kind: 'multi',
        organization: { kind: 'organization', key: organizationId, name: organizationId },
        user: { kind: 'user', key: useId }
      })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [organizationId, useId])
  
  useEffect(() => {
    const signInWithClerk = async () => {
      const auth = getAuth()
      const token = await getToken({ template: 'integration_firebase' })
      await signInWithCustomToken(auth, token!)
    }
    
    signInWithClerk()
    
    // eslint-disable-next-line react-compiler/react-compiler
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])
  
  const { getToken, isSignedIn } = useAuth()
  useDirection()
  useLocale()
  
  const AppLayout = useMemo(() => {
    if (isSignedIn) {
      if (location.href.includes('onboarding')) {
        return lazy(() => import('./AuthLayout'))
      }
      return lazy(() => import('./ModernLayout'))
    }
    return lazy(() => import('./AuthLayout'))
  }, [isSignedIn])
  
  if (!isOrg && isSignedIn) {
    return <ChooseOrgPage />
  }
  return (
    <Suspense fallback={<></>} key={organizationId}>
      <AppLayout />
    </Suspense>
  )
}

export default Layout
