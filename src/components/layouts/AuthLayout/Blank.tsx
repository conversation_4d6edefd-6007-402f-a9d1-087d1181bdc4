import { cloneElement } from 'react'
import Avatar from '@/components/ui/Avatar'
import Logo from '@/components/template/Logo'
import { APP_NAME } from '@/constants/app.constant'
import type { CommonProps } from '@/@types/common'

interface BlankProps extends CommonProps {
  content?: React.ReactNode
}

const Blank = ({ children, content, ...rest }: BlankProps) => {
  return (
    <div className="w-full h-full">
      <div className="bg-no-repeat bg-cover flex-col justify-between hidden lg:flex">
        {/* <Logo mode="light" className='py-6 px-16' style={{position: 'absolute'}} /> */}
      </div>
      <div className="col-span-1 flex flex-col w-full h-full justify-center items-center bg-white dark:bg-gray-800">
        <div className="xl:min-w-[450px] px-8">
          <div className="mb-8 -mt-20">{content}</div>
          {children
            ? cloneElement(children as React.ReactElement, {
                ...rest,
              })
            : null}
        </div>
      </div>
    </div>
  )
}

export default Blank
