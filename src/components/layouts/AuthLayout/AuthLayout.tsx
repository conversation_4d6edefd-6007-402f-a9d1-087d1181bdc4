// import Cover from './Cover'
// import Simple from './Simple'
import { LAYOUT_TYPE_BLANK } from '@/constants/theme.constant'
import { useAppSelector } from '@/store'
import View from '@/views'
import { useAuth } from '@clerk/clerk-react'
import Blank from './Blank'

const AuthLayout = () => {
  const layoutType = useAppSelector((state) => state.theme.layout.type)
  const { isLoaded } = useAuth()
  const isOnboarding = location.href.includes('onboarding')

  if (isLoaded) {
    return (
      <div className="app-layout-blank flex flex-auto flex-col h-[100vh]">
        {layoutType === LAYOUT_TYPE_BLANK || isOnboarding ? (
          <View />
        ) : (
          <Blank>
            <View />
          </Blank>
        )}
      </div>
    )
  }
}

export default AuthLayout
