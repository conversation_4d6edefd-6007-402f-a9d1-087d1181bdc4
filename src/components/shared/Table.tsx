import {
  useReactTable,
  getCoreRowModel,
  flexRender,
  getPaginationRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  SortingState,
  VisibilityState,
  OnChangeFn,
  ColumnDef,
  ColumnMeta,
  ColumnFiltersState,
} from '@tanstack/react-table'
import { Button, CardBody, cn, ScrollShadow, Input } from '@nextui-org/react'
import CustomCard from '@/customComponents/CustomCard'

import _ from 'lodash'
import { Settings, Eye, EyeOff, ChevronLeft, ChevronRight, Search, ArrowUp, ArrowDown } from 'lucide-react'
import React, { useMemo, useRef, useState, useEffect, useCallback } from 'react'
import { Popover, PopoverContent, PopoverTrigger } from '@nextui-org/react'
import { CustomHeader } from '@/views/NDR/Inventory/entityProfile/EntityTrafficPatternsTab'
import { CellActionsMenu } from '@/components/shared/TableComponents'
import useDeepCompareEffect from '@/hooks/useDeepCompareEffect'
import Skeleton from '../ui/Skeleton'
import {
  normalizeSuggestedValue,
  useAvailableFields,
} from '@/views/NDR/PortflowPage/components/MultiSelect/MultiSelectContainer'
import formatNumber from '@/utils/formatNumber'
import { useGraphStore } from '@/views/NDR/PortflowPage/enteties/store'
import useGraphConfig from '@/views/NDR/PortflowPage/hooks/useGraphConfig'
import { formatFilterOption } from '@/views/NDR/PortflowPage/components/MultiSelect/helpers'
import EmptyTableView from '@/views/NDR/Inventory/entityTables/EmptyTableView'

const scrollbarStyles = {
  // For Webkit browsers (Chrome, Safari)
  '&::-webkit-scrollbar': {
    width: '8px',
    height: '8px',
  },
  '&::-webkit-scrollbar-track': {
    background: 'transparent',
  },
  '&::-webkit-scrollbar-thumb': {
    background: '#E4E4E7',
    borderRadius: '4px',
    '&:hover': {
      background: '#D4D4D8',
    },
  },
  // For Firefox
  scrollbarWidth: 'thin',
  scrollbarColor: '#E4E4E7 transparent',
}

interface PaginationControlsProps {
  currentPage: number
  totalPages: number
  onPageChange: (page: number) => void
  isTotalPageLoading: boolean
}

const PaginationControls = ({
  currentPage,
  totalPages,
  onPageChange,
  table,
  data,
  createDefaultColumn,
  isTotalPageLoading,
  isManualPagination,
  hideChangeColumnVisibilityButton,
}: PaginationControlsProps & {
  table: any
  data: any[]
  createDefaultColumn: (columnId: string) => any
  isManualPagination: boolean
  hideChangeColumnVisibilityButton: boolean
}) => {
  const refInput = useRef(null)
  const MIN_PAGE_VALUE = 1
  const currentPageNormalized = isManualPagination ? currentPage : currentPage + 1
  const [pageInput, setPageInput] = useState(currentPageNormalized.toString())

  // Keep input in sync with currentPage
  useEffect(() => {
    setPageInput(currentPageNormalized.toString())
  }, [currentPageNormalized])

  const handlePageInputChange = (value: string) => {
    // Only allow numbers
    if (!/^\d*$/.test(value)) {
      return
    }

    // Convert to number and validate immediately
    const page = parseInt(value)
    if (!isNaN(page)) {
      // Clamp the value between 1 and totalPages
      const clampedPage = Math.min(Math.max(page, MIN_PAGE_VALUE), totalPages)

      // Update input with clamped value if it exceeds limits
      if (page !== clampedPage) {
        setPageInput(clampedPage.toString())
        if (clampedPage !== currentPageNormalized) {
          onPageChange(clampedPage)
        }
        return
      }

      // If value is within valid range and different from current page
      if (page !== currentPageNormalized) {
        onPageChange(page)
      }
    }

    setPageInput(value)
  }

  const handleInputBlur = () => {
    // When input loses focus, reset to current page if invalid
    const page = parseInt(pageInput)
    if (isNaN(page) || page < MIN_PAGE_VALUE || page > totalPages) {
      setPageInput(currentPageNormalized.toString())
    }
  }

  const handleInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      const page = parseInt(pageInput)
      if (!isNaN(page)) {
        const clampedPage = Math.min(Math.max(page, MIN_PAGE_VALUE), totalPages)
        if (clampedPage !== currentPageNormalized) {
          onPageChange(clampedPage)
        }
        setPageInput(clampedPage.toString())
      } else {
        setPageInput(currentPageNormalized.toString())
      }
    }
  }

  const handlePreviousPage = () => {
    if (currentPageNormalized > 1) {
      onPageChange(currentPageNormalized - 1)
    }
  }

  const handleNextPage = () => {
    if (currentPageNormalized <= totalPages) {
      onPageChange(currentPageNormalized + 1)
    }
  }

  const getInputWidth = (value: string) => {
    const contentWidth = Math.max(value.length * 9, 45)

    return `${contentWidth}px`
  }

  return (
    <div className="flex items-center justify-between px-4 py-3 bg-gradient-to-b dark:from-[#111827] dark:to-[#111827] dark:border-gray-700 from-white to-gray-50/80 border-t border-gray-100">
      {isTotalPageLoading ? (
        <Skeleton variant="block" className="w-16 h-8" />
      ) : (
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-1.5">
            <div className="w-1.5 h-1.5 rounded-full bg-emerald-400" />
            <span className="text-xs dark:text-gray-400 text-gray-500">
              Page <span className="font-medium dark:text-gray-200 text-gray-700">{currentPageNormalized}</span> of{' '}
              <span className="font-medium dark:text-gray-200 text-gray-700">{formatNumber(totalPages)}</span>
            </span>
          </div>
          {!hideChangeColumnVisibilityButton && (
            <ColumnToggle table={table} data={data} createDefaultColumn={createDefaultColumn} />
          )}
        </div>
      )}

      <div className="flex items-center gap-2">
        <Button
          isIconOnly
          variant="flat"
          size="sm"
          isDisabled={isTotalPageLoading || currentPageNormalized === MIN_PAGE_VALUE}
          onClick={handlePreviousPage}
          className="min-w-[32px] bg-white dark:bg-gray-800 dark:text-gray-200 hover:bg-gray-50 text-gray-500 transform transition-all duration-200 ease-in-out active:scale-90 focus:outline-none"
        >
          <ChevronLeft className="h-4 w-4 transform transition-all duration-200 ease-in-out active:scale-90 focus:outline-none" />
        </Button>

        <div className="relative group">
          <div className="group-focus-within:ring-blue-100 group-focus-within:ring-2 group-focus-within:!border-blue-300 group-focus-within:!bg-blue-50/50 transition-all duration-150 group-hover:bg-blue-50/50 rounded-lg min-h-unit-7 bg-white dark:bg-[#111827] shadow-sm border border-gray-200 dark:border-gray-700 group-hover:border-blue-200">
            <input
              type="text"
              value={pageInput}
              onFocus={() => refInput.current?.select()}
              onChange={(e) => handlePageInputChange(e.target.value)}
              onBlur={handleInputBlur}
              onKeyDown={handleInputKeyDown}
              style={{ width: getInputWidth(pageInput) }}
              className="w-[45px] border-transparent transition-[width] group-hover:text-blue-600 dark:bg-gray-800 dark:border-gray-700 text-center text-sm font-medium dark:text-gray-200 text-gray-600 rounded-lg  !px-1 !py-1"
              ref={refInput}
            />
          </div>
        </div>

        <span className="text-sm font-medium dark:text-gray-200 text-gray-400">/ {formatNumber(totalPages)}</span>

        <Button
          isIconOnly
          variant="flat"
          size="sm"
          isDisabled={isTotalPageLoading || currentPageNormalized === totalPages}
          onClick={handleNextPage}
          className="min-w-[32px] dark:bg-gray-800 dark:text-gray-200 bg-white hover:bg-gray-50 text-gray-500"
        >
          <ChevronRight className="h-4 w-4 transform transition-all duration-200 ease-in-out active:scale-90 focus:outline-none" />
        </Button>
      </div>
    </div>
  )
}

interface ColumnMetaWithDimensions extends ColumnMeta<any, unknown> {
  width?: string
  minWidth?: string
  maxWidth?: string
}

export type onFilterColumnChangeType = (sorting: ColumnFiltersState) => Promise<void>

interface TableProps {
  data: any[]
  columns: ColumnDef<any, unknown>[]
  pageSize?: number
  meta: any
  scrollDirection?: 'x' | 'y'
  className?: string
  pageIndex: number
  totalPages: number
  setPagination: (value: any) => void
  columnFilters: any[]
  columnVisibility?: VisibilityState
  setColumnVisibility?: OnChangeFn<VisibilityState>
  onSortingColumnChange?: (filters: SortingState) => Promise<void>
  onFilterColumnChange?: onFilterColumnChangeType

  onPropagateCurrentPageData?: (currentPageData: any) => void

  manualPagination?: boolean
  manualSorting?: boolean
  manualFiltering?: boolean

  isTotalPageLoading?: boolean
  isNextPageDataLoading?: boolean
  hideChangeColumnVisibilityButton?: boolean
}

const ColumnToggle = React.memo(
  ({
    table,
    data,
    createDefaultColumn,
  }: {
    table: any
    data: any[]
    createDefaultColumn: (columnId: string) => any
  }) => {
    const [searchValue, setSearchValue] = useState('')
    const { visibleColumns, setVisibleColumns } = useGraphStore()
    const { tableId } = useGraphConfig()
    const { availableFields } = useAvailableFields({ tableId })

    const allPossibleColumns = useMemo(() => {
      return availableFields.map((field) => field.replaceAll('.', '_'))
    }, [availableFields])

    const formatColumnName = useCallback((id: string) => {
      return id.split('_').join('.')
    }, [])

    const handleVisibilityChange = useCallback(
      (columnId: string, isVisible: boolean) => {
        // Get column safely
        let column = null
        try {
          const allColumns = table.getAllColumns()
          column = allColumns.find((col) => col.id === columnId)
        } catch (e) {
          console.warn(`Could not find column ${columnId}`)
          return
        }

        if (!column && !isVisible) {
          // If we're trying to show a column that doesn't exist, create it
          const newColumn = createDefaultColumn(columnId)
          table.options.meta?.addColumn(newColumn)

          // Try getting the column again
          try {
            const allColumns = table.getAllColumns()
            column = allColumns.find((col) => col.id === columnId)
          } catch (e) {
            console.warn(`Could not create column ${columnId}`)
            return
          }
        }

        if (isVisible) {
          // If hiding the column
          if (column.getFilterValue()) {
            column.setFilterValue('') // Clear any filters
          }

          // Update store first
          setVisibleColumns((prev) => prev.filter((col) => col !== columnId))

          // Update table state with all visibility states
          const currentVisibility = table.getState().columnVisibility
          table.setColumnVisibility({
            ...currentVisibility,
            [columnId]: false,
          })
        } else {
          // If showing the column
          // Update store first
          setVisibleColumns((prev) => [...prev, columnId])

          // Update table state with all visibility states
          const currentVisibility = table.getState().columnVisibility
          table.setColumnVisibility({
            ...currentVisibility,
            [columnId]: true,
          })
        }
      },
      [setVisibleColumns, table, createDefaultColumn],
    )

    const filteredColumns = useMemo(() => {
      return allPossibleColumns.filter(
        (columnId) => !searchValue || formatColumnName(columnId).toLowerCase().includes(searchValue.toLowerCase()),
      )
    }, [allPossibleColumns, searchValue, formatColumnName])

    return (
      <Popover
        className="dark:bg-[#111827]"
        onOpenChange={(open) => {
          table.options.meta?.onColumnVisibilityChange?.(open)
        }}
      >
        <PopoverTrigger>
          <Button
            size="sm"
            variant="flat"
            isIconOnly
            className="ml-auto h-8 w-8 bg-transparent dark:hover:bg-gray-700 hover:bg-gray-100"
          >
            <Settings className="h-4 w-4 stroke-gray-400 dark:stroke-gray-200" />
            <span className="sr-only">Toggle columns</span>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="p-0 w-[400px] overflow-hidden dark:bg-[#111827] dark:border-gray-700 border">
          <div className="sticky top-0 bg-white z-10 w-full">
            <div className="flex items-center justify-between px-4 py-3 border-b dark:border-gray-700 bg-gray-50 dark:bg-[#111827]">
              <h4 className="font-medium text-sm dark:text-gray-200 text-gray-700">Column Visibility</h4>
              <span className="text-xs dark:text-gray-200 text-gray-500">
                {visibleColumns.length} / {allPossibleColumns.length}
              </span>
            </div>
            <Input
              size="sm"
              placeholder="Search columns..."
              value={searchValue}
              onChange={(e) => setSearchValue(e.target.value)}
              startContent={<Search className="h-4 w-4 dark:text-gray-200 text-gray-400 flex-shrink-0" />}
              classNames={{
                base: 'rounded-none border-none',
                mainWrapper: 'h-10',
                input: 'text-sm',
                inputWrapper:
                  'h-10 rounded-none shadow-none border-b bg-white dark:bg-[#111827] dark:border-b-gray-700',
              }}
            />
          </div>
          <div className="h-[400px] overflow-hidden">
            <ScrollShadow className="h-full ">
              <div className="p-2 space-y-0.5">
                {filteredColumns.length === 0 ? (
                  <div className="flex flex-col items-center justify-center py-8 px-4">
                    <Search className="h-8 w-8 text-gray-300 mb-2" />
                    <p className="text-sm text-gray-500 text-center">No columns match your search</p>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 gap-0.5">
                    {filteredColumns.map((columnId) => {
                      let isVisible = visibleColumns?.includes(columnId)

                      const columnName = formatColumnName(columnId)

                      return (
                        <div
                          title={columnId.replaceAll('_', '.')}
                          key={columnId}
                          className="flex items-center w-full px-3 py-2 hover:bg-gray-50 dark:bg-[#111827] rounded-md group transition-colors cursor-default"
                        >
                          <div className="w-[300px] min-w-[300px] mr-2 overflow-hidden">
                            <span className="text-sm truncate inline-block w-full text-gray-600 dark:text-gray-200  dark:group-hover:text-gray-300 group-hover:text-gray-900">
                              {columnName}
                            </span>
                          </div>
                          <Button
                            isIconOnly
                            size="sm"
                            variant="light"
                            className="flex-shrink-0 w-8 min-w-[32px] transition-colors ml-auto"
                            onClick={() => handleVisibilityChange(columnId, isVisible)}
                          >
                            {isVisible ? (
                              <Eye className="h-4 w-4 text-primary" />
                            ) : (
                              <EyeOff className="h-4 w-4 text-gray-400" />
                            )}
                          </Button>
                        </div>
                      )
                    })}
                  </div>
                )}
              </div>
            </ScrollShadow>
          </div>
        </PopoverContent>
      </Popover>
    )
  },
)

const defaultFilterFn = (row, columnId, value) => {
  if (!value) return true
  const itemValue = row.getValue(columnId)
  if (!itemValue) return false

  // Split multiple filters
  const filters = value.split('||').map((f) => f.trim())

  // Separate exclusions and inclusions
  const exclusions = filters.filter((f) => f.startsWith('!'))
  const inclusions = filters.filter((f) => !f.startsWith('!'))

  const compareValue = String(itemValue).toLowerCase()

  // Check if value matches any exclusion - if so, exclude it
  const isExcluded = exclusions.some((filter) => {
    const excludeValue = filter.substring(1).toLowerCase()
    return compareValue === excludeValue
  })

  // If excluded, return false immediately
  if (isExcluded) return false

  // If we have inclusions, check if value matches any inclusion
  if (inclusions.length > 0) {
    return inclusions.some((filter) => {
      return String(itemValue).toLowerCase().includes(filter.toLowerCase())
    })
  }

  // If we only had exclusions and item wasn't excluded, show it
  return true
}

const Table = ({
  data,
  columns: initialColumns,
  pageSize = 6,
  meta,

  pageIndex,
  totalPages,

  setPagination,

  onPropagateCurrentPageData,

  columnVisibility: externalColumnVisibility,
  setColumnVisibility,

  onSortingColumnChange,
  onFilterColumnChange,

  scrollDirection = 'x',
  className,
  manualPagination,
  manualSorting,
  manualFiltering,
  isTotalPageLoading,
  isNextPageDataLoading,
  hideChangeColumnVisibilityButton,
}: TableProps) => {
  const leftColumnRef = useRef<HTMLTableElement>(null)
  const tableContainerRef = useRef<HTMLDivElement>(null)
  const [columns, setColumns] = useState(() => initialColumns)

  const [sorting, setSorting] = useState<SortingState>([])
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])

  const [internalColumnVisibility, setInternalColumnVisibility] = useState<VisibilityState>({})

  // Get visible columns from store
  const { visibleColumns, setVisibleColumns } = useGraphStore()
  // @ts-ignore

  const { columnFilters: columnFiltersGlobal } = useGraphStore()

  useEffect(() => {
    if (columnFiltersGlobal) {
      setColumnFilters(columnFiltersGlobal)
    }
  }, [columnFiltersGlobal])

  // Initialize visible columns on mount
  useEffect(() => {
    if (visibleColumns.length === 0) {
      const defaultColumns = columns.map((col) => col.id)
      setVisibleColumns(defaultColumns)
    }
  }, [columns, setVisibleColumns, visibleColumns.length])

  // Sync store with visibility state
  useEffect(() => {
    const newVisibilityState = visibleColumns.reduce<VisibilityState>((acc, colId) => {
      acc[colId] = true
      return acc
    }, {})
    setInternalColumnVisibility(newVisibilityState)
  }, [visibleColumns])

  const createDefaultColumn = useCallback(
    (columnId: string): ColumnDef<any, unknown> => ({
      id: columnId,
      accessorKey: columnId,
      header: (props) => <CustomHeader {...props} title={columnId.split('_').join('.')} />,
      cell: ({ row, column }) => {
        const value = normalizeSuggestedValue(row.getValue(columnId))
        const valueString = Array.isArray(value) ? value.join(', ') : value
        return (
          <CellActionsMenu value={value} column={column}>
            <div className="flex justify-center text-center text-gray-500 min-w-[150px] max-w-[400px] overflow-hidden mx-auto">
              <span className="truncate">{formatFilterOption(columnId, valueString) || 'N/A'}</span>
            </div>
          </CellActionsMenu>
        )
      },
      enableColumnFilter: true,
      filterFn: defaultFilterFn,
      enableSorting: true,
      meta: {} as ColumnMetaWithDimensions,
    }),
    [],
  )

  // Initialize columns with both initial and visible columns
  // TODO: REFACTORING IS REQUIRED. IT WORKS SOMEHOW AND THIS IS GOOD
  useDeepCompareEffect(() => {
    if (visibleColumns.length > 0) {
      const existingColumnIds = _.uniq(columns.map((col) => col.accessorKey).concat(['src_name', 'dst_name']))
      const newColumns = visibleColumns
        .filter((colId) => !existingColumnIds.includes(colId))
        .map((colId) => createDefaultColumn(colId))

      const existingWithMagicallyHidden = existingColumnIds.filter(
        (colId) =>
          ![
            'firewallEnabled',
            'dst_addr',
            'src_addr',
            'src_name',
            'dst_name',
            'port',
            'dst_process',
            'src_process',
          ].includes(colId),
      )
      const visibleWithMagicallyHidden = _.uniq([
        'firewallEnabled',
        'dst_addr',
        'src_addr',
        'src_name',
        'dst_name',
        'port',
        'dst_process',
        'src_process',
        ...visibleColumns,
      ])

      if (newColumns.length > 0) {
        setColumns((prev) => [...prev, ...newColumns])
      } else if (existingWithMagicallyHidden.length < visibleWithMagicallyHidden.length) {
        setColumns((prev) =>
          prev.filter((prev) => {
            return visibleWithMagicallyHidden.includes(prev.accessorKey)
          }),
        )
      }
    }
  }, [visibleColumns, columns, createDefaultColumn])

  useDeepCompareEffect(
    () => {
      onSortingColumnChange?.(sorting)
    },
    [sorting],
    { ignoreInitialRender: true },
  )

  useDeepCompareEffect(
    () => {
      onFilterColumnChange?.(columnFilters)
    },
    [columnFilters],
    { ignoreInitialRender: true },
  )

  // Handle visibility changes
  const handleVisibilityChange: OnChangeFn<VisibilityState> = useCallback(
    (updater) => {
      const newVisibility = typeof updater === 'function' ? updater(internalColumnVisibility) : updater

      setInternalColumnVisibility(newVisibility)
      // Update store
      const newVisibleColumns = Object.entries(newVisibility)
        .filter(([_, isVisible]) => isVisible)
        .map(([colId]) => colId)
      setVisibleColumns(newVisibleColumns)
      // Notify parent
      setColumnVisibility?.(updater)
    },
    [internalColumnVisibility, setColumnVisibility, setVisibleColumns],
  )

  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
      pagination: { pageIndex, pageSize },
      columnFilters,
      columnVisibility: externalColumnVisibility || internalColumnVisibility,
    },
    onSortingChange: setSorting,
    onColumnVisibilityChange: handleVisibilityChange,
    onColumnFiltersChange: setColumnFilters,
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    manualPagination: Boolean(manualPagination),
    manualSorting: Boolean(manualSorting),
    manualFiltering: Boolean(manualFiltering),
    ...(!manualFiltering && { getFilteredRowModel: getFilteredRowModel() }),
    ...(!manualSorting && { getSortedRowModel: getSortedRowModel() }),
    ...(!manualPagination && { getPaginationRowModel: getPaginationRowModel() }),
  })

  const filteredRowsLength = table.getFilteredRowModel().rows.length
  const totalPagesNumber = manualPagination ? totalPages : Math.ceil(filteredRowsLength / pageSize)
  const currentPage = pageIndex

  const handlePageChange = (page: number) => {
    if (manualPagination) {
      setPagination(page)
    } else {
      table.setPageIndex(page - 1)
    }
  }

  // Enhanced scroll handler
  useEffect(() => {
    const container = tableContainerRef.current
    if (!container) return

    let isTouchPad = false
    // let lastDeltaY = 0
    const TOUCHPAD_THRESHOLD = 10 // Threshold to detect touchpad

    const handleWheel = (e: WheelEvent) => {
      // Detect touchpad by checking for precise scrolling
      if (Math.abs(e.deltaY) < TOUCHPAD_THRESHOLD) {
        isTouchPad = true
      }

      // If using touchpad, use natural scrolling
      if (isTouchPad) {
        return
      }

      // For mouse wheel, prevent default and handle custom scrolling
      if (scrollDirection === 'x') {
        e.preventDefault()
        container.scrollLeft += e.deltaY
      } else if (scrollDirection === 'y') {
        e.preventDefault()
        container.scrollTop += e.deltaY
      }
    }

    container.addEventListener('wheel', handleWheel, { passive: false })

    return () => {
      container.removeEventListener('wheel', handleWheel)
    }
  }, [scrollDirection])

  const currentRows = _.map(table.getRowModel().rows, 'original')

  useDeepCompareEffect(() => {
    onPropagateCurrentPageData?.(currentRows)
  }, [currentRows])

  return (
    <div className={cn('h-full flex flex-col max-h-full', className)}>
      <div className="flex-1 overflow-hidden">
        <CustomCard className="h-full flex flex-col dark:border-gray-700 dark:rounded-none">
          <CardBody className="p-0 flex flex-col h-full">
            <div
              ref={tableContainerRef}
              className={cn(
                'flex-1 min-h-0',
                'overflow-auto',
                'relative',
                '[mask-image:linear-gradient(to_right,transparent,black_20px,black_calc(100%-20px),transparent)]',
                'dark:[mask-image:none]',
                {
                  'animate-pulse': isNextPageDataLoading,
                },
              )}
              style={{
                ...scrollbarStyles,
                scrollBehavior: 'smooth',
                overflowX: 'auto', // Allow horizontal scroll
                overflowY: 'auto', // Always allow vertical scroll
              }}
            >
              <table
                className={cn(
                  'w-full border-separate border-spacing-0',
                  'relative',
                  'after:absolute after:pointer-events-none after:inset-0 after:shadow-[inset_-20px_0_16px_-16px_rgba(0,0,0,0.05)]',
                  'before:absolute before:pointer-events-none before:inset-0 before:shadow-[inset_20px_0_16px_-16px_rgba(0,0,0,0.05)]',
                )}
                ref={leftColumnRef}
              >
                <colgroup>
                  {table.getVisibleFlatColumns().map((column) => (
                    <col
                      key={column.id}
                      style={{
                        width: column.columnDef.meta?.width,
                        minWidth: column.columnDef.meta?.minWidth,
                        maxWidth: column.columnDef.meta?.maxWidth,
                      }}
                    />
                  ))}
                </colgroup>
                <thead className="sticky top-0 z-[10] shadow-sm">
                  <tr className="bg-gray/50 dark:bg-gray-900 backdrop-blur-lg">
                    {table.getFlatHeaders().map((header) => {
                      const canSort = header.column.getCanSort()
                      const sorted = header.column.getIsSorted()

                      return (
                        <th
                          key={header.id}
                          className={cn(
                            'px-2 first-of-type:px-7 text-left text-xs font-medium text-gray-500 tracking-wider whitespace-nowrap',
                            canSort && 'cursor-pointer select-none hover:bg-gray-100/50',
                          )}
                          onClick={header.column.getToggleSortingHandler()}
                          style={{
                            width: header.column.columnDef.meta?.width,
                            minWidth: header.column.columnDef.meta?.minWidth,
                            maxWidth: header.column.columnDef.meta?.maxWidth,
                          }}
                        >
                          <div
                            className="flex items-center justify-between gap-1.5"
                            style={{ justifyContent: header.column.columnDef.meta?.justifyContent || 'space-between' }}
                          >
                            <div className="flex items-center gap-2">
                              {flexRender(header.column.columnDef.header, header.getContext())}
                            </div>
                            {sorted && (
                              <div className="text-blue-500 flex-shrink-0">
                                {sorted === 'asc' ? <ArrowUp className="w-3 h-3" /> : <ArrowDown className="w-3 h-3" />}
                              </div>
                            )}
                          </div>
                        </th>
                      )
                    })}
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-[#111827]">
                  {table.getRowModel().rows.length > 0 ? (
                    table.getRowModel().rows.map((row) => (
                      <tr
                        onMouseEnter={() => {
                          if (typeof meta?.onRowMouseOver === 'function') {
                            meta.onRowMouseOver(row.original)
                          }
                        }}
                        onMouseLeave={() => {
                          if (typeof meta?.onRowMouseLeave === 'function') {
                            meta.onRowMouseLeave({})
                          }
                        }}
                        key={row.id}
                        className="border-t border-b border-gray-200 dark:border-gray-700 dark:hover:!bg-gray-700 hover:!bg-blue-50 duration-300"
                      >
                        {row.getVisibleCells().map((cell) => (
                          <td
                            key={cell.id}
                            className={`p-2 whitespace-nowrap ${row.original.disabled ? 'opacity-50' : ''}`}
                            style={{
                              width: cell.column.columnDef.meta?.width,
                              minWidth: cell.column.columnDef.meta?.minWidth,
                              maxWidth: cell.column.columnDef.meta?.maxWidth,
                            }}
                          >
                            {flexRender(cell.column.columnDef.cell, cell.getContext())}
                          </td>
                        ))}
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={table.getVisibleFlatColumns().length}>
                        <EmptyTableView />
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </CardBody>
        </CustomCard>
      </div>
      <div className="flex-shrink-0 mt-auto border-t dark:border-gray-700 dark:bg-[#111827] border-gray-100 bg-white">
        <PaginationControls
          currentPage={currentPage}
          totalPages={totalPagesNumber}
          onPageChange={handlePageChange}
          table={table}
          data={data}
          createDefaultColumn={createDefaultColumn}
          isTotalPageLoading={isTotalPageLoading}
          isManualPagination={manualPagination}
          hideChangeColumnVisibilityButton={hideChangeColumnVisibilityButton}
        />
      </div>
    </div>
  )
}

export default Table
