import type { CommonProps } from '@/@types/common'
import { Spinner } from '@nextui-org/react'
import classNames from 'classnames'
import type { ElementType, ReactNode } from 'react'

interface BaseLoadingProps extends CommonProps {
  asElement?: ElementType
  customLoader?: ReactNode
  loading: boolean
  spinnerClass?: string
}

interface LoadingProps extends BaseLoadingProps {
  type?: 'default' | 'cover' | 'fullscreen'
  loading: boolean
  asElement?: 'div'
}

const DefaultLoading = (props: BaseLoadingProps) => {
  const { loading, children, className, asElement: Component = 'div', customLoader } = props

  return loading ? (
    <Component className={classNames(!customLoader && 'flex items-center justify-center h-full', className)}>
      {customLoader ? <>{customLoader}</> : <Spinner color="primary" size="lg" />}
    </Component>
  ) : (
    <>{children}</>
  )
}

const CoveredLoading = (props: BaseLoadingProps) => {
  const { loading, children, className, asElement: Component = 'div', customLoader } = props

  return (
    <Component className={classNames(loading ? 'relative' : '', className)}>
      {children}
      {loading && (
        <div className="w-full h-full bg-white dark:bg-gray-800 dark:bg-opacity-60 bg-opacity-50 absolute inset-0" />
      )}
      {loading && (
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-10">
          {customLoader ? <>{customLoader}</> : <Spinner color="primary" size="lg" />}
        </div>
      )}
    </Component>
  )
}

const FullscreenLoading = (props: BaseLoadingProps) => {
  const { loading, customLoader } = props

  if (!loading) return null

  return (
    <div className="fixed inset-0 bg-white z-50 flex items-center justify-center">
      {customLoader ? <>{customLoader}</> : <Spinner color="primary" size="lg" />}
    </div>
  )
}

const Loading = ({ type, ...rest }: LoadingProps) => {
  switch (type) {
    case 'default':
      return <DefaultLoading {...rest} />
    case 'cover':
      return <CoveredLoading {...rest} />
    case 'fullscreen':
      return <FullscreenLoading {...rest} />
    default:
      return <DefaultLoading {...rest} />
  }
}

export default Loading
