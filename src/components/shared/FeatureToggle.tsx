import { useTypedFlags } from '@/customHooks/useTypedFlags'

import type { ReactNode } from 'react'
import type { FeatureToggleFlags } from '@/customHooks/useTypedFlags'

type FlagsUnion = keyof FeatureToggleFlags

interface IProps {
  flag: FlagsUnion
  children: ReactNode
}

function FeatureToggle({ flag, children }: IProps) {
  const flagsCollection = useTypedFlags()

  if (flag in flagsCollection && flagsCollection[flag]) {
    return children
  }

  return null
}

export default FeatureToggle
