import { ReactNode } from 'react'
import { followCursor } from 'tippy.js'
import Tippy, { useS<PERSON>leton } from '@tippyjs/react'

export const tooltipDefaultProps = {
  offset: [0, 10],
  duration: [200, 100],
  animation: 'fade',
  plugins: [followCursor],
  followCursor: true,
  arrow: true,
}

const TooltipWrapper = ({ children, className, body }: any) => {
  const [source, target] = useSingleton({
    overrides: ['placement', 'className'],
  })

  return (
    <>
      {source && <Tippy singleton={source} {...tooltipDefaultProps} />}
      <Tippy singleton={target} content={body} className={className} {...tooltipDefaultProps} >
        {children}
      </Tippy>
    </>
  )
}

function TooltipContainer({ jsxBodyComponent, children }: { jsxBodyComponent: ReactNode, children: ReactNode }) {
  if (jsxBodyComponent) {
    return (
      <TooltipWrapper placement="top right" body={jsxBodyComponent}>
        {children}
      </TooltipWrapper>
    )
  }

  return <>{children}</>
}

export default TooltipContainer
