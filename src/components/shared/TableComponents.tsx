import React, { useRef, useState, useEffect } from 'react'
import { Popover, PopoverTrigger, PopoverContent, Button } from '@nextui-org/react'
import { Tooltip } from '../ui/Tooltip'
import { Icon } from '@iconify/react'
import { Column } from '@tanstack/react-table'
import { formatFilterOption } from '@/views/NDR/PortflowPage/components/MultiSelect/helpers'

export const CellActionsMenu = ({
  value,
  column,
  children,
  extraActions = [],
  onOpenChange,
}: {
  value: string
  column: Column<any, unknown>
  children: React.ReactNode
  extraActions?: Array<{
    label: string
    icon: string
    onClick: () => void
  }>
  onOpenChange?: (isOpen: boolean) => void
}) => {
  const [isOpen, setIsOpen] = useState(false)

  useEffect(() => {
    onOpenChange?.(isOpen)
  }, [isOpen])

  // Check if value is in current filters
  const currentFilter = column.getFilterValue() as string
  const isFiltered = currentFilter?.includes(value)
  const isExcluded = currentFilter?.includes(`!${value}`)

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(value)
      setIsOpen(false)
    } catch (err) {
      console.error('Failed to copy text:', err)
    }
  }

  const handleFilter = () => {
    if (isFiltered) {
      // Remove this value from filters
      const filters = currentFilter.split('||').map((f) => f.trim())
      const newFilters = filters.filter((f) => f !== value)
      column.setFilterValue(newFilters.length ? newFilters.join('||') : '')
    } else {
      // Check for conflicting exclusion
      if (isExcluded) {
        // Remove the exclusion and add the inclusion
        const filters = currentFilter.split('||').map((f) => f.trim())
        const newFilters = filters
          .filter((f) => f !== `!${value}`) // Remove exclusion
          .concat(value) // Add inclusion
        column.setFilterValue(newFilters.join('||'))
      } else {
        // Add new filter
        column.setFilterValue(currentFilter ? `${currentFilter}||${value}` : value)
      }
    }
    setIsOpen(false)
  }

  const handleExclude = () => {
    if (isExcluded) {
      // Remove exclusion from filters
      const filters = currentFilter.split('||').map((f) => f.trim())
      const newFilters = filters.filter((f) => f !== `!${value}`)
      column.setFilterValue(newFilters.length ? newFilters.join('||') : '')
    } else {
      // Check for conflicting inclusion
      if (isFiltered) {
        // Remove the inclusion and add the exclusion
        const filters = currentFilter.split('||').map((f) => f.trim())
        const newFilters = filters
          .filter((f) => f !== value) // Remove inclusion
          .concat(`!${value}`) // Add exclusion
        column.setFilterValue(newFilters.join('||'))
      } else {
        // Add new exclusion
        const newValue = `!${value}`
        column.setFilterValue(currentFilter ? `${currentFilter}||${newValue}` : newValue)
      }
    }
    setIsOpen(false)
  }

  return (
    <Popover
      isOpen={isOpen}
      onOpenChange={(open) => {
        setIsOpen(open)
        onOpenChange?.(open)
      }}
      placement="bottom-start"
      offset={12}
    >
      <PopoverTrigger>
        <div className="cursor-pointer">{children}</div>
      </PopoverTrigger>
      <PopoverContent className="min-w-[208px] p-0 overflow-hidden !rounded-xl border -mt-2 border-gray-100 shadow-[0_8px_24px_-4px_rgba(0,0,0,0.08),0_6px_12px_-6px_rgba(0,0,0,0.04)]">
        <div className="px-3 py-3 bg-gradient-to-br from-white to-gray-50/80 w-full">
          <div className="flex items-center gap-2.5">
            <div className="relative group">
              <div className="absolute inset-0 bg-blue-100/50 blur-md rounded-lg group-hover:blur-lg transition-all duration-300" />
              <div className="p-2 rounded-lg bg-white text-blue-500 shadow-sm border border-blue-100/50 relative">
                <Icon icon="lucide:text" className="w-4 h-4" />
              </div>
            </div>
            <div className="min-w-0">
              <div className="text-[10px] font-medium text-gray-400 tracking-wider">Selected Value</div>
              <div className="text-sm font-medium text-gray-700 mt-0.5 truncate leading-tight">
                {formatFilterOption(column.id, value) || 'N/A'}
              </div>
            </div>
          </div>
        </div>

        <div className="py-1 bg-white">
          {value !== 'N/A' && (
            <div className="space-y-0.5 flex flex-col ">
              <MenuButton onClick={handleCopy} icon="lucide:copy" label="Copy Value" />

              <MenuButton
                onClick={handleFilter}
                icon={isFiltered ? 'lucide:x' : 'lucide:filter'}
                label={isFiltered ? 'Remove from Filter' : 'Filter in Table'}
              />

              <MenuButton
                onClick={handleExclude}
                icon={isExcluded ? 'lucide:x' : 'lucide:filter-x'}
                label={isExcluded ? 'Remove Exclusion' : 'Exclude from Table'}
              />

              {extraActions.map((action, index) => (
                <MenuButton
                  key={index}
                  onClick={() => {
                    action.onClick()
                    setIsOpen(false)
                  }}
                  icon={action.icon}
                  label={action.label}
                />
              ))}
            </div>
          )}
        </div>
      </PopoverContent>
    </Popover>
  )
}

// New MenuButton component for consistent styling
interface MenuButtonProps {
  onClick: () => void
  icon: string
  label: string
}

export const MenuButton = ({ onClick, icon, label }: MenuButtonProps) => (
  <Button
    onClick={onClick}
    className="h-8 justify-start group px-2 hover:bg-blue-50/50"
    variant="light"
    startContent={
      <div className="flex items-center justify-center w-6 h-6">
        <Icon
          icon={icon}
          className="w-4 h-4 text-gray-400 group-hover:text-blue-500 transition-all duration-200 ease-in-out group-hover:scale-110"
        />
      </div>
    }
  >
    <span className="text-xs font-medium text-gray-600 group-hover:text-blue-600 transition-colors duration-200">
      {label}
    </span>
  </Button>
)

interface TruncatedCellProps {
  value: string
  column: Column<any, unknown>
}

export const TruncatedCell = ({ value, column }: TruncatedCellProps) => {
  const textRef = useRef<HTMLSpanElement>(null)
  const [isTextTruncated, setIsTextTruncated] = useState(false)

  useEffect(() => {
    const checkTruncation = () => {
      const element = textRef.current
      if (element) {
        setIsTextTruncated(element.scrollWidth > element.clientWidth)
      }
    }

    checkTruncation()
    const resizeObserver = new ResizeObserver(checkTruncation)
    if (textRef.current) {
      resizeObserver.observe(textRef.current)
    }

    return () => resizeObserver.disconnect()
  }, [value])

  const content = (
    <CellActionsMenu value={value} column={column}>
      <div className="flex justify-center text-center text-gray-500 max-w-[150px] overflow-hidden mx-auto">
        <span className="truncate" ref={textRef}>
          {value}
        </span>
      </div>
    </CellActionsMenu>
  )

  if (!isTextTruncated) {
    return content
  }

  return <Tooltip title={value}>{content}</Tooltip>
}
