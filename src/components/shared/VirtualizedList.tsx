import React, { useRef } from 'react'
import { useVirtualizer } from '@tanstack/react-virtual'
import { useInView } from 'react-intersection-observer'
import useDeepCompareEffect from '@/hooks/useDeepCompareEffect'
import EmptyTableView from '@/views/NDR/Inventory/entityTables/EmptyTableView'

interface VirtualizedListProps<T> {
  items: T[]
  hasNextPage: boolean
  isFetchingNextPage: boolean
  fetchNextPage?: () => void
  estimateSize?: (index: number) => number
  renderItem: (item: T, index: number) => React.ReactNode
  renderContainer?: (props: React.HTMLAttributes<HTMLDivElement>) => React.ReactNode
  renderLoader?: () => React.ReactNode
  renderEmpty?: () => React.ReactNode
  className?: string
  children?: React.ReactNode
}

export default function VirtualizedList<T>({
  items,
  hasNextPage,
  isFetchingNextPage,
  fetchNextPage,
  estimateSize = () => 80,
  renderItem,
  renderContainer,
  renderLoader,
  renderEmpty,
  className,
  children,
}: VirtualizedListProps<T>) {
  const parentRef = useRef<HTMLDivElement>(null)
  const { ref, inView } = useInView({
    threshold: 0.8,
  })

  const rowVirtualizer = useVirtualizer({
    count: items.length,
    getScrollElement: () => parentRef.current,
    estimateSize,
    overscan: 5,
  })

  useDeepCompareEffect(
    () => {
      if (inView && hasNextPage && !isFetchingNextPage) {
        fetchNextPage?.()
      }
    },
    [inView, fetchNextPage, hasNextPage, isFetchingNextPage],
    { ignoreInitialRender: true },
  )

  const paddingTop = rowVirtualizer.getVirtualItems().length > 0 ? rowVirtualizer.getVirtualItems()[0].start : 0
  const paddingBottom =
    rowVirtualizer.getVirtualItems().length > 0
      ? rowVirtualizer.getTotalSize() -
        (rowVirtualizer.getVirtualItems()[rowVirtualizer.getVirtualItems().length - 1].end || 0)
      : 0

  const Container = renderContainer || 'div'

  return (
    <Container className={className}>
      {children}
      {items.length > 0 ? (
        <div ref={parentRef} className="h-full overflow-auto">
          <div style={{ height: `${rowVirtualizer.getTotalSize()}px`, width: '100%', position: 'relative' }}>
            <div style={{ paddingTop: `${paddingTop}px`, paddingBottom: `${paddingBottom}px` }}>
              {rowVirtualizer.getVirtualItems().map((virtualItem) => (
                <div key={virtualItem.key} data-index={virtualItem.index} ref={rowVirtualizer.measureElement}>
                  {renderItem(items[virtualItem.index], virtualItem.index)}
                </div>
              ))}
            </div>
          </div>
          {hasNextPage && !isFetchingNextPage ? (
            <div ref={ref} className="h-20 flex justify-center" aria-hidden="true">
              {renderLoader ? renderLoader() : <div>Loading...</div>}
            </div>
          ) : null}
        </div>
      ) : renderEmpty ? (
        renderEmpty()
      ) : (
        <EmptyTableView />
      )}
    </Container>
  )
}
