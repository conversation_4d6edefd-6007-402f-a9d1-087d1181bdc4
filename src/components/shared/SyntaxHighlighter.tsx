// import useDarkMode from '@/utils/hooks/useDarkmode'
import type { SyntaxHighlighterProps as ReactSyntaxHighlighterProps } from 'react-syntax-highlighter'
import { Prism } from 'react-syntax-highlighter'
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism'

type SyntaxHighlighterProps = ReactSyntaxHighlighterProps

const SyntaxHighlighter = (props: SyntaxHighlighterProps) => {
    const { children, ...rest } = props
    // const [darkMode] = useDarkMode()
    return (
        <Prism
            style={oneDark}
            // className="shadow-primary-shadow"
            // codeTagProps={{ className: 'bg-none' }}
            {...rest}
        >
            {children}
        </Prism>
    )
}

export default SyntaxHighlighter
