import React, { Component, type ReactNode } from "react"

import { AlertCircle } from 'lucide-react';
import { Alert } from '@/components/ui/Alert'
import { Button } from '@nextui-org/react';

interface Props {
  children?: ReactNode
  fallback?: ReactNode
}

interface State {
  hasError: boolean
  error: Error | null
  errorInfo: React.ErrorInfo | null
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: any) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null
    };
  }
  
  static getDerivedStateFromError(error: any) {
    return { hasError: true };
  }
  
  componentDidCatch(error: any, errorInfo: any) {
    this.setState({
      error: error,
      errorInfo: errorInfo
    });
    
    // You can log the error to an error reporting service here
    console.error('ErrorBoundary:', error, errorInfo);
    if (error && error.stack) {
      console.error('ErrorBoundary::Stack trace:', error.stack);
    }
  }
  
  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null
    });
  }
  
  public render() {
    if (this.state.hasError) {
      return (
        this.props.fallback || (
          <div className="w-full p-6 space-y-6">
            <Alert type="danger">
              <div className="flex gap-2 align-center">
                <AlertCircle className="h-5 w-5" />
                <p className="ml-2">
                  Table Error
                </p>
              </div>
              <p className="mt-2">
                {this.state.error && this.state.error.toString()}
              </p>
            </Alert>
            
            <div className="flex flex-col items-center justify-center space-y-4 p-8 bg-gray-50 rounded-lg">
              <div className="text-center space-y-2">
                <h3 className="text-lg font-medium">Something went wrong while loading the table</h3>
                <p className="text-sm text-gray-500">
                  We're sorry for the inconvenience. You can try reloading the table or contact support if the problem
                  persists.
                </p>
              </div>
              
              <div className="flex space-x-4">
                <Button
                  type="button"
                  onClick={this.handleRetry}
                  className="bg-primary"
                >
                  Retry Loading
                </Button>
                <Button
                  type="button"
                  variant="flat"
                  onClick={() => window.location.reload()}
                >
                  Refresh Page
                </Button>
              </div>
            </div>
          </div>
        )
      )
    }
    
    return this.props.children
  }
}

export default ErrorBoundary

