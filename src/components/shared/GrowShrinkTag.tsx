import { forwardRef } from 'react'
import classNames from 'classnames'
import Tag from '@/components/ui/Tag'
import { HiArrowUp, HiArrowDown } from 'react-icons/hi'
import growShrinkColor from '@/utils/growShrinkColor'
import type { ReactNode } from 'react'

type GrowShrinkTagProps = {
    value?: number
    showIcon?: boolean
    prefix?: ReactNode | string
    suffix?: ReactNode | string
    flip?: boolean
    className?: string
}

const GrowShrinkTag = forwardRef<HTMLDivElement, GrowShrinkTagProps>(
    (props, ref) => {
        const {
            value = 0,
            className,
            prefix,
            suffix,
            showIcon = true,
            flip = false,
        } = props

        //format value to show , if number is greater than 1000

        const getFormattedValue = (value: number) => {
            if (flip) {
                return value < 0 ? value * -1 : value
            } else {
                return value > 1000 ? `${value / 1000}k` : value
            }
        }

        const formattedValue = getFormattedValue(value)

        return (
            <Tag
                ref={ref}
                className={classNames(
                    'gap-1 font-bold border-0',
                    growShrinkColor(value, 'text', flip),
                    growShrinkColor(value, 'bg', flip),
                    className,
                )}
            >
                {value !== 0 && (
                    <span>
                        {showIcon &&
                            (value > 0 ? <HiArrowUp /> : <HiArrowDown />)}
                    </span>
                )}
                <span>
                    {prefix}
                    {formattedValue}
                    {suffix}
                </span>
            </Tag>
        )
    },
)

GrowShrinkTag.displayName = 'GrowShrinkTag'

export default GrowShrinkTag
