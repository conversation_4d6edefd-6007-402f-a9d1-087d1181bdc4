import { bwNdrEntityIconMap, getNDREntityIcon } from '@/customUtils/entityMaps'
import ModernTable, { onFilterColumnChangeType } from '@/components/shared/Table'
import { CellActionsMenu, MenuButton } from '@/components/shared/TableComponents'
import { ColumnDef, Row } from '@tanstack/react-table'
import { useState, useEffect, useMemo, useRef } from 'react'
import CIDRIP from 'ip-cidr'
import { cn, Popover, PopoverContent, PopoverTrigger } from '@nextui-org/react'
import { ArrowUpRight, ArrowDownLeft, Network, Loader2, Ellipsis } from 'lucide-react'
import { Button } from '@nextui-org/react'
import { CustomHeader } from '@/views/NDR/Inventory/entityProfile/EntityTrafficPatternsTab'
import { Icon } from '@iconify/react'
import { SortingState, ColumnFiltersState } from '@tanstack/react-table'
import formatNumber from '@/utils/formatNumber'
import ErrorBoundary from '@/components/shared/ErrorBoundary'
import { Loading } from '@/components/shared/index'
import { useGraphStore } from '@/views/NDR/PortflowPage/enteties/store'
import { useConfirm } from '@/hooks/useConfirm'
import { useSearchParams } from 'react-router-dom'
import { convertSimpleQueryToComplexQuery } from '@/views/NDR/PortflowPage/utils/queryConvertors'
import { portsProtocols } from '@/constants/port.constant'
import useDeepCompareEffect from '@/hooks/useDeepCompareEffect'
import useGraphConfig from '@/views/NDR/PortflowPage/hooks/useGraphConfig'
import { PortflowQueryType } from '@/firestoreQueries/ndr/portflowShortcuts/portflowShortcutsTypes'
import { useAppConfig } from '@/zustandStores/useAppConfig'
import ExportCSVButton from '@/components/ExportCSVButton/ExportCSVButton'
import { getExportColumns, getExportData } from '@/customUtils/exportCSVUtils'

interface TrafficPatternsTableProps {
  data: any[]
  isLoading?: boolean
  meta?: any
  onColumnVisibilityChange?: (isOpen: boolean) => void
  onPageDataChange?: (pageData: any[]) => void
  className?: string
  pageSize?: number
  isPortflow?: boolean

  pageIndex?: number
  totalPages?: number
  totalItemsFound?: number
  sortBy?: { id: string; desc: boolean }[]
  columnFilters?: { id: 'string'; value: string }[]

  onPageIndexChange?: (pageIndex: number) => Promise<void>
  onSortingColumnChange?: (soring: SortingState) => Promise<void>
  onFilterColumnChange?: (soring: ColumnFiltersState) => Promise<void>

  manualPagination?: boolean
  manualSorting?: boolean
  manualFiltering?: boolean

  isTotalPageLoading?: boolean
  isNextPageDataLoading?: boolean
}

interface TableMeta {
  map?: any
  isTableVisible?: boolean
  isPinned?: boolean
  onPinClick?: (isPinned: boolean) => void
  onPopoverChange?: (isOpen: boolean) => void
}

const defaultFilterFn = (row: Row<any>, columnId: string, value: string) => {
  const itemValue = row.getValue(columnId)
  if (!value) return true
  if (!itemValue) return false

  // Split multiple filters
  const filters = value.split('||').map((f) => f.trim())

  // Separate exclusions and inclusions
  const exclusions = filters.filter((f) => f.startsWith('!'))
  const inclusions = filters.filter((f) => !f.startsWith('!'))

  const compareValue = String(itemValue).toLowerCase()

  // Check if value matches any exclusion - if so, exclude it
  const isExcluded = exclusions.some((filter) => {
    const excludeValue = filter.substring(1).toLowerCase()
    return compareValue === excludeValue
  })

  // If excluded, return false immediately
  if (isExcluded) return false

  // If we have inclusions, check if value matches any inclusion
  if (inclusions.length > 0) {
    return inclusions.some((filter) => {
      // Handle CIDR filters
      if (CIDRIP.isValidCIDR(filter)) {
        const cidr = new CIDRIP(filter)
        return cidr.contains(itemValue)
      }

      return String(itemValue).toLowerCase().includes(filter.toLowerCase())
    })
  }

  // If we only had exclusions and item wasn't excluded, show it
  return true
}

const EntityCell = ({ icon, name, addr, column, table, isSource, meta, row, withAddToQuery, onFilterChange }) => {
  const { visibleColumns, setVisibleColumns, filterOptions, toggleModalWindow, setActiveNodeId } = useGraphStore()

  const ipColumnId = isSource ? 'src_addr' : 'dst_addr'
  const ipColumn = table.getColumn(ipColumnId)
  const isIpVisible = ipColumn?.getIsVisible()

  const toggleIpColumn = () => {
    if (isIpVisible) {
      setVisibleColumns(visibleColumns.filter((col) => col !== ipColumnId))
    } else {
      setVisibleColumns([...visibleColumns, ipColumnId])
    }
  }

  const manageEntityModal = () => {
    toggleModalWindow(true)
    setActiveNodeId(column.id === 'src_name' ? row.original.src_id : row.original.dst_id)
  }

  const onAddToQuery = () => {
    const field = column.id
    const value = name
    const newTag = {
      key: field.replaceAll('_', '.'),
      values: [value],
      operator: 'equal',
    }

    const currentTags = filterOptions || []
    const existingTagIndex = currentTags.findIndex((tag: PortflowQueryType) => tag.key === newTag.key)

    if (existingTagIndex >= 0) {
      const existingTag = currentTags[existingTagIndex]
      if (!existingTag.values.includes(value)) {
        const updatedTags = [...currentTags]
        updatedTags[existingTagIndex] = {
          ...existingTag,
          values: [...existingTag.values, value],
        }
        onFilterChange?.(updatedTags)
      }
    } else {
      onFilterChange?.([...currentTags, newTag])
    }
  }

  return (
    <CellActionsMenu
      value={name}
      column={column}
      onOpenChange={(open) => meta?.onPopoverChange?.(open)}
      extraActions={[
        ...(withAddToQuery
          ? [
              {
                label: 'Add to Query',
                icon: 'lucide:search',
                onClick: onAddToQuery,
              },
            ]
          : []),
        {
          label: isIpVisible ? 'Hide IP Column' : 'Show IP Column',
          icon: 'lucide:columns',
          onClick: toggleIpColumn,
        },
        {
          label: 'Open Profile',
          icon: 'lucide:eye',
          onClick: manageEntityModal,
        },
      ]}
      onFilterChange={onFilterChange}
    >
      <div className="flex items-center gap-2.5 group px-3 py-1.5 rounded-lg bg-gradient-to-r from-gray-50 dark:from-transparent dark:to-transparent dark:hover:from-transparent dark:hover:to-transparent hover:from-blue-50/50 transition-all duration-200">
        <div className="relative">
          <div className="w-8 h-8 rounded-lg bg-white dark:bg-gray-200 flex items-center justify-center border dark:border-gray-700 dark:border-700 group-hover:border-blue-200 group-hover:shadow-sm transition-all">
            <div className="text-gray-500 dark:text-gray-800 group-hover:text-blue-500 dark:group-hover:text-sky-500 transition-colors">
              {icon}
            </div>
          </div>
          <div
            className={cn(
              'absolute -bottom-0.5 -right-0.5 w-2 h-2 rounded-full ring-2 ring-white',
              name === addr ? 'bg-red-400' : 'bg-emerald-400',
            )}
          />
        </div>
        <div className="min-w-0">
          <p className="text-sm font-medium text-gray-700 truncate group-hover:text-blue-600 dark:group-hover:text-sky-500">
            {name}
          </p>
          <p className="text-[11px] text-gray-400 truncate">{addr}</p>
        </div>
      </div>
    </CellActionsMenu>
  )
}

const ProcessCell = ({ value, column, meta, withAddToQuery, onFilterChange }) => (
  <CellActionsMenu
    value={value}
    column={column}
    onOpenChange={(open) => meta?.onPopoverChange?.(open)}
    withAddToQuery={withAddToQuery}
    onFilterChange={onFilterChange}
  >
    <div className="flex items-center gap-1.5 max-w-[140px] group hover:cursor-pointer">
      <div className="flex items-center gap-1 px-1.5 py-1 rounded-lg border dark:border-gray-700 dark:from-[#111827] dark:to-[#111827] border-gray-200 bg-gradient-to-r from-gray-50/80 via-white to-gray-50/80  hover:from-blue-50/50 hover:via-blue-50/30 hover:to-blue-50/50 dark:hover:border-sky-500 hover:border-blue-200 transition-all duration-200 w-full shadow-sm hover:shadow group-hover:scale-[1.02]">
        <div className="flex-shrink-0 flex items-center gap-1">
          <div className="relative">
            <div
              className={cn(
                'w-1 h-1 rounded-full transition-colors',
                value === 'N/A' || !value
                  ? 'bg-red-400 group-hover:bg-red-500'
                  : 'bg-emerald-400 group-hover:bg-blue-400',
              )}
            />
            <div
              className={cn(
                'absolute inset-0 rounded-full animate-ping',
                value === 'N/A' || !value
                  ? 'bg-red-400/30 group-hover:bg-red-500/30'
                  : 'bg-emerald-400/30 group-hover:bg-blue-400/30',
              )}
            />
          </div>
          <div className="p-0.5 rounded dark:bg-[#111827] bg-gray-100/80 group-hover:bg-gray-200 group-hover:bg-blue-100/50">
            <svg
              className="w-2.5 h-2.5 dark:text-gray-700 text-gray-500 group-hover:text-blue-600"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6" />
              <polyline points="15 3 21 3 21 9" />
              <line x1="10" y1="14" x2="21" y2="3" />
            </svg>
          </div>
        </div>
        <div className="flex flex-col min-w-0">
          <span className="truncate text-[11px] font-medium  dark:text-gray-300 text-gray-700 dark:group-hover:text-sky-500 group-hover:text-blue-700">
            {value}
          </span>
        </div>
      </div>
    </div>
  </CellActionsMenu>
)

const DirectionCell = ({ value }) => {
  const isOutbound = value === 'outbound'
  return (
    <div
      className={cn(
        'px-3 py-1.5 rounded-lg flex items-center gap-2 w-fit',
        isOutbound
          ? 'bg-blue-50/50 text-blue-600 border border-blue-100'
          : 'bg-purple-50/50 text-purple-600 border border-purple-100',
      )}
    >
      {isOutbound ? <ArrowUpRight className="w-3.5 h-3.5" /> : <ArrowDownLeft className="w-3.5 h-3.5" />}
      <span className="text-xs font-medium">{value}</span>
    </div>
  )
}

const PortCell = ({ port, table, columnId }) => {
  const protocol = (portsProtocols as any)[port]
  if (!table) {
    return (
      <div className="flex items-center justify-center relative group h-8">
        <div
          className={cn(
            'relative px-3 py-0.5 rounded-full dark:bg-[#111827] bg-white border border-gray-200 group-hover:border-blue-200 group-hover:bg-blue-50 transition-all mx-10',
            'shadow-sm',
          )}
        >
          <div className="flex items-center gap-1.5">
            <Network className="w-3 h-3 text-gray-400 group-hover:text-blue-500" />
            <span className="text-xs font-medium dark:text-gray-200 text-gray-600 group-hover:text-blue-600">
              {port}
            </span>
          </div>
        </div>
      </div>
    )
  }

  // Get column positions and visibility
  const columns = table.getAllColumns()
  const portIndex = columns.findIndex((col) => col.id === columnId)
  const srcProcessColumn = table.getColumn('src_process')
  const dstProcessColumn = table.getColumn('dst_process')

  const srcProcessIndex = columns.findIndex((col) => col.id === 'src_process')
  const dstProcessIndex = columns.findIndex((col) => col.id === 'dst_process')

  // Check if processes are correctly positioned AND visible
  const isCorrectLayout =
    srcProcessIndex < portIndex &&
    dstProcessIndex > portIndex &&
    srcProcessColumn?.getIsVisible() &&
    dstProcessColumn?.getIsVisible()

  return (
    <div className="flex items-center justify-center relative group h-8">
      {isCorrectLayout ? (
        <div className="absolute inset-0 flex items-center">
          <div className="w-full h-[2px] bg-gradient-to-r dark:from-gray-100 dark:to-gray-100 from-gray-200 via-gray-300 to-gray-200 group-hover:bg-gradient-animate overflow-hidden transition-all duration-300">
            <div className="absolute right-0 top-1/2 -translate-y-1/2 w-2 h-2 rounded-full dark:bg-gray-100 bg-gray-300 group-hover:bg-blue-400 dark:group-hover:bg-sky-700 transition-all" />
          </div>
        </div>
      ) : null}
      <div
        className={cn(
          'relative px-2 py-0.5 mx-4 rounded-full bg-white border dark:border-gray-700 dark:bg-[#111827] border-gray-200 group-hover:border-blue-200 group-hover:bg-blue-50 transition-all',
          isCorrectLayout ? '' : 'shadow-sm',
        )}
      >
        <div className="flex items-center justify-center gap-1">
          <Network className="w-3 h-3 dark:text-gray-700 text-gray-400 dark:group-hover:text-gray-700 group-hover:text-blue-500" />
          <span className="text-xs font-medium text-gray-600 dark:text-gray-200 dark:group-hover:text-gray-700 group-hover:text-blue-600">
            {port}
          </span>
          {protocol && <span className="font-medium text-gray-400 text-[11px] border-l pl-1">{protocol}</span>}
        </div>
      </div>
    </div>
  )
}

export const portflowColumns = (meta: TableMeta, onFilterChange: any, withAddToQuery?: boolean): ColumnDef<any>[] => [
  {
    accessorKey: 'firewallEnabled',
    enableColumnFilter: false,
    enableSorting: false,
    enableHiding: true,
    header: (props) => <span />,
    cell: ({ row, column, table }) => {
      const [isOpen, setIsOpen] = useState<boolean>(false)
      const [searchParams, setSearchParams] = useSearchParams()
      const {
        setFirewallEntityId,
        setInitialTrafficPattern,
        isFirewallHasUnsavedChanges,
        setFirewallUnsavedChanged,
        firewallEntityId,
        setFilterOptions,
        setQueryRules,
        toggleQueryBuilderVisible,
      } = useGraphStore()
      const { confirm } = useConfirm()

      const handleSelectFirewallPolicy = (policy: 'ACCEPT' | 'BLOCK') => async () => {
        if (firewallEntityId && row.original.dst_id !== firewallEntityId && isFirewallHasUnsavedChanges) {
          const shouldClose = await confirm({
            title: 'Unsaved Changes',
            message: 'You have unsaved changes. Are you sure you want to ignore?',
            confirmText: 'Close',
            cancelText: 'Cancel',
          })

          if (shouldClose) {
            setFirewallUnsavedChanged(false)
            setFirewallEntityId(row.original.dst_id as string)
            setInitialTrafficPattern({
              ...row.original,
              policy,
            })
          }
        } else {
          setFirewallEntityId(row.original.dst_id as string)
          setInitialTrafficPattern({
            ...row.original,
            policy,
          })
        }
        setIsOpen(false)

        const labelFilter = [
          {
            key: 'dst.name',
            values: [row.original.dst_name],
            operator: 'equal',
          },
          {
            key: 'shortcutsVisible',
            values: ['false'],
          },
          {
            key: 'operator',
            values: ['and'],
          },
        ]

        const params = new URLSearchParams()

        labelFilter.forEach((filter) => {
          params.set(filter.key, filter.values.join(','))
        })

        const queryRules = convertSimpleQueryToComplexQuery(labelFilter)
        setSearchParams([...params], { replace: false })
        setFilterOptions(labelFilter)
        setQueryRules(queryRules)
        toggleQueryBuilderVisible(false)
      }

      return (
        <Popover isOpen={isOpen} onOpenChange={setIsOpen} placement="bottom-start" offset={12}>
          <PopoverTrigger disabled>
            <div className="flex items-center justify-center">
              <button
                type="button"
                className={cn(
                  'flex items-center transition-all duration-300 ease-in-out hover:scale-[1.02] active:scale-[0.98] p-1 rounded-full',
                )}
              >
                <Ellipsis className={cn('w-4 h-4')} />
              </button>
            </div>
          </PopoverTrigger>
          <PopoverContent className="w-52 p-0 overflow-hidden !rounded-xl border -mt-2 border-gray-100 shadow-[0_8px_24px_-4px_rgba(0,0,0,0.08),0_6px_12px_-6px_rgba(0,0,0,0.04)]">
            <div className="py-1 bg-white">
              <div className="space-y-0.5">
                <MenuButton
                  onClick={handleSelectFirewallPolicy('ACCEPT')}
                  icon="lucide:shield-check"
                  label="Enable in firewall editor"
                />

                <MenuButton
                  onClick={handleSelectFirewallPolicy('BLOCK')}
                  icon="lucide:shield-ban"
                  label="Block in firewall editor"
                />
              </div>
            </div>
          </PopoverContent>
        </Popover>
      )
    },
  },
  {
    accessorKey: 'src_name',
    header: (props) => <CustomHeader {...props} title="src_name" />,
    filterFn: defaultFilterFn,
    enableColumnFilter: true,
    cell: ({ row, column, table }) => {
      const icon = getNDREntityIcon(row.original, 'src_type', 'src_type.src_id')

      return (
        <EntityCell
          icon={icon}
          name={row.getValue('src_name')}
          addr={row.original.src_addr}
          column={column}
          table={table}
          isSource={true}
          meta={meta}
          withAddToQuery={withAddToQuery}
          row={row}
          onFilterChange={onFilterChange}
        />
      )
    },
  },
  {
    accessorKey: 'src_addr',
    header: (props) => <CustomHeader {...props} title="src_addr" />,
    filterFn: defaultFilterFn,
    enableColumnFilter: true,
    cell: ({ row, column }) => (
      <CellActionsMenu
        value={row.getValue('src_addr')}
        column={column}
        onOpenChange={(open) => meta?.onPopoverChange?.(open)}
        withAddToQuery={withAddToQuery}
        onFilterChange={onFilterChange}
      >
        <div className="text-sm text-gray-600 px-3">{row.getValue('src_addr')}</div>
      </CellActionsMenu>
    ),
  },
  {
    accessorKey: 'dst_name',
    header: (props) => <CustomHeader {...props} title="dst_name" />,
    filterFn: defaultFilterFn,
    enableColumnFilter: true,
    cell: ({ row, column, table }) => {
      const icon = getNDREntityIcon(row.original, 'dst_type', 'dst_type.dst_id')

      return (
        <EntityCell
          icon={icon}
          name={row.getValue('dst_name')}
          addr={row.original.dst_addr}
          column={column}
          table={table}
          isSource={false}
          meta={meta}
          withAddToQuery={withAddToQuery}
          row={row}
          onFilterChange={onFilterChange}
        />
      )
    },
  },
  {
    accessorKey: 'dst_addr',
    header: (props) => <CustomHeader {...props} title="dst_addr" />,
    filterFn: defaultFilterFn,
    enableColumnFilter: true,
    cell: ({ row, column }) => (
      <CellActionsMenu
        value={row.getValue('dst_addr')}
        column={column}
        onOpenChange={(open) => meta?.onPopoverChange?.(open)}
        withAddToQuery={withAddToQuery}
        onFilterChange={onFilterChange}
      >
        <div className="text-sm text-gray-600 px-3">{row.getValue('dst_addr')}</div>
      </CellActionsMenu>
    ),
  },
  {
    accessorKey: 'src_process',
    header: (props) => <CustomHeader {...props} title="src_process" />,
    meta: {
      width: '140px',
      minWidth: '140px',
      maxWidth: '140px',
    },
    filterFn: defaultFilterFn,
    enableColumnFilter: true,
    cell: ({ row, column }) => {
      const value = row.original.src_process ? row.original.src_process : 'N/A'
      return (
        <ProcessCell
          value={value}
          column={column}
          meta={meta}
          withAddToQuery={withAddToQuery}
          onFilterChange={onFilterChange}
        />
      )
    },
  },
  {
    accessorKey: 'port',
    header: (props) => <CustomHeader {...props} title="port" />,
    filterFn: defaultFilterFn,
    enableColumnFilter: true,
    meta: {
      justifyContent: 'center',
    },
    cell: ({ row, column, table }) => {
      const port = (row.getValue('port') as number)?.toString()
      return (
        <CellActionsMenu
          value={port}
          column={column}
          onOpenChange={(open) => meta?.onPopoverChange?.(open)}
          withAddToQuery={withAddToQuery}
          onFilterChange={onFilterChange}
        >
          <PortCell port={port} table={table} columnId={column.id} />
        </CellActionsMenu>
      )
    },
  },
  {
    accessorKey: 'dst_process',
    header: (props) => <CustomHeader {...props} title="dst_process" />,
    meta: {
      width: '140px',
      minWidth: '140px',
      maxWidth: '140px',
    },
    filterFn: defaultFilterFn,
    enableColumnFilter: true,
    cell: ({ row, column }) => {
      const value = row.original.dst_process ? row.original.dst_process : 'N/A'
      return (
        <ProcessCell
          value={value}
          column={column}
          meta={meta}
          withAddToQuery={withAddToQuery}
          onFilterChange={onFilterChange}
        />
      )
    },
  },
]

const tabColumns = (meta: TableMeta): ColumnDef<any>[] => [
  {
    accessorKey: 'remoteEntity.name',
    header: (props) => <CustomHeader {...props} title="remoteEntity.name" />,
    filterFn: defaultFilterFn,
    enableColumnFilter: true,
    cell: ({ row, column, table }) => {
      const name = row.original.remoteEntity?.name || row.original.remoteAddr
      return (
        <EntityCell
          icon={bwNdrEntityIconMap[row.original.remoteEntity?.type]}
          name={name}
          addr={row.original.remoteAddr}
          column={column}
          table={table}
          isSource={true}
          meta={meta}
        />
      )
    },
  },
  {
    accessorKey: 'providerInformation.process',
    header: (props) => <CustomHeader {...props} title="providerInformation.process" />,
    meta: {
      width: '140px',
      minWidth: '140px',
      maxWidth: '140px',
    },
    filterFn: defaultFilterFn,
    enableColumnFilter: true,
    cell: ({ row, column }) => {
      const value = row.original.providerInformation?.process || 'N/A'
      return <ProcessCell value={value} column={column} meta={meta} />
    },
  },
  {
    accessorKey: 'port',
    header: (props) => <CustomHeader {...props} title="port" />,
    filterFn: defaultFilterFn,
    enableColumnFilter: true,
    meta: {
      justifyContent: 'center',
    },
    cell: ({ row, column, table }) => {
      const port = row.getValue('port')
      return (
        <CellActionsMenu
          value={port?.toString() || ''}
          column={column}
          onOpenChange={(open) => meta?.onPopoverChange?.(open)}
        >
          <PortCell port={port} table={table} columnId={column.id} />
        </CellActionsMenu>
      )
    },
  },
  {
    accessorKey: 'direction',
    header: (props) => <CustomHeader {...props} title="direction" />,
    meta: {
      width: '120px',
      minWidth: '120px',
      maxWidth: '120px',
    },
    filterFn: defaultFilterFn,
    enableColumnFilter: true,
    cell: ({ row }) => {
      const direction = row.getValue('direction') as string
      const value = direction === 'egress' ? 'outbound' : 'inbound'
      return <DirectionCell value={value} />
    },
  },
]

const createColumns = (
  isPortflow: boolean,
  meta: TableMeta,
  withAddToQuery?: boolean,
  onFilterChange?: PortflowQueryType[],
): ColumnDef<any>[] => {
  return isPortflow ? portflowColumns(meta, onFilterChange, withAddToQuery) : tabColumns(meta)
}

export function TrafficPatternsTable({
  data,
  isLoading,
  meta,
  onColumnVisibilityChange,
  onPageDataChange,
  className,
  isPortflow = false,
  isIssueDrawer = false,
  pageSize: propPageSize,

  pageIndex: propPageIndex,
  totalPages: propTotalPages,
  totalItemsFound: propTotalItemsFound,

  onSortingColumnChange,
  onFilterColumnChange,

  onSpyCurrentPageChange,

  onPageIndexChange,

  manualPagination,
  manualSorting,
  manualFiltering,

  isTotalPageLoading,
  isNextPageDataLoading,
  hideChangeColumnVisibilityButton = false,
  withAddToQuery = false,
  onFilterChange,
}: TrafficPatternsTableProps & {
  isPortflow?: boolean
  isIssueDrawer?: boolean
  hideChangeColumnVisibilityButton: boolean
  onSpyCurrentPageChange?: (onSpyCurrentPageChange: any) => void
  withAddToQuery: boolean
  onFilterChange?: (filterOptions: PortflowQueryType[]) => void
}) {
  const [columnFiltersState, setColumnFiltersState] = useState([])

  const [{ pageIndex }, setPagination] = useState({
    pageIndex: 1,
    pageSize: propPageSize || 6,
  })

  const { tableId } = useGraphConfig()
  const { integrationType } = useAppConfig()

  const pageIndexNumber = propPageIndex ?? pageIndex

  const totalItemsFound = propTotalItemsFound || data.length

  const setPageChange = onPageIndexChange || setPagination
  const setColumnFilters = onFilterColumnChange || setColumnFiltersState

  // Get visible columns from store
  const { visibleColumns, setVisibleColumns } = useGraphStore()
  const isInitialized = useRef<boolean>(false)

  useEffect(() => {
    if (data?.length === 0 || !integrationType) return
    if (isInitialized.current) return

    // Set default columns based on integration type
    const defaultColumns = ['src_name', 'dst_name', 'port']

    // Only add process columns if not AWS integration and process data exists
    if (integrationType !== 'aws') {
      const hasProcessData = data.some((row) => row.src_process || row.dst_process)
      if (hasProcessData) {
        defaultColumns.push('src_process', 'dst_process')
      }
    }

    if (!isIssueDrawer && integrationType !== 'aws') {
      defaultColumns.unshift('firewallEnabled')
    }

    isInitialized.current = true
    setVisibleColumns(defaultColumns)
  }, [data, visibleColumns.length, integrationType])

  // Initialize visible columns on mount, excluding IP address columns
  useDeepCompareEffect(() => {
    if (visibleColumns.length === 0) {
      const defaultColumns = portflowColumns(meta)
        .map((col) => col.accessorKey)
        .filter((key) => !['dst_addr', 'src_addr', isIssueDrawer ? 'firewallEnabled' : ''].includes(key))

      setVisibleColumns(defaultColumns)
    }
  }, [visibleColumns.length, setVisibleColumns])

  // Create column visibility object from array
  const columnVisibility = useMemo(() => {
    const visibilityObj = {}
    const allColumns = portflowColumns(meta).map((col) => col.accessorKey)

    // Set all columns to false by default
    allColumns.forEach((colId) => {
      visibilityObj[colId] = false
    })

    // Set visible columns to true
    visibleColumns?.forEach((colId) => {
      visibilityObj[colId] = true
    })

    return visibilityObj
  }, [visibleColumns])
  const columns = createColumns(isPortflow, meta, withAddToQuery, onFilterChange)

  // Get export columns and data
  const exportColumns = useMemo(() => getExportColumns(columns, visibleColumns), [columns, visibleColumns])
  const exportData = useMemo(() => getExportData(data, visibleColumns), [data, visibleColumns])

  // // Update parent with current page data and sorting changes
  useEffect(() => {
    onPageDataChange?.(data)
  }, [data, onPageDataChange])

  if (isLoading) {
    return (
      <div className="h-full w-full flex items-center justify-center dark:bg-[#111827]">
        <Loading loading />
      </div>
    )
  }

  return (
    <ErrorBoundary>
      <div className={cn('h-full flex flex-col max-h-full', className)}>
        <div className="flex-shrink-0 flex items-center justify-between px-4 py-2.5 bg-white dark:border-gray-700 dark:bg-[#111827] border border-gray-200/50 rounded-t-lg shadow-sm">
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 rounded-lg bg-blue-50  dark:bg-gray-700 flex items-center justify-center">
              {isTotalPageLoading ? (
                <Loader2 className="animate-spin w-4 h-4 text-blue-500 dark:text-gray-200" />
              ) : (
                <Network className="w-4 h-4 text-blue-500 dark:text-gray-200" />
              )}
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-700">
                {tableId === 'traffic' ? 'Traffic' : 'Traffic Patterns'}
              </h3>
              <p className="text-xs text-gray-500">
                {isTotalPageLoading ? (
                  <span className="text-gray-400 animate-pulse">
                    {tableId === 'traffic' ? 'Loading traffics...' : 'Loading patterns...'}
                  </span>
                ) : (
                  <span>
                    {tableId === 'traffic' ? (
                      <>
                        {formatNumber(totalItemsFound)} {totalItemsFound === 1 ? 'traffic' : 'traffics'} found
                      </>
                    ) : (
                      <>
                        {formatNumber(totalItemsFound)} {totalItemsFound === 1 ? 'pattern' : 'patterns'} found
                      </>
                    )}
                  </span>
                )}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <ExportCSVButton columns={exportColumns} data={exportData} totalItems={totalItemsFound} tableId={tableId} />
            {meta?.isPinned !== undefined && (
              <Button
                size="sm"
                variant="flat"
                className={cn(
                  'h-8 px-3 relative overflow-hidden rounded-lg dark:border-gray-700',
                  'transition-all duration-300 ease-in-out',
                  'hover:scale-[1.02] active:scale-[0.98]',
                  'before:absolute before:inset-0 before:transition-all before:duration-300',
                  'after:absolute after:inset-0 after:transition-all after:duration-300',
                  meta.isPinned
                    ? [
                        'bg-gradient-to-r dark:from-sky-600 dark:to-sky-400 from-blue-50 to-blue-100/50',
                        'text-blue-600 hover:text-blue-700',
                        'before:bg-blue-400/5 before:opacity-0 hover:before:opacity-100',
                        'after:border after:border-blue-200/50 after:rounded-lg',
                        'shadow-[inset_0_0_0_1px_rgba(37,99,235,0.2)]',
                        'hover:shadow-[inset_0_0_0_1px_rgba(37,99,235,0.4),0_2px_4px_rgba(37,99,235,0.1)]',
                      ]
                    : [
                        'bg-gradient-to-r dark:from-gray-800 dark:to-gray-700 from-gray-50 to-gray-100/50',
                        'text-gray-600 hover:text-gray-700',
                        'before:bg-gray-400/5 before:opacity-0 hover:before:opacity-100',
                        'after:border  dark:after:border-gray-700 after:border-gray-200 after:rounded-lg',
                        'shadow-[inset_0_0_0_1px_rgba(0,0,0,0.1)]',
                        'hover:shadow-[inset_0_0_0_1px_rgba(0,0,0,0.15),0_2px_4px_rgba(0,0,0,0.05)]',
                      ],
                )}
                onClick={() => meta.onPinClick?.(!meta.isPinned)}
              >
                <div className="flex items-center gap-2 dark:text-gray-200">
                  <Icon
                    icon={meta.isPinned ? 'ph:push-pin-fill' : 'ph:push-pin'}
                    className={cn(
                      'w-4 h-4 relative z-10',
                      'transition-all duration-300',
                      meta.isPinned ? 'rotate-45 scale-110 group-hover:scale-110' : 'group-hover:scale-110',
                    )}
                  />
                  <span className={cn('text-xs font-medium relative z-10', 'transition-all duration-300')}>
                    {meta.isPinned ? 'Pinned' : 'Pin'}
                  </span>
                </div>
              </Button>
            )}
          </div>
        </div>
        <div className="flex-1 min-h-0 overflow-hidden">
          <ModernTable
            columns={columns}
            data={data}
            meta={{
              ...meta,
              onColumnVisibilityChange,
            }}
            columnFilters={columnFiltersState}
            setPagination={setPageChange}
            totalPages={propTotalPages!}
            pageIndex={pageIndexNumber}
            onSortingColumnChange={onSortingColumnChange}
            onFilterColumnChange={setColumnFilters as onFilterColumnChangeType}
            columnVisibility={columnVisibility}
            manualPagination={manualPagination}
            manualSorting={manualSorting}
            manualFiltering={manualFiltering}
            isTotalPageLoading={isTotalPageLoading}
            isNextPageDataLoading={isNextPageDataLoading}
            setColumnVisibility={() => {}}
            className={cn(
              'h-full flex flex-col max-h-full',
              '!dark:rounded-none border border-gray-200/50 dark:border-gray-700 dark:bg-[#111827] shadow-sm',
              '[&_td]:px-1 [&_td]:py-1.5',
              '[&_th]:px-1 [&_th]:py-2',
              '[&_tr:hover]:bg-gray-50/50 dark:[&_tr:hover]:bg-gray-700',
              '[&_thead_tr]:bg-gray-50/50 [&_thead_tr]:border-b [&_thead_tr]:border-gray-100',
              className,
            )}
            scrollDirection="y"
            onPropagateCurrentPageData={onSpyCurrentPageChange}
            hideChangeColumnVisibilityButton={hideChangeColumnVisibilityButton}
          />
        </div>
      </div>
    </ErrorBoundary>
  )
}
