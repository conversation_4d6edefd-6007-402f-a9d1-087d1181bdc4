import CustomCard from '@/customComponents/CustomCard'
import { CardBody } from '@nextui-org/react'
import { motion } from 'framer-motion'
import { ReactFlowProvider } from 'reactflow'
import { Info, AlertCircle } from 'lucide-react'
import { TrafficPatternsTable } from '@/components/shared/TrafficPatternsTable'
import EntityTrafficPatternsGraph from '@/views/NDR/Inventory/entityProfile/components/EntityTrafficPatternsGraph'
import {
  MAX_WORD_COUNT,
  processToJSX,
} from '@/views/NDR/Issues/components/NdrIssueTableDrawer/ndrIssueTableDrawerutils'
import { EntityTypeWithId } from '@/firestoreQueries/ndr/entities/customEntitiesTypes'
import { PortflowQueryType } from '@/firestoreQueries/ndr/portflowShortcuts/portflowShortcutsTypes'
import { useNavigate } from 'react-router-dom'

interface SharedTrafficViewProps {
  isExpanded: boolean
  toggleExpanded: () => void
  explanation: string
  patternsCount: number
  tableData: any[]
  selectedEntityId: string | null
  currentPagePatterns: any[]
  nodeRefId: any
  onRowMouseOver: (rowOriginal: any) => void
  onRowMouseLeave: () => void
  onPageDataChange: (pageData: any[]) => void
  entity: EntityTypeWithId
  isLoading?: boolean
  withAddToQuery?: boolean
  fullWidthReason?: boolean
  withImpactAnalysis?: boolean
}

const SharedTrafficView = ({
  isExpanded,
  toggleExpanded,
  explanation,
  patternsCount,
  tableData,
  selectedEntityId,
  currentPagePatterns,
  nodeRefId,
  onRowMouseOver,
  onRowMouseLeave,
  onPageDataChange,
  entity,
  isLoading = false,
  withAddToQuery = true,
  fullWidthReason = false,
  withImpactAnalysis = true,
}: SharedTrafficViewProps) => {
  const navigate = useNavigate()

  const onFilterChangeHandler = (newFilters: PortflowQueryType[]) => {
    const params = new URLSearchParams()
    params.set(newFilters[0].key, newFilters[0].values.join(','))
    navigate('/graph' + '?' + params.toString())
  }

  return (
    <div className="flex flex-col gap-6">
      {/* Description Cards */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className={fullWidthReason ? 'flex flex-col gap-6' : 'grid grid-cols-2 gap-6'}
      >
        <CustomCard className="bg-gradient-to-br from-blue-50 via-white to-white border border-blue-100/50 shadow-sm hover:shadow-md transition-all duration-300">
          <CardBody className="p-5">
            <div className="flex items-start gap-3 mb-3">
              <div className="p-2 rounded-lg bg-blue-500/10 border border-blue-500/20 shrink-0">
                <Info className="w-4 h-4 text-blue-500" />
              </div>
              <div>
                <h4 className="text-base font-semibold text-gray-900 mb-1">Reason</h4>
                <p className="text-sm text-gray-500">Detailed explanation of the detection</p>
              </div>
            </div>
            <div>{processToJSX(explanation, MAX_WORD_COUNT, isExpanded, toggleExpanded)}</div>
          </CardBody>
        </CustomCard>

        {withImpactAnalysis && (
          <CustomCard className="bg-gradient-to-br from-amber-50 via-white to-white border border-amber-100/50 shadow-sm hover:shadow-md transition-all duration-300">
            <CardBody className="p-5">
              <div className="flex items-start gap-3 mb-3">
                <div className="p-2 rounded-lg bg-amber-500/10 border border-amber-500/20 shrink-0">
                  <AlertCircle className="w-4 h-4 text-amber-500" />
                </div>
                <div>
                  <h4 className="text-base font-semibold text-gray-900 mb-1">Impact Analysis</h4>
                  <p className="text-sm text-gray-500">Potential security implications</p>
                </div>
              </div>
              <div>
                <div className="text-sm text-gray-600 leading-relaxed">
                  This detection affects {patternsCount} traffic patterns and may impact system security.
                </div>
              </div>
            </CardBody>
          </CustomCard>
        )}
      </motion.div>

      {/* Traffic Patterns Table and Graph */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="grid grid-cols-3"
      >
        {/* Table */}
        <CustomCard className="col-span-2 bg-white/80 backdrop-blur-sm shadow-sm">
          <CardBody className="p-0 h-[400px]">
            <TrafficPatternsTable
              data={tableData}
              pageSize={10}
              meta={{
                onRowMouseOver,
                onRowMouseLeave,
              }}
              onPageDataChange={onPageDataChange}
              isPortflow
              isIssueDrawer
              className="flex-1 min-h-0 flex flex-col"
              isLoading={isLoading}
              hideChangeColumnVisibilityButton
              withAddToQuery={withAddToQuery}
              onFilterChange={onFilterChangeHandler}
            />
          </CardBody>
        </CustomCard>

        {/* Graph */}
        <CustomCard className="bg-gradient-to-br from-white via-gray-50/90 to-gray-100/80 shadow-sm">
          <CardBody className="p-0 h-[400px]">
            <ReactFlowProvider>
              <EntityTrafficPatternsGraph
                entityId={selectedEntityId || ''}
                filteredTrafficPatterns={currentPagePatterns}
                hoverableNodeRef={nodeRefId}
                isIssueModal={true}
                entity={entity}
              />
            </ReactFlowProvider>
          </CardBody>
        </CustomCard>
      </motion.div>
    </div>
  )
}

export default SharedTrafficView
