import { Tooltip } from '@nextui-org/react'
import { ReactNode } from 'react'

interface ExplanationTooltipProps {
  children: ReactNode
  tooltipContent: string
  placement?:
    | 'top'
    | 'bottom'
    | 'right'
    | 'left'
    | 'top-start'
    | 'top-end'
    | 'bottom-start'
    | 'bottom-end'
    | 'left-start'
    | 'left-end'
    | 'right-start'
    | 'right-end'
}

const ExplanationTooltip = ({ children, tooltipContent, placement }: ExplanationTooltipProps) => {
  return (
    <Tooltip
      content={tooltipContent}
      placement={placement || 'bottom'}
      classNames={{
        content: ['bg-gray-700', 'text-white', 'text-sm', 'px-3 py-2', 'rounded-lg', 'shadow-md', 'max-w-[280px]'],
        base: 'before:bg-gray-700',
      }}
      delay={0}
      closeDelay={0}
    >
      {children}
    </Tooltip>
  )
}

export default ExplanationTooltip
