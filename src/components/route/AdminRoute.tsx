// src/components/route/AdminRoute.tsx
import useDashboardStore from '@/zustandStores/useDashboardsStore'
import { useAuth } from '@clerk/clerk-react'
import { Navigate, Outlet } from 'react-router-dom'

const AdminRoute = () => {
  const isAdmin = useAuth().orgRole === 'admin'
  const basePath = useDashboardStore((d) => d.basePath)
  if (!isAdmin) {
    return <Navigate replace to={basePath} />
  }

  return <Outlet />
}

export default AdminRoute
