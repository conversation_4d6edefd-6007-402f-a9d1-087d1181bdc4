import { Icon } from '@iconify/react'
import {
    Accordion,
    AccordionItem,
    cn,
    Listbox,
    ListboxItem,
    ListboxSection,
    Tooltip,
    type ListboxProps,
    type ListboxSectionProps,
    type Selection,
} from '@nextui-org/react'
import React, { useCallback, useEffect } from 'react'

import reactQueryKeyStore from '@/configs/reactQueryKeyStore'
import useGetIntegrations from '@/firestoreQueries/integrations/hooks/useGetIntegrations'
import useDashboardStore from '@/zustandStores/useDashboardsStore'
import { useAuth, useOrganization } from '@clerk/clerk-react'
import { useNavigate } from 'react-router-dom'

export enum SidebarItemType {
    Nest = 'nest',
}

export type SidebarItem = {
    key: string
    title: string
    icon?: string
    href?: string
    type?: SidebarItemType.Nest
    startContent?: React.ReactNode
    endContent?: React.ReactNode
    items?: SidebarItem[]
    className?: string
}

export type SidebarProps = Omit<ListboxProps<SidebarItem>, 'children'> & {
    items: SidebarItem[]
    isCompact?: boolean
    hideEndContent?: boolean
    iconClassName?: string
    sectionClasses?: ListboxSectionProps['classNames']
    classNames?: ListboxProps['classNames']
    defaultSelectedKey: string
    onSelect?: (key: string) => void
}

const Sidebar = React.forwardRef<HTMLElement, SidebarProps>(
    (
        {
            items,
            isCompact,
            defaultSelectedKey,
            onSelect,
            hideEndContent,
            sectionClasses: sectionClassesProp = {},
            itemClasses: itemClassesProp = {},
            iconClassName,
            classNames,
            className,
            ...props
        },
        ref
    ) => {
        const organizationId = useOrganization().organization?.id
        const { data: integrations } = useGetIntegrations({
            queryKey: reactQueryKeyStore.integrations(),
            organizationId,
        })

        const basePath = useDashboardStore((s) => s.basePath)
        const isUserAdmin = useAuth().orgRole === 'admin'
        const navigate = useNavigate()
        useEffect(() => {
            if (integrations && integrations.length === 0 && !isUserAdmin) {
                navigate('/waiting-setup')
            } else if (integrations && integrations.length === 0 && isUserAdmin) {
                const allowedPaths = ['/integrations', '/resources/new/standalone', '/resources/new/aws']
                const path = window.location.pathname

                const isAllowed = allowedPaths.some((p) => {
                    if (path.startsWith(p)) {
                        // Special handling for "/resources/new/aws" to allow additional segments
                        if (p === '/resources/new/aws' && path.length > p.length) {
                            return true
                        }
                        return path === p || path.startsWith(p + '/')
                    }
                    return false
                })

                if (!isAllowed) {
                    navigate(basePath)
                }
            }
        }, [integrations, isUserAdmin, navigate, basePath])

        const [selected, setSelected] = React.useState<React.Key>(defaultSelectedKey)

        const sectionClasses = {
            ...sectionClassesProp,
            base: cn(sectionClassesProp?.base, 'w-full', {
                'p-0 max-w-[44px]': isCompact,
            }),
            group: cn(sectionClassesProp?.group, {
                'flex flex-col gap-1': isCompact,
            }),
            heading: cn(sectionClassesProp?.heading, {
                hidden: isCompact,
            }),
        }

        useEffect(() => {
            handleUrlChange()

            // eslint-disable-next-line react-compiler/react-compiler
            // eslint-disable-next-line react-hooks/exhaustive-deps
        }, [location.pathname, basePath])

        const handleUrlChange = () => {
            const path =
                window.location.pathname.split('/')[0] === '/'
                    ? window.location.pathname.split('/')[0]
                    : window.location.pathname.split('/')[1]

            if (!items.some((item) => item.key === `/${path}`)) {
                navigate(defaultSelectedKey)
            }

            if (path === '') {
                setSelected(basePath)
            } else {
                setSelected(`/${path}`)
            }
        }
        const isAdmin = useAuth().orgRole === 'admin'

        const itemClasses = {
            ...itemClassesProp,
            base: cn(itemClassesProp?.base, {
                'w-11 h-11 gap-0 p-0': isCompact,
            }),
        }

        const renderItem = useCallback(
            (item: SidebarItem) => {
                const isNestType = item.items && item.items?.length > 0 && item?.type === SidebarItemType.Nest

                if (isNestType) {
                    return renderNestItem(item)
                }
                return (
                    <ListboxItem
                        aria-label={item.title}
                        {...item}
                        onClick={(e) => {
                            e.preventDefault()
                        }}
                        key={item.key}
                        startContent={
                            isCompact ? null : item.icon ? (
                                <Icon
                                    className={cn(
                                        'text-default-500 group-data-[selected=true]:text-default-50',
                                        iconClassName
                                    )}
                                    icon={item.icon}
                                    width={24}
                                />
                            ) : (
                                item.startContent ?? null
                            )
                        }
                        textValue={item.title}
                        title={isCompact ? null : item.title}
                    >
                        {isCompact ? (
                            <Tooltip aria-label={item.title} content={item.title} placement="right">
                                <div className="flex w-full items-center justify-center">
                                    {item.icon ? (
                                        <Icon
                                            className={cn(
                                                'text-default-500 group-data-[selected=true]:text-default-50',
                                                iconClassName
                                            )}
                                            icon={item.icon}
                                            width={24}
                                        />
                                    ) : (
                                        item.startContent ?? null
                                    )}
                                </div>
                            </Tooltip>
                        ) : null}
                    </ListboxItem>
                )
            },

            // eslint-disable-next-line react-hooks/exhaustive-deps
            [iconClassName, isCompact]
        )

        const renderNestItem = useCallback(
            (item: SidebarItem) => {
                const isNestType = item.items && item.items?.length > 0 && item?.type === SidebarItemType.Nest

                if (isNestType) {
                    // Is a nest type item , if so we need to remove the href
                    delete item.href
                }

                return (
                    <ListboxItem
                        aria-label={item.title}
                        {...item}
                        key={item.key}
                        classNames={{
                            base: cn(
                                { 'h-auto p-0': !isCompact && isNestType },
                                {
                                    'inline-block w-11': isCompact && isNestType,
                                }
                            ),
                        }}
                        endContent={isCompact || isNestType || hideEndContent ? null : item.endContent ?? null}
                        startContent={
                            isCompact || isNestType ? null : item.icon ? (
                                <Icon
                                    className={cn(
                                        'text-default-500 group-data-[selected=true]:text-default-50',
                                        iconClassName
                                    )}
                                    icon={item.icon}
                                    width={24}
                                />
                            ) : (
                                item.startContent ?? null
                            )
                        }
                        title={isCompact || isNestType ? null : item.title}
                    >
                        {isCompact ? (
                            <Tooltip content={item.title} placement="right">
                                <div className="flex w-full items-center justify-center">
                                    {item.icon ? (
                                        <Icon
                                            className={cn(
                                                'text-default-500 group-data-[selected=true]:text-default-50',
                                                iconClassName
                                            )}
                                            icon={item.icon}
                                            width={24}
                                        />
                                    ) : (
                                        item.startContent ?? null
                                    )}
                                </div>
                            </Tooltip>
                        ) : null}
                        {!isCompact && isNestType ? (
                            <Accordion aria-label={item.title + ' accordion'} className={'p-0'}>
                                <AccordionItem
                                    key={item.key}
                                    aria-label={item.title}
                                    classNames={{
                                        heading: 'pr-3',
                                        trigger: 'p-0',
                                        content: 'py-0 pl-4',
                                    }}
                                    title={
                                        item.icon ? (
                                            <div className={'flex h-11 items-center gap-2 px-2 py-1.5'}>
                                                <Icon
                                                    className={cn(
                                                        'text-default-500 group-data-[selected=true]:text-default-50',
                                                        iconClassName
                                                    )}
                                                    icon={item.icon}
                                                    width={24}
                                                />
                                                <span className="text-small font-medium text-default-500 group-data-[selected=true]:text-foreground">
                                                    {item.title}
                                                </span>
                                            </div>
                                        ) : (
                                            item.startContent ?? null
                                        )
                                    }
                                >
                                    {item.items && item.items?.length > 0 ? (
                                        <Listbox
                                            className={'mt-0.5'}
                                            classNames={{
                                                list: cn('border-l border-default-200 pl-4'),
                                            }}
                                            items={item.items}
                                            variant="flat"
                                        >
                                            {item.items.map(() => renderItem(item))}
                                        </Listbox>
                                    ) : (
                                        renderItem(item)
                                    )}
                                </AccordionItem>
                            </Accordion>
                        ) : null}
                    </ListboxItem>
                )
            },

            // eslint-disable-next-line react-hooks/exhaustive-deps
            [iconClassName, hideEndContent, isCompact]
        )

        return (
            <Listbox
                aria-label="sidebar"
                key={isCompact ? 'compact' : 'default'}
                ref={ref}
                hideSelectedIcon
                as="nav"
                className={cn('list-none', className)}
                classNames={{
                    ...classNames,
                    list: cn('items-center', classNames?.list),
                }}
                color="default"
                itemClasses={{
                    ...itemClasses,
                    base: cn(
                        'px-3 min-h-11 rounded-large h-[44px] data-[selected=true]:!bg-foreground',
                        itemClasses?.base
                    ),
                    title: cn(
                        'text-small font-medium text-default-500 group-data-[selected=true]:text-default-50',
                        itemClasses?.title
                    ),
                }}
                items={items}
                selectedKeys={[selected] as unknown as Selection}
                selectionMode="single"
                variant="flat"
                disabledKeys={
                    integrations && integrations.length === 0
                        ? ['/dashboard', '/users', '/outbound-flow', '/port-flow', '/scanner', '/portals', '/resources']
                        : []
                }
                onAction={(key) => {
                    setSelected(key as React.Key)
                    onSelect?.(key as string)
                }}
                {...props}
            >
                {(item) => {
                    if (!isAdmin && item.key === '/adminMenu') {
                        return null as any
                    }

                    return item.items && item.items?.length > 0 && item?.type === SidebarItemType.Nest ? (
                        renderNestItem(item)
                    ) : item.items && item.items?.length > 0 ? (
                        <ListboxSection
                            key={item.key}
                            classNames={sectionClasses}
                            showDivider={isCompact}
                            title={item.title}
                        >
                            {item.items.map((item) => renderItem(item))}
                        </ListboxSection>
                    ) : (
                        renderItem(item)
                    )
                }}
            </Listbox>
        )
    }
)

Sidebar.displayName = 'Sidebar'

export default Sidebar
