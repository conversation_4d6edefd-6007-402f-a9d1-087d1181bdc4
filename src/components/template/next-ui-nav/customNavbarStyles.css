.cl-organizationSwitcher-root {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.cl-organizationSwitcher-root * {
    outline: none;
    @apply outline-none border-none focus:outline-none !important;
}

.cl-organizationSwitcherTrigger {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    outline: none;
    @apply rounded-full;
}

.cl-avatarBox {
    @apply rounded-full;
}

.cl-organizationSwitcher-root *:focus {
    outline: none !important;
    box-shadow: none !important;
}

.cl-rootBox *:focus {
    outline: none !important;
    box-shadow: none !important;
}

.cl-organizationSwitcherTriggerIcon {
    display: none;
    padding: 0;
}

.hide-text * > .cl-organizationPreviewTextContainer {
    display: none;
}

.hide-text * > .cl-internal-1ntkffu {
    display: none;
}

.hide-text * > .cl-organizationSwitcherTrigger {
    padding: 0;
    @apply flex justify-center items-center;
}

.hide-text * > .cl-rootBox {
    @apply flex justify-center items-center rounded-full;
    width: auto;
}

@media (max-width: 768px) {
    .cl-organizationPreviewTextContainer {
        display: none;
    }
    .cl-internal-1ntkffu {
        display: none;
    }

    .cl-organizationSwitcherTrigger {
        padding: 0;
    }

    .cl-rootBox {
        @apply flex justify-center items-center rounded-full;
        width: auto;
    }
}
