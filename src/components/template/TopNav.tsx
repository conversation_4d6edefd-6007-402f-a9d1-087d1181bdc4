import cn from 'classnames'
import { Mode } from '@/@types/theme'
import { useTypedFlags } from '@/customHooks/useTypedFlags'
import { getNavbarObject } from '@/customUtils/getNavbarGroups'
import { parentIcons } from '@/customUtils/navBarIconsMap'
import useDarkMode from '@/utils/hooks/useDarkmode'
import {
  OrganizationProfile,
  OrganizationSwitcher,
  useAuth,
  useOrganization,
  UserButton,
  useUser,
} from '@clerk/clerk-react'
import { Tab, Tabs, Tooltip } from '@nextui-org/react'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import Logo from './Logo'
import { Activity, BellRing, Plug, Webhook } from 'lucide-react'
import { CiMail } from 'react-icons/ci'
import ActivityLog from '@/components/ActivityLogs/components/ActivityLogs'
import useGetNdrIssuesRealtime from '@/firestoreQueries/ndr/issues/hooks/useGetNdrIssuesRealtime'
import useFirestoreTimeRange from '@/customHooks/useFirestoreTimeRange'
import _ from 'lodash'
import { baseDates } from '@/utils/baseDates'
import IntegrationsScreen from '@/views/Integrations'
import { NotificationSettings } from '../NotificationSettings/NotificationSettings'
import { AllModals } from './AllModals'
import { IoMdSettings } from 'react-icons/io'
import { useIntegrationProfileModalStore } from '@/store/integrationProfileModalStore'
import Webhooks from '@/components/template/Webhooks/Weebhoks'
import EmailSettings from '@/components/template/EmailSettings/EmailSettings'
import useWebhookStore from '@/components/template/Webhooks/store/store'
import ExplanationTooltip from '../ExplanationTooltip/ExplanationTooltip'

const gradientStyles = {
  access: 'bg-gradient-to-r from-teal-500 to-teal-600',
  ndr: 'bg-gradient-to-r from-blue-500 to-blue-600',
  scanner: 'bg-gradient-to-r from-purple-500 to-purple-600',
  organization: 'bg-gradient-to-r from-gray-500 to-gray-600',
  hunterx: 'bg-gradient-to-r from-red-500 to-red-600',
}

const NewIcon = () => {
  const [isDarkMode] = useDarkMode()
  return (
    <>
      <div
        style={{
          boxShadow: '0px 0px 60px 10px #008fff',
          width: '20px',
          position: 'absolute',
          top: '0',
          opacity: '0.75',
        }}
      />
      <div
        style={{
          boxShadow: '45px 40px 60px 10px rgb(255 0 0 / 50%)',
          width: '20px',
          right: '42px',
          position: 'absolute',
          top: 0,
        }}
      />
      <div
        style={{
          boxShadow: '45px 40px 60px 10px rgb(122 0 255 / 50%)',
          width: '20px',
          right: '42px',
          position: 'absolute',
        }}
      />
      <div
        style={{
          boxShadow: '-45px 0 60px 10px #af00ff',
          width: '20px',
          position: 'absolute',
          opacity: '0.75',
        }}
      />
      <Logo mode={isDarkMode ? 'dark' : 'light'} />
    </>
  )
}

interface NavigationTabContentProps {
  item: { title: string; key: string }
  issuesCount: number
  onMouseOver: () => void
  onMouseOut: () => void
}

function NavigationTabContent({ item, issuesCount, onMouseOver, onMouseOut }: NavigationTabContentProps) {
  const title = <span>{item.title}</span>
  return (
    <div onMouseOver={onMouseOver} onMouseOut={onMouseOut}>
      {item.key === 'insights' ? (
        <div className="flex items-center gap-1 relative">
          <ExplanationTooltip
            tooltipContent="Smart finding, reveals hidden security issues or improvements."
            placement="bottom-start"
          >
            {title}
          </ExplanationTooltip>
          <span
            hidden={issuesCount === 0}
            className="w-[17px] h-[17px] pt-[5px] px-[1px] truncate text-center leading-none bg-red-500 text-white rounded-full text-[0.55em] absolute top-[-7px] right-[-18px]"
          >
            {issuesCount > 99 ? '99+' : issuesCount}
          </span>
        </div>
      ) : (
        title
      )}
    </div>
  )
}

async function simulateClicks() {
  // Click the organization avatar
  const avatar = document.querySelector('.cl-organizationPreviewAvatarBox') as HTMLElement
  if (avatar) avatar?.click()

  // Wait 100ms
  await new Promise((resolve) => setTimeout(resolve, 100))

  // Click the "Manage" button
  const manageButton = document.querySelector(
    '.cl-organizationSwitcherPopoverActionButton__manageOrganization',
  ) as HTMLElement
  if (manageButton) manageButton?.click()
}

export function SettingsPopover() {
  // Helper to open the org-modal on a given tab
  const openOrgTab = () => {
    simulateClicks()
  }

  return (
    <button className="p-1" onClick={openOrgTab}>
      <IoMdSettings size={20} className="text-gray-600 hover:text-gray-800" />
    </button>
  )
}

export default function HorizontalNavbar() {
  const organization = useOrganization().organization
  const { timestamp } = useFirestoreTimeRange(baseDates)
  const organizationId = useOrganization().organization?.id as string
  const { issues } = useGetNdrIssuesRealtime({
    organizationId: organizationId!,
    timestamp,
    filters: [
      {
        fieldPath: 'status',
        opStr: '==',
        value: 'open',
      },
    ],
  })
  const issuesWithOpenedStatusLen = (issues ?? []).length
  const setModalState = useIntegrationProfileModalStore((state) => state.setModalState)
  const navigate = useNavigate()
  const [isDarkmode, setDarkmode] = useDarkMode()
  const location = useLocation()
  const { isMock, showAccessModule, showScannerModule, showOrganizationModule, showHunterXModule } = useTypedFlags()
  const isAdmin = useAuth().orgRole === 'admin'
  const menuItems = getNavbarObject(
    isMock,
    isAdmin,
    showAccessModule,
    showScannerModule,
    showOrganizationModule,
    showHunterXModule,
  )
  const [isTransitioning, setIsTransitioning] = useState(false)

  const { setOrganizationId } = useWebhookStore()

  useEffect(() => {
    setOrganizationId(organizationId)
  }, [organizationId])

  const handleThemeChange = useCallback((newTheme: Mode) => {
    setIsTransitioning(true)
    setDarkmode(newTheme)
    localStorage.setItem('theme', newTheme)
    // Immediately start fading out
    requestAnimationFrame(() => {
      setIsTransitioning(false)
    })
  }, [])

  const { user } = useUser()
  const userId = user?.id as string

  useEffect(() => {
    const theme = localStorage.getItem('theme')
    if (!theme) {
      localStorage.setItem('theme', 'light')
    }
  }, [])

  const getActiveCategoryFromPath = useCallback(
    (path: string) => {
      for (const category of menuItems) {
        if (category.items.some((item) => path.startsWith(item.path))) {
          return category.key
        }
      }
      return menuItems[0].key
    },
    [menuItems],
  )

  useEffect(() => {
    const currentCategory = getActiveCategoryFromPath(location.pathname)
    // TODO(pkuzina): Excluded condition for `/hunterx-graph` `/hunterx-detections`, `/hunterx-controls` pages, because dark theme is not ready
    if (
      currentCategory === 'hunterx' &&
      !['/hunterx-graph', '/hunterx-detections', '/hunterx-controls'].includes(location.pathname)
    ) {
      setDarkmode('dark')
      localStorage.setItem('theme', 'dark')
    } else {
      setDarkmode('light')
      localStorage.setItem('theme', 'light')
    }
  }, [setDarkmode, location.pathname, getActiveCategoryFromPath])
  const [icon, setIcon] = useState<'dark' | 'light'>(isDarkmode ? 'dark' : 'light')
  const [openTooltip, setOpenTooltip] = useState('none')
  useEffect(() => {
    setIcon(isDarkmode ? 'dark' : 'light')
  }, [isDarkmode])

  const [activeCategory, setActiveCategory] = useState(() => getActiveCategoryFromPath(location.pathname))

  useEffect(() => {
    const newActiveCategory = getActiveCategoryFromPath(location.pathname)
    setActiveCategory(newActiveCategory)
  }, [location.pathname, getActiveCategoryFromPath])

  const activeMenuItems = useMemo(
    () => menuItems.find((category) => category.key === activeCategory)?.items || [],
    [activeCategory, menuItems],
  )

  const selectedKey = useMemo(
    () => activeMenuItems.find((item) => location.pathname.startsWith(item.path))?.path || activeMenuItems[0]?.path,
    [activeMenuItems, location.pathname],
  )

  const handleCategoryChange = useCallback(
    (categoryKey: string) => {
      const newCategory = menuItems.find((category) => category.key === categoryKey)
      if (newCategory) {
        if (categoryKey === 'hunterx') {
          handleThemeChange('dark')
        } else {
          handleThemeChange('light')
        }
        setActiveCategory(categoryKey)
        navigate(newCategory.items[0].path)
      }
    },
    [navigate, menuItems, handleThemeChange],
  )

  const [hoveredTab, setHoveredTab] = useState<string | null>(null)

  return (
    <>
      <div
        className={cn(
          'fixed inset-0 z-40 pointer-events-none',
          isTransitioning ? 'opacity-100' : 'opacity-0 transition-opacity duration-400',
        )}
        style={{
          backgroundColor: isDarkmode ? '#111827' : '#ffffff',
        }}
      />
      <div className="sticky top-0 pt-4 bg-white dark:bg-dark-blue z-50">
        <div className="bg-white dark:bg-[#111827] w-full">
          <div className="bg-white dark:bg-[#111827]">
            <div className="px-8">
              <div className="flex items-center justify-between group">
                <div className="flex-1 flex gap-2 items-center">
                  <NewIcon />
                </div>
                <div className="flex-1 space-x-4 flex items-center justify-center">
                  {menuItems.map((category) => (
                    <Tooltip
                      isOpen={openTooltip === category.key}
                      key={category.key}
                      content={category.title}
                      placement="bottom"
                      closeDelay={0}
                      classNames={{
                        content: ['text-white bg-transparent'],
                        base: [gradientStyles[category.key as keyof typeof gradientStyles], 'rounded-primary'],
                      }}
                    >
                      <button
                        onMouseEnter={() => setOpenTooltip(category.key)}
                        onMouseLeave={() => setOpenTooltip('none')}
                        tabIndex={-1}
                        onClick={() => handleCategoryChange(category.key)}
                        className={cn(
                          'w-10 h-10 flex items-center justify-center rounded-primary',
                          activeCategory === category.key
                            ? gradientStyles[category.key as keyof typeof gradientStyles] + ' active-category'
                            : null,
                          activeCategory === category.key ? 'text-white' : 'category',
                        )}
                      >
                        {parentIcons[category.icon]}
                      </button>
                    </Tooltip>
                  ))}
                </div>
                <div className="flex flex-1 justify-end items-center space-x-4">
                  <ExplanationTooltip tooltipContent="Account settings and preferences" placement="left-end">
                    <div>
                      <SettingsPopover />
                    </div>
                  </ExplanationTooltip>

                  <OrganizationSwitcher hidePersonal>
                    <OrganizationSwitcher.OrganizationProfilePage label="general" children={<></>} />
                    <OrganizationSwitcher.OrganizationProfilePage label="members" />
                    <OrganizationProfile.Page
                      label="Integrations"
                      url="integrations"
                      labelIcon={<Plug className="w-4 h-4" fill="currentColor" strokeWidth={2} />}
                    >
                      <IntegrationsScreen organizationId={organizationId} setModalState={setModalState} />
                    </OrganizationProfile.Page>

                    <OrganizationProfile.Page
                      label="Webhooks"
                      url="webhooks"
                      labelIcon={<Webhook className="w-4 h-4" fill="currentColor" strokeWidth={2} />}
                    >
                      <Webhooks />
                    </OrganizationProfile.Page>

                    <OrganizationProfile.Page
                      label="Email Notifications"
                      url="email-notification"
                      labelIcon={<CiMail className="w-4 h-4" fill="currentColor" strokeWidth={2} />}
                    >
                      <EmailSettings />
                    </OrganizationProfile.Page>
                    <OrganizationProfile.Page
                      label="Activity"
                      url="activity-log"
                      labelIcon={<Activity className="w-4 h-4" fill="black" stroke="black" />}
                    >
                      <ActivityLog organization={organization!} />
                    </OrganizationProfile.Page>
                  </OrganizationSwitcher>

                  <UserButton afterSignOutUrl="/">
                    <UserButton.UserProfilePage
                      label="Notifications"
                      url="notifications"
                      labelIcon={<BellRing className="w-4 h-4" fill="black" stroke="black" />}
                    >
                      <NotificationSettings userId={userId} />
                    </UserButton.UserProfilePage>
                  </UserButton>

                  <AllModals />
                </div>
              </div>
            </div>
          </div>

          <Tabs
            radius="full"
            variant="underlined"
            onSelectionChange={(e) => {
              const newPath = e.toString()
              if (newPath !== location.pathname) {
                navigate(newPath)
              }
            }}
            selectedKey={selectedKey}
            style={{
              marginBottom: '0',
              justifyContent: 'center',
              width: '100%',
            }}
          >
            {activeMenuItems.map((item) => (
              <Tab
                style={{
                  opacity: hoveredTab === item.path ? '0.3' : '1',
                }}
                key={item.path}
                title={
                  <NavigationTabContent
                    issuesCount={issuesWithOpenedStatusLen}
                    item={item}
                    onMouseOver={() => setHoveredTab(item.path)}
                    onMouseOut={() => setHoveredTab(null)}
                  />
                }
              />
            ))}
          </Tabs>
          <style>{`
                  @keyframes slideIn {
                      from {
                          transform: scale(50%);
                          opacity: 0;
                      }
                      to {
                          transform: scale(100%);
                          opacity: 1;
                      }
                  }
                  button:is(.dark *).category:hover:before {
                  box-shadow: 0px 18px 25px rgb(255 255 255 / 100%);
                  }

                  button:is(.dark *).active-category:before,
                  button:is(.dark *).category:before {
                  box-shadow: 0px 18px 25px rgb(255 255 255 / 100%);
                  }
                  .category:hover:before {
                      transition: 0.25s;

                      opacity: 1;
                      transform: scale(100%);
                      animation: slideIn 0.2s ease-out;
                      content: '';
                      position: absolute;
                      width: 50px;
                      height: 5px;
                      top: -18px;
                      background: transparent;
                      border-radius: 0;
                      border-top-right-radius: 8px;
                      border-bottom-right-radius: 8px;
                      box-shadow: 0px 18px 25px rgb(0 0 0 / 80%);
                  }
                  .active-category:before,
                  .category:before {
                      transition: 0.5s;

                      opacity: 0;
                      transform: scale(100%);
                      animation: slideIn 1s ease-in;
                      content: '';
                      position: absolute;
                      width: 50px;
                      height: 5px;
                      top: -18px;
                      background: trasparent;
                      border-radius: 0;
                      border-top-right-radius: 8px;
                      border-bottom-right-radius: 8px;
                      box-shadow: 0px 18px 25px rgb(0 0 0 / 80%);
                  }

                  @keyframes coin-flip {
                      0% {
                          transform: scaleX(0.95);
                          border-radius: 1005;
                      }
                      50% {
                          transform: scaleX(0.08);
                          border-radius: 30%;
                      }
                      100% {
                          transform: scaleX(0.95);
                          border-radius: 1005;
                      }
                  }

                  .coin:hover div {
                      animation: coin-flip 0.5s ease-in-out;
                      animation-iteration-count: 1;
                      animation-delay: 3s;
                  }

                  .shadow-small{
                box-shadow: unset;
                }
                span.absolute.z-0.inset-0.rounded-full{
                background-color: hsl(var(--nextui-default-100));
                  }

                  [aria-label="Breadcrumbs"] { display: none;}

                  .transition-opacity {
                    transition: opacity 400ms ease-in-out;
                  }
              `}</style>
        </div>
      </div>
    </>
  )
}
