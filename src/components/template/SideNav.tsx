import { Mode } from '@/@types/theme'
import logoDark from '@/assets/images/logo-dark.png'
import logoLight from '@/assets/images/logo-light.png'
import { childIcons, parentIcons } from '@/customUtils/navBarIconsMap'
import useDarkMode from '@/utils/hooks/useDarkmode'
import { OrganizationSwitcher, UserButton } from '@clerk/clerk-react'
import { Icon } from '@iconify/react'
import { Button, Tooltip, cn } from '@nextui-org/react'
import React, { useEffect, useState } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'

const themeIconMap = {
  dark: 'solar:sun-2-linear',
  light: 'solar:moon-linear',
}

const menuItems = [
  {
    key: 'access',
    icon: 'carbon:connect',
    title: 'Access',
    items: [
      { key: 'shortcuts', title: 'Shortcuts', icon: 'carbon:lightning', path: '/shortcuts' },
      { key: 'portals', title: 'Portals', icon: 'carbon:connect', path: '/portals' },
      { key: 'portal-groups', title: 'Portal Groups', icon: 'carbon:connect', path: '/portal-groups' },
      { key: 'resources', title: 'Resources', icon: 'carbon:software-resource-cluster', path: '/resources' },
    ],
  },
  {
    key: 'ndr',
    icon: 'carbon:content-delivery-network',
    title: 'Network Detection & Response',
    items: [
      {
        key: 'Dashboard',
        title: 'Dashboard',
        icon: 'ic:round-dashboard',
        path: '/ndr-dashboard',
      },
      { key: 'insights', title: 'Insights', icon: 'material-symbols:error-outline-rounded', path: '/insights' },
      {
        key: 'recommendations',
        title: 'Recommendations',
        icon: 'material-symbols:lightbulb-outline-rounded',
        path: '/recommendations',
      },
      {
        key: 'inventory',
        title: 'Inventory',
        icon: 'material-symbols:inventory-2-outline-rounded',
        path: '/inventory',
      },
      { key: 'explore', title: 'Explore', icon: 'material-symbols:explore-outline-rounded', path: '/explore' },
      { key: 'controls', title: 'Controls', icon: 'material-symbols:tune-rounded', path: '/controls' },
    ],
  },
  {
    key: 'scanner',
    icon: 'carbon:radar',
    title: 'External Attack Surface',
    items: [{ key: 'Scans', title: 'Scans', icon: 'carbon:list-boxes', path: '/scanner' }],
  },
  {
    key: 'organization',
    icon: 'icons8:organization',
    title: 'Organization',
    items: [
      {
        key: 'users',
        title: 'Users',
        icon: 'material-symbols:person-outline-rounded',
        path: '/users',
        count: 10,
      },
      {
        key: 'groups',
        title: 'Groups',
        icon: 'material-symbols:group-outline-rounded',
        path: '/groups',
        count: 2,
      },
      { key: 'integrations', title: 'Integrations', icon: 'ri:apps-2-add-line', path: '/integrations' },
      { key: 'jitAccess', title: 'JIT Access', icon: 'basil:lock-time-solid', path: '/jit-access' },
      {
        key: 'schedules',
        title: 'Schedules',
        icon: 'material-symbols:calendar-month-outline-rounded',
        path: '/schedules',
      },
      { key: 'billing', title: 'Billing', icon: 'material-symbols:payments-outline-rounded', path: '/billing' },
      {
        key: 'settings',
        title: 'Settings',
        icon: 'material-symbols:settings-outline-rounded',
        path: '/settings',
      },
    ],
  },
]

const gradientStyles = {
  access: 'bg-gradient-to-r from-teal-500 to-teal-600',
  ndr: 'bg-gradient-to-r from-blue-500 to-blue-600',
  scanner: 'bg-gradient-to-r from-purple-500 to-purple-600',
  organization: 'bg-gradient-to-r from-gray-500 to-gray-600',
}

// const iconColors = {
//     access: 'text-green-500',
//     ndr: 'text-blue-500',
//     scanner: 'text-blue-500',
//     organization: 'text-red-500',
// }

const NewIcon = () => {
  const [isDarkmode] = useDarkMode()
  return (
    <div className="relative w-9 h-9 coin">
      <div className="absolute inset-0 bg-black dark:bg-white rounded-full"></div>
      <div className="absolute inset-0 flex items-center justify-center">
        <img
          src={isDarkmode ? logoLight : logoDark}
          className="object-contain h-6"
          style={{ width: '12px' }}
          alt="logo"
        />
      </div>
    </div>
  )
}

const PlanCard = () => (
  <div className="bg-white dark:bg-default-50 p-4 rounded-primary border border-gray-200 dark:border-default-50 shadow-sm">
    <div className="flex items-center mb-3">
      <div className="bg-gray-200 p-2 rounded-full">
        <Icon icon="material-symbols:person-outline-rounded" className="text-gray-700 w-6 h-6" />
      </div>
      <div className="ml-3">
        <h3 className="text-lg font-semibold text-gray-900">Free</h3>
        <p className="text-sm text-gray-500">For individuals or small teams</p>
      </div>
    </div>
    <div className="flex items-center mb-3">
      <Icon icon="material-symbols:group-outline-rounded" className="text-gray-400 w-5 h-5 mr-2" />
      <p className="text-sm text-gray-700">1 of 5 Users</p>
    </div>
    <div className="flex items-center mb-3">
      <Icon icon="material-symbols:link-rounded" className="text-gray-400 w-5 h-5 mr-2" />
      <p className="text-sm text-gray-700">0 of 100 Peers</p>
    </div>
    <div className="h-1 w-full bg-gray-300 rounded mb-3">
      <div className="h-1 bg-primary-500 rounded" style={{ width: '20%' }}></div>
    </div>
    <Button className="w-full" color="primary">
      Upgrade Plan
    </Button>
  </div>
)

const BottomNavBar = () => (
  <div className="flex flex-col items-center w-full p-4 border-t border-gray-200 dark:border-default-50 bg-white dark:bg-dark-blue">
    <div className="mb-5">
      <PlanCard />
    </div>
    <OrganizationSwitcher />
  </div>
)

export default function RefinedModernSecuritySidebar() {
  const navigate = useNavigate()
  const [isDarkmode, setDarkmode] = useDarkMode()

  useEffect(() => {
    const theme = localStorage.getItem('theme')

    if (theme) {
      setDarkmode(theme as Mode)
    } else {
      localStorage.setItem('theme', 'light')
    }
  }, [setDarkmode])
  const [icon, setIcon] = React.useState<'dark' | 'light'>(isDarkmode ? 'dark' : 'light')

  useEffect(() => {
    setIcon(isDarkmode ? 'dark' : 'light')
  }, [isDarkmode])

  const location = useLocation()

  const isPathMatch = (itemPath: string, currentPath: string) => {
    return currentPath.startsWith(itemPath)
  }

  const [activeCategory, setActiveCategory] = useState(() => {
    // Initialize activeCategory based on current path
    const currentPath = location.pathname
    for (const category of menuItems) {
      if (category.items.some((item) => isPathMatch(item.path, currentPath))) {
        return category.key
      }
    }
    return menuItems[0].key // Default to first category if no match
  })

  const [isSliding, setIsSliding] = useState(false)

  const activeCategoryItems = menuItems.find((category) => category.key === activeCategory)?.items || []

  // useEffect(() => {
  //     const currentPath = location.pathname
  //     const activeCategory = menuItems.find((category) =>
  //         category.items.some((item) => isPathMatch(item.path, currentPath))
  //     )

  //     if (activeCategory) {
  //         setActiveCategory(activeCategory.key)
  //     } else {
  //         // If no matching category is found, you might want to set a default
  //         setActiveCategory(menuItems[0].key)
  //     }
  // }, [location.pathname])

  useEffect(() => {
    const currentPath = location.pathname
    for (const category of menuItems) {
      if (category.items.some((item) => isPathMatch(item.path, currentPath))) {
        setActiveCategory(category.key)
        break
      }
    }
  }, [location.pathname])

  // useEffect(() => {
  //     const currentPath = location.pathname
  //     const activeCategoryItems = menuItems.find((category) => category.key === activeCategory)?.items || []

  //     // Check if the current path matches any item in the active category
  //     const matchingItem = activeCategoryItems.find((item) => isPathMatch(item.path, currentPath))

  //     // Only navigate if there's no matching item in the current category
  //     if (!matchingItem && activeCategoryItems.length > 0) {
  //         navigate(activeCategoryItems[0].path)
  //     }
  // }, [activeCategory, navigate, location.pathname])

  useEffect(() => {
    setIsSliding(true)
    const timeout = setTimeout(() => setIsSliding(false), 300)
    return () => clearTimeout(timeout)
  }, [activeCategory])

  return (
    <div className="sticky left-0 top-0 flex flex-col h-screen bg-white dark:bg-[#111827]">
      <div className="flex h-full">
        {/* Icon column */}
        <div
          className="w-[80px] flex flex-col items-center py-4 bg-white dark:bg-dark-blue border-r border-gray-200 dark:border-default-50"
          style={{ minWidth: '80px' }}
        >
          <div className="w-10 h-10 flex items-center justify-center mb-4">
            <NewIcon />
          </div>
          {menuItems
            ?.filter((obj) => !(obj.key == 'organization'))
            ?.map((category) => (
              <Tooltip tabIndex={-1} key={category.key} content={category.title} placement="right">
                <button
                  //make the button unfocusable
                  tabIndex={-1}
                  onClick={() => {
                    navigate(category.items[0].path)
                    setActiveCategory(category.key)
                  }}
                  className={cn(
                    'w-10 h-10 flex items-center justify-center mb-2 rounded-primary',
                    activeCategory === category.key
                      ? gradientStyles[category.key as keyof typeof gradientStyles] + ' active-category'
                      : null,
                    activeCategory === category.key ? 'text-white' : 'category',
                  )}
                >
                  {/* <Icon icon={category.icon} className="w-6 h-6" /> */}
                  {parentIcons[category.icon]}
                </button>
              </Tooltip>
            ))}
          <div className="flex items-center justify-center mt-auto">
            {menuItems
              ?.filter((obj) => obj.key == 'organization')
              ?.map((category) => (
                <Tooltip tabIndex={-1} key={category.key} content={category.title} placement="right">
                  <button
                    tabIndex={-1}
                    onClick={() => {
                      navigate(category.items[0].path)
                      setActiveCategory(category.key)
                    }}
                    className={cn(
                      'w-10 h-10 flex items-center justify-center mb-2 rounded-primary',
                      activeCategory === category.key
                        ? gradientStyles[category.key as keyof typeof gradientStyles] + ' active-category'
                        : null,
                      activeCategory === category.key ? 'text-white' : 'category',
                    )}
                  >
                    {/* <Icon icon={category.icon} className="w-6 h-6" /> */}
                    {parentIcons[category.icon]}
                  </button>
                </Tooltip>
              ))}
          </div>
          <div className="">
            <Button
              onClick={() => {
                localStorage.setItem('theme', icon === 'dark' ? 'light' : 'dark')

                const newTheme = icon === 'dark' ? 'light' : 'dark'
                setDarkmode(newTheme as Mode)
              }}
              className="bg-transparent mb-3"
              isIconOnly
            >
              <Icon icon={themeIconMap[icon]} width={20} height={20} />
            </Button>
            <div
              style={{
                justifyContent: 'center',
                display: 'flex',
              }}
            >
              <UserButton afterSignOutUrl="/" />
            </div>
          </div>
        </div>

        {/* Expanded menu */}
        <div
          className={` flex flex-col bg-white dark:bg-dark-blue overflow-y-auto p-4 w-[250px] transition-transform duration-300 ${
            isSliding ? 'slide-in' : ''
          }`}
          style={{ minWidth: '250px' }}
        >
          <div>
            <div className="py-4">
              <div className="flex items-center justify-between pb-2">
                <span className="text-xs font-semibold text-gray-500">
                  {menuItems.find((category) => category.key === activeCategory)?.title.toUpperCase()}
                </span>
              </div>
              <ul>
                {activeCategoryItems.map((item) => (
                  <li tabIndex={-1} key={item.key} className="mb-1">
                    <button
                      tabIndex={-1}
                      onClick={() => navigate(item.path)}
                      className={cn('w-full flex items-center px-4 py-2 text-sm rounded-primary', {
                        'bg-gray-100 text-gray-900 dark:text-black': isPathMatch(item.path, location.pathname),
                        'text-gray-700 hover:bg-gray-100 hover:dark:bg-default-50/20': !isPathMatch(
                          item.path,
                          location.pathname,
                        ),
                      })}
                    >
                      {/* <Icon icon={item.icon} className="w-5 h-5 mr-3" /> */}
                      {childIcons[item.icon]}
                      <span>{item.title}</span>
                      {item.count !== undefined && <span className="ml-auto text-xs text-gray-500">{item.count}</span>}
                    </button>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </div>
      <BottomNavBar />
      <style>{`
                .category:hover:before {
                    transition: 0.25s;

                    opacity: 1;
                    transform: scale(100%);
                    animation: slideIn 0.2s ease-out;
                    content: '';
                    position: absolute;
                    left: 0;
                    width: 5px;
                    height: 50px;
                    background: white;
                    border-radius: 0;
                    border-top-right-radius: 8px;
                    border-bottom-right-radius: 8px;
                    box-shadow: 35px 0px 25px black;
                }
                .active-category:before,
                .category:before {
                    transition: 0.5s;

                    opacity: 0;
                    transform: scale(100%);
                    animation: slideIn 1s ease-in;
                    content: '';
                    position: absolute;
                    left: 0;
                    width: 5px;
                    height: 50px;
                    background: white;
                    border-radius: 0;
                    border-top-right-radius: 8px;
                    border-bottom-right-radius: 8px;
                    box-shadow: 35px 0px 25px black;
                }

                @keyframes coin-flip {
                    0% {
                        transform: scaleX(0.95);
                        border-radius: 1005;
                    }
                    50% {
                        transform: scaleX(0.08);
                        border-radius: 30%;
                    }
                    100% {
                        transform: scaleX(0.95);
                        border-radius: 1005;
                    }
                }

                .coin:hover div {
                    animation: coin-flip 0.5s ease-in-out;
                    animation-iteration-count: 1;
                    animation-delay: 3s;
                }
            `}</style>
    </div>
  )
}
