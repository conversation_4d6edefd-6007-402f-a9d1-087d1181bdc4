import { useMutation, useQueryClient } from '@tanstack/react-query'
import { updateWebhook, Webhook } from '@/utils/cloudFunctions'
import { toast } from '@/components/ui'
import ToastComponent from '@/generalComponents/ToastComponent'

function useUpdateWebhook({ organizationId }: { organizationId: string }) {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({
      organizationId,
      webhookId,
      updatedData,
    }: {
      organizationId: string
      webhookId: string
      updatedData: Webhook
    }) => updateWebhook({ organizationId, webhookId, updatedData }),
    onMutate: async ({ organizationId, webhookId, updatedData }) => {
      await queryClient.cancelQueries({ queryKey: ['webhooks', organizationId] })

      const previousWebhooks = queryClient.getQueryData(['webhooks', organizationId])

      queryClient.setQueryData(['webhooks', organizationId], (old: any) => ({
        ...old,
        data: old.data.map((webhook: Webhook) => (webhook.id === webhookId ? { ...webhook, ...updatedData } : webhook)),
      }))

      return { previousWebhooks }
    },
    onError: (err, { organizationId }, context) => {
      queryClient.setQueryData(['webhooks', organizationId], context?.previousWebhooks)
      
      toast.push(
        ToastComponent({
          title: 'Error',
          message: err.message,
          type: 'danger',
        }),
      )
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ['webhooks', organizationId] })
    },
  })
}

export default useUpdateWebhook
