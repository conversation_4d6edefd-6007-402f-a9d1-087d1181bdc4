import { useMutation, useQueryClient } from '@tanstack/react-query'
import { deleteWebhook } from '@/utils/cloudFunctions'
import { toast } from '@/components/ui'
import ToastComponent from '@/generalComponents/ToastComponent'

function useDeleteWebhook() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ organizationId, webhookId }: { organizationId: string; webhookId: string }) =>
      deleteWebhook({ organizationId, webhookId }),

    onMutate: async ({ organizationId, webhookId }) => {
      await queryClient.cancelQueries({ queryKey: ['webhooks', organizationId] })

      const previousWebhooks = queryClient.getQueryData(['webhooks', organizationId])

      queryClient.setQueryData(['webhooks', organizationId], (old: { data: any[] }) => {
        return {
          data: old?.data.filter((webhook) => webhook.id !== webhookId)
        }
      })

      return { previousWebhooks }
    },

    onError: (err, { organizationId }, context) => {
      queryClient.setQueryData(['webhooks', organizationId], context?.previousWebhooks)
      
      toast.push(
        ToastComponent({
          title: 'Error',
          message: err.message,
          type: 'danger',
        }),
      )
    },

    onSettled: (_, __, { organizationId }) => {
      queryClient.invalidateQueries({ queryKey: ['webhooks', organizationId] })
    },
  })
}

export default useDeleteWebhook
