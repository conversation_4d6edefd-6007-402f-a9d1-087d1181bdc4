import { useMutation, useQueryClient } from '@tanstack/react-query'
import { createWebhook, Webhook } from '@/utils/cloudFunctions'
import { toast } from '@/components/ui'
import ToastComponent from '@/generalComponents/ToastComponent'

function useCreateWebhook({ organizationId }: { organizationId: string }) {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ organizationId, webhook }: { organizationId: string; webhook: Webhook }) =>
      createWebhook({ organizationId, webhook }),
    onMutate: async ({ webhook, organizationId }) => {
      await queryClient.cancelQueries({ queryKey: ['webhooks', organizationId] })

      const previousWebhooks = queryClient.getQueryData(['webhooks', organizationId])

      queryClient.setQueryData(['webhooks', organizationId], (old: { data: Webhook[] }) => {
        return {
          data: [
            ...(old?.data ?? []),
            {
              ...webhook,
              id: 'temp-id-' + Date.now(),
              createdAt: { _seconds: +new Date() },
            },
          ],
        }
      })

      return { previousWebhooks }
    },
    onError: (err, newWebhook, context) => {
      queryClient.setQueryData(['webhooks', organizationId], context?.previousWebhooks)

      toast.push(
        ToastComponent({
          title: 'Error',
          message: err.message,
          type: 'danger',
        }),
      )
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ['webhooks', organizationId] })
    },
  })
}

export default useCreateWebhook
