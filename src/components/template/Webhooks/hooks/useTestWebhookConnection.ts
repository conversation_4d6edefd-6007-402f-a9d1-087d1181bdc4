import { useMutation } from '@tanstack/react-query'
import { testConnectionWebhook, Webhook } from '@/utils/cloudFunctions'
import { toast } from '@/components/ui'
import ToastComponent from '@/generalComponents/ToastComponent'

function useTestWebhookConnection() {
  return useMutation({
    mutationFn: ({ organizationId, webhook }: { organizationId: string; webhook: Webhook }) =>
      testConnectionWebhook({ organizationId, webhook }),
    onError: (err) => {
      toast.push(
        ToastComponent({
          title: 'Error',
          message: err.message,
          type: 'danger',
        }),
      )
    },
  })
}

export default useTestWebhookConnection
