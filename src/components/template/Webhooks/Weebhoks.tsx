import { useMemo, useState } from 'react'

import { Mo<PERSON>, ModalContent } from '@nextui-org/react'

import EmptyState from '@/components/template/Webhooks/components/EmptyState'
import CreateWebhookForm, { FormValues } from '@/components/template/Webhooks/components/CreateWebhookForm'
import useFetchWebhooks from '@/components/template/Webhooks/hooks/useFetchWebhooks'
import useCreateWebhook from '@/components/template/Webhooks/hooks/useCreateWebhook'
import useUpdateEffect from '@/hooks/useUpdateEffect'
import useTestWebhookConnection from '@/components/template/Webhooks/hooks/useTestWebhookConnection'
import {
  searchWebhook,
  transformPayloadToFormValues,
  transformValuesToPayload,
} from '@/components/template/Webhooks/utils'
import useWebhookStore from '@/components/template/Webhooks/store/store'
import useUpdateWebhook from '@/components/template/Webhooks/hooks/useUpdateWebhook'
import WebhookCard from '@/components/template/Webhooks/components/WebhookCard'
import { Webhook } from '@/utils/cloudFunctions'
import { Plus } from 'lucide-react'
import _ from 'lodash'
import useDeleteWebhook from '@/components/template/Webhooks/hooks/useDeleteWebhook'
import Section from '@/components/template/Webhooks/components/Section'
import SearchInput from '@/components/template/Webhooks/components/SearchInput'

function Webhooks() {
  const { isCreateWebhook, toggleCreateWebhook, formValues, setFormValues, organizationId } = useWebhookStore()

  const [searchTerm, setSearchTerm] = useState('')
  const { data: response, isLoading } = useFetchWebhooks(organizationId)

  const {
    mutate: mutateUpdateWebhook,
    isError: isFailedUpdate,
    isSuccess: isSuccessUpdate,
  } = useUpdateWebhook({ organizationId })
  const {
    mutate: addWebhook,
    isError: isFailedCreation,
    isSuccess: isSuccessCreation,
  } = useCreateWebhook({ organizationId })
  const {
    mutateAsync: testConnectionAsync,
    status: testConnectionStatus,
    reset: resetTestConnectionFn,
  } = useTestWebhookConnection()

  const { mutate: mutateDeleteWebhook } = useDeleteWebhook()

  const webhooksData = useMemo(() => {
    if (!response || response?.data?.length === 0) {
      return {
        enabled: [],
        disabled: [],
        totalCount: 0,
      }
    }

    return {
      enabled: _.filter(response.data, { enabled: true }).filter(searchWebhook(searchTerm)),
      disabled: _.filter(response.data, { enabled: false }).filter(searchWebhook(searchTerm)),
      totalCount: response.data.length,
    }
  }, [response, searchTerm])

  const handleTestConnection = async (values: FormValues) => {
    if (['success', 'pending'].includes(testConnectionStatus)) return

    const payload = transformValuesToPayload(values)

    await testConnectionAsync({ organizationId, webhook: payload })
  }

  const handleDelete = (webhookId: string) => {
    mutateDeleteWebhook({ organizationId, webhookId })
  }

  const handleUpdate = (webhookId: string) => {
    const webhook: Webhook = _.find(response?.data, { id: webhookId })
    const formValues = transformPayloadToFormValues(webhook)

    toggleCreateWebhook(true)
    setFormValues(formValues)
  }

  const handleChangeStatus = (webhookId: string) => {
    const webhook: Webhook = _.find(response?.data, { id: webhookId })

    const payload = { ...webhook, enabled: !webhook.enabled }

    mutateUpdateWebhook({
      organizationId,
      webhookId,
      updatedData: payload,
    })
  }

  const handleApplyChanges = async (values: FormValues) => {
    const payload = transformValuesToPayload(values)

    if ('webhookId' in values) {
      mutateUpdateWebhook({
        organizationId,
        webhookId: values.webhookId as string,
        updatedData: payload,
      })
    } else {
      addWebhook({ organizationId, webhook: payload })
    }

    setFormValues(values)
    toggleCreateWebhook(false)
  }

  const handleClose = () => {
    setFormValues(null)
    toggleCreateWebhook(false)
  }

  const isError = isFailedCreation || isFailedUpdate
  const isSuccess = isSuccessCreation || isSuccessUpdate
  const foundedWebhooksCount = webhooksData.enabled.length + webhooksData.disabled.length
  const hasData = (response?.data ?? []).length > 0

  useUpdateEffect(() => {
    if (isError) {
      toggleCreateWebhook(true)
    }

    if (isSuccess) {
      resetTestConnectionFn()
      setFormValues(null)
    }
  }, [isError, isSuccess])

  return (
    <div className="flex flex-col h-full pr-1">
      {hasData ? (
        <>
          <div className="flex flex-col pb-[16px]">
            <div className="flex gap-1 flex-wrap border-b border-black/7">
              <div
                className="text-[17px] opacity-[0.8] text-[#212126] leading-[1.41176] font-bold mb-[16px] force-light-mode"
                style={{ fontWeight: 700 }}
              >
                Webhooks
              </div>
            </div>

            <div className="p-[4px] flex gap-4 justify-between pt-[16px] force-light-mode">
              <SearchInput
                value={searchTerm}
                onChange={setSearchTerm}
                totalCount={webhooksData.totalCount}
                foundedCount={foundedWebhooksCount}
              />

              <button onClick={() => toggleCreateWebhook(true)} type="button" className="clerk-primary-button">
                <span>Add webhook</span>
                <Plus size={16} className="ml-2 text-gray-400" />
              </button>
            </div>
          </div>

          <div className="space-y-4">
            <Section
              className="border-b border-black/7 pb-6 pt-[16px]"
              title="Active Webhooks"
              emptyStateMessage="No active webhooks"
            >
              {webhooksData.enabled.map((webhook) => (
                <WebhookCard
                  key={webhook.id}
                  data={webhook}
                  onRemove={handleDelete}
                  onChangeStatus={handleChangeStatus}
                  onEdit={handleUpdate}
                  searchTerm={searchTerm}
                />
              ))}
            </Section>

            <Section title="Available Webhooks" emptyStateMessage="No available webhooks">
              {webhooksData.disabled.map((webhook) => (
                <WebhookCard
                  key={webhook.id}
                  data={webhook}
                  onRemove={handleDelete}
                  onChangeStatus={handleChangeStatus}
                  onEdit={handleUpdate}
                  searchTerm={searchTerm}
                />
              ))}
            </Section>
          </div>
        </>
      ) : (
        <EmptyState isLoading={isLoading} onCreate={() => toggleCreateWebhook(true)} />
      )}

      <Modal
        className="force-light-mode"
        isOpen={isCreateWebhook}
        closeButton={<></>}
        onClose={handleClose}
        onOpenChange={handleClose}
      >
        <ModalContent>
          <CreateWebhookForm
            initialValues={formValues as FormValues}
            onCancel={handleClose}
            onApplyChanges={handleApplyChanges}
            onClickTestConnection={handleTestConnection}
            testConnectionStatus={testConnectionStatus}
            onResetConnectionStatus={resetTestConnectionFn}
          />
        </ModalContent>
      </Modal>
    </div>
  )
}

export default Webhooks
