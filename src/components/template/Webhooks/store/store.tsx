import { create } from 'zustand'
import { FormValues } from '@/components/template/Webhooks/components/CreateWebhookForm'

interface WebhookStoreTypes {
  organizationId: string
  isCreateWebhook: boolean
  toggleCreateWebhook: (value: boolean) => void
  setOrganizationId: (id: string) => void
  reset: () => void
  formValues: FormValues | null
  setFormValues: (formValues: FormValues | null) => void
}

const initialState = {
  organizationId: '',
  isCreateWebhook: false,
  formValues: null,
}

const useWebhookStore = create<WebhookStoreTypes>((set) => ({
  ...initialState,
  toggleCreateWebhook: (value) => set({ isCreateWebhook: value }),
  setOrganizationId: (value) => set({ organizationId: value }),
  setFormValues: (value) => set({ formValues: value }),
  reset: () => set(initialState),
}))

export default useWebhookStore
