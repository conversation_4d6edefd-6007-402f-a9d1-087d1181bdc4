import { Form<PERSON>rovider, useForm } from 'react-hook-form'
import React, { useEffect } from 'react'
import ChangeStatus from '@/components/template/Webhooks/components/inputs/ChangeStatus'
import FieldSet from '@/components/template/Webhooks/components/inputs/FieldSet'
import FieldSpy from '@/components/template/Webhooks/components/inputs/FieldSpy'
import AuthenticationTypeRadioGroup from '@/components/template/Webhooks/components/inputs/AuthenticationTypeRadioGroup'
import TestConnectionButton from '@/components/template/Webhooks/components/inputs/TestConnectionButton'
import cn from 'classnames'
import TypePicker from '@/components/template/Webhooks/components/inputs/TypePicker'
import { Spinner } from '@nextui-org/react'

export type SIEMTypes = 'generic' | 'splunk' | 'elastic'
export type FormValues = {
  authenticationType: 'apiKey' | 'credentials'
  eventTypes: string[]
  siemType: SIEMTypes
  enabled: boolean
  url: string
  user_name: string
  user_pass: string
  user_pass_confirm: string
  apiKey: string
  webhookId?: string
}

interface CreateWebhookFormProps {
  onCancel: () => void
  initialValues: FormValues | null
  onApplyChanges: (values: FormValues) => void
  testConnectionStatus: 'success' | 'error' | 'pending' | 'idle'
  onClickTestConnection: (values: FormValues) => void
  onResetConnectionStatus: () => void
}

function Row({ children }: { children: React.ReactNode }) {
  return <div className="grid grid-cols-[150px_1fr] items-start gap-2">{children}</div>
}

function Label({ children }: { children: React.ReactNode }) {
  return (
    <div className="h-[30px] text-sm font-medium text-gray-700 flex justify-end items-center force-light-mode">
      {children}
    </div>
  )
}

const defaultFormValues: FormValues = {
  authenticationType: 'credentials',
  eventTypes: ['detection', 'insight'],
  siemType: 'generic',
  enabled: true,
  url: '',
  user_name: '',
  user_pass: '',
  user_pass_confirm: '',
  apiKey: '',
}

function CreateWebhookForm({
  onCancel,
  onApplyChanges,
  initialValues,
  onClickTestConnection,
  testConnectionStatus,
  onResetConnectionStatus,
}: CreateWebhookFormProps) {
  const form = useForm({
    defaultValues: initialValues ?? defaultFormValues,
    mode: 'onChange',
  })

  useEffect(() => {
    return () => {
      onResetConnectionStatus()
    }
  }, [])

  const handleTestConnection = async () => {
    const values = form.getValues()

    await onClickTestConnection(values)
  }

  const getButtonText = () => {
    return initialValues && 'webhookId' in initialValues ? 'Update' : 'Save'
  }

  return (
    <FormProvider {...form}>
      <form
        autoComplete="off"
        onSubmit={form.handleSubmit(onApplyChanges)}
        noValidate
        className="relative h-[570px] flex flex-col"
        id="webhhok-form"
      >
        <div className="flex flex-col gap-4 flex-grow overflow-auto p-4 ">
          <Row>
            <Label>Enable webhook:</Label>
            <div className="flex items-center h-[30px]">
              <ChangeStatus name="enabled" />
            </div>
          </Row>

          <Row>
            <Label>Type:</Label>

            <TypePicker name="siemType" options={['generic', 'splunk', 'elastic']} />
          </Row>

          <Row>
            <Label>URL:</Label>

            <FieldSet
              name="url"
              placeholder="Where to send notifications"
              validation={{
                required: {
                  value: true,
                  message: 'URL is required',
                },
                pattern: {
                  value: /^https?:\/\/.*$/i,
                  message: 'Please enter a valid URL starting with http:// or https://',
                },
                validate: (value) => {
                  const lowerCaseURL = value.toLowerCase()
                  try {
                    new URL(lowerCaseURL)
                    return undefined
                  } catch {
                    return 'Please enter a valid URL'
                  }
                },
              }}
            />
          </Row>

          {/*<Row>*/}
          {/*  <Label>Events type:</Label>*/}
          {/*  <EventTypeSelector name="eventTypes" options={['detection', 'insight']} />*/}
          {/*</Row>*/}

          <div className="mt-4">
            <Row>
              <Label>Authentication Type:</Label>
              <div className="flex items-center gap-4">
                <AuthenticationTypeRadioGroup name="authenticationType" />
              </div>
            </Row>
          </div>

          <FieldSpy value="credentials" name="authenticationType">
            <Row>
              <Label>Username:</Label>
              <FieldSet
                name="user_name"
                className="w-[200px]"
                placeholder="User name..."
                autocomplete="off"
                validation={{
                  required: {
                    value: true,
                    message: 'Username is required',
                  },
                }}
              />
            </Row>
            <Row>
              <Label>Password:</Label>
              <FieldSet
                name="user_pass"
                className="w-[200px]"
                placeholder="User password..."
                autocomplete="current-password"
                type="password"
                validation={{
                  required: {
                    value: true,
                    message: 'Password is required',
                  },
                }}
              />
            </Row>

            <Row>
              <Label>Confirm password:</Label>
              <FieldSet
                name="user_pass_confirm"
                className="w-[200px]"
                placeholder="Confirm password..."
                autocomplete="current-password"
                type="password"
                validation={{
                  required: {
                    value: true,
                    message: 'Password is required',
                  },
                  validate: (value: string, formValues: FormValues) => {
                    if (value !== formValues.user_pass) {
                      return 'Passwords do not match'
                    }
                    return true
                  },
                }}
              />
            </Row>
          </FieldSpy>

          <FieldSpy value="apiKey" name="authenticationType">
            <Row>
              <Label>API Key:</Label>
              <FieldSet
                name="apiKey"
                placeholder="Type api key here..."
                validation={{
                  required: {
                    value: true,
                    message: 'Api key is required',
                  },
                }}
              />
            </Row>
          </FieldSpy>

          <div className="py-6 flex-grow flex flex-col justify-end">
            <Row>
              <Label>Verify connection:</Label>
              <TestConnectionButton
                testConnectionStatus={testConnectionStatus}
                onTestConnection={handleTestConnection}
                onResetConnectionStatus={onResetConnectionStatus}
              />
            </Row>
          </div>
        </div>

        <div className="bg-white border-t border-gray-200 pb-4">
          <div className="pt-4 px-4 flex justify-end space-x-3">
            {!form.formState.isSubmitting && (
              <button
                type="button"
                className="px-4 py-2 border rounded-md text-sm font-medium text-gray-700 force-light-mode hover:bg-gray-50"
                onClick={onCancel}
              >
                Cancel
              </button>
            )}

            <button
              disabled={form.formState.isSubmitting || testConnectionStatus === 'pending'}
              type="submit"
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 transition-colors grid grid-cols-1 grid-rows-1 place-items-center"
            >
              <Spinner
                size="sm"
                className={cn('row-start-1 col-start-1 invisible', {
                  visible: form.formState.isSubmitting || testConnectionStatus === 'pending',
                })}
              />
              <span
                className={cn('row-start-1 col-start-1', {
                  invisible: form.formState.isSubmitting || testConnectionStatus === 'pending',
                })}
              >
                {getButtonText()}
              </span>
            </button>
          </div>
        </div>
      </form>
    </FormProvider>
  )
}

export default CreateWebhookForm
