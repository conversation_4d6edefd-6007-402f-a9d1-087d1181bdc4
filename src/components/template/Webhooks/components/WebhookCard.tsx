import CustomCard from '@/customComponents/CustomCard'
import { <PERSON><PERSON>, CardBody, CardFooter, Switch } from '@nextui-org/react'
import { HiOutlineTrash } from 'react-icons/hi'
import { Webhook } from '@/utils/cloudFunctions'
import { useHighlightSuggestions } from '@/views/NDR/PortflowPage/components/MultiSelect/MultiSelect'
import { useState, ChangeEvent, useRef, useEffect } from 'react'

type TProps = {
  data: Webhook
  searchTerm: string
  onEdit: (id: string) => void
  onRemove: (id: string) => void
  onChangeStatus: (id: string) => void
}

function WebhookCard({ data: { siemType, url, enabled, id }, onEdit, onRemove, onChangeStatus, searchTerm }: TProps) {
  const { renderHighlighted } = useHighlightSuggestions(searchTerm)
  const [isChecked, toggleChecked] = useState(enabled)
  const timerRef = useRef(null)

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    if (timerRef.current) {
      clearTimeout(timerRef.current)
    }
    toggleChecked(!isChecked)

    timerRef.current = setTimeout(() => onChangeStatus(id as string), 300)
  }

  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current)
      }
    }
  }, [])

  return (
    <CustomCard
      style={{
        width: '100%',
        height: 'auto',
      }}
    >
      <>
        <CardBody>
          <div className="grid grid-cols-[1fr_70px] gap-4">
            <div className="flex flex-col space-y-2">
              <button className="hover:text-primary-500" type="button" onClick={() => onEdit(id as string)}>
                <div className="flex gap-1 items-center">
                  <p className="text-[#11181C] force-light-mode">Type:</p>
                  <div className="hover:text-primary-500 text-left first-letter:uppercase bg-gray-100 rounded-lg px-2 py-1 force-light-mode text-[#11181C]">
                    {renderHighlighted(siemType)}
                  </div>
                </div>
              </button>

              <button className="force-light-mode" type="button" onClick={() => onEdit(id as string)}>
                <div className="flex gap-1 items-center force-light-mode">
                  <div className="text-[#11181C] force-light-mode">URL:</div>
                  <div className="truncat text-gray-500 force-light-mode">{renderHighlighted(url)}</div>
                </div>
              </button>
            </div>

            <Switch isSelected={isChecked} checked={isChecked} color="success" onClick={handleChange} />
          </div>
        </CardBody>

        <CardFooter className="pt-0 mt-2">
          <Button
            type="button"
            onClick={() => onRemove(id as string)}
            className="bg-[#f8f8f8] hover:bg-[#f0f0f0] text-[#333333] border border-[#e0e0e0] rounded-lg px-4 py-2 text-sm font-medium flex items-center gap-2 force-light-mode"
          >
            <HiOutlineTrash size={16} />
            Delete Webhook
          </Button>
        </CardFooter>
      </>
    </CustomCard>
  )
}

export default WebhookCard
