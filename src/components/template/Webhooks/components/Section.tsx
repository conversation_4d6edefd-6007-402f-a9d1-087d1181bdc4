import { ReactNode, Children } from 'react'
import cn from 'classnames'

type TProps = {
  title: string
  className?: string
  children: ReactNode
  emptyStateMessage: string
}

function Section({ title, children, emptyStateMessage, className }: TProps) {
  return (
    <section className={cn('flex flex-col gap-4', className)}>
      <div
        style={{ fontWeight: 700 }}
        className="text-[17px] opacity-[0.8] text-[#212126] leading-[1.41176] pb-[16px] border-b border-black/7 force-light-mode"
      >
        {title}
      </div>
      <div className="flex flex-row flex-wrap gap-4 mt-[16px]">
        {Children.toArray(children).length > 0 ? children : <span className='force-light-mode text-[#212126]'>{emptyStateMessage}</span>}
      </div>
    </section>
  )
}

export default Section
