import React, { useRef, useState } from 'react'
import _ from 'lodash'
import useClickOutsideMultiple from '@/hooks/useClickOutsideMultiple'
import { Ellip<PERSON>, Pencil, Trash2 } from 'lucide-react'
import { createPortal } from 'react-dom'
import useDeleteWebhook from '@/components/template/Webhooks/hooks/useDeleteWebhook'
import useWebhookStore from '@/components/template/Webhooks/store/store'
import useFetchWebhooks from '@/components/template/Webhooks/hooks/useFetchWebhooks'
import { Webhook } from '@/utils/cloudFunctions'
import { transformPayloadToFormValues } from '@/components/template/Webhooks/utils'

function ActionsTool({
  webhookId,
  noEdit = false,
  onRemove,
}: {
  webhookId: string
  noEdit: boolean
  onRemove: (webhookId: string) => void
}) {
  const { organizationId, toggleCreateWebhook, setFormValues } = useWebhookStore()
  const { data } = useFetchWebhooks(organizationId)
  const [isOpen, setOpen] = useState(false)
  const containerRef = useRef(null)
  const buttonRef = useRef<HTMLButtonElement>(null)

  const { mutate: mutateDeleteWebhook } = useDeleteWebhook()

  useClickOutsideMultiple([containerRef], () => setOpen(false))

  const handleDelete = () => {
    if (onRemove) {
      onRemove(webhookId)
    } else {
      mutateDeleteWebhook({ organizationId, webhookId })
    }
    setOpen(false)
  }

  const handleUpdate = () => {
    const webhook: Webhook = _.find(data?.data, { id: webhookId })
    const formValues = transformPayloadToFormValues(webhook)

    toggleCreateWebhook(true)
    setOpen(false)
    setFormValues(formValues)
  }

  const getDropdownPosition = () => {
    if (!buttonRef.current) return { top: 0, left: 0 }
    const rect = buttonRef.current.getBoundingClientRect()
    return {
      top: rect.bottom + window.scrollY + 4,
      left: rect.left + window.scrollX,
    }
  }

  return (
    <div className="relative">
      <button type="button" ref={buttonRef} onClick={() => setOpen((prev) => !prev)}>
        <Ellipsis size={16} className="text-gray-400" />
      </button>

      {isOpen &&
        createPortal(
          <div
            className="fixed z-50 flex gap-1 bg-white shadow-lg rounded-md border border-gray-200 p-1"
            ref={containerRef}
            style={getDropdownPosition()}
          >
            <div className="flex flex-col">
              <button
                onClick={handleDelete}
                type="button"
                className="text-gray-400 hover:text-gray-500 transition-colors p-1 hover:bg-gray-100 rounded"
              >
                <div className="flex gap-2 items-center">
                  <Trash2 className="flex-none" size={12} />
                  <div className="whitespace-nowrap text-[12px] text-gray-600">Remove</div>
                </div>
              </button>

              {noEdit ? null : (
                <button
                  type="button"
                  className="text-gray-400 hover:text-gray-500 transition-colors p-1 hover:bg-gray-100 rounded"
                  onClick={handleUpdate}
                >
                  <div className="flex gap-2 items-center">
                    <Pencil className="flex-none" size={12} />
                    <div className="whitespace-nowrap text-[12px] text-gray-600">Edit</div>
                  </div>
                </button>
              )}
            </div>
          </div>,
          document.body,
        )}
    </div>
  )
}

export default ActionsTool
