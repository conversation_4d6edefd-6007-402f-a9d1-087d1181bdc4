import { motion } from 'framer-motion'
import React, { ReactNode } from 'react'

function SlidingDrawer({ children, onClose }: { children: ReactNode; onClose: () => void }) {
  return (
    <>
      <motion.div
        initial={{ y: '100%', opacity: 1 }}
        animate={{ y: 0 , opacity: 1}}
        exit={{ y: '100%', opacity: 0 }}
        transition={{
          type: 'spring',
          stiffness: 200,
          damping: 20,
          mass: 0.4,
        }}
        className="absolute inset-x-0 bottom-0 bg-white border-t border-gray-200 shadow-lg rounded-t-xl overflow-y-auto z-[9999] mx-auto w-[75%]"
      >
        {children}
      </motion.div>

      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{
          duration: 0.075,
          ease: 'easeInOut',
          delay: .1
        }}
        className="absolute inset-0 bg-black/20 backdrop-blur-[1px] z-[9998]"
        onClick={onClose}
      />
    </>
  )
}

export default SlidingDrawer
