import { ArrowUpDown, ChevronDown, ChevronUp, Plus, Trash2, X } from 'lucide-react'
import Tooltip<PERSON>ontainer from '@/components/shared/TooltipContainer'
import cn from 'classnames'
import React, { ReactNode, useMemo, useState } from 'react'
import { useHighlightSuggestions } from '@/views/NDR/PortflowPage/components/MultiSelect/MultiSelect'
import { FaSearch } from 'react-icons/fa'
import { Webhook } from '@/utils/cloudFunctions'
import ActionsTool from '@/components/template/Webhooks/components/ActionsTool'
import { formatDate } from '@/components/template/Webhooks/utils'
import { enumEventTypes } from '@/components/template/Webhooks/constants/enum'

type SortField = 'siemType' | 'createdAt'
type SortDirection = 'asc' | 'desc'

interface WebhookTableProps {
  webhooks: Webhook[]
  toggleSort: (field: SortField) => void
  renderHighlighted: (text: string) => ReactNode
  onRemove?: (webhookId: string) => void
}

export function WebhookTable({ webhooks, toggleSort, renderHighlighted, onRemove }: WebhookTableProps) {
  return (
    <div className="p-[4px] flex-grow">
      <table className="min-w-full border-0 border-separate border-spacing-0 rounded-lg shadow-subtle overflow-hidden">
        <thead className="bg-white">
          <tr>
            <th
              scope="col"
              className="px-2 py-2 text-left tracking-wider cursor-pointer"
              onClick={() => toggleSort('siemType')}
            >
              <div
                className="whitespace-nowrap pl-4 flex items-center gap-1 text-[12px] text-[#1F1F239E]"
                style={{ fontWeight: 400 }}
              >
                Type
              </div>
            </th>

            <th scope="col" className="w-[300px] px-2 py-2 text-left tracking-wider cursor-pointer">
              <div className="flex items-center gap-2 text-[12px] text-[#1F1F239E]" style={{ fontWeight: 400 }}>
                URL
              </div>
            </th>

            <th scope="col" className="px-2 py-2 text-left tracking-wider">
              <div
                className="whitespace-nowrap flex items-center gap-2 text-[12px] text-[#1F1F239E]"
                style={{ fontWeight: 400 }}
              ></div>
            </th>
          </tr>
        </thead>
        <tbody className="bg-white">
          <tr hidden={Boolean(webhooks?.length)}>
            <td
              colSpan={3}
              style={{
                borderTop: '1px solid rgba(0, 0, 0, 0.07)',
              }}
            >
              <div className="italic text-gray-300 py-2 text-center">No webhooks added yet</div>
            </td>
          </tr>
          {webhooks?.map((webhook, idx) => (
            <tr key={idx} className="hover:bg-[rgba(0,0,0,0.03)]">
              <td
                className="w-[100px] first-letter:uppercase pl-6 pr-3 py-2 whitespace-nowrap text-[0.75rem] font-medium text-gray-900"
                style={{
                  borderTop: '1px solid rgba(0, 0, 0, 0.07)',
                }}
              >
                {renderHighlighted(webhook.siemType)}
              </td>

              <td
                className="w-[300px]  px-3 py-2 whitespace-nowrap text-[0.75rem] text-gray-500"
                style={{
                  borderTop: '1px solid rgba(0, 0, 0, 0.07)',
                }}
              >
                <div className="w-[300px] truncate">
                  <TooltipContainer
                    jsxBodyComponent={
                      <div className="bg-gray-900 text-white px-3 py-2 rounded shadow-lg text-sm z-[99999999]">
                        {webhook.url}
                      </div>
                    }
                  >
                    <span>{renderHighlighted(webhook.url)}</span>
                  </TooltipContainer>
                </div>
              </td>

              <td
                className="w-[44px] px-3 py-2 whitespace-nowrap text-left"
                style={{
                  borderTop: '1px solid rgba(0, 0, 0, 0.07)',
                }}
              >
                <button
                  onClick={onRemove && (onRemove(idx.toString()) as any)}
                  type="button"
                  className="text-gray-400 hover:text-gray-500 transition-colors p-1 hover:bg-gray-100 rounded"
                >
                  <div className="flex gap-2 items-center">
                    <Trash2 className="flex-none" size={12} />
                  </div>
                </button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}

function WebhooksTableContainer({ data, onCreate }: { data: Webhook[]; onCreate: () => void }) {
  const [searchTerm, setSearchTerm] = useState('')
  const [sortField, setSortField] = useState<SortField>('createdAt')
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc')
  const [statusFilter, setStatusFilter] = useState<'active' | 'disabled'>('active')
  const { renderHighlighted } = useHighlightSuggestions(searchTerm)

  const toggleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection((prev) => (prev === 'asc' ? 'desc' : 'asc'))
    } else {
      setSortField(field)
      setSortDirection('asc')
    }
  }

  const filteredByEnabled = useMemo(() => {
    return [...data].filter((webhook) => {
      return statusFilter === 'active' ? webhook.enabled : !webhook.enabled
    })
  }, [data, statusFilter])

  const filteredAndSortedData = useMemo(() => {
    return [...filteredByEnabled]
      .filter((webhook) => {
        const matchesSearch =
          webhook.url.toLowerCase().includes(searchTerm.toLowerCase()) ||
          webhook.siemType.toLowerCase().includes(searchTerm.toLowerCase()) ||
          webhook.eventTypes.includes(searchTerm.toLowerCase())

        return matchesSearch
      })
      .sort((a, b) => {
        const direction = sortDirection === 'asc' ? 1 : -1

        switch (sortField) {
          case 'siemType':
            return a.siemType.localeCompare(b.siemType) * direction
          case 'createdAt':
            // Handle both Date and TimestampLike objects
            const aTime = a.createdAt instanceof Date ? a.createdAt.getTime() : a.createdAt._seconds * 1000
            const bTime = b.createdAt instanceof Date ? b.createdAt.getTime() : b.createdAt._seconds * 1000
            return (aTime - bTime) * direction
          default:
            return 0
        }
      })
  }, [filteredByEnabled, searchTerm, sortField, sortDirection, statusFilter])

  // Group webhooks by status
  const groupedWebhooks = useMemo(() => {
    const active = data.filter((webhook) => webhook.enabled)
    const disabled = data.filter((webhook) => !webhook.enabled)
    return { active, disabled }
  }, [data])

  return (
    <div className="flex flex-col gap-4">
      {/* Search and filter bar */}
      <div className="relative flex flex-col">
        {/* Filter buttons */}
        <div className="flex gap-2 w-full border-b">
          <button
            className={cn('text-[13px] py-2 relative flex gap-2 items-center', {
              "after:content-[''] after:absolute after:bottom-[-1px] after:left-0 after:w-full after:h-[1px] after:bg-gray-900":
                statusFilter === 'active',
            })}
            onClick={() => setStatusFilter('active')}
          >
            <div className="flex items-center gap-2">Active connections</div>
            <div className="border px-[4px] py-[2px] leading-none text-[10px]" style={{ borderRadius: '8px' }}>
              {groupedWebhooks.active.length}
            </div>
          </button>
          <button
            className={cn('text-[13px] py-2 relative flex gap-2 items-center', {
              "after:content-[''] after:absolute after:bottom-[-1px] after:left-0 after:w-full after:h-[1px] after:bg-gray-900":
                statusFilter === 'disabled',
            })}
            onClick={() => setStatusFilter('disabled')}
          >
            <div>Available connections</div>
            <div className="border px-[4px] py-[2px] leading-none text-[10px]" style={{ borderRadius: '8px' }}>
              {groupedWebhooks.disabled.length}
            </div>
          </button>
        </div>
      </div>

      <div className="p-[4px] mb-2 flex gap-2 justify-between">
        <div className="relative w-[300px]">
          <div className="relative flex-none w-full overflow-hidden shadow-clerkBorder rounded-md h-[30px] flex item-center force-light-mode">
            <div className="absolute inset-y-0 left-0 pl-[14px] flex items-center pointer-events-none">
              <FaSearch size={12} className="text-black" />
            </div>
            <input
              type="text"
              placeholder="Search webhooks..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-[40px] pr-4 border-0 focus:ring-0 focus:outline-none text-[13px]"
            />
            {searchTerm.length > 0 ? (
              <button
                type="button"
                className="w-6 flex items-center justify-center h-full"
                onClick={() => setSearchTerm('')}
              >
                <X size={12} className="text-gray-400" />
              </button>
            ) : null}
          </div>
          <div className="absolute z-[10] right-0 bottom-[-20px] pointer-events-none">
            {searchTerm.length > 0 ? (
              <span className="text-[12px] leading-none text-gray-600">
                Search result:{' '}
                <b>
                  {filteredAndSortedData.length}/{filteredByEnabled.length}
                </b>
              </span>
            ) : null}
          </div>
        </div>

        <button onClick={onCreate} type="button" className="clerk-primary-button">
          <span>Add webhook</span>
          <Plus size={16} className="ml-2 text-gray-400" />
        </button>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        {filteredAndSortedData.length > 0 && (
          <WebhookTable
            webhooks={filteredAndSortedData}
            toggleSort={toggleSort}
            renderHighlighted={renderHighlighted}
          />
        )}

        {filteredAndSortedData.length === 0 && (
          <div>
            <div className="px-6 py-12 text-center">
              <div className="flex flex-col items-center justify-center text-gray-500">
                <svg className="w-12 h-12 mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={1.5}
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  />
                </svg>
                <p className="text-lg font-medium">No results found</p>
                <p className="text-sm">Try adjusting your search to find what you're looking for.</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default WebhooksTableContainer
