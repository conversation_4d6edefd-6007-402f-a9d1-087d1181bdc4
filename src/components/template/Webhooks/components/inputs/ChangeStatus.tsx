import { use<PERSON><PERSON>roller } from 'react-hook-form'
import cn from 'classnames'
import { Switch } from '@headlessui/react'
import React from 'react'

function ChangeStatus({ name }: { name: string }) {
  const { field } = useController({ name })

  return (
    <Switch
      onChange={() => field.onChange(!field.value)}
      className={cn(
        'relative inline-flex h-4 w-10 items-center rounded-full transition-colors focus:outline-none',
        field.value ? 'bg-[radial-gradient(circle_at_center,#60A5FA_0%,#3B82F6_100%)]' : 'bg-gray-100',
      )}
    >
      <span className="sr-only">Enable webhook</span>
      <span
        className={cn(
          'inline-block h-5 w-5 transform rounded-full bg-white transition-transform shadow-[0_1px_4px_rgba(0,0,0,0.3)] -my-0.5 ring-1 ring-black/[0.05]',
          field.value ? 'translate-x-5' : 'translate-x-0',
        )}
      />
    </Switch>
  )
}

export default ChangeStatus
