import { useWatch } from 'react-hook-form'
import SelectMultipleFields from '../../../../../views/Firewall/components/inputs/SelectMultipleFields/SelectMultipleFields'
import { Plus, X } from 'lucide-react'
import cn from 'classnames'
import React from 'react'

function EventTypeSelector({ name, options }: { name: string; options: string[] }) {
  const selectedEventTypes = useWatch({ name })

  const isAddButtonHidden = selectedEventTypes?.length === options.length

  return (
    <SelectMultipleFields
      className=""
      name={name}
      options={options}
      validators={[]}
      beforeEnterCallback={() => ({})}
      onFilter={() => true}
    >
      <div className="flex gap-1 flex-wrap items-center">
        {!isAddButtonHidden && (
          <SelectMultipleFields.ToggleVisible>
            {({ onOpen, elementRef }) => (
              <button
                ref={elementRef}
                type="button"
                className="flex items-center gap-2 px-2 py-[4px] rounded-md hover:bg-gray-50 transition-colors border border-gray-200 hover:border-gray-300"
                onClick={onOpen}
              >
                <Plus size={19} className="text-gray-600" />
              </button>
            )}
          </SelectMultipleFields.ToggleVisible>
        )}

        <SelectMultipleFields.SelectedList>
          {({ selected, onRemove }) => {
            return (
              <div className="flex items-center flex-wrap gap-2">
                {selected.map((option, index) => (
                  <div key={option as unknown as string} className="border px-2 py-[3px] rounded-md flex gap-1 items-center">
                    <span className="first-letter:uppercase ">{option as unknown as string}</span>
                    <button type="button" onClick={onRemove(index)}>
                      <X size={14} />
                    </button>
                  </div>
                ))}
              </div>
            )
          }}
        </SelectMultipleFields.SelectedList>
      </div>

      <SelectMultipleFields.Visible className="w-[120px] mt-1 bg-white rounded-lg shadow-lg border border-gray-100">
        <SelectMultipleFields.List>
          {({ list, onAppend, selected }) => {
            return (
              <div className="py-1 overflow-hidden flex flex-col items-between">
                {list.map((option) => {
                  return (
                    <button
                      type="button"
                      className="first-letter:uppercase text-left px-2 py-1 hover:bg-gray-100 bg-transparent"
                      onClick={() => onAppend(option)}
                      key={option}
                      disabled={selected.includes(option)}
                    >
                      <span
                        className={cn({
                          'text-gray-200': selected.includes(option),
                        })}
                      >
                        {option}
                      </span>
                    </button>
                  )
                })}
              </div>
            )
          }}
        </SelectMultipleFields.List>
      </SelectMultipleFields.Visible>
    </SelectMultipleFields>
  )
}

export default EventTypeSelector
