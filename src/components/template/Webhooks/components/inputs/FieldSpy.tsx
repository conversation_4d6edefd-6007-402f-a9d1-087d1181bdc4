import { ReactNode } from 'react'
import { useWatch } from 'react-hook-form'
import _ from 'lodash'

function FieldSpy({ name, value, children }: { name: string; children: ReactNode; value: string | string[] | boolean }) {
  const actualValue = useWatch({ name })
  
  if (!_.isNil(value) && actualValue === value) {
    return children
  }
  
  if (Array.isArray(value) && value.includes(actualValue)) {
    return children
  }
  
  return null
}

export default FieldSpy
