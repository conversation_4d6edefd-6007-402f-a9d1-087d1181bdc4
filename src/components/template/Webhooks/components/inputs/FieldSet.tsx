import { ReactNode, useState } from 'react'
import { use<PERSON><PERSON>roller } from 'react-hook-form'
import cn from 'classnames'
import { Eye, EyeOff } from 'lucide-react'

interface FieldSetProps {
  className?: string
  name: string
  placeholder: string
  type?: string
  autocomplete?: string
  button?: {
    icon?: ReactNode
    onClick: () => void
    label?: string
  }
  validation?: {
    pattern?: {
      value: RegExp
      message: string
    }
    required?: {
      value: boolean
      message: string
    }
    validate?: (value: string) => string | undefined
  }
}

function FieldSet({ className, name, placeholder, type = 'text', validation, autocomplete, button }: FieldSetProps) {
  const [showPassword, setShowPassword] = useState(false)
  const {
    field,
    fieldState: { error, isTouched },
  } = useController({
    name,
    rules: validation,
  })

  const isPassword = type === 'password'
  const effectiveType = isPassword ? (showPassword ? 'text' : 'password') : type

  return (
    <div className={className ?? ''}>
      <div className="relative flex items-center">
        <input
          {...field}
          type={effectiveType}
          placeholder={placeholder}
          autoComplete={autocomplete}
          className={cn(
            'w-full h-[30px] px-3 py-1 border rounded-md shadow-sm bg-white force-light-mode text-[#9ca3af]',
            'focus:ring-blue-500 focus:border-blue-500',
            {
              'border-red-300': error,
              'border-gray-300': !error,
              'pr-[60px]': button || isPassword,
            },
          )}
        />

        <div className="absolute right-0 top-0 h-full flex items-center gap-1 pr-2 force-light-mode">
          {isPassword && (
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="p-1 text-gray-400 hover:text-gray-600 force-light-mode"
            >
              {showPassword ? <EyeOff size={14} /> : <Eye size={14} />}
            </button>
          )}

          {button && (
            <button
              type="button"
              onClick={button.onClick}
              className="flex items-center gap-1 text-gray-600 hover:text-gray-800 force-light-mode"
            >
              {button.icon}
              {button.label && <span className="text-xs">{button.label}</span>}
            </button>
          )}
        </div>
      </div>
      {error && isTouched && <div className="text-xs text-red-500 force-light-mode">{error.message}</div>}
    </div>
  )
}

export default FieldSet
