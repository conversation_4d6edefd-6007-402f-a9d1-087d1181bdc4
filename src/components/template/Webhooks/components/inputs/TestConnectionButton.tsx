import cn from 'classnames'
import { useMemo, useRef } from 'react'
import { useFormContext } from 'react-hook-form'
import useUpdateEffect from '@/hooks/useUpdateEffect'

type Props = {
  testConnectionStatus: 'success' | 'error' | 'idle' | 'pending'
  onTestConnection: () => void
  onResetConnectionStatus: () => void
}

function TestConnectionButton({ onTestConnection, testConnectionStatus, onResetConnectionStatus }: Props) {
  const { watch } = useFormContext()

  const testConnectionRef = useRef(testConnectionStatus)
  testConnectionRef.current = testConnectionStatus

  const url = watch('url')
  const authenticationType = watch('authenticationType')
  const username = watch('user_name')
  const password = watch('user_pass')
  const apiKey = watch('apiKey')

  useUpdateEffect(() => {
    if (['error', 'success'].includes(testConnectionRef.current)) {
      onResetConnectionStatus()
    }
  }, [url, username, password, apiKey])

  const isValid = useMemo(() => {
    if (!url) return false

    if (authenticationType === 'credentials') {
      return Boolean(username && password)
    }

    if (authenticationType === 'apiKey') {
      return Boolean(apiKey)
    }

    return false
  }, [url, authenticationType, username, password, apiKey])

  const getButtonLabel = () => {
    switch (testConnectionStatus) {
      case 'pending':
        return 'Testing Connection...'
      case 'success':
        return 'Connection Established'
      case 'error':
        return 'Connection Failed'
      default:
        return 'Test Connection'
    }
  }

  return (
    <div className="relative flex items-center gap-2">
      <button
        type="button"
        onClick={onTestConnection}
        disabled={!isValid || testConnectionStatus === 'pending'}
        className={cn(
          'relative inline-flex items-center gap-2 px-4 py-1 text-sm font-medium force-light-mode',
          'rounded-lg transition-all duration-200 ease-in-out',
          {
            'bg-white border border-gray-300 text-gray-700 hover:bg-gray-50':
              testConnectionStatus === 'idle' && isValid,
            'bg-gray-100 border border-gray-300 text-gray-500': testConnectionStatus === 'pending',
            'bg-white border border-gray-300 text-gray-700':
              testConnectionStatus === 'success' || testConnectionStatus === 'error',
            'opacity-50 bg-gray-50': !isValid,
          },
        )}
      >
        <span className={cn('text-gray-500', { 'animated-loading-text': testConnectionStatus === 'pending' })}>
          {getButtonLabel()}
        </span>
      </button>

      {(testConnectionStatus === 'success' || testConnectionStatus === 'error') && (
        <div className="relative ml-2">
          <div
            className={cn('w-2 h-2 rounded-full transition-all duration-300', 'shadow-[0_0_8px_rgba(0,0,0,0.1)]', {
              'bg-gradient-to-br from-emerald-400 via-green-400 to-emerald-500': testConnectionStatus === 'success',
              'bg-gradient-to-br from-rose-400 via-red-400 to-rose-500': testConnectionStatus === 'error',
            })}
          >
            <div
              className={cn('absolute -inset-1 rounded-full blur-sm transition-opacity duration-500 opacity-40', {
                'bg-gradient-to-br from-emerald-300 to-green-400': testConnectionStatus === 'success',
                'bg-gradient-to-br from-rose-300 to-red-400': testConnectionStatus === 'error',
              })}
            />
          </div>
        </div>
      )}
    </div>
  )
}

export default TestConnectionButton
