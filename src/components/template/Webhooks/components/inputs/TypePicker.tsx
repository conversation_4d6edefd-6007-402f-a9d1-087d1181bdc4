import SelectSingleField from '@/views/Firewall/components/inputs/SelectSingleField/SelectSingleField'
import { Check } from 'lucide-react'

interface TypePickerProps {
  name: string
  options: string[]
}

function TypePicker({ name, options }: TypePickerProps) {
  return (
    <div className="flex gap-2 items-center">
      <SelectSingleField className="" name={name} validators={[]} options={options}>
        <SelectSingleField.InputButton
          className="w-[120px] h-[30px] px-3 py-1 text-sm font-medium border rounded-md shadow-sm cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          disabled={false}
          showArrowIcon
        />

        <SelectSingleField.Visible
          className="w-[120px] mt-1 bg-white rounded-lg shadow-lg border border-gray-100"
          hostId="webhhok-form"
        >
          <SelectSingleField.List>
            {({ list, onSelect, selected }) => (
              <div className="py-1 overflow-hidden flex flex-col items-between">
                {list.map((item) => (
                  <button
                    type="button"
                    className="text-left hover:bg-gray-100 py-1 px-2 flex items-center justify-between text-black force-light-mode"
                    onClick={onSelect(item)}
                  >
                    <div className="first-letter:uppercase text-[#6b7280]">{item}</div>
                    <Check size={14} className="flex-none" visibility={selected === item ? 'none' : 'hidden'} />
                  </button>
                ))}
              </div>
            )}
          </SelectSingleField.List>
        </SelectSingleField.Visible>
      </SelectSingleField>
    </div>
  )
}

export default TypePicker
