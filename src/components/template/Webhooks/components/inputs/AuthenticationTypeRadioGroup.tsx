import { useController, useFormContext, useWatch } from 'react-hook-form'
import cn from 'classnames'
import React from 'react'
import useUpdateEffect from '@/hooks/useUpdateEffect'

function AuthenticationTypeRadioGroup({ name }: { name: string }) {
  const { resetField } = useFormContext()
  const { field } = useController({ name })

  const authenticationTypeValue = useWatch({ name })

  useUpdateEffect(() => {
    if (authenticationTypeValue === 'credentials') {
      resetField('apiKey')
    }

    if (authenticationTypeValue === 'apiKey') {
      resetField('user_name')
      resetField('user_pass')
      resetField('user_pass_confirm')
    }
  }, [authenticationTypeValue])

  return (
    <>
      <label className="inline-flex items-center">
        <div className="relative">
          <input
            type="radio"
            value="credentials"
            name={field.name}
            onBlur={field.onBlur}
            onChange={field.onChange}
            className="sr-only"
          />
          <div
            className={cn(
              'w-5 h-5 rounded-full transition-colors duration-200 shadow-[0_1px_4px_rgba(0,0,0,0.1)]',
              field.value === 'credentials'
                ? 'bg-[radial-gradient(circle_at_center,#60A5FA_0%,#3B82F6_100%)]'
                : 'bg-gray-100',
            )}
          >
            <div
              className={cn(
                'absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2',
                'w-3 h-3 rounded-full bg-white transform',
              )}
            />
          </div>
        </div>
        <span className="ml-3 text-sm font-medium text-gray-700">Username</span>
      </label>
      <label className="inline-flex items-center">
        <div className="relative">
          <input
            type="radio"
            value="apiKey"
            name={field.name}
            onBlur={field.onBlur}
            onChange={field.onChange}
            className="sr-only"
          />
          <div
            className={cn(
              'w-5 h-5 rounded-full transition-colors duration-200 shadow-[0_1px_4px_rgba(0,0,0,0.1)]',
              field.value === 'apiKey'
                ? 'bg-[radial-gradient(circle_at_center,#60A5FA_0%,#3B82F6_100%)]'
                : 'bg-gray-100',
            )}
          >
            <div
              className={cn(
                'absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2',
                'w-3 h-3 rounded-full bg-white transform',
              )}
            />
          </div>
        </div>
        <span className="ml-3 text-sm font-medium text-gray-700">API Key</span>
      </label>
    </>
  )
}

export default AuthenticationTypeRadioGroup
