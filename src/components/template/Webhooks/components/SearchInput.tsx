import { FaSearch } from 'react-icons/fa'
import { X } from 'lucide-react'
import React from 'react'

type TProps = {
  value: string
  onChange: (value: string) => void
  totalCount: number
  foundedCount: number
}

function SearchInput({ value, onChange, foundedCount, totalCount }: TProps) {
  return (
    <div className="relative w-[300px]">
      <div className="relative flex-none w-full overflow-hidden shadow-clerkBorder rounded-md h-[30px] flex item-center force-light-mode">
        <div className="absolute inset-y-0 left-0 pl-[14px] flex items-center pointer-events-none">
          <FaSearch size={12} className="text-black" />
        </div>
        <input
          type="text"
          placeholder="Search webhooks..."
          value={value}
          onChange={(e) => onChange(e.target.value)}
          className="w-full pl-[40px] pr-4 border-0 focus:ring-0 focus:outline-none text-[13px] bg-white"
        />
        {value.length > 0 ? (
          <button type="button" className="w-6 flex items-center justify-center h-full" onClick={() => onChange('')}>
            <X size={12} className="text-gray-400" />
          </button>
        ) : null}
      </div>
      <div className="absolute z-[10] right-0 bottom-[-20px] pointer-events-none">
        {value.length > 0 ? (
          <span className="text-[12px] leading-none text-gray-600">
            Search result:{' '}
            <b>
              {foundedCount}/{totalCount}
            </b>
          </span>
        ) : null}
      </div>
    </div>
  )
}

export default SearchInput
