import { FormValues, SIEMTypes } from '@/components/template/Webhooks/components/CreateWebhookForm'
import { Webhook } from '@/utils/cloudFunctions'
import { enumEventTypes } from '@/components/template/Webhooks/constants/enum'

export function transformValuesToPayload(values: FormValues) {
  const payload = {} as Webhook

  payload.url = values.url
  payload.siemType = values.siemType
  payload.enabled = values.enabled

  if (values.authenticationType === 'apiKey') {
    payload.authorizationHeader = `Bearer ${values.apiKey}`
  }

  if (values.authenticationType === 'credentials') {
    payload.userPasswordAuthorization = `${values.user_name}:${values.user_pass}`
  }

  payload.eventTypes = []

  values.eventTypes.forEach((eventType) => {
    payload.eventTypes.push(enumEventTypes[eventType as keyof typeof enumEventTypes])
  })

  return payload
}

export function transformPayloadToFormValues(payload: Webhook) {
  const formValues: FormValues = {} as FormValues

  formValues.webhookId = payload.id
  formValues.url = payload.url
  formValues.enabled = payload.enabled
  formValues.siemType = payload.siemType as SIEMTypes
  formValues.eventTypes = []

  if (payload.authorizationHeader) {
    const token = payload.authorizationHeader.replace('Bearer ', '')

    formValues.authenticationType = 'apiKey'
    formValues.apiKey = token
  }

  if (payload.userPasswordAuthorization) {
    const [username, password] = payload.userPasswordAuthorization.split(':')

    formValues.authenticationType = 'credentials'
    formValues.user_name = username
    formValues.user_pass = password
    formValues.user_pass_confirm = password
  }

  payload.eventTypes.forEach((eventType) => {
    formValues.eventTypes.push(enumEventTypes[eventType as keyof typeof enumEventTypes])
  })

  return formValues
}

export interface TimestampLike {
  _seconds: number
  _nanoseconds: number
}

export function formatDate(date: Date | TimestampLike): string {
  if (!date) return 'n/a'

  let dateObj = '_seconds' in date ? new Date(date._seconds * 1000) : new Date()

  const year = dateObj.getFullYear()
  const month = (dateObj.getMonth() + 1).toString().padStart(2, '0')
  const day = dateObj.getDate().toString().padStart(2, '0')

  return `${year}/${month}/${day}`
}

export function searchWebhook(searchTerm: string) {
  const lowerSearch = searchTerm.toLowerCase().trim()

  return function (webhook: Webhook) {
    return lowerSearch
      ? webhook.url.includes(lowerSearch) || webhook.siemType.toLowerCase().includes(lowerSearch)
      : true
  }
}
