import IntegrationProfileModal from '@/views/Integrations/IntegrationProfile/IntegrationProfileModal'
import { DeleteIntegrationModal } from './DeleteIntegrationModal'
import { useIntegrationProfileModalStore } from '@/store/integrationProfileModalStore'
import SentinelOneInstallationDialog from '@/views/Integrations/components/installationDialogs/SentinelOneInstallationDialog'
import CrowdStrikeInstallationDialog from '@/views/Integrations/components/installationDialogs/CrowdStrikeInstallationDialog'
import MSDefenderInstallationDialog from '@/views/Integrations/components/installationDialogs/MSDefenderInstallationDialog/MSDefenderInstallationDialogContainer'
import AWSInstallationDialog from '@/views/Integrations/components/installationDialogs/AwsInstallationDialog/AwsInstallationDialogContainer'
import CreateNewResourceModal from '@/views/Access/Resources/new/CreateNewResourceModal'
import { Dispatch, SetStateAction } from 'react'
import { NewResourceType } from '@/customHooks/useNewResourceStates'

export const AllModals = () => {
  const { 
    installationDialogState, 
    newResourceModalState,
    setNewResourceModalOpen,
    setNewResourceType,
    setNewResourceIntegrationId,
    resetNewResourceModalState
  } = useIntegrationProfileModalStore()

  const handleNewResourceCancel = () => {
    resetNewResourceModalState()
  }

  // Create wrapper functions that match the expected types
  const handleSetIntegrationId: Dispatch<SetStateAction<string | undefined>> = (value) => {
    if (typeof value === 'function') {
      // Handle function updater pattern
      const newValue = value(newResourceModalState.integrationId)
      setNewResourceIntegrationId(newValue)
    } else {
      // Handle direct value
      setNewResourceIntegrationId(value)
    }
  }

  const handleSetType: Dispatch<SetStateAction<NewResourceType>> = (value) => {
    if (typeof value === 'function') {
      // Handle function updater pattern
      const newValue = value(newResourceModalState.type)
      setNewResourceType(newValue)
    } else {
      // Handle direct value
      setNewResourceType(value)
    }
  }

  return (
    <>
      <IntegrationProfileModal />
      <DeleteIntegrationModal />
      
      {/* Installation Dialogs */}
      {installationDialogState.type === 'sentinelOne' && (
        <SentinelOneInstallationDialog />
      )}
      {installationDialogState.type === 'crowdstrike' && (
        <CrowdStrikeInstallationDialog />
      )}
      {installationDialogState.type === 'aws' && (
        <AWSInstallationDialog />
      )}
      {installationDialogState.type === 'msDefender' && (
        <MSDefenderInstallationDialog />
      )}
      
      {/* New Resource Modal */}
      <CreateNewResourceModal
        integrationId={newResourceModalState.integrationId}
        isOpen={newResourceModalState.isOpen}
        onCancel={handleNewResourceCancel}
        onSuccess={handleNewResourceCancel}
        onOpenChange={() => setNewResourceModalOpen({ isOpen: false })}
        setIntegrationId={handleSetIntegrationId}
        setType={handleSetType}
        type={newResourceModalState.type}
      />
    </>
  )
}
