import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>I<PERSON>, Input, Button as UIButton } from '@/components/ui'
import reactQueryKeyStore from '@/configs/reactQueryKeyStore'
import useDeleteIntegration from '@/firestoreQueries/integrations/hooks/useDeleteIntegration'
import { Modal, ModalContent } from '@nextui-org/react'
import { Field, Form, Formik } from 'formik'

import { useIntegrationProfileModalStore } from '@/store/integrationProfileModalStore'

export const DeleteIntegrationModal = () => {
  const { deleteModalState, setDeleteModalOpen } = useIntegrationProfileModalStore()

  const { mutate: deleteIntegration, isPending: isDeletePending } = useDeleteIntegration()

  const handleDelete = () => {
    deleteIntegration({
      integrationRef: deleteModalState.integration!.ref,
      queryKey: reactQueryKeyStore.integrations(),
    })
  }

  return (
    <>
      <Modal
        isOpen={deleteModalState.isOpen}
        onOpenChange={() =>
          isDeletePending
            ? undefined
            : setDeleteModalOpen({ isOpen: deleteModalState.isOpen, integration: deleteModalState.integration })
        }
      >
        <ModalContent>
          {() => (
            <div className="p-4">
              <h5 className="mb-4">Delete Integration</h5>
              <Alert showIcon type="danger">
                Are you certain you want to delete this integration? This is a permanent action and cannot be undone.
              </Alert>
              <Formik
                initialValues={{
                  integrationName: '',
                }}
                onSubmit={handleDelete}
              >
                {({ values, touched, errors }) => {
                  return (
                    <Form>
                      <FormContainer>
                        <FormItem
                          asterisk
                          label={
                            <label className="font-normal">
                              Please type <b>{deleteModalState?.integration?.name}</b> to confirm
                            </label>
                          }
                          invalid={(errors['integrationName'] && touched['integrationName']) as boolean}
                          errorMessage={errors['integrationName'] as string}
                          className="flex flex-col mt-4"
                        >
                          <Field
                            className="mt-2"
                            type="text"
                            autoComplete="off"
                            name="integrationName"
                            placeholder={''}
                            value={values.integrationName}
                            component={Input}
                          />
                        </FormItem>
                        <div className="flex flex-row justify-end">
                          <UIButton
                            disabled={isDeletePending}
                            className="ltr:mr-2 rtl:ml-2"
                            type="button"
                            onClick={() => setDeleteModalOpen({ isOpen: false, integration: null })}
                          >
                            Cancel
                          </UIButton>
                          <UIButton
                            disabled={deleteModalState?.integration?.name !== values.integrationName}
                            variant="solid"
                            loading={isDeletePending}
                            type="submit"
                            color="red-500"
                          >
                            {isDeletePending ? 'Deleting' : 'Delete'}
                          </UIButton>
                        </div>
                      </FormContainer>
                    </Form>
                  )
                }}
              </Formik>
            </div>
          )}
        </ModalContent>
      </Modal>
    </>
  )
}
