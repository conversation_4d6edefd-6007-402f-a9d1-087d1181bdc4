import { FormProvider, useForm } from 'react-hook-form'
import useFetchEmailSettings from '@/components/template/EmailSettings/hooks/useFetchEmailSettings'
import EmptyState from '@/components/template/EmailSettings/components/EmptyState'
import EmailSettingsList from '@/components/template/EmailSettings/components/EmailSettingsList'
import useAutosaveEmailSettings from '@/components/template/EmailSettings/hooks/useAutosaveEmailSettings'
import useSmartFormReInitialisation from '@/components/template/EmailSettings/hooks/useSmartFormReInitialisation'
import useWebhookStore from '@/components/template/Webhooks/store/store'

export type UUID = string

export type TValue = {
  value: string
  description: string
  uuid: UUID
}

interface EmailSettingsForm {
  emailSettings: Record<UUID, TValue>
}

function EmailSettings() {
  const { organizationId } = useWebhookStore()
  const { isLoading } = useFetchEmailSettings(organizationId)

  const form = useForm<EmailSettingsForm>({
    defaultValues: { emailSettings: {} },
    mode: 'onChange',
  })

  useSmartFormReInitialisation(organizationId, form)
  useAutosaveEmailSettings(organizationId, form)

  const values = form.watch('emailSettings')
  const hasData = Object.keys(values).length > 0

  const appendField = () => {
    const uuid = Math.random().toString(36).substr(2, 16)

    const prevValues = form.getValues()

    const payload = { uuid, value: '', description: '' }
    const updated = { ...prevValues.emailSettings, [uuid]: payload }

    form.setValue('emailSettings', updated)

    setTimeout(() => form.setFocus(`emailSettings.${uuid}.value`), 100)
  }

  return (
    <div className="flex flex-col h-full">
      <FormProvider {...form}>
        <form onSubmit={(e) => e.preventDefault()} autoComplete="off">
          <div className="flex gap-1 flex-wrap border-b border-black/[0.07]">
            <div
              className="text-[17px] opacity-[0.8] text-[#212126] leading-[1.41176] font-bold mb-[16px] force-light-mode"
              style={{ fontWeight: 700 }}
            >
              Email Notifications Settings
            </div>
          </div>

          {hasData && !isLoading ? (
            <div className="space-y-4">
              <div className="" />

              <div>
                <EmailSettingsList values={values} onCreate={appendField} />
              </div>
            </div>
          ) : (
            <EmptyState onCreate={appendField} isLoading={isLoading} />
          )}
        </form>
      </FormProvider>
    </div>
  )
}

export default EmailSettings
