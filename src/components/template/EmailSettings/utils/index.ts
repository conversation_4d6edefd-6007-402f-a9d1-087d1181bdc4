import { TValue, UUID } from '@/components/template/EmailSettings/EmailSettings'

export const getUUID = () => Math.random().toString(36).substr(2, 16)

export const findUUID = (emailSettings: Record<UUID, TValue>, email: string) => {
  let result = ''
  
  Object.keys(emailSettings).map((uuid) => {
    const currentEmail = emailSettings[uuid].value
    
    if (!result && currentEmail === email) {
      result = uuid
    }
  })
  
  return result
}
