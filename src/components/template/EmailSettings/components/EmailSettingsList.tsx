import { useMemo, useState } from 'react'
import { FaSearch } from 'react-icons/fa'
import { Plus, Trash2, X } from 'lucide-react'
import EmailFieldSet from '@/components/template/EmailSettings/components/EmailFieldSet'
import { TValue, UUID } from '@/components/template/EmailSettings/EmailSettings'
import _ from 'lodash'
import { useFormContext } from 'react-hook-form'
import DescriptionFieldSet from '@/components/template/EmailSettings/components/DescriptionFieldSet'

function EmailSettingsList({ values, onCreate }: { values: Record<UUID, TValue>; onCreate: () => void }) {
  const [searchTerm, setSearchTerm] = useState('')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc')

  const { result, meta } = useMemo(() => {
    if (!searchTerm) {
      const uuids = _.map(values, 'uuid')
      // Sort by email when no search term
      return {
        meta: { filtered: 0, total: 0 },
        result: _.sortBy(uuids, (uuid) => values[uuid].value?.toLowerCase()),
      }
    }

    const searchTermLowerCase = searchTerm.toLowerCase()

    const filteredResult = Object.values(values).filter(
      (item) =>
        (item.value && item.value.includes(searchTermLowerCase)) || item.description.includes(searchTermLowerCase),
    )

    // Sort filtered results by email
    const sortedResult = _.sortBy(filteredResult, (item) => item.value?.toLowerCase())

    return {
      result: _.map(sortDirection === 'asc' ? sortedResult : sortedResult.reverse(), 'uuid'),
      meta: {
        filtered: filteredResult.length,
        total: Object.keys(values).length,
      },
    }
  }, [searchTerm, values, sortDirection])

  const handleSort = () => {
    setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
  }

  return (
    <div className="space-y-6">
      <div className="p-[4px] pt-0 mb-2 flex items-center justify-between gap-2">
        <div className="relative w-[300px]">
          <div className="relative flex-none w-full overflow-hidden shadow-clerkBorder rounded-md h-[30px] flex item-center">
            <div className="absolute inset-y-0 left-0 pl-[14px] flex items-center pointer-events-none">
              <FaSearch size={12} className="text-black" />
            </div>
            <input
              type="text"
              placeholder="Search by email..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-[40px] pr-4 border-0 focus:ring-0 focus:outline-none text-[13px] force-light-mode bg-white text-gray-500"
            />
            {searchTerm.length > 0 ? (
              <button
                type="button"
                className="w-6 flex items-center justify-center h-full"
                onClick={() => setSearchTerm('')}
              >
                <X size={12} className="text-gray-400" />
              </button>
            ) : null}
          </div>
          <div className="absolute z-[10] right-0 bottom-[-20px] pointer-events-none">
            {searchTerm.length > 0 ? (
              <span className="text-[12px] leading-none text-gray-600">
                Search result:{' '}
                <b>
                  {meta.filtered}/{meta.total}
                </b>
              </span>
            ) : null}
          </div>
        </div>

        <button onClick={onCreate} type="button" className="clerk-primary-button force-light-mode">
          <span>Add emails</span>
          <Plus size={16} className="ml-2 text-gray-400" />
        </button>
      </div>

      <div className="text-sm text-black mb-2 force-light-mode">Configure email recipients for daily insights</div>

      <div className="flex flex-wrap gap-2">
        <div className="p-[4px]">
          <table className="min-w-full border-0 border-separate border-spacing-0 rounded-lg shadow-subtle overflow-hidden">
            <thead className="">
              <tr>
                <th scope="col" className="px-2 py-2 text-left tracking-wider cursor-pointer" onClick={handleSort}>
                  <div
                    className="whitespace-nowrap pl-4 flex items-center gap-1 text-[12px] text-[#1F1F239E] force-light-mode"
                    style={{ fontWeight: 400 }}
                  >
                    Email
                    <span className="text-xs ml-1">{sortDirection === 'asc' ? '↑' : '↓'}</span>
                  </div>
                </th>
                <th scope="col" className="whitespace-nowrap px-2 py-2 text-left tracking-wider cursor-pointer">
                  <div
                    className="flex items-center gap-2 text-[12px] text-[#1F1F239E] force-light-mode"
                    style={{ fontWeight: 400 }}
                  >
                    Description
                  </div>
                </th>
                <th scope="col" className="px-2 py-2 text-left tracking-wider cursor-pointer" onClick={() => ({})}>
                  <div
                    className="whitespace-nowrap flex items-center gap-1 text-[12px] text-[#1F1F239E] force-light-mode"
                    style={{ fontWeight: 400 }}
                  >
                    Actions
                  </div>
                </th>
              </tr>
            </thead>
            <tbody className="bg-white">
              {result.length > 0 ? (
                result.map((uuid: UUID) => (
                  <tr key={uuid} className="hover:bg-[rgba(0,0,0,0.03)]">
                    <td
                      className="first-letter:uppercase pl-6 pr-3 py-2 whitespace-nowrap text-[0.75rem] font-medium text-gray-900"
                      style={{
                        borderTop: '1px solid rgba(0, 0, 0, 0.07)',
                      }}
                    >
                      <EmailFieldSet uuid={uuid} searchTerm={searchTerm} />
                    </td>

                    <td
                      className="max-w-[220px] px-3 py-2 whitespace-nowrap text-[0.75rem] font-medium text-gray-900"
                      style={{
                        borderTop: '1px solid rgba(0, 0, 0, 0.07)',
                      }}
                    >
                      <DescriptionFieldSet uuid={uuid} searchTerm={searchTerm} />
                    </td>

                    <td
                      className=" px-3 py-2 whitespace-nowrap text-center"
                      style={{ borderTop: '1px solid rgba(0, 0, 0, 0.07)' }}
                    >
                      <RemoveEmail uuid={uuid} />
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={3} className="min-w-[300px] text-center py-4 text-gray-500 text-sm">
                    {searchTerm ? 'No emails found matching your search' : 'No emails added yet'}
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}

function RemoveEmail({ uuid }: { uuid: string }) {
  const { getValues, reset } = useFormContext()

  const handleRemove = () => {
    const values = getValues()

    delete values.emailSettings[uuid]

    reset(
      { emailSettings: values.emailSettings },
      {
        keepErrors: false,
      },
    )
  }

  return (
    <button
      onClick={handleRemove}
      type="button"
      className="text-gray-400 hover:text-gray-500 transition-colors p-1 hover:bg-gray-100 rounded force-light-mode"
    >
      <Trash2 className="flex-none" size={12} />
    </button>
  )
}

export default EmailSettingsList
