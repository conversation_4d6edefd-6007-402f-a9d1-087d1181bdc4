import cn from 'classnames'
import { useController } from 'react-hook-form'
import { useHighlightSuggestions } from '@/views/NDR/PortflowPage/components/MultiSelect/MultiSelect'

type TProps = { uuid: string; searchTerm: string }

function EmailFieldSet({ uuid, searchTerm }: TProps) {
  const { renderHighlighted } = useHighlightSuggestions(searchTerm)

  const { field, fieldState } = useController({
    name: `emailSettings.${uuid}.value`,
    rules: {
      required: 'Email is required',
      pattern: {
        value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
        message: 'Invalid email address',
      },
    },
  })

  const showError = Boolean(fieldState.error) && fieldState.isTouched

  return (
    <div className="flex flex-col gap-1 group rounded-lg">
      <div className="flex gap-1 relative">
        <div className="relative py-1 pl-1">
          <input
            type="email"
            {...field}
            onFocus={(e) => e.target.select()}
            className={cn(
              `px-1 border bg-white text-gray-900 py-2 rounded-md h-[30px] leading-none min-w-[100px] field-sizing-content force-light-mode`,
              {
                'border-red-500': showError,
                'border-none': !showError,
                'text-transparent': Boolean(searchTerm),
              },
            )}
          />
          {searchTerm && field.value && (
            <div className="absolute inset-0 pointer-events-none pl-[7px] pr-[24px] pt-[11px] h-[30px] border border-transparent leading-none force-light-mode text-gray-900">
              {renderHighlighted(field.value)}
            </div>
          )}
        </div>
      </div>
      {showError && <span className="pl-1 text-red-500 text-xs">{fieldState.error?.message}</span>}
    </div>
  )
}

export default EmailFieldSet
