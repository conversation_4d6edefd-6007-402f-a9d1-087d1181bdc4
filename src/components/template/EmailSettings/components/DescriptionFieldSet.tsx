import cn from 'classnames'
import { useController } from 'react-hook-form'
import { useHighlightSuggestions } from '@/views/NDR/PortflowPage/components/MultiSelect/MultiSelect'

type TProps = { uuid: string; searchTerm: string }

function DescriptionFieldSet({ uuid, searchTerm }: TProps) {
  const { renderHighlighted } = useHighlightSuggestions(searchTerm)

  const { field, fieldState } = useController({
    name: `emailSettings.${uuid}.description`,
  })

  const showError = Boolean(fieldState.error) && fieldState.isTouched

  return (
    <div className="flex flex-col gap-1 group rounded-lg">
      <div className="flex gap-1 relative">
        <div className="relative py-1 pl-1">
          <input
            type="text"
            {...field}
            placeholder="Add a description"
            onFocus={(e) => e.target.select()}
            className={cn(`px-1 bg-white text-gray-900 border py-2 rounded-md h-[30px] leading-none w-[200px]`, {
              'border-red-500': showError,
              'border-none': !showError,
              'text-transparent': <PERSON><PERSON>an(searchTerm),
            })}
          />
          {searchTerm && field.value && (
            <div className="absolute inset-0 pointer-events-none pl-[7px] pr-[24px] pt-[11px] h-[30px] border border-transparent leading-none force-light-mode text-gray-900">
              {renderHighlighted(field.value)}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default DescriptionFieldSet
