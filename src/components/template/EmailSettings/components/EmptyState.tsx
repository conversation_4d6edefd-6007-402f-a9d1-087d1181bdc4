import { MailCheck, Plus } from 'lucide-react'
import React from 'react'
import cn from 'classnames'
import { motion } from 'framer-motion'

interface EmptyStateProps {
  onCreate: () => void
  isLoading: boolean
}

function EmptyState({ onCreate, isLoading }: EmptyStateProps) {
  return (
    <div className="pt-[160px] flex flex-col items-center justify-center">
      <div className="flex flex-col items-center max-w-[360px] text-center">
        <div className="bg-blue-50 p-4 rounded-full mb-4">
          <MailCheck className="w-8 h-8 text-blue-600" />
        </div>
        <motion.h3
          className={cn('force-light-mode text-lg font-semibold text-gray-900 mb-2', {
            'animated-loading-text': isLoading,
          })}
          initial={{ y: 0, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.2, delay: 0.25 }}
        >
          {isLoading ? 'Fetching email settings...' : 'No email settings yet'}
        </motion.h3>
        {!isLoading && (
          <>
            <motion.p
              className="force-light-mode text-gray-500 mb-6"
              initial={{ y: 10, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.2, delay: 0.3 }}
            >
              Add email addresses to receive important notifications and daily insights. Configure which severity levels and
              updates each recipient should receive.
            </motion.p>
            <motion.button
              className="force-light-mode bg-[#f8f8f8] hover:bg-[#f0f0f0] text-[#333333] border border-[#e0e0e0] rounded-lg px-4 py-2 text-sm font-medium flex items-center gap-2"
              onClick={onCreate}
              initial={{ y: 10, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.3, delay: 0.33 }}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Plus className="w-4 h-4" />
              Create New
            </motion.button>
          </>
        )}
      </div>
    </div>
  )
}

export default EmptyState
