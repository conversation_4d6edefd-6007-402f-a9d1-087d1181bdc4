import useFetchEmailSettings from '@/components/template/EmailSettings/hooks/useFetchEmailSettings'
import { useWatch } from 'react-hook-form'
import { useRef } from 'react'
import useDeepCompareEffect from '@/hooks/useDeepCompareEffect'
import _ from 'lodash'
import { findUUID, getUUID } from '@/components/template/EmailSettings/utils'
import { EmailSettings } from '@/components/template/EmailSettings/hooks/useCreateEmailSettings'

function useSmartFormReInitialisation(organizationId: string, form: any) {
  const { data: response } = useFetchEmailSettings(organizationId)
  
  const emailSettingsValues = useWatch({ name: 'emailSettings', control: form.control })
  const emailSettingsValuesRef = useRef(emailSettingsValues)
  emailSettingsValuesRef.current = emailSettingsValues
  
  useDeepCompareEffect(() => {
    if(!response) return;
    
    const emailSettingsResponse = _.head(response)
    const emails: EmailSettings = _.get(emailSettingsResponse, 'emailAddresses', []) as EmailSettings

    const copyEmailSettingsValues = structuredClone(emailSettingsValuesRef.current)
    
    _.forEach(emails, (email) => {
      const maybeUUID = findUUID(copyEmailSettingsValues, email.value)
      
      if (!maybeUUID) {
        const newUUID = getUUID()
        
        Object.assign(copyEmailSettingsValues, {
          [newUUID]: { uuid: newUUID, value: email.value, description: email.description },
        })
      }
    })
    
    form.setValue('emailSettings', copyEmailSettingsValues)
  }, [response])
}

export default useSmartFormReInitialisation
