import { useEffect, useMemo, useRef } from 'react'
import { useWatch } from 'react-hook-form'
import _ from 'lodash'
import { useDebounceCallback } from 'usehooks-ts'
import useFetchEmailSettings from '@/components/template/EmailSettings/hooks/useFetchEmailSettings'
import useCreateEmailSettings, { EmailSettings } from '@/components/template/EmailSettings/hooks/useCreateEmailSettings'
import useUpdateEffect from '@/hooks/useUpdateEffect'
import useDeepCompareEffect from '@/hooks/useDeepCompareEffect'

function useAutosaveEmailSettings(organizationId: string, form: any) {
  const { data } = useFetchEmailSettings(organizationId)
  const { mutate } = useCreateEmailSettings(organizationId)
  const prevEmails = useRef<EmailSettings | null>(null)

  const formValues = useWatch({ name: 'emailSettings', control: form.control })
  const hasFieldErrors = Object.keys(form.formState.errors).length > 0

  const formValueEmails = useMemo(
    () =>
      (Object.values(formValues) as EmailSettings)
        .filter((email) => Boolean(email.value))
        .map(({ value, description }) => ({ value, description })),
    [formValues],
  )

  useDeepCompareEffect(() => {
    if (!data) return

    const emailSettingsResponse = _.head(data)

    prevEmails.current = _.get(emailSettingsResponse, 'emailAddresses', []) as EmailSettings
  }, [data])

  const onSave = (emails: EmailSettings) => {
    mutate({
      organizationId,
      emailAddresses: emails,
    })

    prevEmails.current = emails
  }

  const debouncedOnSave = useDebounceCallback(onSave, 700)

  useUpdateEffect(() => {
    if (!prevEmails.current) return

    if (_.isEqual(formValueEmails, prevEmails.current) || hasFieldErrors) return

    debouncedOnSave(formValueEmails)
  }, [formValueEmails, hasFieldErrors])

  useEffect(() => {
    return () => {
      debouncedOnSave.cancel()
    }
  }, [debouncedOnSave])
}

export default useAutosaveEmailSettings
