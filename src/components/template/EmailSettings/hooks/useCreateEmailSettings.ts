import { useMutation, useQueryClient } from '@tanstack/react-query'
import { doc, addDoc, setDoc, collection, getDocs } from 'firebase/firestore'
import pathStore from '@/firestoreQueries/utils/pathStore'
import { db } from '@/configs/firebase'
import { toast } from '@/components/ui'
import ToastComponent from '@/generalComponents/ToastComponent'

export type EmailSettings = { value: string; description: string }[];

interface Payload {
  emailAddresses: EmailSettings
}

async function createEmailSettings({
  emailAddresses,
  organizationId,
}: {
  organizationId: string
  emailAddresses: EmailSettings
}) {
  const payload: Payload = { emailAddresses }

  const path = pathStore.emailNotifications({ organizationId })
  try {
    const querySnapshot = await getDocs(collection(db, path))
    if (!querySnapshot.empty) {
      const existingDoc = querySnapshot.docs[0]
      await setDoc(doc(db, path, existingDoc.id), payload)
      return existingDoc.id
    } else {
      const newDocRef = await addDoc(collection(db, path, '/data'), payload)
      return newDocRef.id
    }
  } catch (e) {
    console.error('Error updating email settings:', e)
    throw e
  }
}

function useCreateEmailSettings(organizationId: string) {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ organizationId, emailAddresses }: { organizationId: string; emailAddresses: EmailSettings }) =>
      createEmailSettings({ organizationId, emailAddresses }),

    onMutate: async ({ emailAddresses }) => {
      await queryClient.cancelQueries({ queryKey: ['emailSettings', organizationId] })

      const previousEmails = queryClient.getQueryData(['emailSettings', organizationId])

      queryClient.setQueryData(['emailSettings', organizationId], { emailAddresses })

      return { previousEmails }
    },

    onSuccess: () => {
      toast.push(ToastComponent({ title: 'Updated successfully', type: 'success' }))

      queryClient.invalidateQueries({ queryKey: ['emailSettings', organizationId] })
    },

    onError: (err, newEmails, context) => {
      toast.push(ToastComponent({ title: err.message ?? 'Failed', type: 'danger' }))
      
      queryClient.setQueryData(['emailSettings', organizationId], context?.previousEmails)
    },
  })
}

export default useCreateEmailSettings
