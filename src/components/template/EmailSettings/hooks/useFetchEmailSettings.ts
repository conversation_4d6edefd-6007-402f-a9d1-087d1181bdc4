import { useQuery } from '@tanstack/react-query'
import { collection, getDocs } from 'firebase/firestore'
import pathStore from '@/firestoreQueries/utils/pathStore'
import { db } from '@/configs/firebase'

interface EmailSettings {
  id: string
  emailAddresses: string[]
}

async function fetchEmailSettings(organizationId: string): Promise<EmailSettings[]> {
  const path = pathStore.emailNotifications({ organizationId })
  const snapshot = await getDocs(collection(db, path))
  
  return snapshot.docs.map(doc => ({
    id: doc.id,
    ...doc.data()
  })) as EmailSettings[]
}

function useFetchEmailSettings(organizationId: string) {
  return useQuery({
    queryKey: ['emailSettings', organizationId],
    queryFn: () => fetchEmailSettings(organizationId)
  })
}

export default useFetchEmailSettings
