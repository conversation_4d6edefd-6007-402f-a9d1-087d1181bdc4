import { Suspense } from 'react'
import classNames from 'classnames'
import Container from '@/components/shared/Container'
import { PAGE_CONTAINER_GUTTER_X, PAGE_CONTAINER_GUTTER_Y } from '@/constants/theme.constant'
import Footer from '@/components/template/Footer'
import type { CommonProps } from '@/@types/common'
import type { Meta } from '@/@types/routes'
import type { ElementType, ComponentPropsWithRef } from 'react'
import type { FooterPageContainerType } from '@/components/template/Footer'

export type PageContainerType = 'default' | 'gutterless' | 'contained'

export interface PageContainerProps extends CommonProps, Meta {
    contained?: boolean
    pageContainerType?: PageContainerType
    withoutPaddings?: boolean
}

const CustomHeader = <T extends ElementType>({
    header,
    ...props
}: {
    header: T
} & ComponentPropsWithRef<T>) => {
    const Header = header
    return <Header {...props} />
}

const PageContainer = (props: PageContainerProps) => {
    const { 
        pageContainerType = 'default', 
        children, 
        header, 
        contained = false, 
        extraHeader, 
        footer = true, 
        withoutPaddings = false 
    } = props

    const containerClasses = classNames(
        'page-container relative h-full flex flex-auto flex-col',
        {
            [`${PAGE_CONTAINER_GUTTER_X} ${PAGE_CONTAINER_GUTTER_Y}`]: !withoutPaddings && pageContainerType !== 'gutterless',
            'container mx-auto': pageContainerType === 'contained'
        }
    )

    const headerClasses = classNames(
        'flex items-center justify-between mb-4',
        {
            'container mx-auto': contained
        }
    )

    return (
        <div className="h-full flex flex-auto flex-col justify-between">
            <main className="h-full">
                <div className={containerClasses}>
                    {(header || extraHeader) && (
                        <div className={headerClasses}>
                            <div>
                                {header && typeof header === 'string' && <h3>{header}</h3>}
                                <Suspense fallback={<div></div>}>
                                    {header && typeof header !== 'string' && <CustomHeader header={header} />}
                                </Suspense>
                            </div>
                            <Suspense fallback={<div></div>}>
                                {extraHeader && typeof extraHeader !== 'string' && (
                                    <CustomHeader header={extraHeader} />
                                )}
                            </Suspense>
                        </div>
                    )}
                    {pageContainerType === 'contained' ? (
                        <Container className="h-full">
                            <>{children}</>
                        </Container>
                    ) : (
                        <>{children}</>
                    )}
                </div>
            </main>
            {footer && <Footer pageContainerType={pageContainerType as FooterPageContainerType} />}
        </div>
    )
}

export default PageContainer
