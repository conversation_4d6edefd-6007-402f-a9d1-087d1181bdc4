import classNames from 'classnames'
import Container from '@/components/shared/Container'
import { APP_NAME } from '@/constants/app.constant'
import { PAGE_CONTAINER_GUTTER_X } from '@/constants/theme.constant'

export type FooterPageContainerType = 'gutterless' | 'contained'

type FooterProps = {
  pageContainerType: FooterPageContainerType
}

const FooterContent = () => {
  return (
    <div className="flex items-center justify-between flex-auto w-full">
      <span>
        Copyright &copy; {`${new Date().getFullYear()}`} <span className="font-semibold">{`${APP_NAME}`}</span> All
        rights reserved.
      </span>
      <div className="relative">
        <a className="text-gray" href="https://port0.io/terms-of-service.pdf" target="_blank" rel="noopener noreferrer">
          Term & Conditions
        </a>
        <span className="mx-2 text-muted"> | </span>
        <a className="text-gray" href="https://port0.io/privacy-policy.pdf" target="_blank" rel="noopener noreferrer">
          Privacy & Policy
        </a>
      </div>
    </div>
  )
}
export default function Footer({ pageContainerType = 'contained' }: FooterProps) {
  return (
    <footer
      className={classNames(`footer flex flex-auto items-center h-10 dark:bg-[#111827] ${PAGE_CONTAINER_GUTTER_X}`)}
    >
      {pageContainerType === 'contained' ? (
        <Container>
          <FooterContent />
        </Container>
      ) : (
        <FooterContent />
      )}
    </footer>
  )
}
