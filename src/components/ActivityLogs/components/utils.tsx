import { format } from 'date-fns'
import jsPDF from 'jspdf'
import autoTable, { FontStyle } from 'jspdf-autotable'
import logoLight from '@/assets/images/logo-light.png'
import { formatCategoryName } from '@/views/NDR/Inventory/Components/Labels/utils'
import { getActivityTemplate, formatActivityMessage } from './activityTemplates'
import { Label } from '@/firestoreQueries/ndr/labels/hooks/useLabels'
import { ExtendedActivity } from './types'

type TextColor = [number, number, number]

export const exportActivitiesPDF = (activities: ExtendedActivity[], orgName: string, labels: Label[]) => {
  if (!activities?.length) return

  // Get organization name from the first activity
  const orgId = orgName.toLowerCase().replaceAll(' ', '_') || 'unknown'
  const currentDate = format(new Date(), 'yyyy-MM-dd')
  const fileName = `activity-log-${orgId}-${currentDate}.pdf`

  // Use A4 size for more space with additional columns
  const doc = new jsPDF({ format: 'a4', orientation: 'landscape' })
  const pageWidth = doc.internal.pageSize.getWidth()
  const pageHeight = doc.internal.pageSize.getHeight()
  const margin = 10

  // Add logo-light.png logo
  doc.addImage(logoLight, 'PNG', margin, margin, 5, 5)

  // Add title with modern styling
  doc.setFontSize(16)
  doc.setTextColor(41, 128, 185)
  doc.text('Activity Log', pageWidth / 2, margin + 14, { align: 'center' })

  // Add subtitle with date
  doc.setFontSize(8)
  doc.setTextColor(100, 100, 100)
  const dateRange = `Generated on ${format(new Date(), 'MMMM d, yyyy HH:mm')}`
  doc.text(dateRange, pageWidth / 2, margin + 22, { align: 'center' })

  // Add decorative line
  doc.setDrawColor(41, 128, 185)
  doc.setLineWidth(0.3)
  doc.line(margin, margin + 26, pageWidth - margin, margin + 26)
  // Prepare table data with enhanced formatting
  const tableData = activities.map((activity) => {
    const formattedTime = activity?.createdAt?.seconds
      ? format(new Date(activity?.createdAt?.seconds * 1000), 'MMM d, yyyy HH:mm')
      : ''

    const template = getActivityTemplate(activity.asset || '', activity.eventType || '')
    const title = template?.title || activity.title || 'Activity'
    const formattedMessage = template
      ? formatActivityMessage(template.message, activity.additionalData!)
      : activity.description

    return [
      {
        content: formatCategoryName(activity.asset) || 'Activity',
        styles: { fontStyle: 'bold' as FontStyle },
      },
      {
        content: title,
        styles: { textColor: [52, 73, 94] as TextColor },
      },
      {
        content: formattedMessage,
        styles: { textColor: [52, 73, 94] as TextColor },
      },
      formattedTime,
      activity.user?.ip || '',
    ]
  })

  // Add table with enhanced styling
  autoTable(doc, {
    startY: margin + 30,
    head: [['Asset', 'Type', 'Activity', 'Created At', 'IP']],
    body: tableData,
    theme: 'grid',
    styles: {
      fontSize: 7,
      cellPadding: 2,
      lineColor: [220, 220, 220],
      lineWidth: 0.1,
      overflow: 'linebreak',
    },
    headStyles: {
      fillColor: [41, 128, 185],
      textColor: 255,
      fontSize: 8,
      fontStyle: 'bold',
      halign: 'center',
    },
    alternateRowStyles: {
      fillColor: [245, 245, 245],
    },
    margin: { left: margin, right: margin },
    tableWidth: pageWidth - margin * 2,
    columnStyles: {
      2: { cellWidth: 100 }, // Set Activity column width
    },
    didDrawPage: function (data) {
      // Add page numbers
      doc.setFontSize(7)
      doc.setTextColor(100, 100, 100)
      doc.text(
        `Page ${data.pageNumber} of ${doc.internal.pages.length - 1}`,
        data.settings.margin.left,
        pageHeight - margin,
      )
    },
  })

  // Save the PDF with organization name and date in filename
  doc.save(fileName)
}

export const formatLogDate = (date: number) => {
  return date ? format(new Date(date * 1000), 'MMM d, yyyy HH:mm') : ''
}
