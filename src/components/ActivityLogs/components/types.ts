import { Timestamp } from 'firebase/firestore'

export type AssetsTypes = (typeof ASSETS_TYPES)[keyof typeof ASSETS_TYPES]

export const EVENT_TYPES = {
  DELETION: 'deletion',
  MODIFICATION: 'modification',
  CREATION: 'creation',
} as const

export type EventTypes = (typeof EVENT_TYPES)[keyof typeof EVENT_TYPES]

export const ASSETS_TYPES = {
  INTEGRATION_CONFIGURATION: 'integration_configuration',
  WEBHOOKS_CONFIGURATION: 'webhooks_configuration',
  USERS_INVITATIONS: 'users_invitations',
  USERS_SESSIONS: 'users_sessions',
  USER_MANAGEMENT: 'user_management',
  INSIGHTS: 'insights',
  SAVED_QUERIES: 'saved_queries',
  LABELS: 'labels',
  ENTITY_LABEL: 'entity_label',
  CONDITION: 'condition',
  DETECTION: 'detection',
  CONTROLS: 'controls',
  FIREWALL: 'firewall',
} as const

export enum FirewallActivityType {
  CREATED = 'firewallRuleCreated',
  UPDATED = 'firewallRuleUpdated',
  DELETED = 'firewallRuleDeleted',
}

export interface Rule {
  policy: 'ACCEPT' | 'BLOCK'
  direction: string
  portRanges: string[]
  remoteIdentifiers: Identifier[]
  localIdentifier: Identifier
  isActive: boolean
  isSuggested: boolean
  alert: boolean
  priorityOrder: number
  notes?: string
}

export interface Identifier {
  type: string
  value: string
}

export interface FirewallActivity {
  createdAt?: Timestamp
  expiredAt?: Timestamp
  user?: { id: string; ip: string }
  eventType: EventTypes
  asset: AssetsTypes
  additionalData?: Record<string, any>
}

export interface BaseActivity {
  id: string
  type: string | undefined
  title: string | undefined
  description: string | undefined
  details: string | undefined
  asset: string | undefined
  eventType: string | undefined
  createdAt: { seconds: number }
  ip: string | undefined
  userId?: string
}

export interface ExtendedActivity extends BaseActivity {
  additionalData?: {
    id: string
    title: string
    description: string
    activityType: string
    entityId: string
    activityData: {
      postState: any
      previousState: any
    }
  }
  user?: {
    id: string
    ip: string
  }
}
