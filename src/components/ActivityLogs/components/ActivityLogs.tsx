import { memo, useCallback, useEffect, useMemo, useState, useRef, LegacyRef } from 'react'
import { PlusCircle, Trash2, Pencil, Activity, Download } from 'lucide-react'
import { Loading } from '@/components/shared'
import cn from 'classnames'
import useGetOneEntity from '@/firestoreQueries/ndr/entities/hooks/useGetOneEntity'
import { isAWSEntity } from '@/views/Firewall/utils'
import { ASSETS_TYPES, ExtendedActivity, FirewallActivityType, Identifier, Rule } from './types'
import { useMembershipsStore } from '@/zustandStores/useMemberships'
import { exportActivitiesPDF, formatLogDate } from './utils'
import { Entity } from '@/views/Firewall/FirewallContainer'
import { ExpandIcon } from '@/components/ExpandIcon/ExpandIcon'
import { getActivityTemplate, formatActivityMessage } from './activityTemplates'
import Dialog from '@/components/ui/Dialog/Dialog'
import DatePickerRange from '@/components/ui/DatePicker/DatePickerRange'
import ToastComponent from '@/generalComponents/ToastComponent'
import { Spinner, toast } from '@/components/ui'
import { getDateRange } from '@/views/HunterX/Dashboard/utils'
import { useInfiniteQuery } from '@tanstack/react-query'
import getActivities from '@/firestoreQueries/ndr/activities/queries/getActivities'
import SearchComponent from '@/generalComponents/SearchComponent'
import { Label, useGetLabels } from '@/firestoreQueries/ndr/labels/hooks/useLabels'
import { useVirtualizer } from '@tanstack/react-virtual'
import { useInfiniteScroll } from '@nextui-org/use-infinite-scroll'
import { useOrganizationMemberships } from '@/hooks/useOrganizationMembers'
import type { MouseEvent } from 'react'

const isFirewallActivity = (activity: ExtendedActivity) => Boolean(activity?.asset?.includes(ASSETS_TYPES.FIREWALL))
const isLabelActivity = (activity: ExtendedActivity) =>
  Boolean([ASSETS_TYPES.LABELS, ASSETS_TYPES.ENTITY_LABEL].includes(activity?.asset as 'labels' | 'entity_label'))

interface ActivityItemProps {
  activity: ExtendedActivity
  handleActivityClick: (activity: ExtendedActivity) => Promise<void>
  expandedActivityId: string | null
  organizationId: string
  memberList: { userId: string; email: string }[]
  labels: Label[]
}

const ActivityItem = memo(
  ({ activity, handleActivityClick, expandedActivityId, organizationId, memberList, labels }: ActivityItemProps) => {
    const formattedTime = useMemo(() => formatLogDate(activity?.createdAt?.seconds), [activity?.createdAt?.seconds])

    const isFirewall = isFirewallActivity(activity)
    const isLabel = isLabelActivity(activity)
    const isExpanded = expandedActivityId === activity?.id
    const activityType = isFirewall ? activity.additionalData?.activityType || 'unknown' : activity.type || 'unknown'
    const members = useMembershipsStore((s) => s.memberships)
    const correctId = activity.user?.id
    const currentUser = members?.find((member) => member.userId === correctId)
    const userFullName = currentUser ? `${currentUser.firstName} ${currentUser.lastName}` : 'System log'
    const userAvatar = currentUser?.imageUrl
    const withExpand = isFirewall || isLabel

    const template = getActivityTemplate(activity.asset || '', activity.eventType || '')
    const userFromList = memberList.find((m) => m.userId === activity?.user?.id)
    const messageData = { user: { email: userFromList?.email }, ...activity.additionalData }
    const formattedMessage = template ? formatActivityMessage(template.message, messageData) : activity.description
    const title = template?.title || activity.title

    const getActivityIcon = (activityType: string | undefined, isFirewall: boolean) => {
      const className = 'w-4 h-4'
      if (!isFirewall) {
        return <Activity className={`${className} text-blue-600`} />
      }
      switch (activityType) {
        case FirewallActivityType.CREATED:
          return <PlusCircle className={className} />
        case FirewallActivityType.UPDATED:
          return <Pencil className={className} />
        case FirewallActivityType.DELETED:
        default:
          return <Trash2 className={className} />
      }
    }

    const getActivityStyle = (activityType: string | undefined) => {
      if (!isFirewall) {
        return 'bg-blue-100 text-blue-600'
      }

      switch (activityType) {
        case FirewallActivityType.CREATED:
          return 'bg-green-100 text-green-600'
        case FirewallActivityType.UPDATED:
          return 'bg-orange-100 text-orange-600'
        case FirewallActivityType.DELETED:
        default:
          return 'bg-red-100 text-red-600'
      }
    }

    return (
      <div
        key={isFirewall ? activity.additionalData?.id : activity.id}
        className="w-full min-h-[140px] px-2 hover:bg-gray-50 transition-colors duration-150 relative border-b border-gray-200 bg-white"
      >
        <div className="flex min-h-[140px] items-start gap-3 pt-3 pb-3">
          <div className={`mt-1 p-1.5 rounded-full force-light-mode ${getActivityStyle(activityType)}`}>
            {getActivityIcon(activityType, isFirewall)}
          </div>
          <div className="flex flex-1 flex-col h-full gap-2 max-w-[300px]">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <button
                  onClick={() => withExpand && handleActivityClick(activity)}
                  className="flex items-center gap-1 text-gray-900 hover:text-gray-700 group force-light-mode"
                >
                  <h3 className="text-sm font-medium force-light-mode">{title || 'Firewall Activity'}</h3>
                  {withExpand && <ExpandIcon isExpanded={isExpanded} />}
                </button>
              </div>
            </div>

            <p className="text-sm text-gray-500 force-light-mode">{formattedMessage}</p>
          </div>

          <div className="flex items-center self-end gap-2 text-xs text-gray-500">
            <time className="text-gray-500">{formattedTime}</time>
            <div className="flex items-center gap-1">
              <span className="text-gray-500">•</span>
              <div className="flex items-center gap-1">
                {currentUser ? (
                  <>
                    {userAvatar ? (
                      <img src={userAvatar} alt={userFullName} className="w-4 h-4 rounded-full" />
                    ) : (
                      <div className="w-4 h-4 rounded-full bg-gray-300 flex items-center justify-center text-[10px] text-gray-600">
                        {userFullName.charAt(0)}
                      </div>
                    )}
                    <span className="font-medium text-gray-500">{userFullName}</span>
                  </>
                ) : (
                  <span className="font-medium text-gray-500">System log</span>
                )}
              </div>
            </div>
          </div>
        </div>

        <div className={cn('mt-3', isExpanded ? 'opacity-100' : 'max-h-0 opacity-0')}>
          <ActivityDetails
            activity={activity}
            organizationId={organizationId}
            messageData={messageData}
            labels={labels}
          />
        </div>
      </div>
    )
  },
)

const ActivityDetails = memo(
  ({
    activity,
    organizationId,
    messageData,
    labels,
  }: {
    activity: ExtendedActivity
    organizationId: string
    messageData: any
    labels: Label[]
  }) => {
    const isFirewall = isFirewallActivity(activity)

    const renderLabels = (data: any, labels: Label[]) => {
      if (data.afterDocument?.entityId && data.beforeDocument?.labelIds && data.afterDocument?.labelIds) {
        const beforeLabels: string[] = data.beforeDocument.labelIds
        const afterLabels: string[] = data.afterDocument.labelIds

        const formatLabel = (labelId: string): { key: string; value: string; color: string } | null => {
          const label = labels.find((l) => l.id === labelId)
          if (!label?.key) return null
          const value = Array.isArray(label.values) && label.values.length > 0 ? label.values[0] : ''
          return value ? { key: label.key, value, color: label.color || 'blue' } : null
        }

        const getLabelChanges = (
          currentLabels: string[],
          previousLabels: string[],
        ): { key: string; value: string; color: string }[] => {
          const changedLabels = currentLabels.filter((label) => !previousLabels.includes(label))
          if (changedLabels.length === 0) return []

          return changedLabels.map(formatLabel).filter(Boolean) as { key: string; value: string; color: string }[]
        }

        const addedChanges = getLabelChanges(afterLabels, beforeLabels)
        const removedChanges = getLabelChanges(beforeLabels, afterLabels)

        const colorThemes = {
          blue: 'bg-blue-100 text-blue-600 border-blue-300',
          green: 'bg-green-100 text-green-600 border-green-300',
          amber: 'bg-amber-100 text-amber-600 border-amber-300',
          red: 'bg-red-100 text-red-600 border-red-300',
          purple: 'bg-purple-100 text-purple-600 border-purple-300',
          pink: 'bg-pink-100 text-pink-600 border-pink-300',
          indigo: 'bg-indigo-100 text-indigo-600 border-indigo-300',
          sky: 'bg-sky-100 text-sky-600 border-sky-300',
          teal: 'bg-teal-100 text-teal-600 border-teal-300',
          lime: 'bg-lime-100 text-lime-600 border-lime-300',
          yellow: 'bg-yellow-100 text-yellow-600 border-yellow-300',
          orange: 'bg-orange-100 text-orange-600 border-orange-300',
          rose: 'bg-rose-100 text-rose-600 border-rose-300',
          slate: 'bg-slate-100 text-slate-600 border-slate-300',
          gray: 'bg-gray-100 text-gray-600 border-gray-300',
          stone: 'bg-stone-100 text-stone-600 border-stone-300',
        }

        return (
          <div className="mt-4 px-2">
            <div className="py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider force-light-mode">
              Label Changes
            </div>
            <div className="text-sm mt-3">
              {addedChanges.length > 0 && (
                <div className="mb-3">
                  <div className="text-xs font-medium text-green-600 mb-2 force-light-mode">Added Labels:</div>
                  <div className="flex gap-1 flex-wrap">
                    {addedChanges.map((label, index) => (
                      <div
                        key={index}
                        className={cn(
                          'px-2.5 py-1 rounded-full text-sm font-medium flex items-center gap-1.5 force-light-mode',
                          colorThemes[label.color as keyof typeof colorThemes] || colorThemes.blue,
                        )}
                      >
                        <div className="font-semibold text-[12px] border-inherit pr-1.5 border-r force-light-mode">
                          {label.key}
                        </div>
                        <span className="opacity-80 force-light-mode">{label.value}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
              {removedChanges.length > 0 && (
                <div>
                  <div className="text-xs font-medium text-red-600 mb-2 force-light-mode">Removed Labels:</div>
                  <div className="flex gap-1 flex-wrap pb-2">
                    {removedChanges.map((label, index) => (
                      <div
                        key={index}
                        className={cn(
                          'px-2.5 py-1 rounded-full text-sm font-medium flex items-center gap-1.5 force-light-mode',
                          colorThemes[label.color as keyof typeof colorThemes] || colorThemes.blue,
                        )}
                      >
                        <div className="font-semibold text-[12px] border-inherit pr-1.5 border-r force-light-mode">
                          {label.key}
                        </div>
                        <span className="opacity-80 force-light-mode">{label.value}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        )
      }
      return null
    }

    if (isLabelActivity(activity)) {
      return <div>{renderLabels(messageData, labels)}</div>
    }

    if (!isFirewall) {
      return (
        <div className="mt-4 px-2">
          <div className="py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Activity Details
          </div>
          <div className="text-sm mt-3 text-gray-900">
            {activity.details || <span className="text-gray-400 italic">No additional details available</span>}
          </div>
        </div>
      )
    }

    const isUpdate = activity.additionalData?.activityType === FirewallActivityType.UPDATED
    const isDeleted = activity.additionalData?.activityType === FirewallActivityType.DELETED
    const previousState = activity.additionalData?.activityData?.previousState
    const postState = activity.additionalData?.activityData?.postState

    const { data: entity } = useGetOneEntity({
      entityId: activity.additionalData?.entityId || '',
      organizationId,
      enabled: true,
    })

    const isAWS = entity ? isAWSEntity(entity as unknown as Entity) : false

    if (!previousState && !postState) return null

    const areIdentifiersEqual = (prev: Identifier[], current: Identifier[]): boolean => {
      if (prev.length !== current.length) return false
      return prev.every(
        (prevItem, index) => prevItem.type === current[index].type && prevItem.value === current[index].value,
      )
    }

    const renderState = (state: Rule) => (
      <tr className="border-t border-gray-200 hover:bg-gray-50 transition-all duration-300">
        <td className="px-2 py-4 whitespace-nowrap">
          <div
            className={cn(
              'text-sm font-medium px-2 py-1 rounded w-max border force-light-mode',
              'bg-gray-50 text-gray-600 border-gray-200',
            )}
          >
            {state.isActive ? 'Active' : 'Inactive'}
          </div>
        </td>
        <td className="px-2 py-4 whitespace-nowrap">
          <div className="border rounded bg-gray-50 flex items-center gap-1 w-max px-2 py-1 text-sm force-light-mode text-gray-600">
            {state.localIdentifier.value}
          </div>
        </td>
        <td className="px-2 py-4">
          {state.remoteIdentifiers.map((identifier, index) => (
            <div
              key={index}
              className="border rounded bg-gray-50 flex items-center gap-1 w-max px-2 py-1 text-sm force-light-mode text-gray-600"
            >
              {identifier.value}
            </div>
          ))}
        </td>
        <td className="px-2 py-4">
          {state.portRanges.map((port) => (
            <div
              key={port}
              className="border rounded bg-gray-50 flex items-center gap-1 w-max px-2 py-1 text-sm force-light-mode text-gray-600"
            >
              {port}
            </div>
          ))}
        </td>
        <td className="px-2 py-4 whitespace-nowrap">
          <div
            className={cn(
              'text-sm font-medium px-2 py-1 rounded w-max border force-light-mode',
              state.policy === 'ACCEPT'
                ? 'bg-green-50 text-green-600 border-green-200'
                : 'bg-red-50 text-red-600 border-red-200',
              'first-letter:uppercase lowercase',
            )}
          >
            {state.policy}
          </div>
        </td>
        <td className="px-2 py-4 whitespace-nowrap">
          <div className="border rounded bg-gray-50 flex items-center gap-1 w-max px-2 py-1 text-sm force-light-mode text-gray-600">
            {state.direction}
          </div>
        </td>
      </tr>
    )

    const renderNotes = (notes: string | undefined, label: string) => (
      <div className="mt-4 px-2">
        <div className="text-left text-xs font-medium text-gray-500 uppercase tracking-wider force-light-mode">
          {label}
        </div>
        <div className="text-sm mt-1 text-gray-900 force-light-mode pb-3">
          {notes || <span className="text-gray-400 italic force-light-mode">No notes</span>}
        </div>
      </div>
    )

    return (
      <div className="mt-4">
        <div className="max-w-4xl mx-auto">
          {isUpdate ? (
            <div className="overflow-auto">
              <table className="min-w-full">
                <thead>
                  <tr className="bg-gray-50/50 backdrop-blur-lg">
                    <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Field
                    </th>
                    <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Previous State
                    </th>
                    <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Updated State
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    className={cn(
                      'border-t border-gray-200 transition-all duration-300',
                      previousState.isActive !== postState.isActive &&
                        'bg-gradient-to-r from-blue-50 via-blue-100 to-blue-50 animate-gradient',
                    )}
                  >
                    <td className="px-2 py-4 whitespace-nowrap text-sm font-medium text-gray-500">Status</td>
                    <td className="px-2 py-4 whitespace-nowrap">
                      <div
                        className={cn(
                          'text-sm font-medium px-2 py-1 rounded w-max border force-light-mode',
                          'bg-gray-50 text-gray-600 border-gray-200',
                        )}
                      >
                        {previousState.isActive ? 'Active' : 'Inactive'}
                      </div>
                    </td>
                    <td className="px-2 py-4 whitespace-nowrap">
                      <div
                        className={cn(
                          'text-sm font-medium px-2 py-1 rounded w-max border force-light-mode',
                          'bg-gray-50 text-gray-600 border-gray-200',
                        )}
                      >
                        {postState.isActive ? 'Active' : 'Inactive'}
                      </div>
                    </td>
                  </tr>
                  <tr
                    className={cn(
                      'border-t border-gray-200 transition-all duration-300',
                      previousState.localIdentifier.value !== postState.localIdentifier.value &&
                        'bg-gradient-to-r from-blue-50 via-blue-100 to-blue-50 animate-gradient',
                    )}
                  >
                    <td className="px-2 py-4 whitespace-nowrap text-gray-500 text-sm font-medium force-light-mode">
                      {isAWS ? 'Security Group' : 'Local IP'}
                    </td>
                    <td className="px-2 py-4 whitespace-nowrap">
                      <div
                        className={cn(
                          'border rounded bg-gray-50 flex items-center gap-1 w-max px-2 py-1 text-sm force-light-mode text-gray-500',
                        )}
                      >
                        {previousState.localIdentifier.value}
                      </div>
                    </td>
                    <td className="px-2 py-4 whitespace-nowrap">
                      <div
                        className={cn(
                          'border rounded bg-gray-50 flex items-center gap-1 w-max px-2 py-1 text-sm force-light-mode text-gray-500',
                        )}
                      >
                        {postState.localIdentifier.value}
                      </div>
                    </td>
                  </tr>
                  <tr
                    className={cn(
                      'border-t border-gray-200 transition-all duration-300',
                      !areIdentifiersEqual(previousState.remoteIdentifiers, postState.remoteIdentifiers) &&
                        'bg-gradient-to-r from-blue-50 via-blue-100 to-blue-50 animate-gradient',
                    )}
                  >
                    <td className="px-2 py-4 whitespace-nowrap text-sm font-medium text-gray-500">Remote IPs</td>
                    <td className="px-2 py-4">
                      <div className="flex flex-wrap gap-2">
                        {previousState.remoteIdentifiers.map((identifier: Identifier, index: number) => (
                          <div
                            key={index}
                            className="border rounded bg-gray-50 flex items-center gap-1 w-max px-2 py-1 text-sm force-light-mode text-gray-500"
                          >
                            {identifier.value}
                          </div>
                        ))}
                      </div>
                    </td>
                    <td className="px-2 py-4">
                      <div className="flex flex-wrap gap-2">
                        {postState.remoteIdentifiers.map((identifier: Identifier, index: number) => (
                          <div
                            key={index}
                            className="border rounded bg-gray-50 flex items-center gap-1 w-max px-2 py-1 text-sm force-light-mode text-gray-500"
                          >
                            {identifier.value}
                          </div>
                        ))}
                      </div>
                    </td>
                  </tr>
                  <tr
                    className={cn(
                      'border-t border-gray-200 transition-all duration-300',
                      JSON.stringify(previousState.portRanges) !== JSON.stringify(postState.portRanges) &&
                        'bg-gradient-to-r from-blue-50 via-blue-100 to-blue-50 animate-gradient',
                    )}
                  >
                    <td className="px-2 py-4 whitespace-nowrap text-sm font-medium text-gray-500">Port Ranges</td>
                    <td className="px-2 py-4">
                      <div className="flex flex-wrap gap-2">
                        {previousState.portRanges.map((port: string, index: number) => (
                          <div
                            key={index}
                            className="border rounded bg-gray-50 flex items-center gap-1 w-max px-2 py-1 text-sm force-light-mode text-gray-500"
                          >
                            {port}
                          </div>
                        ))}
                      </div>
                    </td>
                    <td className="px-2 py-4">
                      <div className="flex flex-wrap gap-2">
                        {postState.portRanges.map((port: string, index: number) => (
                          <div
                            key={index}
                            className="border rounded bg-gray-50 flex items-center gap-1 w-max px-2 py-1 text-sm force-light-mode text-gray-500"
                          >
                            {port}
                          </div>
                        ))}
                      </div>
                    </td>
                  </tr>
                  <tr
                    className={cn(
                      'border-t border-gray-200 transition-all duration-300',
                      previousState.policy !== postState.policy &&
                        'bg-gradient-to-r from-blue-50 via-blue-100 to-blue-50 animate-gradient',
                    )}
                  >
                    <td className="px-2 py-4 whitespace-nowrap text-sm font-medium text-gray-500">Policy</td>
                    <td className="px-2 py-4 whitespace-nowrap">
                      <div
                        className={cn(
                          'text-sm font-medium px-2 py-1 rounded w-max border force-light-mode',
                          previousState.policy === 'ACCEPT'
                            ? 'bg-green-50 text-green-600 border-green-200'
                            : 'bg-red-50 text-red-600 border-red-200',
                          'first-letter:uppercase lowercase',
                        )}
                      >
                        {previousState.policy}
                      </div>
                    </td>
                    <td className="px-2 py-4 whitespace-nowrap">
                      <div
                        className={cn(
                          'text-sm font-medium px-2 py-1 rounded w-max border force-light-mode',
                          postState.policy === 'ACCEPT'
                            ? 'bg-green-50 text-green-600 border-green-200'
                            : 'bg-red-50 text-red-600 border-red-200',
                          'first-letter:uppercase lowercase',
                        )}
                      >
                        {postState.policy}
                      </div>
                    </td>
                  </tr>
                  <tr
                    className={cn(
                      'border-t border-gray-200 transition-all duration-300',
                      previousState.direction !== postState.direction &&
                        'bg-gradient-to-r from-blue-50 via-blue-100 to-blue-50 animate-gradient',
                    )}
                  >
                    <td className="px-2 py-4 whitespace-nowrap text-sm font-medium text-gray-500">Direction</td>
                    <td className="px-2 py-4 whitespace-nowrap">
                      <div className="border rounded bg-gray-50 flex items-center gap-1 w-max px-2 py-1 text-sm force-light-mode text-gray-500">
                        {previousState.direction}
                      </div>
                    </td>
                    <td className="px-2 py-4 whitespace-nowrap">
                      <div className="border rounded bg-gray-50 flex items-center gap-1 w-max px-2 py-1 text-sm force-light-mode text-gray-500">
                        {postState.direction}
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          ) : (
            <div className="overflow-auto">
              <table className="min-w-full">
                <thead>
                  <tr className="bg-gray-50/50 backdrop-blur-lg">
                    <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {isAWS ? 'Security Group' : 'Local IP'}
                    </th>
                    <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Remote IPs
                    </th>
                    <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Port Ranges
                    </th>
                    <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Policy
                    </th>
                    <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Direction
                    </th>
                  </tr>
                </thead>
                <tbody>{renderState(isDeleted ? previousState : postState)}</tbody>
              </table>
            </div>
          )}
          {!isUpdate && renderNotes(isDeleted ? previousState.notes : postState.notes, 'Notes')}
        </div>
      </div>
    )
  },
)

interface ActivityLogProps {
  entityId?: string
  organization?: {
    id: string
    name: string
  }
}

let savedOffset = 0

const ActivityLog = ({ entityId, organization }: ActivityLogProps) => {
  const pageSize = 10
  const organizationId = organization?.id
  const containerRef = useRef<HTMLDivElement>(null)
  const [searchFilter, setSearchFilter] = useState('')
  const { data: labels } = useGetLabels(organizationId)
  const { memberList } = useOrganizationMemberships()
  const [expandedActivityId, setExpandedActivityId] = useState<string | null>(null)
  const [showExportModal, setShowExportModal] = useState(false)
  const { startDate, endDate } = getDateRange({ days: 30, hours: 0, minutes: 0 })
  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([startDate, endDate])
  const [expandedHeights, setExpandedHeights] = useState<Record<string, number>>({})
  const contentRefs = useRef<Record<string, HTMLDivElement | null>>({})

  const { data, isLoading, fetchNextPage, hasNextPage, isFetchingNextPage } = useInfiniteQuery({
    queryKey: ['activities', organizationId],
    queryFn: ({ pageParam }) =>
      getActivities({
        organizationId: organizationId!,
        page: 1,
        pageSize,
        lastDoc: pageParam as any,
      }),
    getNextPageParam: (lastPage) => (lastPage.hasMore ? lastPage.lastDoc : undefined),
    initialPageParam: undefined as any,
    enabled: !!organizationId,
    refetchOnWindowFocus: false,
  })

  const handleLoadMore = () => {
    if (!isFetchingNextPage && hasNextPage) {
      fetchNextPage()
    }
  }

  const [loaderRef, scrollerRef] = useInfiniteScroll({
    hasMore: hasNextPage,
    onLoadMore: handleLoadMore,
    distance: 200,
  })

  // Flatten the pages array to get all activities
  const activities: ExtendedActivity[] = data?.pages.flatMap((page) => page.activities) ?? []

  const filteredActivities = activities.filter((activity) => {
    // Filter by entityId if provided
    if (entityId && activity.additionalData?.entityId !== entityId) {
      return false
    }

    // Filter by search term
    if (searchFilter) {
      const searchLower = searchFilter.toLowerCase()
      const title = activity.title?.toLowerCase() || ''
      const description = activity.description?.toLowerCase() || ''
      const asset = activity.asset?.toLowerCase() || ''
      const eventType = activity.eventType?.toLowerCase() || ''
      const user = memberList.find((m) => m.userId === activity?.user?.id)
      const userEmail = user?.email?.toLowerCase() || ''

      return (
        title.includes(searchLower) ||
        description.includes(searchLower) ||
        asset.includes(searchLower) ||
        eventType.includes(searchLower) ||
        userEmail.includes(searchLower)
      )
    }

    return true
  })

  const measureContent = useCallback((id: string) => {
    const content = contentRefs.current[id]
    if (content) {
      const height = content.getBoundingClientRect().height
      setExpandedHeights((prev) => ({
        ...prev,
        [id]: height,
      }))
    }
  }, [])

  const defaultRowHeight = 140 // Default height for non-expanded rows

  const rowVirtualizer = useVirtualizer({
    count: filteredActivities.length,
    getScrollElement: () => containerRef.current,
    estimateSize: useCallback(
      (index) => {
        const activity = filteredActivities[index]
        const isExpanded = expandedActivityId === activity.id
        if (isExpanded) {
          return expandedHeights[activity.id] || defaultRowHeight
        }
        return defaultRowHeight
      },
      [filteredActivities, expandedActivityId, expandedHeights],
    ),
    overscan: 5,
    getItemKey: (index) => filteredActivities[index].id,
  })

  const handleScroll = (e: MouseEvent<HTMLDivElement>) => {
    const target = e.target as HTMLDivElement
    savedOffset = target.scrollTop
  }

  // Restore scroll position on mount
  useEffect(() => {
    if (scrollerRef.current) {
      scrollerRef.current.scrollTop = savedOffset
    }
  }, [])

  useEffect(() => {
    if (expandedActivityId) {
      setTimeout(() => {
        measureContent(expandedActivityId)
      }, 0)
    }
  }, [expandedActivityId, measureContent])

  const handleExportClick = () => setShowExportModal(true)

  const handleExportPDF = useCallback(() => {
    if (!dateRange[0] || !dateRange[1]) return

    const filteredByDate = filteredActivities?.filter((activity) => {
      const activityDate = new Date(activity?.createdAt?.seconds * 1000)
      return activityDate >= dateRange[0]! && activityDate <= dateRange[1]!
    })
    if (!filteredByDate?.length) {
      return toast.push(ToastComponent({ title: 'There are no logs for this date range', type: 'danger' }))
    }
    exportActivitiesPDF(filteredByDate as ExtendedActivity[], organization?.name || '', labels || [])
    setShowExportModal(false)
  }, [filteredActivities, dateRange, organization?.name])

  const handleActivityClick = useCallback(
    async (activity: ExtendedActivity) => {
      const activityId = activity?.id
      setExpandedActivityId(expandedActivityId === activityId ? null : activityId)
    },
    [expandedActivityId],
  )

  if (isLoading) {
    return <Loading loading />
  }

  return (
    <>
      <div>
        <div className="border-black/7 pb-3 border-b">
          <div
            className="text-[17px] opacity-[0.8] text-[#212126] leading-[1.41176] font-bold force-light-mode"
            style={{ fontWeight: 700 }}
          >
            Activity Logs
          </div>
        </div>
        <div className="flex items-center justify-between gap-4 my-2">
          <SearchComponent
            filter={searchFilter}
            setFilter={setSearchFilter}
            handleReset={() => setSearchFilter('')}
            showReset={searchFilter !== ''}
            placeholder="Search activities..."
            inputClassName="force-light-mode bg-white text-[#6b7280]"
            prefixClassName="force-light-mode text-[#6b7280]"
            suffixClassName="force-light-mode text-[#6b7280]"
          />
          <button onClick={handleExportClick} type="button" className="clerk-primary-button">
            <span> Export PDF</span>
            <Download size={16} className="ml-2 text-gray-400" />
          </button>
        </div>
        <div
          className="h-[550px] w-full overflow-auto p-4"
          ref={scrollerRef as LegacyRef<HTMLDivElement>}
          onScroll={handleScroll}
        >
          <div ref={containerRef}>
            <div
              style={{
                height: `${rowVirtualizer.getTotalSize()}px`,
                width: '100%',
                position: 'relative',
              }}
            >
              {filteredActivities?.length === 0 && (
                <div className="px-2 py-8 text-center text-gray-500">
                  {searchFilter
                    ? 'No activities found matching your search criteria'
                    : "Play with your firewall... don't be scared we are secured"}
                </div>
              )}
              {rowVirtualizer.getVirtualItems().map((virtualRow) => {
                const item = filteredActivities[virtualRow.index]
                return (
                  <div
                    key={item.id}
                    style={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      width: '100%',
                      transform: `translateY(${virtualRow.start}px)`,
                    }}
                  >
                    <div ref={(el) => (contentRefs.current[item.id] = el)}>
                      <ActivityItem
                        activity={item}
                        expandedActivityId={expandedActivityId}
                        handleActivityClick={handleActivityClick}
                        organizationId={organizationId!}
                        memberList={memberList}
                        labels={labels || []}
                      />
                    </div>
                  </div>
                )
              })}
            </div>

            <div
              ref={loaderRef as LegacyRef<HTMLDivElement>}
              className="flex justify-center items-center py-6 my-4"
              style={{ minHeight: '100px' }}
            >
              {isFetchingNextPage ? (
                <Spinner size="md" />
              ) : (
                hasNextPage && <div className="h-8 w-full bg-gray-100/30 rounded-full" />
              )}
            </div>
          </div>
        </div>
      </div>

      <Dialog
        isOpen={showExportModal}
        onClose={() => setShowExportModal(false)}
        width={400}
        height={200}
        contentClassName="force-light-mode"
        style={{
          content: {
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
          },
        }}
      >
        <div className="p-4">
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">Select Date Range</label>
            <div className="relative">
              <DatePickerRange
                value={dateRange}
                onChange={setDateRange}
                className="w-full force-light-mode"
                clearable={false}
                maxDate={new Date()}
              />
            </div>
          </div>
          <div className="flex justify-end gap-2">
            <button
              type="button"
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 force-light-mode"
              onClick={() => setShowExportModal(false)}
            >
              Cancel
            </button>
            <button
              type="button"
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 force-light-mode"
              onClick={handleExportPDF}
              disabled={!dateRange[0] || !dateRange[1]}
            >
              Export
            </button>
          </div>
        </div>
      </Dialog>
    </>
  )
}

export default ActivityLog
