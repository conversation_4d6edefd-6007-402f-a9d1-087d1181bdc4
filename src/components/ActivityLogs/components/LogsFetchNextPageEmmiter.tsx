import { useEffect } from 'react'
import { useInView } from 'react-intersection-observer'

const ActivityLogItemSkeleton = () => {
  return (
    <div className="w-full px-2 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors duration-150 relative border-b border-gray-200 bg-white dark:bg-gray-900">
      <div className="flex items-start gap-3">
        <div className="mt-1 p-1.5 rounded-full bg-gray-200 dark:bg-gray-700 animate-pulse w-7 h-7" />
        <div className="flex-1">
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center gap-2">
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-32" />
            </div>
          </div>
          <div className="my-5">
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-full mb-2" />
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-3/4" />
          </div>
        </div>
      </div>
      <div className="absolute bottom-1 right-2 flex items-center gap-2">
        <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-24" />
        <div className="flex items-center gap-1">
          <span>•</span>
          <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-32" />
        </div>
      </div>
    </div>
  )
}

interface LogsFetchNextPageEmitterProps {
  isLoading: boolean
  onViewTrigger: () => void
}

const LogsFetchNextPageEmitter = ({ isLoading, onViewTrigger }: LogsFetchNextPageEmitterProps) => {
  const { ref, inView } = useInView({
    threshold: 0,
  })

  useEffect(() => {
    if (inView && !isLoading) {
      onViewTrigger()
    }
  }, [inView, isLoading, onViewTrigger])

  return (
    <div ref={ref} className="flex justify-center items-center py-6 my-4" style={{ minHeight: '100px' }}>
      {isLoading ? (
        <div className="w-full space-y-4">
          <ActivityLogItemSkeleton />
          <ActivityLogItemSkeleton />
          <ActivityLogItemSkeleton />
        </div>
      ) : (
        <div className="h-8 w-full bg-gray-100/30 rounded-full" />
      )}
    </div>
  )
}

export default LogsFetchNextPageEmitter
