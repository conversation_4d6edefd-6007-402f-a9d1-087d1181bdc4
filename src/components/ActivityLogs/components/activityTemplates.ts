import { Label } from '@/firestoreQueries/ndr/labels/hooks/useLabels'
import { ASSETS_TYPES, EventTypes } from './types'

interface ActivityTemplate {
  title: string
  message: string
}

type ActivityTemplates = {
  [key in keyof typeof ASSETS_TYPES]: {
    [key in EventTypes]?: ActivityTemplate
  }
}

export const ACTIVITY_TEMPLATES: ActivityTemplates = {
  INTEGRATION_CONFIGURATION: {
    creation: {
      title: 'Integration Created',
      message: 'created integration',
    },
    modification: {
      title: 'Integration Updated',
      message: 'updated integration',
    },
    deletion: {
      title: 'Integration Deleted',
      message: 'deleted integration',
    },
  },
  WEBHOOKS_CONFIGURATION: {
    creation: {
      title: 'Webhook Created',
      message: 'created a webhook',
    },
    modification: {
      title: 'Webhook Updated',
      message: 'updated a webhook',
    },
    deletion: {
      title: 'Webhook Deleted',
      message: 'deleted a webhook',
    },
  },
  USERS_INVITATIONS: {
    creation: {
      title: 'Invitation Sent',
      message: 'sent an invitation to',
    },
    deletion: {
      title: 'Invitation Revoked',
      message: 'revoked the invitation for',
    },
  },
  USERS_SESSIONS: {
    creation: {
      title: 'User Logged In',
      message: 'logged in from',
    },
  },
  USER_MANAGEMENT: {
    creation: {
      title: 'User Created',
      message: 'created user',
    },
    modification: {
      title: 'User Updated',
      message: 'updated user',
    },
    deletion: {
      title: 'User Deleted',
      message: 'deleted user',
    },
  },
  INSIGHTS: {
    modification: {
      title: 'Insight Resolved',
      message: 'resolved insight',
    },
    deletion: {
      title: 'Insight Dismissed',
      message: 'dismissed insight',
    },
  },
  LABELS: {
    creation: {
      title: 'Label Created',
      message: 'created label',
    },
    modification: {
      title: 'Label Updated',
      message: 'updated label',
    },
    deletion: {
      title: 'Label Deleted',
      message: 'deleted label',
    },
  },
  ENTITY_LABEL: {
    creation: {
      title: 'Entity Label Created',
      message: 'created entity label',
    },
    modification: {
      title: 'Entity Label Updated',
      message: 'updated entity label',
    },
    deletion: {
      title: 'Entity Label Deleted',
      message: 'deleted entity label',
    },
  },
  CONDITION: {
    creation: {
      title: 'Label Condition Created',
      message: 'created label condition',
    },
    modification: {
      title: 'Label Condition Updated',
      message: 'updated label condition',
    },
    deletion: {
      title: 'Label Condition Deleted',
      message: 'deleted label condition',
    },
  },
  DETECTION: {
    creation: {
      title: 'Detection Created',
      message: 'created detection',
    },
    modification: {
      title: 'Detection Updated',
      message: 'updated detection',
    },
    deletion: {
      title: 'Detection Deleted',
      message: 'deleted detection',
    },
  },
  CONTROLS: {
    creation: {
      title: 'Control Created',
      message: 'created control',
    },
    modification: {
      title: 'Control Updated',
      message: 'updated control',
    },
    deletion: {
      title: 'Control Deleted',
      message: 'deleted control',
    },
  },
  FIREWALL: {
    creation: {
      title: 'Firewall Rule Created',
      message: 'created firewall rule for entity',
    },
    modification: {
      title: 'Firewall Rule Updated',
      message: 'updated firewall rule for entity',
    },
    deletion: {
      title: 'Firewall Rule Deleted',
      message: 'deleted firewall rule for entity',
    },
  },
  SAVED_QUERIES: {
    creation: {
      title: 'Saved Query Created',
      message: 'created saved query',
    },
    modification: {
      title: 'Saved Query Updated',
      message: 'updated saved query',
    },
    deletion: {
      title: 'Saved Query Deleted',
      message: 'deleted saved query',
    },
  },
}

export const getActivityTemplate = (asset: string, eventType: string): ActivityTemplate | undefined => {
  const assetKey = Object.entries(ASSETS_TYPES).find(([_, value]) => value === asset)?.[0]
  if (!assetKey) return undefined

  return ACTIVITY_TEMPLATES[assetKey as keyof typeof ACTIVITY_TEMPLATES]?.[eventType as EventTypes]
}

const formatEntityName = (data: Record<string, any>): string => {
  // Handle entity label changes
  if (data.afterDocument?.entityId && data.beforeDocument?.labelIds && data.afterDocument?.labelIds) {
    return `${data.afterDocument.entityId}`
  }

  // Handle label creation/update
  if (data.afterDocument?.key && data.afterDocument?.values?.[0]) {
    return `"${data.afterDocument.key}: ${data.afterDocument.values[0]}"`
  }

  const entityFields = {
    integrationName: data.integrationName,
    webhookName: data.webhookName,
    invitedEmail: data.invitedEmail,
    newUserEmail: data.newUserEmail,
    targetEmail: data.targetEmail,
    insightId: data.insightId,
    labelName: data.labelName,
    conditionName: data.conditionName,
    detectionName: data.detectionName,
    entityName: data.entityName,
    queryName: data.queryName,
    afterName: data.afterDocument?.name,
    beforeName: data.beforeDocument?.name,
    afterValue: data.afterDocument?.values?.[0],
    beforeValue: data.beforeDocument?.values?.[0],
  }

  const value = Object.entries(entityFields).find(([_, value]) => value !== undefined)?.[1]

  if (!value) return ''

  // Add quotes for string values that aren't emails or IDs
  const shouldQuote = !['invitedEmail', 'newUserEmail', 'targetEmail', 'insightId', 'entityName'].includes(
    Object.entries(entityFields).find(([_, v]) => v === value)?.[0] || '',
  )

  return shouldQuote ? `"${value}"` : value
}

const formatChangedField = (data: Record<string, any>): string => {
  if (!data.changedField) return ''
  return `, changed ${data.changedField}`
}

export const formatActivityMessage = (template: string, data: Record<string, any>): string => {
  const userEmail = data.user?.email || 'System'
  const entityName = formatEntityName(data)
  const changedField = formatChangedField(data)
  const ip = data.ip ? ` from ${data.ip}` : ''

  return `${userEmail} ${template} ${entityName}${changedField}${ip}`
}
