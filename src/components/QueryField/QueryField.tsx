import React from 'react'
import { RuleChangeEventArgs } from '@syncfusion/ej2-react-querybuilder'
import { TAndOrUnion } from '@/views/NDR/PortflowPage/components/PortFlowHeader/AndOrButtons'
import MultiSelectContainer from '@/views/NDR/PortflowPage/components/MultiSelect'
import AndOrButtons from '@/views/NDR/PortflowPage/components/PortFlowHeader/AndOrButtons'
import { useAvailableFields } from '@/views/NDR/PortflowPage/components/MultiSelect/MultiSelectContainer'
import useGraphConfig from '@/views/NDR/PortflowPage/hooks/useGraphConfig'
import { PortflowQueryType } from '@/views/NDR/PortflowPage/components/MultiSelect/MultiSelectContainer'
import { Button, Tooltip } from '@nextui-org/react'
import { Icon } from '@iconify/react'
import { colors } from '@nextui-org/react'

interface QueryFieldProps {
  initialTags?: PortflowQueryType[]
  onTagsChange: (filters: PortflowQueryType[]) => void
  queryRules?: RuleChangeEventArgs
  updateRule: (rule: RuleChangeEventArgs) => void
  operator?: TAndOrUnion
  onCleanTags?: () => void
  disabled?: boolean
  allowCustomValues?: boolean
}

const QueryField: React.FC<QueryFieldProps> = ({
  initialTags = [],
  onTagsChange,
  queryRules,
  updateRule,
  operator = 'and',
  onCleanTags,
  disabled = false,
  allowCustomValues = false,
}) => {
  const { tableId } = useGraphConfig()
  const { availableFields } = useAvailableFields({ tableId })

  return (
    <div className="flex flex-col gap-1 overflow-hidden">
      <MultiSelectContainer
        initialTags={initialTags}
        onTagsChange={onTagsChange}
        dynamicSuggestions={[]}
        availableFields={availableFields}
        operator={operator}
        hostIdForPopUp="create-control-form"
        disabled={disabled}
        allowCustomValues={allowCustomValues}
      >
        <div className="flex flex-grow">
          {initialTags.length > 0 && onCleanTags && (
            <Tooltip content="Reset Filter">
              <Button isIconOnly onClick={onCleanTags} className="bg-background border-default-200">
                <Icon color={colors.blue['500']} fontSize={22} icon="fluent:text-clear-formatting-24-filled" />
              </Button>
            </Tooltip>
          )}
        </div>
        <div className="flex sticky right-[12px] bottom-0">
          <AndOrButtons value={operator} queryRules={queryRules as RuleChangeEventArgs} ruleChange={updateRule} />
        </div>
      </MultiSelectContainer>
    </div>
  )
}

export default QueryField
