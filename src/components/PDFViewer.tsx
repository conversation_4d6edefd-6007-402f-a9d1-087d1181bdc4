import privacyPolicySourcePdfFile from '@/assets/pdf/Port0-Privacy_Policy.pdf'
import termsAndConditionSourcePdfFile from '@/assets/pdf/Port0-Terms_of_Service.pdf'
import { createPortal } from 'react-dom'
import React from 'react'

const pdfFiles = {
  'privacy-policy': privacyPolicySourcePdfFile,
  'terms-and-condition': termsAndConditionSourcePdfFile,
}

const PDFViewer = ({ pdfType }: { pdfType: 'privacy-policy' | 'terms-and-condition' }) => {
  const fileSrc = pdfFiles[pdfType]

  return createPortal(
    <div
      className="w-full left-0 top-0 fixed flex flex-col items-center justify-center h-full"
      style={{ zIndex: 10_0001 }}
    >
      <iframe src={fileSrc} title="" width="100%" height="100%" />
    </div>,
    document.body,
  )
}

export default PDFViewer
