.mentions {
  @apply w-full min-h-[48px] rounded-xl border border-gray-300 focus:border-blue-400 focus:ring-2 focus:ring-blue-100 text-base bg-white text-black transition-all;

  textarea {
    border-radius: 10px;
    padding: 12px 14px;
    padding-right: 48px; /* Make room for the send button */
    overflow: hidden;
    transition: all 0.2s ease;
  }
}

.mentions__suggestions__list {
  max-height: 300px;
  overflow-y: auto;
  background-color: white;
  border-radius: 8px;
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.1),
    0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.05);
  animation: fadeIn 0.2s ease-out;
}

.mentions--multiLine .mentions__highlighter {
  border: 1px solid transparent;
  padding: 9px;
  min-height: 63px;
}

.mentions--multiLine .mentions__input {
  border: 1px solid transparent;
  padding: 9px;
  outline: 0;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulseAnimation {
  0% {
    transform: scale(1.05);
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.5);
  }
  50% {
    transform: scale(1.12);
    box-shadow: 0 0 0 6px rgba(59, 130, 246, 0);
  }
  100% {
    transform: scale(1.05);
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

.mentions__mention {
  position: relative;
  z-index: 1;
  color: #2563eb;
  font-weight: 500;
  text-shadow:
    1px 1px 1px white,
    1px -1px 1px white,
    -1px 1px 1px white,
    -1px -1px 1px white;
  text-decoration: underline;
  pointer-events: none;
  max-height: 100px;
}

.commentText {
  white-space: pre-wrap;
  word-break: break-word;
  overflow-wrap: break-word;
  margin-bottom: 15px;
}

.sendButton {
  transition: all 0.25s ease;
}

.sendButton:hover:not(:disabled) {
  transform: scale(1.05);
  background-color: #2563eb;
  animation: pulseAnimation 1.5s infinite;
}

.sendButton:active:not(:disabled) {
  transform: scale(0.95);
  animation: none;
}

.sendButton:disabled {
  background-color: #94a3b8;
  opacity: 0.6;
  cursor: not-allowed;
}

.commentItem {
  animation: slideIn 0.3s ease-out;
  transition: all 0.2s ease;
}

.commentItem:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.fadeIn {
  animation: fadeIn 0.3s ease-out;
}
