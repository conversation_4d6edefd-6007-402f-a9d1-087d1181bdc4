import { User } from '@/zustandStores/useMemberships'
import { mentionRegex } from './constants'
import { Fragment } from 'react/jsx-runtime'

export const getInitials = (userName: string) => userName.match(/\b\w/g)?.join('').toUpperCase().slice(0, 2) || ''

export const extractMentions = (text: string) => {
  const mentions = []
  let match
  while ((match = mentionRegex.exec(text))) {
    mentions.push({ name: match[1], userId: match[2] })
  }
  return mentions
}

export const getActualTextLength = (text: string) => text.replace(mentionRegex, '').length

export const truncate = (text: string) => {
  const lines = text.split('\n')
  if (lines.length > 3) {
    let truncated = lines.slice(0, 3).join('\n')
    let match
    let lastMentionEnd = 0
    while ((match = mentionRegex.exec(truncated)) !== null) {
      lastMentionEnd = match.index + match[0].length
    }
    if (lastMentionEnd < truncated.length) {
      truncated = truncated.slice(0, lastMentionEnd)
    }
    return truncated + '...'
  }
  if (text.length > 240) {
    let cut = text.slice(0, 240)
    let match
    let lastMentionEnd = 0
    while ((match = mentionRegex.exec(cut)) !== null) {
      lastMentionEnd = match.index + match[0].length
    }
    if (lastMentionEnd < cut.length) {
      cut = cut.slice(0, lastMentionEnd)
    }
    return cut + '...'
  }
  return text
}

export const renderCommentText = (text: string, users: User[]) => {
  let keyCounter = 0

  const processLine = (line: string) => {
    let lineLastIndex = 0
    let lineMatch: RegExpExecArray | null
    const lineParts = []

    mentionRegex.lastIndex = 0

    while ((lineMatch = mentionRegex.exec(line))) {
      if (lineMatch.index > lineLastIndex) {
        lineParts.push(line.slice(lineLastIndex, lineMatch.index))
      }

      const matchUserId = lineMatch[2]
      keyCounter++

      if (matchUserId) {
        const mentionedUser = users.find((u) => u.userId === matchUserId)
        lineParts.push(
          <span
            key={`${matchUserId}_${keyCounter}`}
            className="inline-flex items-center gap-1 bg-blue-50 text-blue-700 px-2 py-0.5 rounded-full font-medium hover:bg-blue-100 transition-colors cursor-pointer mr-1"
          >
            <span className="w-4 h-4 rounded-full bg-blue-100 flex items-center justify-center text-[10px]">
              {mentionedUser?.imageUrl ? (
                <img
                  src={mentionedUser.imageUrl}
                  alt={mentionedUser.firstName}
                  className="w-4 h-4 rounded-full object-cover"
                />
              ) : (
                getInitials(mentionedUser?.firstName || 'U')
              )}
            </span>
            @{lineMatch[1]}
          </span>,
        )
      }

      lineLastIndex = lineMatch.index + lineMatch[0].length
    }

    if (lineLastIndex < line.length) {
      lineParts.push(line.slice(lineLastIndex))
    }

    return lineParts
  }

  const lines = text.split('\n')
  return lines.map((line, i) => (
    <Fragment key={`line-${i}`}>
      {i > 0 && <br />}
      {processLine(line)}
    </Fragment>
  ))
}

export const formatTextForEmail = (text: string) => text.replace(mentionRegex, '@$1')
