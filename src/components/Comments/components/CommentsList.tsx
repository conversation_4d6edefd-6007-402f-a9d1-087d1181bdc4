import dayjs from 'dayjs'
import { useRef, useState, useMemo } from 'react'
import { extractMentions, formatTextForEmail, getInitials, renderCommentText, truncate } from '../utils'
import EditCommentModal from './EditCommentModal'
import styles from '../styles.module.css'
import { useEditComment } from '@/firestoreQueries/ndr/comments/hooks/useEditComment'
import { useDeleteComment } from '@/firestoreQueries/ndr/comments/hooks/useDeleteComment'
import { User } from '@/zustandStores/useMemberships'
import { SuggestionFunc } from 'react-mentions'
import { maxMentionsLabel } from '../constants'
import { useForm, FormProvider } from 'react-hook-form'
import { IComment } from '../Comments'

interface EditFormValues {
  text: string
}

function CommentsList({
  comments,
  users,
  currentUser,
  organizationId,
  entityId,
  renderSuggestion,
}: {
  comments: IComment[]
  users: User[]
  currentUser: any
  organizationId: string | undefined
  entityId: string
  renderSuggestion: SuggestionFunc
}) {
  const parentRef = useRef<HTMLDivElement>(null)
  const [expanded, setExpanded] = useState<{ [id: string]: boolean }>({})
  const [editId, setEditId] = useState<string | null>(null)
  const [editingComment, setEditingComment] = useState<IComment | null>(null)
  const editCommentMutation = useEditComment(organizationId, entityId)
  const deleteCommentMutation = useDeleteComment(organizationId, entityId)

  const form = useForm<EditFormValues>({
    defaultValues: { text: '' },
    mode: 'onChange',
    resolver: (values) => {
      const errors: { text?: { message: string } } = {}
      const mentions = extractMentions(values.text || '')

      if (mentions.length > 5) {
        errors.text = { message: maxMentionsLabel }
      }

      return {
        values,
        errors,
      }
    },
  })

  const mentionData = useMemo(
    () =>
      users.map(({ userId, firstName, lastName }) => {
        const fullName = `${firstName} ${lastName}`
        return {
          id: userId,
          display: fullName,
          label: fullName,
        }
      }),
    [users],
  )

  const handleEdit = async (commentId: string) => {
    const values = form.getValues()
    const mentions = extractMentions(values.text)
    const emailText = formatTextForEmail(values.text)
    const editedComment = {
      commentId,
      text: values.text,
      emailText,
      authorId: currentUser.userId!,
      mentioned: mentions.map(({ userId }) => ({ userId })),
    }

    await editCommentMutation.mutateAsync(editedComment)
    closeEditModal()
  }

  const startEdit = (comment: IComment) => {
    setEditId(comment.id)
    setEditingComment(comment)
    form.reset({ text: comment.text })
  }

  const closeEditModal = () => {
    setEditId(null)
    setEditingComment(null)
    form.reset({ text: '' })
  }

  const deleteComment = (id: string) => deleteCommentMutation.mutate({ commentId: id })

  const handleExpandComment = (commentId: string) => {
    setExpanded((text) => {
      const newExpanded = { ...text, [commentId]: true }
      return newExpanded
    })
  }

  const handleCollapseComment = (commentId: string) => {
    setExpanded((text) => {
      const newExpanded = { ...text }
      delete newExpanded[commentId]
      return newExpanded
    })
  }

  const formattedDates = useMemo(() => {
    return comments.reduce(
      (acc, comment) => {
        acc[comment.id] = dayjs(comment.createdAt.toDate()).format('DD/MM/YYYY HH:mm')
        return acc
      },
      {} as Record<string, string>,
    )
  }, [comments])

  return (
    <>
      <div
        ref={parentRef}
        className="overflow-auto"
        style={{
          willChange: 'transform',
          maxHeight: 'calc(100vh - 500px)',
          minHeight: '200px',
        }}
      >
        <div style={{ display: 'grid', gridAutoFlow: 'row', rowGap: '1px', padding: '1px 0' }}>
          {comments.map((comment) => {
            const isOwn = comment.authorId === currentUser.userId
            const isExpanded = expanded[comment.id]
            const textToShow = isExpanded ? comment.text : truncate(comment.text)
            const authorUser = comment.authorId ? users.find((u) => u.userId === comment.authorId) : undefined

            return (
              <div key={comment.id} className="w-full mt-2">
                <div
                  data-comment-id={comment.id}
                  className={`flex gap-1 items-start bg-white rounded-lg shadow-sm hover:shadow p-2 border border-gray-100 relative group transition-all duration-200 ${styles.commentItem}`}
                >
                  <div className="w-7 h-7 rounded-full bg-blue-100 flex items-center justify-center text-blue-700 font-bold text-sm select-none flex-shrink-0">
                    {authorUser?.imageUrl ? (
                      <img
                        src={authorUser.imageUrl}
                        alt={authorUser.firstName}
                        className="w-7 h-7 rounded-full object-cover"
                      />
                    ) : (
                      getInitials(authorUser?.firstName || 'U')
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="space-y-0.5">
                      <div className={styles.commentText}>
                        {renderCommentText(textToShow, users)}
                        {!isExpanded && comment.text.length > textToShow.length && (
                          <span
                            className="text-blue-500 cursor-pointer ml-2 font-medium hover:underline"
                            onClick={() => handleExpandComment(comment.id)}
                          >
                            more
                          </span>
                        )}
                        {isExpanded && (
                          <span
                            className="text-blue-500 cursor-pointer ml-2 font-medium hover:underline"
                            onClick={() => handleCollapseComment(comment.id)}
                          >
                            less
                          </span>
                        )}
                      </div>
                      <div className="flex justify-between items-center mt-1 text-xs text-gray-500">
                        <span className="flex items-center gap-1">
                          <span className="font-semibold text-gray-700">
                            {authorUser?.firstName ? `${authorUser.firstName} ${authorUser.lastName}` : 'Unknown'}
                          </span>
                          {isOwn && (
                            <span className="text-xs text-blue-600 bg-blue-50 px-2 py-0.5 rounded-full">
                              Your comment
                            </span>
                          )}
                          <span className="text-gray-300 mx-1">•</span>
                          {formattedDates[comment.id]}
                        </span>
                        <span className="flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                          {isOwn && (
                            <>
                              <button
                                className="hover:bg-blue-50 p-1 rounded-full transition-colors"
                                onClick={() => startEdit(comment)}
                                title="Edit"
                              >
                                <span className="material-icons text-blue-500 text-sm">edit</span>
                              </button>
                              <button
                                className="hover:bg-red-50 p-1 rounded-full transition-colors"
                                onClick={() => deleteComment(comment.id)}
                                title="Delete"
                              >
                                <span className="material-icons text-red-500 text-sm">delete</span>
                              </button>
                            </>
                          )}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      </div>
      <FormProvider {...form}>
        <EditCommentModal
          isOpen={!!editId}
          comment={editingComment}
          onClose={closeEditModal}
          onSave={handleEdit}
          mentionData={mentionData}
          renderSuggestion={renderSuggestion}
          editText={form.watch('text')}
          setEditText={(text) => form.setValue('text', text, { shouldValidate: true })}
          editMentionCount={extractMentions(form.watch('text')).length}
          editMentionError={form.formState.errors.text?.message || ''}
        />
      </FormProvider>
    </>
  )
}

export default CommentsList
