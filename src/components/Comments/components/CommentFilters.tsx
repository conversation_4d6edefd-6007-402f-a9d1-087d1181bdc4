import { useState } from 'react'
import DatePickerRange, { DatePickerRangeValue } from '@/components/ui/DatePicker/DatePickerRange'

interface CommentFiltersProps {
  onKeywordFilterChange: (keyword: string) => void
  dateRange: DatePickerRangeValue
  onDateRangeChange: (range: DatePickerRangeValue) => void
}

function CommentFilters({
  onKeywordFilterChange,
  dateRange,
  onDateRangeChange,
}: CommentFiltersProps) {
  const [keyword, setKeyword] = useState('')
  const [keywordFilterFocused, setKeywordFilterFocused] = useState(false)

  const handleKeywordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value
    setKeyword(newValue)
    onKeywordFilterChange(newValue)
  }

  const handleClearKeyword = () => {
    setKeyword('')
    onKeywordFilterChange('')
  }

  return (
    <>
      <div className="relative">
        <input
          placeholder="Search keyword..."
          className="border border-gray-300 px-3 py-1.5 rounded-lg focus:border-blue-400 focus:ring-1 focus:ring-blue-100 transition-all h-[45px] pr-8 overflow-hidden text-ellipsis"
          value={keyword}
          onChange={handleKeywordChange}
          onFocus={() => setKeywordFilterFocused(true)}
          onBlur={() => setKeywordFilterFocused(false)}
        />
        {keyword && !keywordFilterFocused && (
          <button
            className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
            onClick={handleClearKeyword}
          >
            <span className="material-icons text-sm">close</span>
          </button>
        )}
      </div>
      <div className="relative min-w-[180px]">
        <div className="flex items-center">
          <DatePickerRange
            className="h-[45px] rounded-lg focus:border-blue-400 focus:ring-1 focus:ring-blue-100 transition-all"
            clearable
            inputFormat="DD/MM/YYYY"
            placeholder="Filter by date"
            value={dateRange}
            onChange={onDateRangeChange}
            singleDate
            placement="bottom-start"
            flipEnabled={false}
          />
        </div>
      </div>
    </>
  )
}

export default CommentFilters
