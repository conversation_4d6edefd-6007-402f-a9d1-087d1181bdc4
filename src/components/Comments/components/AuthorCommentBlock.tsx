import { useRef, useState } from 'react'
import { getInitials } from '../utils'
import useClickOutsideMultiple from '@/hooks/useClickOutsideMultiple'
import { Author } from '../Comments'

interface AuthorCommentBlockProps {
  uniqueAuthors: <AUTHORS>
  selectedAuthor: Author
  handleClearAuthor: (event: React.MouseEvent) => void
  handleSelectAuthor: (authorId: string) => void
  authorDropdownOpen: boolean
  setAuthorDropdownOpen: (open: boolean) => void
}

const AuthorCommentBlock = ({
  uniqueAuthors,
  selectedAuthor,
  handleClearAuthor,
  handleSelectAuthor,
  authorDropdownOpen,
  setAuthorDropdownOpen,
}: AuthorCommentBlockProps) => {
  const [authorFilterFocused, setAuthorFilterFocused] = useState(false)
  const authorDropdownRef = useRef<HTMLDivElement>(null)

  const handleAuthorFocus = () => setAuthorFilterFocused(true)
  const handleAuthorBlur = () => setAuthorFilterFocused(false)
  const handleToggleAuthorDropdown = () => setAuthorDropdownOpen(!authorDropdownOpen)

  useClickOutsideMultiple([authorDropdownRef], () => setAuthorDropdownOpen(false))

  return (
    <div className="relative" ref={authorDropdownRef}>
      <div
        className="border border-gray-300 px-3 py-1.5 rounded-lg focus:border-blue-400 focus:ring-1 focus:ring-blue-100 transition-all bg-white cursor-pointer flex items-center gap-2 h-[45px] min-w-[180px] overflow-hidden whitespace-nowrap pr-12 relative"
        tabIndex={0}
        onClick={handleToggleAuthorDropdown}
        onFocus={handleAuthorFocus}
        onBlur={handleAuthorBlur}
      >
        {selectedAuthor ? (
          <>
            <div className="flex items-center gap-2 overflow-hidden whitespace-nowrap text-ellipsis pr-2 w-full">
              <div className="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center text-blue-700 font-bold text-xs">
                {selectedAuthor.imageUrl ? (
                  <img
                    src={selectedAuthor.imageUrl}
                    alt={`${selectedAuthor.name} avatar`}
                    className="w-6 h-6 rounded-full object-cover"
                  />
                ) : (
                  getInitials(selectedAuthor.name)
                )}
              </div>
              <span className="overflow-hidden whitespace-nowrap text-ellipsis pr-2 block max-w-[calc(100%-2.5rem)]">
                {selectedAuthor.name}
              </span>
            </div>
            {!authorFilterFocused && (
              <button
                className="ml-auto text-gray-400 hover:text-gray-600 absolute right-2"
                onClick={handleClearAuthor}
              >
                <span className="material-icons text-sm">close</span>
              </button>
            )}
          </>
        ) : (
          <span className="text-gray-400">Filter by author</span>
        )}
      </div>
      {authorDropdownOpen && (
        <div className="absolute top-full left-0 mt-1 w-full bg-white border border-gray-200 rounded-lg shadow-lg z-10 max-h-[300px] overflow-y-auto">
          {uniqueAuthors.map((author) => (
            <div
              key={author.id}
              className="px-3 py-2 hover:bg-blue-50 cursor-pointer flex items-center gap-2"
              onClick={() => handleSelectAuthor(author.id)}
            >
              <div className="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center text-blue-700 font-bold text-xs">
                {author.imageUrl ? (
                  <img src={author.imageUrl} alt="" className="w-6 h-6 rounded-full object-cover" />
                ) : (
                  getInitials(author.name)
                )}
              </div>
              <span>{author.name}</span>
            </div>
          ))}
          {uniqueAuthors.length === 0 && <div className="px-3 py-2 text-gray-500">No authors found</div>}
        </div>
      )}
    </div>
  )
}

export default AuthorCommentBlock
