import dayjs from 'dayjs'
import { useMemo } from 'react'
import { Author } from '../Comments'

interface FilterSummaryProps {
  filters: {
    author: string
    keyword: string
    date: string
  }
  selectedAuthor: Author
  handleClearAllFilters: () => void
}

const FilterSummary = ({ filters, selectedAuthor, handleClearAllFilters }: FilterSummaryProps) => {
  const formattedDate = useMemo(() => (filters.date ? dayjs(filters.date).format('DD/MM/YYYY') : ''), [filters.date])

  return (
    <>
      {(filters.author || filters.keyword || filters.date) && (
        <div className="mb-4 ml-2 mt-1 flex items-center gap-2 flex-wrap">
          <span className="text-sm text-gray-500">Filtered by:</span>
          {filters.author && (
            <span className="text-xs bg-blue-100 text-blue-700 px-2 py-0.5 rounded-full">
              Author: {selectedAuthor?.name}
            </span>
          )}
          {filters.keyword && (
            <span className="text-xs bg-blue-100 text-blue-700 px-2 py-0.5 rounded-full">
              Keyword: {filters.keyword}
            </span>
          )}
          {filters.date && (
            <span className="text-xs bg-blue-100 text-blue-700 px-2 py-0.5 rounded-full">Date: {formattedDate}</span>
          )}
          <button
            className="text-xs text-gray-500 hover:text-gray-700 font-medium hover:underline"
            onClick={handleClearAllFilters}
          >
            Clear all
          </button>
        </div>
      )}
    </>
  )
}

export default FilterSummary
