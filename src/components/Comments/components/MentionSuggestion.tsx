import { SuggestionDataItem } from 'react-mentions'

interface MentionSuggestionProps {
  suggestion: SuggestionDataItem
  focused: boolean
  users: Array<{
    userId: string
    firstName: string
    lastName: string
    imageUrl?: string
  }>
}

function MentionSuggestion({ suggestion, focused, users }: MentionSuggestionProps) {
  const user = users.find((u) => u.userId === suggestion.id)
  
  if (!user) return null

  return (
    <div className={`flex items-center gap-2 px-2 py-1.5 ${focused ? 'bg-blue-50' : ''}`}>
      {user.imageUrl ? (
        <img src={user.imageUrl} alt="" className="w-6 h-6 rounded-full" />
      ) : (
        <div className="w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center">
          <span className="text-xs text-gray-500">
            {user.firstName[0]}{user.lastName[0]}
          </span>
        </div>
      )}
      <span className="text-sm text-gray-700">
        {user.firstName} {user.lastName}
      </span>
    </div>
  )
}

export default MentionSuggestion
