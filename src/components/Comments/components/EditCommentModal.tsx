import { MentionsInput, Mention, SuggestionFunc, SuggestionDataItem } from 'react-mentions'
import { Modal, ModalContent, ModalHeader, ModalBody } from '@nextui-org/react'
import styles from '../styles.module.css'
import { getActualTextLength } from '../utils'
import SaveIcon from '@/assets/icons/SaveIcon'
import { IComment } from '../Comments'

const EditCommentModal = ({
  isOpen,
  comment,
  onClose,
  onSave,
  mentionData,
  renderSuggestion,
  editText,
  setEditText,
  editMentionCount,
  editMentionError,
}: {
  isOpen: boolean
  comment: IComment | null
  onClose: () => void
  onSave: (commentId: string) => void
  mentionData: SuggestionDataItem[]
  renderSuggestion: SuggestionFunc
  editText: string
  setEditText: (text: string) => void
  editMentionCount: number
  editMentionError: string
}) => {
  if (!comment) return null

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      size="lg"
      classNames={{
        base: 'bg-white',
        header: 'border-b border-blue-100',
        body: 'py-6',
        footer: 'border-t border-blue-100',
      }}
    >
      <ModalContent>
        <ModalHeader className="flex justify-between items-center">
          <h3 className="text-lg font-semibold">Edit comment</h3>
        </ModalHeader>
        <ModalBody>
          <div className="space-y-4">
            <div className="relative">
              <MentionsInput
                value={editText}
                classNames={styles}
                onChange={({ target }) => setEditText(target.value)}
                placeholder="Type @ to tag a user (max 5 users)..."
              >
                <Mention
                  trigger="@"
                  data={mentionData}
                  className={styles.mentions__mention}
                  renderSuggestion={renderSuggestion}
                  appendSpaceOnAdd
                  displayTransform={(_, display) => `@${display},`}
                />
              </MentionsInput>
              <button
                onClick={() => onSave(comment.id)}
                className={`absolute right-3 bottom-3 bg-blue-600 text-white rounded-full p-2 shadow-sm flex items-center justify-center ${styles.sendButton}`}
                disabled={!editText.trim() || editMentionCount > 5}
              >
                <SaveIcon />
              </button>
            </div>
            <div className="flex justify-between">
              <div className="text-xs text-rose-500">{editMentionError}</div>
              <div className="text-xs text-gray-500">
                {getActualTextLength(editText)}/500 characters | {editMentionCount}/5 mentions
              </div>
            </div>
          </div>
        </ModalBody>
      </ModalContent>
    </Modal>
  )
}

export default EditCommentModal
