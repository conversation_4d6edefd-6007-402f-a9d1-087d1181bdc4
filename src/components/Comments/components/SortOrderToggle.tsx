import { useState } from 'react'
import { ArrowDownWideNarrow, ArrowUpWideNarrow } from 'lucide-react'

interface SortOrderToggleProps {
  onSortOrderChange: (isNewestOrder: boolean) => void
}

function SortOrderToggle({ onSortOrderChange }: SortOrderToggleProps) {
  const [isNewestOrder, setIsNewestOrder] = useState(true)

  const handleToggle = () => {
    const newOrder = !isNewestOrder
    setIsNewestOrder(newOrder)
    onSortOrderChange(newOrder)
  }

  return (
    <div
      onClick={handleToggle}
      className="flex items-center gap-1.5 px-3 py-1.5 h-[45px] border border-gray-300 rounded-lg bg-white hover:bg-gray-50 transition-colors cursor-pointer ml-auto"
      title={isNewestOrder ? 'Showing newest first' : 'Showing oldest first'}
    >
      <span className="text-gray-700 text-sm whitespace-nowrap">{isNewestOrder ? 'Newest first' : 'Oldest first'}</span>
      {isNewestOrder ? (
        <ArrowDownWideNarrow className="w-4 h-4 text-blue-600" />
      ) : (
        <ArrowUpWideNarrow className="w-4 h-4 text-blue-600" />
      )}
    </div>
  )
}

export default SortOrderToggle
