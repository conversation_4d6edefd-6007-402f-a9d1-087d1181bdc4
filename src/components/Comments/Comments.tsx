import { use<PERSON><PERSON>, Form<PERSON>rov<PERSON>, Controller } from 'react-hook-form'
import { useState, useMemo, useEffect, useCallback } from 'react'
import dayjs from 'dayjs'
import { useAuth, useOrganization } from '@clerk/clerk-react'
import { useMembershipsStore } from '@/zustandStores/useMemberships'
import { extractMentions, formatTextForEmail, getActualTextLength } from './utils'
import { DatePickerRangeValue } from '@/components/ui/DatePicker/DatePickerRange'
import styles from './styles.module.css'
import { MentionsInput, Mention, SuggestionDataItem, SuggestionFunc } from 'react-mentions'
import SendIcon from '@/assets/icons/SendIcon'
import CommentSkeleton from './components/CommentSkeleton'
import { useAddComment } from '@/firestoreQueries/ndr/comments/hooks/useAddComment'
import { useGetCommentsInfinite } from '@/firestoreQueries/ndr/comments/hooks/useGetCommentsInfinite'
import { maxMentionsLabel, PAGE_SIZE } from './constants'
import { DEFAULT_INPUT_FORMAT } from '../ui/DatePicker/DatePicker'
import { useHighlightComment } from './hooks/useHighlightComment'
import { Timestamp } from 'firebase/firestore'
import AuthorCommentBlock from './components/AuthorCommentBlock'
import FilterSummary from './components/FilterSummary'
import CommentFilters from './components/CommentFilters'
import SortOrderToggle from './components/SortOrderToggle'
import { useDebounceValue } from 'usehooks-ts'
import { useCommentFilters } from './hooks/useCommentFilters'
import CommentsList from './components/CommentsList'
import MentionSuggestion from './components/MentionSuggestion'
import InfiniteScrollPagination from '@/components/ui/InfiniteScrollPagination/InfiniteScrollPagination'

export type ResourceType = 'inventory' | 'insight'

export interface IComment {
  id: string
  parentId: null | string
  mentioned: {
    userId: string
  }[]
  text: string
  emailText: string
  entityId: string
  authorId: string
  updatedAt: Timestamp
  createdAt: Timestamp
  resourceType: ResourceType
  insightId?: string | null
}

export interface Author {
  id: string
  name: string
  imageUrl: string | undefined
}

interface CommentsProps {
  entityId: string
  resourceType: ResourceType
  insightId?: string
  commentId?: string
}

interface FormValues {
  text: string
}

const validateCommentForm = (values: FormValues) => {
  const errors: { text?: { message: string } } = {}
  const textLength = getActualTextLength(values.text || '')
  if (textLength > 500) {
    errors.text = { message: 'Maximum 500 characters allowed per comment' }
  }
  const mentions = extractMentions(values.text || '')
  if (mentions.length > 5) {
    errors.text = { message: maxMentionsLabel }
  }
  return {
    values,
    errors,
  }
}

function Comments({ entityId, resourceType, insightId, commentId }: CommentsProps) {
  const organizationId = useOrganization().organization?.id
  const users = useMembershipsStore().memberships
  const currentUser = useAuth()
  const { data, isLoading, fetchNextPage, hasNextPage, isFetchingNextPage } = useGetCommentsInfinite(
    organizationId,
    entityId,
    PAGE_SIZE,
  )
  const addCommentMutation = useAddComment(organizationId, entityId)

  const {
    filters,
    dateRange,
    authorDropdownOpen,
    setAuthorDropdownOpen,
    handleKeywordFilterChange,
    handleDateRangeChange,
    handleClearAllFilters,
    handleSelectAuthor,
    handleClearAuthor,
    handleSortOrderChange,
  } = useCommentFilters()

  const form = useForm<FormValues>({
    defaultValues: { text: '' },
    mode: 'onChange',
    criteriaMode: 'all',
    resolver: validateCommentForm,
  })

  const watchedText = form.watch('text')
  const [debouncedText] = useDebounceValue(watchedText, 300)
  const mentions = useMemo(() => extractMentions(debouncedText || ''), [debouncedText])
  const mentionCount = useMemo(() => mentions.length, [mentions])

  const textError = form.formState.errors.text?.message

  const allComments: IComment[] = useMemo(() => {
    if (!data?.pages) return []
    return data.pages.flatMap((page) => page.comments || [])
  }, [data?.pages])

  const mentionData = useMemo(() => {
    return users.reduce((acc, user) => {
      if (user.userId && user.firstName && user.lastName) {
        acc.push({
          id: user.userId,
          display: `${user.firstName} ${user.lastName}`,
        })
      }
      return acc
    }, [] as SuggestionDataItem[])
  }, [users])

  const uniqueAuthors = useMemo(() => {
    const authorIds = new Set(allComments.map((comment) => comment.authorId))
    return Array.from(authorIds).map((authorId) => {
      const user = users.find((u) => u.userId === authorId)
      return {
        id: authorId,
        name: user ? `${user.firstName} ${user.lastName}` : 'Unknown',
        imageUrl: user?.imageUrl,
      }
    })
  }, [allComments, users])

  const selectedAuthor = useMemo(() => {
    if (!filters.author) return null
    return uniqueAuthors.find((author) => author.id === filters.author)
  }, [filters.author, uniqueAuthors])

  const filteredComments = useMemo(() => {
    const filtered = allComments.filter((c) => {
      const authorMatch = filters.author ? c.authorId === filters.author : true
      const keywordMatch = filters.keyword ? c.text.toLowerCase().includes(filters.keyword.toLowerCase()) : true
      const dateMatch = filters.date ? dayjs(c.createdAt.toDate()).format(DEFAULT_INPUT_FORMAT) === filters.date : true
      return authorMatch && keywordMatch && dateMatch
    })

    if (filters.sortOrder === 'oldest') {
      return [...filtered].sort((a, b) => {
        const dateA = a.createdAt.toDate().getTime()
        const dateB = b.createdAt.toDate().getTime()
        return dateA - dateB
      })
    }

    return filtered
  }, [allComments, filters])

  const createComment = async (values: FormValues) => {
    const emailText = formatTextForEmail(values.text)
    await addCommentMutation.mutateAsync({
      text: values.text,
      emailText,
      authorId: currentUser.userId!,
      mentioned: mentions.map((m) => ({ userId: m.userId })),
      resourceType,
      insightId,
    })
    form.reset()
  }

  useHighlightComment({
    commentId,
    isLoading,
    filteredComments,
  })

  return (
    <div className="p-2 bg-gradient-to-b from-blue-50/50 to-gray-50 shadow-lg border border-blue-100">
      <FormProvider {...form}>
        <form onSubmit={form.handleSubmit(createComment)}>
          <div className="relative">
            <Controller
              name="text"
              control={form.control}
              render={({ field: { onChange, value } }) => (
                <MentionsInput
                  value={value}
                  classNames={styles}
                  onChange={onChange}
                  placeholder="Type @ to tag a user (max 5 users)..."
                >
                  <Mention
                    trigger="@"
                    data={mentionData}
                    className={styles.mentions__mention}
                    renderSuggestion={(suggestion, search, highlightedDisplay, index, focused) => (
                      <MentionSuggestion
                        suggestion={suggestion}
                        focused={focused}
                        users={users}
                      />
                    )}
                    appendSpaceOnAdd
                    displayTransform={(_, display) => `@${display},`}
                  />
                </MentionsInput>
              )}
            />
            <button
              className={`absolute right-3 bottom-3 bg-blue-600 text-white rounded-full p-2 shadow-sm flex items-center justify-center ${styles.sendButton}`}
              type="submit"
              disabled={!form.formState.isValid}
            >
              <SendIcon />
            </button>
          </div>
          <div className="flex justify-between mt-1">
            <div className="text-xs text-rose-500">{textError}</div>
            <div className="text-xs text-gray-500">
              {getActualTextLength(form.watch('text') || '')}/500 characters | {mentionCount}/5 mentions
            </div>
          </div>
        </form>
      </FormProvider>
      <div className="mt-1 flex flex-wrap gap-3 items-start justify-between bg-white p-2 rounded-xl shadow-sm border border-gray-100">
        <div className="flex flex-wrap gap-3 items-start">
          <AuthorCommentBlock
            uniqueAuthors={uniqueAuthors}
            selectedAuthor={selectedAuthor!}
            handleClearAuthor={handleClearAuthor}
            handleSelectAuthor={handleSelectAuthor}
            authorDropdownOpen={authorDropdownOpen}
            setAuthorDropdownOpen={setAuthorDropdownOpen}
          />
          <CommentFilters
            onKeywordFilterChange={handleKeywordFilterChange}
            dateRange={dateRange}
            onDateRangeChange={handleDateRangeChange}
          />
        </div>
        <SortOrderToggle onSortOrderChange={handleSortOrderChange} />
      </div>
      <FilterSummary filters={filters} selectedAuthor={selectedAuthor!} handleClearAllFilters={handleClearAllFilters} />
      <hr className="mb-4" />
      <div className="pr-1">
        {isLoading && <CommentSkeleton />}
        {filteredComments.length === 0 && !isLoading && (
          <div className="text-gray-400 text-center">No comments found.</div>
        )}
        {filteredComments.length > 0 && !isLoading && (
          <CommentsList
            comments={filteredComments}
            users={users}
            currentUser={currentUser}
            organizationId={organizationId!}
            entityId={entityId}
            renderSuggestion={(suggestion, search, highlightedDisplay, index, focused) => (
              <MentionSuggestion
                suggestion={suggestion}
                focused={focused}
                users={users}
              />
            )}
          />
        )}
        <InfiniteScrollPagination
          hasNextPage={hasNextPage}
          isFetchingNextPage={isFetchingNextPage}
          fetchNextPage={fetchNextPage}
        />
      </div>
    </div>
  )
}

export default Comments
