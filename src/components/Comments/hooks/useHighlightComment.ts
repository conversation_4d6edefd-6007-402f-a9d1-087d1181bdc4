import { useEffect, useRef } from 'react'
import { IComment } from '../Comments'

interface UseHighlightCommentProps {
  commentId?: string
  isLoading: boolean
  filteredComments: IComment[]
}

export const useHighlightComment = ({ commentId, isLoading, filteredComments }: UseHighlightCommentProps) => {
  const highlightedComments = useRef<Set<string>>(new Set())

  useEffect(() => {
    const highlightComment = () => {
      if (commentId && !isLoading && filteredComments.length > 0 && !highlightedComments.current.has(commentId)) {
        const commentElement = document.querySelector(`[data-comment-id="${commentId}"]`)
        if (commentElement) {
          commentElement.scrollIntoView({ behavior: 'smooth', block: 'center' })
          commentElement.classList.replace('bg-white', 'bg-blue-50')
          highlightedComments.current.add(commentId)
          setTimeout(() => {
            commentElement.classList.replace('bg-blue-50', 'bg-white')
          }, 2000)
        }
      }
    }
    highlightComment()
  }, [commentId, isLoading, filteredComments])
}
