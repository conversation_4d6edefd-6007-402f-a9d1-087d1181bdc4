import { useState } from 'react'
import dayjs from 'dayjs'
import { DatePickerRangeValue } from '@/components/ui/DatePicker/DatePickerRange'
import { DEFAULT_INPUT_FORMAT } from '@/components/ui/DatePicker/DatePicker'

interface Filters {
  author: string
  keyword: string
  date: string
  sortOrder: 'newest' | 'oldest'
}

interface UseCommentFiltersReturn {
  filters: Filters
  dateRange: DatePickerRangeValue
  setAuthorDropdownOpen: (open: boolean) => void
  authorDropdownOpen: boolean
  handleKeywordFilterChange: (keyword: string) => void
  handleDateRangeChange: (range: DatePickerRangeValue) => void
  handleClearAllFilters: () => void
  handleSelectAuthor: (authorId: string) => void
  handleClearAuthor: (event: React.MouseEvent) => void
  handleSortOrderChange: (isNewestOrder: boolean) => void
}

export function useCommentFilters(): UseCommentFiltersReturn {
  const [filters, setFilters] = useState<Filters>({ author: '', keyword: '', date: '', sortOrder: 'newest' })
  const [authorDropdownOpen, setAuthorDropdownOpen] = useState(false)
  const [dateRange, setDateRange] = useState<DatePickerRangeValue>([null, null])

  const handleKeywordFilterChange = (keyword: string) => setFilters((f) => ({ ...f, keyword }))

  const handleDateRangeChange = (range: DatePickerRangeValue) => {
    setDateRange(range)
    const formattedDate = dayjs(range[0]).format(DEFAULT_INPUT_FORMAT)
    setFilters((prev) => ({ ...prev, date: range[0] ? formattedDate : '' }))
  }

  const handleClearAllFilters = () => {
    setFilters({ author: '', keyword: '', date: '', sortOrder: 'newest' })
    setDateRange([null, null])
  }

  const handleSelectAuthor = (authorId: string) => {
    setFilters((prev) => ({ ...prev, author: authorId }))
    setAuthorDropdownOpen(false)
  }

  const handleClearAuthor = (event: React.MouseEvent) => {
    event.stopPropagation()
    setFilters((prev) => ({ ...prev, author: '' }))
  }

  const handleSortOrderChange = (isNewestOrder: boolean) => {
    setFilters((f) => ({ ...f, sortOrder: isNewestOrder ? 'newest' : 'oldest' }))
  }

  return {
    filters,
    dateRange,
    authorDropdownOpen,
    setAuthorDropdownOpen,
    handleKeywordFilterChange,
    handleDateRangeChange,
    handleClearAllFilters,
    handleSelectAuthor,
    handleClearAuthor,
    handleSortOrderChange,
  }
}
