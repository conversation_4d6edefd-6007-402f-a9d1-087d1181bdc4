import styled, { keyframes } from 'styled-components'

const shimmer = keyframes`
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
`

export const Skeleton = styled.div<{ width: string | number, height: string | number }>`
  width: ${props => typeof props.width === 'number' ? `${props.width}px` : props.width};
  height: ${props => typeof props.height === 'number' ? `${props.height}px` : props.height};
  background-color: rgba(168,85,247,0.1);
  position: relative;
  overflow: hidden;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(168,85,247,0.1),
      transparent
    );
    animation: ${shimmer} 1.5s infinite;
  }
` 