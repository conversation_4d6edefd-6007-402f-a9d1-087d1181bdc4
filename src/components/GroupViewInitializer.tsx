import React, { useEffect } from 'react'
import { useGroupViewInitialization } from '@/hooks/useDefaultGroupView'

/**
 * Component that initializes group view functionality
 * Should be placed high in the component tree, ideally in the main app or route component
 */
const GroupViewInitializer: React.FC<{ children?: React.ReactNode }> = ({ children }) => {
  const { 
    defaultGroupView, 
    isLoading, 
    error, 
    hasDefaultView 
  } = useGroupViewInitialization()

  // Log initialization status for debugging
  useEffect(() => {
    if (!isLoading) {
      if (hasDefaultView) {
        console.log('Group view initialized with default view:', defaultGroupView?.name)
      } else {
        console.log('Group view initialized without default view')
      }
    }
  }, [isLoading, hasDefaultView, defaultGroupView])

  // Handle errors
  useEffect(() => {
    if (error) {
      console.error('Failed to load default group view:', error)
    }
  }, [error])

  return <>{children}</>
}

export default GroupViewInitializer
