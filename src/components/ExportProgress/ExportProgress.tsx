import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Download, ChevronLeft, ChevronRight, X } from 'lucide-react'
import { formatTime, formatSpeed } from '../../views/HunterX/Dashboard/utils/progress'
import { useExportProgress } from '../../views/HunterX/Dashboard/store/useExportProgress'

interface ExportProgressProps {
  onDiscard: () => void
}

const ExportProgress = ({ onDiscard }: ExportProgressProps) => {
  const [collapsed, setCollapsed] = useState(false)
  const { progress, resetToastState } = useExportProgress()

  const onCollapseToggle = () => setCollapsed((prev) => !prev)

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: -30, x: -30 }}
        animate={{ opacity: 1, y: 0, x: 0 }}
        exit={{ opacity: 0, y: -30, x: -30 }}
        transition={{ duration: 0.3 }}
        className="fixed z-[9999] top-4 h-[80px] right-4 shadow-lg rounded-2xl bg-white border border-gray-200 backdrop-blur-lg flex items-center gap-1 px-5 py-4"
        style={{ pointerEvents: 'auto', minWidth: collapsed ? 80 : 500 }}
      >
        <div className="flex items-center gap-1">
          <button
            className="w-7 h-7 flex items-center justify-center rounded-full bg-gray-100 hover:bg-red-100 transition-colors text-gray-400 hover:text-red-500 shadow-sm"
            aria-label="Discard export"
            onClick={() => {
              resetToastState()
              onDiscard()
            }}
          >
            <X size={18} className="text-gray-700" />
          </button>
          <button
            className="flex items-center justify-center w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors mr-2"
            onClick={onCollapseToggle}
            aria-label={collapsed ? 'Expand' : 'Collapse'}
          >
            {collapsed ? (
              <ChevronRight size={20} className="text-gray-600" />
            ) : (
              <ChevronLeft size={20} className="text-gray-600" />
            )}
          </button>
        </div>
        <div className="flex items-center justify-center w-10 h-10 rounded-full bg-blue-50">
          <Download size={22} className="text-blue-500 animate-pulse" />
        </div>
        {collapsed ? (
          <div className="flex flex-col items-center justify-center min-w-0">
            <span className="text-x text-gray-700 font-medium">
              {Math.round(progress.percentage)}%
            </span>
          </div>
        ) : (
          <div className="flex-1 min-w-0 flex flex-col gap-1">
            <div className="flex items-center justify-between text-xs text-gray-700 font-medium">
              <span className="text-gray-700">Exporting</span>
              <span className="text-gray-700">{Math.round(progress.percentage)}%</span>
            </div>
            <div className="h-2 bg-gray-100 rounded-full overflow-hidden mt-1 mb-1">
              <motion.div
                className="h-full bg-gradient-to-r from-blue-400 to-blue-500"
                initial={{ width: 0 }}
                animate={{ width: `${progress.percentage}%` }}
                transition={{ duration: 0.5, ease: 'easeOut' }}
              />
            </div>
            <div className="flex flex-wrap items-center justify-between text-xs text-gray-500 gap-x-2 gap-y-1">
              <span className="text-gray-700">
                {progress.current.toLocaleString()} / {progress.total.toLocaleString()} items
              </span>
              <span className="text-gray-700">{formatSpeed(progress.speed)}</span>
              <span className="text-gray-700">{formatTime(progress.estimatedTimeRemaining)} left</span>
            </div>
          </div>
        )}
      </motion.div>
    </AnimatePresence>
  )
}

export default ExportProgress
