import { useDisclosure, Popover, PopoverTrigger, PopoverContent } from '@nextui-org/react'

// Define the color palette
export const COLOR_PALETTE = [
  '#3b82f6', // blue
  '#6366f1', // indigo
  '#8b5cf6', // purple
  '#ED49C7', // bright pink (#ED49C7)
  '#ef4444', // red
]

export const normalizeRiskScore = (score: number) => Math.max(0, Math.min(90, score ?? 0))

export function getRiskScoreCSS(score: number) {
  let backgroundColor
  let ringColor
  const textColor = 'white'

  if (score >= 80) {
    backgroundColor = COLOR_PALETTE[4] // red for high risk
    ringColor = '#fecaca' // light red
  } else if (score >= 60) {
    backgroundColor = COLOR_PALETTE[3] // pink for medium-high risk
    ringColor = '#fbcfe8' // light pink
  } else if (score >= 40) {
    backgroundColor = COLOR_PALETTE[2] // purple for medium risk
    ringColor = '#e9d5ff' // light purple
  } else if (score >= 20) {
    backgroundColor = COLOR_PALETTE[1] // indigo for low-medium risk
    ringColor = '#c7d2fe' // light indigo
  } else {
    backgroundColor = COLOR_PALETTE[0] // blue for low risk
    ringColor = '#bfdbfe' // light blue
  }

  return {
    backgroundColor,
    ringColor,
    textColor,
  }
}

interface RiskScoreBadgeProps {
  score: number
  size?: number
  className?: string
  withTooltip?: boolean
}

// Extracted badge rendering function to avoid duplication
const renderBadge = (
  normalizedScore: number,
  size: number,
  backgroundColor: string,
  ringColor: string,
  textColor: string,
  progressSize: number,
  fontSize: number,
  ringSize: number,
) => (
  <>
    {/* Outer ring/halo */}
    <div
      className="absolute rounded-full opacity-20"
      style={{
        width: `${ringSize}px`,
        height: `${ringSize}px`,
        backgroundColor: ringColor,
      }}
    ></div>

    {/* Main circle */}
    <div
      className="relative flex items-center justify-center rounded-full shadow-lg transition-transform duration-200 hover:scale-105"
      style={{
        width: `${size}px`,
        height: `${size}px`,
        backgroundColor,
      }}
    >
      {/* Progress track */}
      <div
        className="absolute rounded-full border-2 border-white border-opacity-30"
        style={{
          width: `${size - progressSize}px`,
          height: `${size - progressSize}px`,
          zIndex: 1,
        }}
      ></div>

      {/* Progress indicator - improved conic gradient */}
      <div
        className="absolute inset-0 rounded-full overflow-hidden"
        style={{
          background: `conic-gradient(${backgroundColor}99 0% ${normalizedScore}%, transparent ${normalizedScore}% 100%)`,
          zIndex: 1,
        }}
      ></div>

      {/* Progress indicator dot - fixed positioning */}
      <div
        className="absolute rounded-full bg-white shadow-sm"
        style={{
          width: `${progressSize}px`,
          height: `${progressSize}px`,
          top: `${size / 2 - progressSize / 2}px`,
          left: `${size / 2 - progressSize / 2}px`,
          transform: `rotate(${normalizedScore * 3.6}deg) translateX(${size / 2 - progressSize / 2}px)`,
          zIndex: 2,
        }}
      ></div>

      {/* Score text - now with highest z-index and proper centering */}
      <span
        className="font-medium absolute flex items-center justify-center w-full h-full"
        style={{
          fontSize: `${fontSize}px`,
          color: textColor,
          zIndex: 3,
        }}
      >
        {Math.floor(normalizedScore)}
      </span>
    </div>
  </>
)

export default function RiskScoreBadge({ score, size = 40, className = '', withTooltip }: RiskScoreBadgeProps) {
  const { isOpen, onOpen, onClose } = useDisclosure()
  const normalizedScore = normalizeRiskScore(score)

  // Calculate colors and styles based on score
  const { backgroundColor, ringColor, textColor } = getRiskScoreCSS(normalizedScore)

  // Calculate dimensions
  const fontSize = size * 0.45
  const ringSize = size * 1.15
  const progressSize = size * 0.06

  const badgeContent = renderBadge(
    normalizedScore,
    size,
    backgroundColor,
    ringColor,
    textColor,
    progressSize,
    fontSize,
    ringSize,
  )

  return (
    <div
      className={`relative inline-flex items-center cursor-default justify-center group ${className}`}
      aria-label={`Risk Score: ${normalizedScore}`}
    >
      {withTooltip ? (
        <Popover
          isOpen={isOpen}
          onOpenChange={(open: boolean) => {
            if (open) {
              onOpen()
            } else {
              onClose()
            }
          }}
          placement="top"
          classNames={{
            content: 'py-1 px-2 text-xs bg-white  text-black !p-2 rounded-primary shadow-primary-shadow',
          }}
        >
          <PopoverTrigger>
            <div
              className={`relative inline-flex items-center justify-center group ${className}`}
              onMouseEnter={onOpen}
              onMouseLeave={onClose}
            >
              {badgeContent}
            </div>
          </PopoverTrigger>
          <PopoverContent>
            <div>Risk Score</div>
          </PopoverContent>
        </Popover>
      ) : (
        badgeContent
      )}
    </div>
  )
}
