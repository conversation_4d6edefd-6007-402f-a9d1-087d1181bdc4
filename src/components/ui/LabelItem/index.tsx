import { Label } from '@/firestoreQueries/ndr/labels/hooks/useLabels'
import { colorThemes, iconOptions } from '@/views/NDR/Inventory/Components/Labels/LabelFormModal'
import { Archive } from 'lucide-react'
import React, { ReactNode } from 'react'
import { cn } from '@/lib/utils'

export const getLabelColor = (label: Label) => {
  switch (label.type) {
    case 'ai':
      return 'bg-blue-100 text-blue-800 border-blue-300 dark:bg-blue-900 dark:text-blue-200 dark:border-blue-700'
    case 'static':
      return 'bg-purple-100 text-purple-800 border-purple-300 dark:bg-purple-900 dark:text-purple-200 dark:border-purple-700'
    case 'user':
      return colorThemes[label.color as keyof typeof colorThemes]
    default:
      return 'bg-gray-100 text-gray-800 border-gray-300 dark:bg-gray-800 dark:text-gray-200 dark:border-gray-600'
  }
}

interface LabelItemProps {
  label: Label
  id?: string
  maxWidth?: number
  children?: ReactNode
}

const LabelItem = ({ label, maxWidth, children }: LabelItemProps) => {
  const isUserLabel = label.type === 'user'

  if ('values' in label) {
    return (
      <div className="flex gap-1 flex-wrap max-w-full">
        {label.values.map((value: string, index: number) => (
          <div
            key={index}
            className={cn(
              'px-2.5 py-1 rounded-full text-sm font-medium flex items-center gap-1.5 truncate',
              getLabelColor(label),
            )}
            style={{ width: 'fit-content', ...(maxWidth && { maxWidth }) }}
          >
            {children && <div className="flex-none">{children}</div>}
            <div className="flex-none">
              {isUserLabel ? iconOptions.find((e) => e.name === label.icon)?.icon || <Archive size={18} /> : null}
            </div>
            <div className="font-semibold text-[12px] border-inherit pr-1.5 border-r">{label.key}</div>
            <span className="opacity-80 truncate">{value}</span>
          </div>
        ))}
      </div>
    )
  }

  return (
    <div
      key={label.id}
      className={`px-2 h-7 py-1 rounded-full text-xs font-medium flex items-center gap-1 border ${getLabelColor(label)}`}
    >
      {children}
      {isUserLabel ? iconOptions.find((e) => e.name === label.icon)?.icon || <Archive size={18} /> : null}
      <span
        className="truncate inline-block whitespace-nowrap"
        style={{ ...(maxWidth && { maxWidth }) }}
        title={label.name}
      >
        {label.name}
      </span>
    </div>
  )
}

export default LabelItem
