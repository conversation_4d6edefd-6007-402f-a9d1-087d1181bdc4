import { FC, ChangeEvent } from 'react';
import { Checkbox } from "@nextui-org/react";
import { useField } from 'formik';

interface CheckboxProps {
    label: string
}

export const CheckBox: FC<CheckboxProps> = ({ label }) => {
    const [field, _, helpers] = useField({
        name: "option",
        type: "checkbox"
    });

    return (
        <Checkbox
            isSelected={field.value}
            onChange={(e: ChangeEvent<HTMLInputElement>) => helpers.setValue(e.target.checked)}
        >
            {label && label}
        </Checkbox>
    );
}