import { useInView } from 'react-intersection-observer'
import { useEffect } from 'react'

interface InfiniteScrollPaginationProps {
  hasNextPage: boolean
  isFetchingNextPage: boolean
  fetchNextPage: () => void
  loadingComponent?: React.ReactNode
  loadMoreText?: string
  className?: string
}

export default function InfiniteScrollPagination({
  hasNextPage,
  isFetchingNextPage,
  fetchNextPage,
  loadingComponent,
  loadMoreText = 'Scroll to load more',
  className = '',
}: InfiniteScrollPaginationProps) {
  const { ref: loadMoreRef, inView } = useInView({
    threshold: 0.1,
  })

  useEffect(() => {
    if (inView && hasNextPage && !isFetchingNextPage) {
      fetchNextPage()
    }
  }, [inView, hasNextPage, isFetchingNextPage, fetchNextPage])

  if (!hasNextPage) {
    return null
  }

  return (
    <div ref={loadMoreRef} className={`py-4 text-center ${className}`}>
      {isFetchingNextPage ? (
        loadingComponent || (
          <div className="flex justify-center">
            <div className="w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
          </div>
        )
      ) : (
        <span className="text-sm text-gray-400">{loadMoreText}</span>
      )}
    </div>
  )
} 