import cn from 'classnames'

interface BadgeProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'secondary' | 'outline' | 'black'
}

export default function Badge({
  children,
  variant = 'default',
  className,
  ...props
}: BadgeProps) {
  return (
    <div
      className={cn(
        'inline-flex items-center rounded-full px-2 py-0.5 text-xs font-semibold',
        {
          'bg-gray-100 text-gray-800': variant === 'default',
          'bg-gray-200 text-gray-700': variant === 'secondary',
          'bg-black text-white': variant === 'black',
          'border border-gray-200 text-gray-700': variant === 'outline',
        },
        className
      )}
      {...props}
    >
      {children}
    </div>
  )
}
