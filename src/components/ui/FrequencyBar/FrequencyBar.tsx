import useCountAnimation from "@/hooks/useCountAnimation"
import { useEffect, useRef } from "react"

interface FrequencyBarProps {
  label: string
  frequency: number
  maxFrequency: number
  onClick: () => void
  color: string
  disabled?: boolean
}

const FrequencyBar = ({ label, frequency, maxFrequency, onClick, color, disabled }: FrequencyBarProps) => {
  const width = frequency === maxFrequency ? '100%' : `${(frequency / maxFrequency) * 100}%`
  const { count } = useCountAnimation(frequency, 500)
  const barRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (barRef.current) {
      barRef.current.style.width = '0%'
      barRef.current.offsetHeight
      barRef.current.style.width = width
    }
  }, [width])

  return (
    <div
      className={`w-full mb-2 ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer group'}`}
      onClick={disabled ? undefined : onClick}
    >
      <div className="flex items-center w-full">
        <div className="flex-grow">
          <div className="flex justify-between mb-1">
            <span className="text-sm text-gray-600 truncate max-w-[200px]">{label}</span>
            <span className="text-xs text-gray-400">{count}</span>
          </div>
          <div className="w-full bg-gray-100 rounded-full h-2.5 overflow-hidden">
            <div
              ref={barRef}
              className={`h-2.5 rounded-full transition-[width] duration-700 ease-out ${disabled ? '' : 'group-hover:opacity-80'} ${color}`}
              style={{ width: '0%' }}
            />
          </div>
        </div>
      </div>
    </div>
  )
}

export default FrequencyBar
