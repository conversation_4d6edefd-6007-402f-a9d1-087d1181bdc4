import React, { useEffect, useRef } from 'react'
import { createPortal } from 'react-dom'
import { AnimatePresence, motion } from 'framer-motion'
import { useClickOutsideMultiple } from '@/stories/Firewall'

const Portal = ({ children, dataAttr }: any) => {
  const portalRef = useRef(document.createElement('div'))

  useEffect(() => {
    const node = portalRef.current
    node.setAttribute('data-portal', dataAttr ?? '')
    document.body.appendChild(node)

    return () => {
      document.body.removeChild(node)
    }
  }, [])

  return createPortal(children, portalRef.current)
}

const ModalDrawer = ({ isOpen, onClose, children }: any) => {
  const containerRef = useRef();
  
  useClickOutsideMultiple([containerRef], () => onClose());
  
  return (
    <Portal dataAttr="drawer-firewall">
      <AnimatePresence>
        {isOpen && (
          <motion.div
            className="fixed inset-0 z-[999] flex justify-center items-end"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <motion.div
              className="fixed inset-0 bg-black"
              initial={{ opacity: 0 }}
              animate={{ opacity: 0.5 }}
              exit={{ opacity: 0 }}
            />
            <motion.div
              className="flex items-end justify-center flex-grow"
              initial={{ y: '100%' }}
              animate={{ y: 0 }}
              exit={{ y: '100%' }}
              transition={{
                type: 'spring',
                damping: 30,
                stiffness: 300,
              }}
            >
              {/* TODO: BETTER TO IMPLEMENT ClickOUTTSIDE component*/}
              {children({ref: containerRef})}
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </Portal>
  )
}

export default ModalDrawer
