import { AvailableIntegrations } from '@/firestoreQueries/integrations/IntegrationTypes'
import { integrationIconMap } from '@/views/Integrations/StaticAvailableIntegrations'
import { cn } from '@nextui-org/react'
import { motion } from 'framer-motion'

interface Props {
  integrationType?: AvailableIntegrations
  title: string
  onClick?: () => void
  isDisabled?: boolean
}

const SpecialButton = ({ title, integrationType, onClick, isDisabled }: Props) => {
  return (
    <motion.button
      disabled={isDisabled}
      style={{
        cursor: isDisabled ? 'not-allowed' : 'pointer',
        opacity: isDisabled ? 0.5 : 1,
      }}
      onClick={onClick}
      whileTap={{ scale: isDisabled ? 1 : 0.95 }}
      className="flex lex-row items-center h-10 p-0 justify-between shadow-[0_4px_14px_0_rgb(0,118,255,39%)] overflow-hidden hover:shadow-[0_6px_20px_rgba(0,118,255,23%)] hover:bg-[rgba(0,118,255,0.9] bg-[#0070f3] rounded-md text-white font-light transition duration-200 ease-linear"
    >
      <div
        className={cn('px-8 py-2', {
          'text-center w-full': !integrationType,
        })}
      >
        {title}
      </div>
      {integrationType && (
        <div className="h-10 w-10 bg-white flex items-center justify-center">
          <img src={integrationIconMap[integrationType]} alt={title} className="object-contain rounded-full h-2/4" />
        </div>
      )}
    </motion.button>
  )
}

export default SpecialButton
