import { useTypedFlags } from '@/customHooks/useTypedFlags'
import useDashboardStore from '@/zustandStores/useDashboardsStore'
import React, { ReactNode, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'

interface ErrorBoundaryProps {
  children: ReactNode
  onError: () => void
}

interface ErrorBoundaryState {
  hasError: boolean
}

class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(): ErrorBoundaryState {
    return { hasError: true }
  }

  componentDidCatch(): void {
    this.props.onError()
  }

  resetError = () => {
    this.setState({ hasError: false })
  }

  render(): React.ReactNode {
    if (this.state.hasError) {
      // Render a button to go back home which also resets the error state
      return <ErrorBoundaryComponent onReset={this.resetError} />
    }

    return this.props.children
  }
}

interface WithNavigationErrorBoundaryProps {
  children: ReactNode
}

const WithNavigationErrorBoundary: React.FC<WithNavigationErrorBoundaryProps> = ({ children }) => {
  const navigate = useNavigate()
  const basePath = useDashboardStore((s) => s.basePath)
  const handleError = () => {
    navigate(basePath)
  }

  return <ErrorBoundary onError={handleError}>{children}</ErrorBoundary>
}

export default WithNavigationErrorBoundary

interface TestProps {
  onReset: () => void
}

const ErrorBoundaryComponent: React.FC<TestProps> = ({ onReset }) => {
  const navigate = useNavigate()
  const { isMock, showAccessModule: showAccessDashboard } = useTypedFlags()
  useEffect(() => {
    if (isMock) {
      navigate('/access-dashboard')
      onReset()
      return
    }
    if (showAccessDashboard) {
      navigate('/portals')
      return
    }
    navigate('ndr-dashboard')
    onReset()
    return

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  return null
}
