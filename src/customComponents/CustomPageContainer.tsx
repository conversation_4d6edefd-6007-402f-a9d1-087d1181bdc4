import { ScrollShadow } from '@nextui-org/react'
import { HTMLAttributes, ReactNode } from 'react'
import { twMerge } from 'tailwind-merge'

interface Pro<PERSON> extends HTMLAttributes<HTMLDivElement> {
    children?: ReactNode
    shadow?: boolean
}
const CustomPageContainer = ({ shadow = false, children, className, ...rest }: Props) => {
    if (shadow) {
        return (
            <ScrollShadow className={twMerge('h-[calc(100vh-152px)]', className)} {...rest}>
                {children}
            </ScrollShadow>
        )
    }

    return (
        <div className={twMerge('h-[calc(100vh-152px)]', className)} {...rest}>
            {children}
        </div>
    )
}

export default CustomPageContainer
