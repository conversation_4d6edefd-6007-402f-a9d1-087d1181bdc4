import { AvailableIntegrations } from '@/firestoreQueries/integrations/IntegrationTypes'
import { awsLogoMap } from '@/views/Access/Resources/new/newAWSIntegrationResource/AWSAvailableResourcesTable'
import { integrationIconMap } from '@/views/Integrations/StaticAvailableIntegrations'
import { Avatar } from '@nextui-org/avatar'
import { Emoji } from 'emoji-picker-react'
import { memo } from 'react'

interface Props {
    emoji?: string
    integrationImage?: AvailableIntegrations
    awsResourceType?: 'EC2' | 'RDS' | ''
    userImage?: string
    name: string
    subtitle?: string
    description?: string
    children?: React.ReactNode
}
const ProfileHeader = ({
    name,
    emoji,
    description,
    integrationImage,
    userImage,
    subtitle,
    children,
    awsResourceType,
}: Props) => {
    return (
        <section className="flex flex-col gap-e">
            <div className="items-center flex flex-row gap-4">
                {emoji && (
                    <div className="rounded-full bg-white flex justify-center items-center w-10 h-10">
                        <Emoji unified={emoji} size={28} />
                    </div>
                )}
                {integrationImage && !emoji && (
                    <img
                        src={integrationIconMap[integrationImage]}
                        alt="Resource Avatar"
                        className="w-10 h-10 bg-white rounded-full object-contain border-1 border-gray-300"
                    />
                )}
                {awsResourceType && (
                    <img
                        src={awsLogoMap[awsResourceType]}
                        alt="Resource Avatar"
                        className="w-10 h-10 bg-white rounded-full object-contain"
                    />
                )}
                {userImage && <Avatar src={userImage} className="h-10 w-10" alt="user" />}
                <div>
                    <h2>{name}</h2>
                    {subtitle && <p>{subtitle}</p>}
                </div>
            </div>
            {description && <h2 className="ml-8 text-lg text-slate-500 opacity-80 font-medium">{description}</h2>}
            <div className="">{children}</div>
        </section>
    )
}

export default memo(ProfileHeader)
