import React from 'react'
import { Button, ButtonGroup } from '@nextui-org/react'
import { Icon } from '@iconify/react'

interface ToggleButtonGroupProps {
    isTable: boolean
    // setIsTable: React.Dispatch<React.SetStateAction<boolean>>
    changeTableView: () => void
    className?: string
    size?: 'sm' | 'md' | 'lg'
    iconSize?: number
}

const ToggleButtonGroup: React.FC<ToggleButtonGroupProps> = ({
    isTable,
    changeTableView,
    className = '',
    size = 'sm',
    iconSize = 15,
}) => {
    return (
        <ButtonGroup className={className}>
            <Button
                onClick={changeTableView}
                variant={!isTable ? 'flat' : 'ghost'}
                isDisabled={isTable}
                isIconOnly
                size={size}
            >
                <Icon icon={'radix-icons:rows'} fontSize={iconSize} />
            </Button>
            <Button
                onClick={changeTableView}
                variant={isTable ? 'flat' : 'ghost'}
                isDisabled={!isTable}
                isIconOnly
                size={size}
            >
                <Icon icon={'gridicons:grid'} fontSize={iconSize} />
            </Button>
        </ButtonGroup>
    )
}

export default ToggleButtonGroup
