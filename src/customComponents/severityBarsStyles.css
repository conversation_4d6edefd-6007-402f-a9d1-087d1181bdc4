.signal-bars {
    /* padding: 5em; */
    font-size: 0.5em;
}
.bar {
    height: 1em;
    width: 0.8em;
    margin: 0.1em;
    display: inline-block;
    border-radius: 3px;
}
.bar:nth-of-type(1) {
    height: 1em;
}
.bar:nth-of-type(2) {
    height: 1.5em;
}
.bar:nth-of-type(3) {
    height: 2em;
}
.bar:nth-of-type(4) {
    height: 2.5em;
}
.bar:nth-of-type(5) {
    height: 3em;
}
.signal-bars > .bar {
    @apply bg-black/30 dark:bg-white/30;
}

@media (prefers-color-scheme: dark) {
    .signal-bars > .bar {
    }
}

.signal-bars.low > .bar:nth-of-type(1) {
    background: #67e23f;
}

.signal-bars.medium > .bar:nth-of-type(1) {
    background: #e2923f;
}
.signal-bars.medium > .bar:nth-of-type(2) {
    background: #e2923f;
}

.signal-bars.high > .bar:nth-of-type(1) {
    background: #e2593f;
}
.signal-bars.high > .bar:nth-of-type(2) {
    background: #e2593f;
}
.signal-bars.high > .bar:nth-of-type(3) {
    background: #e2593f;
}

.signal-bars.critical > .bar:nth-of-type(1) {
    background: #e2593f;
}
.signal-bars.critical > .bar:nth-of-type(2) {
    background: #e23f3f;
}
.signal-bars.critical > .bar:nth-of-type(3) {
    background: #e23f3f;
}
.signal-bars.critical > .bar:nth-of-type(4) {
    background: #e23f3f;
}

.signal-bars.info > .bar:nth-of-type(1) {
    background: #4f5fe2;
}
.signal-bars.info > .bar:nth-of-type(2) {
    background: #4f5fe2;
}
.signal-bars.info > .bar:nth-of-type(3) {
    background: #4f5fe2;
}
.signal-bars.info > .bar:nth-of-type(4) {
    background: #4f5fe2;
}
