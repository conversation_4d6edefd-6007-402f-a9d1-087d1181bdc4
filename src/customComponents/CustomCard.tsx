import { Card, CardProps } from '@nextui-org/react'
import { twMerge } from 'tailwind-merge'

interface Props extends CardProps {
  children?: React.ReactNode
}

const CustomCard = ({ children, style, className }: Props) => {
  return (
    <Card style={style} shadow="none" className={twMerge('!shadow-primary-shadow rounded-primary bg-white', className)}>
      {children}
    </Card>
  )
}

export default CustomCard
