import { Tooltip } from '@nextui-org/react'
import './severityBarsStyles.css'
// eslint-disable-next-line import/named
import { AlertCircle, AlertTriangle, CheckCircle, XCircle } from 'lucide-react'
import { cn } from '@/lib/utils'
import React from 'react'

export type Severity = 'info' | 'low' | 'medium' | 'high' | 'critical'

export const severityConfig = {
  info: {
    color: 'text-gray-300',
    bgColor: 'bg-gray-300',
    borderColor: 'border-gray-300',
    hex: '#D1D5DB',
    glow: '',
    icon: AlertCircle,
    bars: 0,
  },
  low: {
    color: 'text-emerald-300',
    bgColor: 'bg-emerald-300',
    borderColor: 'border-emerald-300',
    hex: '#6EE7B7',
    glow: '',
    icon: CheckCircle,
    bars: 1,
  },
  medium: {
    color: 'text-orange-400',
    bgColor: 'bg-orange-400',
    borderColor: 'border-orange-400',
    hex: '#FB923C',
    glow: '',
    icon: AlertTriangle,
    bars: 2,
  },
  high: {
    color: 'text-red-500',
    bgColor: 'bg-red-500',
    borderColor: 'border-red-500',
    hex: '#EF4444',
    glow: '',
    icon: AlertTriangle,
    bars: 3,
  },
  critical: {
    color: 'text-rose-500',
    bgColor: 'bg-rose-500',
    borderColor: 'border-rose-500',
    hex:'#F43F5E',
    icon: XCircle,
    bars: 4,
  },
  default: {
    color: 'text-gray-400',
    hex:'#e2e2e2',
    bgColor: 'bg-gray-400',
    glow: '',
    icon: XCircle,
    bars: 4,
  },
}

export const severitySecondaryColorClasses = {
  info: `${severityConfig.info.bgColor}/50`,
  low: `${severityConfig.low.bgColor}/50`,
  medium: `${severityConfig.medium.bgColor}/50`,
  high: `${severityConfig.high.bgColor}/50`,
  critical: `${severityConfig.critical.bgColor}/50`,
}
export const severityColorClasses = {
  info: severityConfig.info.bgColor,
  low: severityConfig.low.bgColor,
  medium: severityConfig.medium.bgColor,
  high: severityConfig.high.bgColor,
  critical: severityConfig.critical.bgColor,
}

const barsHeight = [6, 10, 14, 18]

const SeverityIndicator = ({ severity, variant = 'default' }: { severity: Severity; variant?: 'compact' | 'default' }) => {
  const { color, bgColor, glow, icon: Icon, bars } = severityConfig?.[severity as Severity ?? 'default'] ?? {}

  return (
    <div className="relative">
      {severity === 'info' ? (
        <div
          className={cn('flex items-center justify-center', variant === 'default' ? 'w-[42px] h-6' : 'w-7 h-4', color)}
        >
          <Icon size={variant === 'default' ? 24 : 20} className={cn('transition-all duration-300', color)} />
        </div>
      ) : (
        <div
          className={cn('flex items-end justify-center gap-[2px]', variant === 'default' ? 'w-[42px] h-6' : 'w-7 h-4')}
        >
          {barsHeight.map((height, index) => (
            <div
              key={index}
              className={cn(
                'transition-all duration-300',
                index < bars ? bgColor : 'bg-gray-200 dark:bg-gray-700',
                index < bars && glow,
                'rounded-md',
              )}
              style={{
                height: `${variant === 'default' ? 1.4 * height : height}px`,
                opacity: index < bars ? 0.9 : 0.7,
                width: variant === 'default' ? '8px' : '6px',
              }}
            />
          ))}
        </div>
      )}
    </div>
  )
}


function SeverityBars({ variant = 'default', severity }: { variant?: 'default' | 'compact'; severity: Severity }) {
  return (
    <Tooltip
      content={
        <div className="rounded-md border border-gray-100 shadow-sm overflow-hidden px-2 py-1 first-letter:uppercase">
          {severity}
        </div>
      }
      placement="top"
    >
      <div>
        <SeverityIndicator variant={variant} severity={severity} />
      </div>
    </Tooltip>
  )
}

export default SeverityBars
