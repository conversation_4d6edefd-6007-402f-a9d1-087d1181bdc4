const metaButtonText = {
  mac: '⌘',
  windows: 'Ctrl',
  linux: 'Ctrl',
}

interface Props {
  keys: string[]
  className?: string
}

const KeyboardShortcutsIcons = ({ keys, className }: Props) => {
  const isMac = navigator.userAgent.includes('Mac')
  return (
    <div className="flex items-center gap-1">
      {keys.map((key) => {
        if (key === 'meta') {
          if (isMac) {
            return (
              <p
                key={key}
                className={`shadow-md border-1 rounded-secondary h-6 w-6 flex items-center justify-center ${className}`}
              >
                {metaButtonText.mac}
              </p>
            )
          } else {
            return (
              <p
                key={key}
                className={`shadow-md border-1 rounded-secondary h-6 w-10 flex items-center justify-center  ${className}`}
              >
                {metaButtonText.windows}
              </p>
            )
          }
        }
        return (
          <p
            key={key}
            className={`shadow-md border-1 rounded-secondary h-6 w-6 flex items-center justify-center ${className}`}
          >
            {key}
          </p>
        )
      })}
    </div>
  )
}

export default KeyboardShortcutsIcons
