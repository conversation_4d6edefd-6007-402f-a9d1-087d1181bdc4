import useDarkMode from '@/utils/hooks/useDarkmode'
import { cn } from '@nextui-org/react'
import EmojiPicker, { Emoji, Theme } from 'emoji-picker-react'
import { AnimatePresence, motion } from 'framer-motion'
import React, { Dispatch, SetStateAction } from 'react'

interface Props {
  showEmojiPicker: boolean
  setShowEmojiPicker: Dispatch<SetStateAction<boolean>>
  selectedEmoji: string
  setSelectedEmoji: Dispatch<SetStateAction<string>>
  isDisabled?: boolean
}

const EmojiSelector = ({
  setSelectedEmoji,
  setShowEmojiPicker,
  showEmojiPicker,
  selectedEmoji,
  isDisabled = false,
}: Props) => {
  const [isDark] = useDarkMode()
  return (
    <div className="flex flex-1 justify-center">
      <div className="relative">
        <AnimatePresence>
          <div
            className={cn('w-[77px] h-[77px] bg-[#f3f4f6] flex justify-center items-center rounded-full', {
              'cursor-pointer': !isDisabled,
            })}
            // style={{
            //   cursor: 'pointer',
            //   background: '#f3f4f6',
            //   width: '77px',
            //   height: '77px',
            //   padding: '7px',
            //   borderRadius: '50px',
            //   display: 'flex',
            //   justifyContent: 'center',
            //   alignItems: 'center',
            // }}
            onClick={() => {
              if (isDisabled) return
              setShowEmojiPicker((p) => !p)
            }}
          >
            <Emoji size={44} unified={selectedEmoji} />
          </div>
          {showEmojiPicker && (
            <motion.div
              key="emoji-picker-modal"
              className="absolute z-10 top-0 left-20 origin-top-left"
              animate={{ scale: 1 }}
              exit={{ scale: 0 }}
              initial={{ scale: 0 }}
            >
              <EmojiPicker
                previewConfig={{
                  showPreview: false,
                }}
                lazyLoadEmojis={false}
                onEmojiClick={(e) => {
                  setShowEmojiPicker(false)
                  setSelectedEmoji(e.unified)
                }}
                theme={isDark ? ('dark' as Theme) : undefined}
              />
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  )
}

export default EmojiSelector
