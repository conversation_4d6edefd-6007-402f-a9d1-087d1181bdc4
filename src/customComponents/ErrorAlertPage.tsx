import { Icon } from '@iconify/react'

interface ErrorAlertPageProps {
  fullHeight?: boolean;
}

const ErrorAlertPage = ({ fullHeight = false }: ErrorAlertPageProps) => {
  return (
    <div className={`w-full flex items-center justify-center bg-white/80 p-4 overflow-hidden ${fullHeight ? 'h-full' : ''}`}>
      {/* AI Animated background gradients */}
      
      {/* Main content */}
      <div className="relative text-center">
        {/* Magic orb container with enhanced glow */}
        <div className="relative w-20 h-20 mx-auto">
          {/* White glow effect */}
          <div className="absolute inset-0 bg-white/40 rounded-full blur-xl" />
          <div className="absolute -inset-1 bg-white/20 rounded-full blur-lg" />
          
          {/* Orb container */}
          <div className="relative h-full w-full p-2 group">
            {/* Background circle with glass effect */}
            <div className="absolute inset-0 bg-gradient-to-br from-white/80 via-white/50 rounded-full backdrop-blur-sm shadow-[0_0_15px_rgba(255,255,255,0.5)]" />
            
            {/* Magic orb layers */}
            <div className="relative h-full w-full flex items-center justify-center">
              {/* Base orb */}
              <Icon 
                icon="ph:circle-fill" 
                className="absolute w-3/4 h-3/4 text-white transition-all duration-300 drop-shadow-[0_0_10px_rgba(255,255,255,0.7)]"
              />
              {/* Energy rings */}
              <Icon 
                icon="carbon:circle-dash" 
                className="absolute w-full h-full text-blue-100 animate-spin-slow transition-all duration-300 group-hover:text-blue-200 drop-shadow-[0_0_8px_rgba(255,255,255,0.6)]"
              />
              {/* Core energy */}
              <Icon 
                icon="clarity:circle-line" 
                className="absolute w-2/3 h-2/3 text-blue-200 animate-pulse-slow transition-all duration-300 group-hover:text-blue-300 drop-shadow-[0_0_12px_rgba(255,255,255,0.8)]"
              />
              {/* Particles */}
              <Icon 
                icon="ph:sparkle-fill" 
                className="absolute w-1/3 h-1/3 text-blue-300 animate-float transition-all duration-300 group-hover:scale-110 group-hover:text-blue-400 drop-shadow-[0_0_15px_rgba(255,255,255,0.9)]"
              />
            </div>
          </div>
        </div>
        
        {/* Text content with enhanced glow */}
        <div className="relative">
          <div className="absolute inset-0 blur-2xl bg-gradient-to-r from-blue-500/3 via-cyan-500/3 to-purple-500/3 rounded-lg" />
          <div className="relative">
            <p className="text-xl text-slate-500 font-medium drop-shadow-[0_0_2px_rgba(255,255,255,0.3)]">
              Coming soon
            </p>
            <p className="text-sm text-slate-500 font-medium drop-shadow-[0_0_2px_rgba(255,255,255,0.3)]">
              Feature will be unlocked shortly
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

// Add these animations to your global CSS or tailwind config
const style = document.createElement('style')
style.textContent = `
  @keyframes float {
    0% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-2px) rotate(10deg); }
    100% { transform: translateY(0px) rotate(0deg); }
  }
  @keyframes float-slow {
    0% { transform: translate(0px, 0px); }
    50% { transform: translate(10px, -10px); }
    100% { transform: translate(0px, 0px); }
  }
  .animate-float {
    animation: float 2s ease-in-out infinite;
  }
  .animate-float-slow {
    animation: float-slow 5s ease-in-out infinite;
  }
  .animate-spin-slow {
    animation: spin 8s linear infinite;
  }
  .animate-spin-reverse-slow {
    animation: spin 8s linear infinite reverse;
  }
  // .animate-pulse-slow {
  //   animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  // }
  // .animate-pulse-fast {
  //   animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  // }
  .bg-gradient-conic {
    background-image: conic-gradient(var(--tw-gradient-stops));
  }
  .drop-shadow-glow {
    filter: drop-shadow(0 0 4px rgba(255, 255, 255, 0.3));
  }
  .animation-delay-2000 {
    animation-delay: 2s;
  }
  .animation-delay-4000 {
    animation-delay: 4s;
  }
`
document.head.appendChild(style)

export default ErrorAlertPage
