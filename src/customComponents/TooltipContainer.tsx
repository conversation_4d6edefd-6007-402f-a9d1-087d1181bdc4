import React, { ReactNode } from 'react'
import { twMerge } from 'tailwind-merge'

interface Props extends React.HTMLAttributes<HTMLDivElement> {
  children?: ReactNode
}

const TooltipContainer = ({ children, className, ...rest }: Props) => {
  return (
    <div
      className={twMerge(
        'dark:bg-black bg-white dark:text-white text-black !p-4 rounded-primary shadow-primary-shadow',
        className,
      )}
      {...rest}
    >
      {children}
    </div>
  )
}

export default TooltipContainer
