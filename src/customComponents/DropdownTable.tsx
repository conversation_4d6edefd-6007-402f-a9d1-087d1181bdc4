/* eslint-disable @typescript-eslint/ban-ts-comment */
import { Accordion, AccordionItem, Checkbox, cn } from '@nextui-org/react'
import { ChevronDown } from 'lucide-react'
import React, { ReactNode } from 'react'

export function useSelectableTable() {
  const [selectedKeys, setSelectedKeys] = React.useState<Map<string, Set<string>>>(new Map())

  function selectAll(selected: boolean, newMap: Map<string, Set<string>>) {
    if (selected) {
      setSelectedKeys(newMap)
    } else {
      setSelectedKeys(new Map())
    }
  }

  return {
    selectedKeys,
    setSelectedKeys,
    selectAll,
  }
}

export function DropdownTable({ children }: { children: ReactNode }) {
  return <div>{children}</div>
}

export function DropdownRow({
  children,
  content,
  index,
  isSelected,
  onSelect,
}: {
  children: ReactNode
  content: ReactNode
  index: number
  onSelect: () => void
  isSelected: boolean
}) {
  const [openKeys, setOpenKeys] = React.useState<Set<string>>(new Set())
  const isOpen = openKeys.has(String(index))
  function toggle() {
    if (isOpen) {
      setOpenKeys(new Set())
    } else {
      setOpenKeys(new Set([String(index)]))
    }
  }

  return (
    <Accordion hideIndicator className="!px-0" selectedKeys={openKeys}>
      <AccordionItem
        key={'0'}
        classNames={{
          startContent: 'w-full',
          content: 'px-4',
          trigger: 'py-2',
        }}
        startContent={
          <DropdownTableRow>
            <td>
              <Checkbox onValueChange={onSelect} isSelected={isSelected} />
            </td>
            <td className="w-4">
              <button className="h-full flex items-center justify-center" onClick={toggle}>
                <ChevronDown
                  size={20}
                  className={cn('transition-all opacity-50', {
                    '-rotate-90': !isOpen,
                  })}
                />
              </button>
            </td>
            {children}
          </DropdownTableRow>
        }
      >
        {content}
      </AccordionItem>
    </Accordion>
  )
}

function DropdownTableRow({ children }: { children: ReactNode }) {
  return <tr className="w-full flex flex-row items-center gap-2 px-1">{children}</tr>
}

export function DropdownTableCell({ children }: { children: React.ReactNode }) {
  return <td className="flex-1 flex items-start">{children}</td>
}

export function DropdownTableHeader({
  children,
  isAllSelected,
  toggleSelectAll,
}: {
  children: ReactNode
  isAllSelected: boolean
  toggleSelectAll: (selected: boolean) => void
}) {
  return (
    <tr className="w-full flex flex-row items-center gap-2 bg-[#F9FAFB] py-2 px-1 rounded-primary">
      <td className="w-12">
        <Checkbox onValueChange={toggleSelectAll} isSelected={isAllSelected} classNames={{ base: 'pr-2' }} />
      </td>
      {children}
    </tr>
  )
}
