import { Avatar, AvatarProps, Tag, Timeline } from '@/components/ui'
import { getMemberRole, getPermittedIds } from '@/customUtils/documentPermissions'
import {
  ConnectToPortalActivityDetailsType,
  DocumentActivityDetailsType,
  DocumentActivityOperationType,
} from '@/firestoreQueries/activities/activitiesTypes'
import { GroupTypeWIthId } from '@/firestoreQueries/groups/groupTypes'
import useGetGroups from '@/firestoreQueries/groups/hooks/useGetGroups'
import { PortalTypeFromDB } from '@/firestoreQueries/portals/portalTypes'
import { ResourceTypeFromDB } from '@/firestoreQueries/resources/resourcesTypes'
import { RolesStringType, RolesType } from '@/firestoreQueries/utils/generalTypes'
import { useMembershipsStore, User } from '@/zustandStores/useMemberships'
import { useOrganization } from '@clerk/clerk-react'
import { Tooltip } from '@nextui-org/react'
import { Emoji } from 'emoji-picker-react'
import { Timestamp } from 'firebase/firestore'
import type { SVGProps } from 'react'
import { ReactNode } from 'react'
import { AiFillQuestionCircle } from 'react-icons/ai'
import { HiOutlineTrash } from 'react-icons/hi'

const TimelineAvatar = ({ children, ...rest }: AvatarProps) => {
  return (
    <Avatar {...rest} size={32} shape="circle">
      {children}
    </Avatar>
  )
}

interface ActivityItemProps {
  timestamp: Timestamp
  user: { name: string; imageUrl: string }
  parentType?: 'Resource' | 'Integration'
  parentName?: string
  parentId?: string
  operationType: DocumentActivityOperationType | 'connectToPortal'
  elementType: 'portal' | 'resource' | 'integration'
  ports?: string[]
  details?: DocumentActivityDetailsType | ConnectToPortalActivityDetailsType
}

export const ActivityItem = ({
  timestamp: activityTimestamp,
  user,
  operationType,
  elementType,
  details,
}: ActivityItemProps) => {
  const timestamp = formatFirestoreTimestamp(activityTimestamp)
  const color = getActivityColor(operationType)
  const text = getActivityText(operationType)
  const members = useMembershipsStore((s) => s.memberships)
  const organizationId = useOrganization().organization?.id
  const { data: groups } = useGetGroups({ organizationId })

  const portArray = getPorts(details, operationType, elementType)
  const membersArray = getMembers(members, details, operationType, groups ?? [])
  const newName = getName(details, operationType)
  const newDescription = getDescription(details, operationType)
  const accessDuration = getAccessDuration(details, operationType, elementType)

  return (
    <Timeline.Item
      className="mb-5"
      media={<TimelineAvatar className={`bg-${color}`}> {getActivityIcon(operationType)}</TimelineAvatar>}
    >
      <p className="flex items-center gap-3 justify-between w-full">
        <span className="text-slate-700 dark:text-slate-100">{timestamp}</span>
      </p>
      <div className="flex flex-row gap-2 items-center w-full">
        <h6 className="text-slate-700 dark:text-slate-100">
          {text} the {elementType}.
        </h6>
      </div>
      {/* {parentType && parentName && (
                    <div className="flex flex-row gap-2">
                        <SubTitle>Parent {parentType}:</SubTitle>
                        <ParentName
                            parentName={parentName}
                            parentId={parentId}
                            parentType={parentType}
                        />
                    </div>
                )} */}
      {newName && (
        <div className="flex flex-row gap-2">
          <SubTitle>New name:</SubTitle>
          <span>{newName}</span>
        </div>
      )}
      {newDescription && (
        <div className="flex flex-row gap-2">
          <SubTitle>New description:</SubTitle>
          <span>{newDescription}</span>
        </div>
      )}
      {portArray && portArray.length !== 0 && (
        <div className="flex flex-row gap-2 ">
          <SubTitle>Ports unlocked:</SubTitle>
          <div className="flex flex-wrap flex-row">
            {portArray.map((port) => (
              <Tag key={port}>{port}</Tag>
            ))}
          </div>
        </div>
      )}
      {accessDuration && (
        <div className="flex flex-row gap-2">
          <SubTitle>Access duration:</SubTitle>
          <span>{formatTime(accessDuration)}</span>
        </div>
      )}
      {membersArray && membersArray.length > 0 && (
        <div className="flex flex-row gap-2 items-center">
          <SubTitle>Members updated:</SubTitle>
          <div className="flex flex-wrap flex-row gap-2">
            {membersArray.map((member, index) => (
              <Tooltip key={index} content={member.role}>
                <Tag className="flex flex-row items-center gap-1">
                  {member.image}
                  <span className="text-slate-500 dark:text-slate-300 text-xs">{member.name}</span>
                </Tag>
              </Tooltip>
            ))}
          </div>
        </div>
      )}
      <div className="flex mt-2">
        <Avatar size={20} shape="circle" src={user.imageUrl} alt={user.name} />
        <span className="font-semibold text-gray-500 dark:text-gray-300 ml-2" style={{ fontSize: '12px' }}>
          {user.name}
        </span>
      </div>
    </Timeline.Item>
  )
}

const SubTitle = ({ children }: { children: ReactNode }) => {
  return <span className="text-slate-500 font-semibold">{children}</span>
}

function formatFirestoreTimestamp(timestamp: Timestamp): string {
  const date = timestamp.toDate()
  const time = date.toLocaleTimeString('en-GB', {
    hour: '2-digit',
    minute: '2-digit',
  })
  const dateString = date.toLocaleDateString('en-GB', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
  })
  return `${time} ${dateString}`
}

function getActivityColor(operation: DocumentActivityOperationType | 'connectToPortal') {
  switch (operation) {
    case 'connectToPortal':
      return 'emerald-500 dark:bg-emerald-500'
    case 'create':
      return 'blue-500 dark:bg-blue-500'
    case 'update':
      return 'purple-500 dark:bg-purple-500'
    case 'delete':
      return 'red-500 dark:bg-red-500'
    default:
      return 'gray-500 dark:bg-gray-500'
  }
}

export function SolarHistory2Linear(props: SVGProps<SVGSVGElement>) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="2em" height="2em" viewBox="0 0 24 24" {...props}>
      <g fill="none" stroke="currentColor" strokeLinecap="round" strokeWidth={1.5}>
        <path d="M2 12c0 5.523 4.477 10 10 10s10-4.477 10-10S17.523 2 12 2"></path>
        <path strokeLinejoin="round" d="M12 9v4h4"></path>
        <circle cx={12} cy={12} r={10} strokeDasharray=".5 3.5"></circle>
      </g>
    </svg>
  )
}

export function SolarPenNewRoundBroken(props: SVGProps<SVGSVGElement>) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="1.7em" height="1.7em" viewBox="0 0 24 24" {...props}>
      <g fill="none">
        <path
          stroke="currentColor"
          strokeLinecap="round"
          strokeWidth={1.5}
          d="m16.652 3.455l.649-.649A2.753 2.753 0 0 1 21.194 6.7l-.65.649m-3.892-3.893s.081 1.379 1.298 2.595c1.216 1.217 2.595 1.298 2.595 1.298m-3.893-3.893L10.687 9.42c-.404.404-.606.606-.78.829c-.205.262-.38.547-.524.848c-.121.255-.211.526-.392 1.068L8.412 13.9m12.133-6.552l-2.983 2.982m-2.982 2.983c-.404.404-.606.606-.829.78a4.59 4.59 0 0 1-.848.524c-.255.121-.526.211-1.068.392l-1.735.579m0 0l-1.123.374a.742.742 0 0 1-.939-.94l.374-1.122m1.688 1.688L8.412 13.9"
        ></path>
        <path
          fill="currentColor"
          d="M22.75 12a.75.75 0 0 0-1.5 0zM12 2.75a.75.75 0 0 0 0-1.5zM7.376 20.013a.75.75 0 1 0-.752 1.298zm-4.687-2.638a.75.75 0 1 0 1.298-.75zM21.25 12A9.25 9.25 0 0 1 12 21.25v1.5c5.937 0 10.75-4.813 10.75-10.75zM12 1.25C6.063 1.25 1.25 6.063 1.25 12h1.5A9.25 9.25 0 0 1 12 2.75zM6.624 21.311A10.704 10.704 0 0 0 12 22.75v-1.5a9.204 9.204 0 0 1-4.624-1.237zM1.25 12a10.7 10.7 0 0 0 1.439 5.375l1.298-.75A9.204 9.204 0 0 1 2.75 12z"
        ></path>
      </g>
    </svg>
  )
}

export function SolarAddCircleBroken(props: SVGProps<SVGSVGElement>) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="2em" height="2em" viewBox="0 0 24 24" {...props}>
      <path
        fill="none"
        stroke="currentColor"
        strokeLinecap="round"
        strokeWidth={1.5}
        d="M15 12h-3m0 0H9m3 0V9m0 3v3M7 3.338A9.954 9.954 0 0 1 12 2c5.523 0 10 4.477 10 10s-4.477 10-10 10S2 17.523 2 12c0-1.821.487-3.53 1.338-5"
      ></path>
    </svg>
  )
}

function getActivityIcon(operation: DocumentActivityOperationType | 'connectToPortal') {
  switch (operation) {
    case 'connectToPortal':
      return <SolarHistory2Linear />
    case 'create':
      return <SolarAddCircleBroken />

    case 'update':
      return <SolarPenNewRoundBroken />

    case 'delete':
      return <HiOutlineTrash size={22} />

    default:
      return <AiFillQuestionCircle size={22} />
  }
}

function getActivityText(operation: DocumentActivityOperationType | 'connectToPortal') {
  switch (operation) {
    case 'connectToPortal':
      return 'Connected to '
    case 'create':
      return 'Created '
    case 'update':
      return 'Updated '
    case 'delete':
      return 'Deleted '
    default:
      return 'Unknown'
  }
}

function getPorts(
  details: DocumentActivityDetailsType | ConnectToPortalActivityDetailsType | undefined,
  operationType: DocumentActivityOperationType | 'connectToPortal',
  elementType: 'resource' | 'portal' | 'integration',
): string[] | undefined {
  if (!details) return undefined
  if (elementType === 'integration') return undefined
  if (operationType === 'connectToPortal' || operationType === 'create' || operationType === 'delete') return undefined

  if (elementType === 'portal') {
    const ports = ((details as DocumentActivityDetailsType).update as Partial<PortalTypeFromDB>).portsAccess

    if (!ports) return undefined

    return Object.keys(ports).map((k) => ports[k as any].portRanges[0])
  }

  if (elementType === 'resource') {
    const ports = ((details as DocumentActivityDetailsType).update as Partial<ResourceTypeFromDB>).portRules!

    const portArray: string[] = []
    if (ports) {
      ports.lockedPorts &&
        Object.keys(ports.lockedPorts).forEach((k) => portArray.push(ports.lockedPorts[k as any].portRanges[0]))

      ports.openPorts &&
        Object.keys(ports.openPorts).forEach((k) => {
          portArray.push(ports.openPorts[k as any].portRanges[0])
        })

      ports.whitelistPorts &&
        Object.keys(ports.whitelistPorts).forEach((k) => {
          portArray.push(ports.whitelistPorts[k as any].portRanges[0])
        })
    }

    return portArray
  }
}

function getMembers(
  members: User[],
  details: DocumentActivityDetailsType | ConnectToPortalActivityDetailsType | undefined,
  operationType: DocumentActivityOperationType | 'connectToPortal',
  groups: GroupTypeWIthId[],
) {
  if (!details) return undefined
  if (operationType === 'connectToPortal' || operationType === 'create' || operationType === 'delete') {
    return undefined
  }

  const roles = ((details as DocumentActivityDetailsType).update as Partial<ResourceTypeFromDB>).roles as {
    Owners: { [key: number]: string }
    Viewers: { [key: number]: string }
    Editors: { [key: number]: string }
  }

  if (!roles) return undefined

  const formattedRoles: RolesType = {
    Owners: [],
    Viewers: [],
    Editors: [],
  }

  Object.keys(roles).forEach((role) => {
    Object.keys(roles[role as 'Owners' | 'Viewers' | 'Editors']).forEach((id) => {
      formattedRoles[role as 'Owners' | 'Viewers' | 'Editors'].push(roles[role as RolesStringType][id as any])
    })
  })

  const ids = getPermittedIds(formattedRoles)
  const newArr = ids.map((id) => {
    const member = members.find((m) => m.userId === id)
    const group = groups.find((g) => g.id === id)
    const name = member ? member.firstName : group ? group.groupInfo.groupName : 'Unknown'
    const emoji = group ? group.groupInfo.groupEmoji : '👤'
    return {
      name,
      image: member ? <Avatar size={18} src={member.imageUrl} shape="circle" /> : <Emoji unified={emoji} size={18} />,

      role: getMemberRole(id, formattedRoles),
    } as { role: string; name: string; image: ReactNode }
  })

  return newArr
}

const getName = (
  details: DocumentActivityDetailsType | ConnectToPortalActivityDetailsType | undefined,
  operationType: DocumentActivityOperationType | 'connectToPortal',
) => {
  if (!details) return undefined
  if (operationType === 'connectToPortal' || operationType === 'create' || operationType === 'delete') {
    return undefined
  }

  return ((details as DocumentActivityDetailsType).update as Partial<ResourceTypeFromDB>).resourceInfo?.resourceName
}

const getDescription = (
  details: DocumentActivityDetailsType | ConnectToPortalActivityDetailsType | undefined,
  operationType: DocumentActivityOperationType | 'connectToPortal',
) => {
  if (!details) return undefined
  if (operationType === 'connectToPortal' || operationType === 'create' || operationType === 'delete') {
    return undefined
  }

  return ((details as DocumentActivityDetailsType).update as Partial<ResourceTypeFromDB>).resourceInfo
    ?.resourceDescription
}

const getAccessDuration = (
  details: DocumentActivityDetailsType | ConnectToPortalActivityDetailsType | undefined,
  operationType: DocumentActivityOperationType | 'connectToPortal',
  elementType: 'portal' | 'resource' | 'integration',
) => {
  if (!details) return undefined
  if (elementType !== 'portal') return undefined
  if (operationType === 'connectToPortal' || operationType === 'create' || operationType === 'delete') {
    return undefined
  }

  return ((details as DocumentActivityDetailsType).update as Partial<PortalTypeFromDB>).accessDuration
}
const formatTime = (seconds: number) => {
  //return seconds if less than 60

  if (seconds < 60) {
    return `${seconds} seconds`
  }

  //convert seconds to minutes
  const minutes = Math.floor(seconds / 60)

  //return minutes if less than 60
  if (minutes < 60) {
    if (minutes === 1) {
      return `${minutes} minute`
    }
    return `${minutes} minutes`
  }

  //convert minutes to hours

  const hours = Math.floor(minutes / 60)

  //return hours if less than 24
  if (hours < 24) {
    if (hours === 1) {
      return `${hours} hour`
    }
    return `${hours} hours`
  }

  //convert hours to days
  const days = Math.floor(hours / 24)

  if (days === 1) {
    return `${days} day`
  }
  return `${days} days`
}
