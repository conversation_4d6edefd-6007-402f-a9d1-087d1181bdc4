import { Tag } from '@/components/ui'
import { ResourceType } from '@/firestoreQueries/resources/resourcesTypes'
import { getUniqueNameColor } from '@/utils/getUniqueNameColor'
import { awsLogoMap } from '@/views/Access/Resources/new/newAWSIntegrationResource/AWSAvailableResourcesTable'
import ResourceProfileModal from '@/views/Access/Resources/resourceProfile/ResourceProfileModal'
import { UserData } from '@/views/Access/Resources/types'
import {
  Avatar,
  AvatarGroup,
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
  useDisclosure,
} from '@nextui-org/react'
import { Emoji } from 'emoji-picker-react'
import { CustomCell, customTableRowClassName } from './CustomTableComponents'
import { getPermittedIds } from '@/customUtils/documentPermissions'

interface Props {
  resource: ResourceType
  members: UserData[]
}
const ParentResourceTableNew = ({ members, resource }: Props) => {
  const resourceMembers = members.filter((member) => getPermittedIds(resource.roles).includes(member.userId))
  const { onOpen, isOpen, onClose, onOpenChange } = useDisclosure()
  return (
    <>
      <Table
        shadow="none"
        classNames={{
          base: 'rounded-primary shadow-custom-shadow',
        }}
        aria-label="Simple Table"
      >
        <TableHeader>
          <TableColumn>Name</TableColumn>
          <TableColumn>Description</TableColumn>
          <TableColumn>IP</TableColumn>
          <TableColumn className="text-center">Members</TableColumn>
        </TableHeader>
        <TableBody>
          <TableRow key={resource.id} className={customTableRowClassName}>
            <CustomCell type="start">
              <div className="flex flex-row items-center gap-2">
                <button
                  type="button"
                  onClick={onOpen}
                  className="hover:text-primary-500 flex flex-row gap-2 items-center"
                >
                  {resource.integration.integrationType === 'aws' && (
                    <img
                      src={awsLogoMap[resource.resourceInfo.instanceType]}
                      className="rounded-full h-[30px] w-[30px] bg-white object-contain"
                    />
                  )}
                  {resource.integration.integrationType === 'standalone' && (
                    <div className="bg-white rounded-full h-[30px] w-[30px] flex items-center justify-center">
                      <Emoji unified={resource.resourceInfo.resourceEmoji} size={18} />
                    </div>
                  )}
                  <span className="hover:text-primary-500">{resource.resourceInfo.resourceName}</span>
                </button>
                {resource.resourceInfo.region && (
                  <Tag
                    style={{
                      color: getUniqueNameColor(resource.resourceInfo.region ?? 'unknown'),
                    }}
                    className={`text-sm font-normal`}
                  >
                    {resource.resourceInfo.region}
                  </Tag>
                )}
                {resource.resourceInfo.securityGroupName && (
                  <Tag
                    style={{
                      color: getUniqueNameColor(resource.resourceInfo.region ?? 'unknown'),
                    }}
                    className={`text-sm font-normal`}
                  >
                    {resource.resourceInfo.securityGroupName}
                  </Tag>
                )}
              </div>
            </CustomCell>
            {/*  */}
            <TableCell>
              {resource.resourceInfo.resourceDescription}
              {/*  */}
            </TableCell>
            <TableCell>
              {resource.resourceInfo.resourceIPs.map((ip) => (ip ? <Tag key={ip}>{ip}</Tag> : null))}
            </TableCell>
            {/*  */}
            <CustomCell type="end">
              <div className="flex items-center justify-center">
                <AvatarGroup isBordered max={3} size="sm">
                  {resourceMembers.map((member) => (
                    <Avatar
                      style={{
                        cursor: 'pointer',
                      }}
                      onClick={onOpen}
                      key={member.userId}
                      src={member.imageUrl}
                      alt={member.firstName}
                      title={member.firstName}
                    />
                  ))}
                </AvatarGroup>
              </div>
            </CustomCell>
          </TableRow>
        </TableBody>
      </Table>
      <ResourceProfileModal
        onDeleteSuccess={() => {
          onClose()
        }}
        resourceId={resource.id}
        isOpen={isOpen}
        onClose={onClose}
        onOpen={onOpen}
        onOpenChange={onOpenChange}
        defaultTab="profile"
      />
    </>
  )
}

export default ParentResourceTableNew
