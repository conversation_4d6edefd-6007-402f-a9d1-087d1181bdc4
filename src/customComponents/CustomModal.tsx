import { cn, Modal, ModalContent } from '@nextui-org/react'
import React from 'react'

export interface CustomModalProps {
  isOpen: boolean
  onOpenChange: () => void
}

interface Props {
  children: React.ReactNode
  size?: 'sm' | 'md' | 'lg'
  isOpen: boolean
  onOpenChange: () => void
}

export default function CustomModal({ children, isOpen, onOpenChange, size = 'lg' }: Props) {
  return (
    <Modal
      id="custom-modal"
      role="dialog"
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      size="full"
      className={cn('!rounded-primary bg-white dark:bg-gray-800', {
        'w-4/5 h-4/5': size === 'lg',
        'w-2/3 h-2/3': size === 'md',
        'w-1/2 h-1/2': size === 'sm',
      })}
    >
      <ModalContent className="pb-4 flex flex-col">{children}</ModalContent>
    </Modal>
  )
}
