import { extendVariants, TableCell } from '@nextui-org/react'

export const CustomCell = extendVariants(TableCell, {
  variants: {
    type: {
      start: 'rounded-l-md  borderRadius-primary',
      end: 'rounded-r-md ',
      single: 'rounded-md ',
      none: '',
    },
  },
})

export const customTableRowClassName =
  'data-[middle=true]:border-t-2 data-[last=true]:border-t-2 border-x-0 border-foreground-100 !overflow-hidden'
// export const customTa
