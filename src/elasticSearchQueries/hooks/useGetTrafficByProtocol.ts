import { elasticSearchQueryKeyStore } from '@/configs/reactQueryKeyStore'
import { EsTimeRange } from '@/customHooks/useElasticSearchTimeRange'
import useElasticStore from '@/zustandStores/useElasticStore'
import { useQuery } from '@tanstack/react-query'
import { useEffect, useMemo, useState } from 'react'
import * as Sentry from '@sentry/react'

interface Bucket {
    key: string
    doc_count: number
    '2': {
        value: number
    }
}

interface AggregationBucket {
    buckets: Array<Bucket>
}

interface Aggregations {
    '0': AggregationBucket
}

interface ElasticsearchResponse {
    data: {
        aggregations: Aggregations
    }
}
type Args = {
    esTimeRange: EsTimeRange
}

export default ({ esTimeRange }: Args) => {
    const query = useMemo(
        () => ({
            aggs: {
                '0': {
                    terms: {
                        field: 'initiation.port',
                        order: {
                            _count: 'desc',
                        },
                        size: 10000,
                    },
                    aggs: {
                        '2': {
                            sum: {
                                field: 'egress-volume',
                            },
                        },
                    },
                },
            },
            size: 0,
            fields: [
                {
                    field: 'end-time',
                    format: 'date_time',
                },
                {
                    field: 'start-time',
                    format: 'date_time',
                },
            ],
            query: {
                bool: {
                    filter: [
                        {
                            range: {
                                'start-time': {
                                    gte: esTimeRange.from,
                                    lte: esTimeRange.to,
                                },
                            },
                        },
                        {
                            term: {
                                'initiation.flow-direction.keyword': 'egress',
                            },
                        },
                    ],
                },
            },
        }),
        [esTimeRange]
    )

    const [initialData, setInitialData] = useState<
        { data: number; key: string }[]
    >([])

    const esClient = useElasticStore((state) => state.client)
    const esIndexPath = useElasticStore((state) => state.indexPath)
    const checkExpiration = useElasticStore((state) => state.checkExpiration)

    useEffect(() => {
        checkExpiration()
    }, [checkExpiration])

    const queryFn = async () => {
        try {
            const response = (await esClient.post(
                esIndexPath,
                query
            )) as ElasticsearchResponse

            const portsData: { data: number; key: string }[] = []

            response.data.aggregations[0].buckets.forEach((bucket) => {
                const key = portToProtocolMap[bucket.key] || 'Unkown'
                const data = {
                    data: bucket[2].value,
                    key,
                }

                const index = portsData.findIndex((el) => el.key === key)

                if (index === -1) {
                    portsData.push(data)
                } else {
                    portsData[index].data += data.data
                }
            })

            setInitialData(portsData)
            return portsData
        } catch (error) {
            Sentry.captureException(error)
            return []
        }
    }

    return useQuery({
        queryKey: elasticSearchQueryKeyStore.trafficByProtocl({ query }),
        queryFn,
        initialData,
        // staleTime: 1000 * 60 * 5,
        gcTime: 1000 * 60 * 1,
    })
}
const portToProtocolMap: { [key: string]: string } = {
    '20': 'FTP Data Transfer',
    '21': 'FTP Command Control',
    '22': 'SSH',
    '23': 'Telnet',
    '25': 'SMTP',
    '53': 'DNS',
    '80': 'HTTP',
    '110': 'POP3',
    '119': 'NNTP',
    '143': 'IMAP',
    '161': 'SNMP',
    '162': 'SNMPTRAP',
    '443': 'HTTPS',
    '465': 'SMTPS',
    '587': 'SMTP (submission)',
    '993': 'IMAPS',
    '995': 'POP3S',
    '2087': 'Webmail',
    '3306': 'MySQL',
    '5432': 'PostgreSQL',
    '5900': 'VNC',
    '6379': 'Redis',
    '8080': 'HTTP',
    '8443': 'HTTPS',
}
