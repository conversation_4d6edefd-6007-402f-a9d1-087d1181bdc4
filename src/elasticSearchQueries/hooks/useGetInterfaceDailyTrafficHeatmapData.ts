import useElasticStore from '@/zustandStores/useElasticStore'
import { useQuery } from '@tanstack/react-query'
import { useEffect } from 'react'
import * as Sentry from '@sentry/react'

export default () => {
    const esClient = useElasticStore((state) => state.client)
    const esIndexPath = useElasticStore((state) => state.indexPath)

    const checkExpiration = useElasticStore((state) => state.checkExpiration)

    useEffect(() => {
        checkExpiration()
    }, [checkExpiration])

    return useQuery({
        queryKey: ['heatMapData', esQuery],
        queryFn: async () => {
            try {
                const response = (await esClient.post(
                    esIndexPath,
                    esQuery
                )) as Result

                const interfaceTrafficMap: {
                    key: string // ip address
                    data: {
                        key: string // time
                        data: number // volume
                    }[]
                }[] = transformResponseToInterfaceTrafficMap(response)

                const processedData = formatToTimeAndData(interfaceTrafficMap)

                return processedData
            } catch (error) {
                Sentry.captureException(error)
                return []
            }
        },
        gcTime: 1000 * 60 * 1,
        initialData: [],
    })
}
const time_zone = Intl.DateTimeFormat().resolvedOptions().timeZone

const esQuery = {
    aggs: {
        result: {
            terms: {
                field: 'interface-id',
                order: {
                    '2': 'desc',
                },
                size: 10000,
            },
            aggs: {
                '1': {
                    date_histogram: {
                        field: 'start-time',
                        calendar_interval: '1h',
                        time_zone,
                        min_doc_count: 0,
                        extended_bounds: {
                            min: 'now-23h/h',
                            max: 'now/h',
                        },
                    },
                    aggs: {
                        '2': {
                            sum: {
                                field: 'session-volume',
                            },
                        },
                    },
                },
                '2': {
                    sum: {
                        field: 'session-volume',
                    },
                },
            },
        },
    },
    size: 0,

    query: {
        bool: {
            must: [],
            filter: [
                {
                    range: {
                        'start-time': {
                            format: 'strict_date_optional_time',
                            gte: 'now-23h/h',
                            lte: 'now/h',
                        },
                    },
                },
            ],
            should: [],
            must_not: [],
        },
    },
}

type InnerBucket = {
    key_as_string: string
    '2': { value: number }
}

type OuterBucket = {
    key: string
    '1': { buckets: InnerBucket[] }
}

type Result = {
    data: { aggregations: { result: { buckets: Array<OuterBucket> } } }
}
const formatToTimeAndData = (
    inputData: Array<{
        key: string
        data: Array<{ key: string; data: number }>
    }>
) => {
    const outputData: {
        key: string
        data: { key: string; data: number }[]
    }[] = []

    inputData.forEach((device) => {
        device.data.forEach((hourData) => {
            let existingHourData = outputData.find(
                (item) => item.key === hourData.key
            )
            if (!existingHourData) {
                existingHourData = {
                    key: hourData.key,
                    data: [],
                }
                outputData.push(existingHourData)
            }
            existingHourData.data.push({
                key: device.key,
                data: hourData.data,
            })
        })
    })

    return outputData
}

const transformResponseToInterfaceTrafficMap = (data: Result) => {
    const ipTrafficMap: {
        key: string // ip address
        data: {
            key: string // time
            data: number // volume
        }[]
    }[] = []

    if (data.data.aggregations.result.buckets.length > 0) {
        data.data.aggregations.result.buckets.forEach((outerBucket) => {
            const ip = outerBucket.key
            const timeItems: { time: string; volume: number }[] = []
            outerBucket[1].buckets.forEach((innerBucket) => {
                const date = new Date(innerBucket.key_as_string)

                // Extract hours and minutes
                const hours = date.getHours().toString().padStart(2, '0')
                const minutes = date.getMinutes().toString().padStart(2, '0')

                // Format in HH:MM
                const timeFormatted = `${hours}:${minutes}`

                timeItems.push({
                    time: timeFormatted,
                    volume: innerBucket[2].value,
                })
            })

            ipTrafficMap.push({
                key: ip,
                data: timeItems.map((item) => ({
                    key: item.time,
                    data: item.volume,
                })),
            })
        })
    }

    return ipTrafficMap
}
