// import { esClient } from '@/configs/elasticSearchClient'
import { elasticSearchQueryKeyStore } from '@/configs/reactQueryKeyStore'
import { EsTimeRange } from '@/customHooks/useElasticSearchTimeRange'
import { convertEsRangeToDate } from '@/utils/elasticSearchTimeUtils/esTimeRangeFns'
import useElasticStore from '@/zustandStores/useElasticStore'
import { useQuery } from '@tanstack/react-query'
import { useCallback, useEffect, useMemo, useState } from 'react'
import * as Sentry from '@sentry/react'

interface AggregationBucket {
    key_as_string: string
    key: number
    doc_count: number
    total_session_volume: {
        value: number
    }
}

interface Aggregations {
    group_by_date: {
        buckets: AggregationBucket[]
    }
}

interface ElasticsearchResponse {
    data: {
        aggregations: Aggregations
    }
}
type Args = {
    esTimeRange: EsTimeRange
}

export default ({ esTimeRange }: Args) => {
    const esQuery = useMemo(
        () => ({
            size: 0,
            query: {
                bool: {
                    must: [
                        {
                            exists: {
                                field: 'egress-volume',
                            },
                        },
                    ],
                    filter: [
                        {
                            range: {
                                'start-time': {
                                    gte: esTimeRange.from,
                                    lt: esTimeRange.to,
                                },
                            },
                        },
                        {
                            term: {
                                'initiation.flow-direction.keyword': 'egress',
                            },
                        },
                    ],
                },
            },
            aggs: {
                group_by_date: {
                    date_histogram: {
                        field: 'start-time',
                        calendar_interval: 'day',
                    },
                    aggs: {
                        total_session_volume: {
                            sum: {
                                field: 'egress-volume',
                            },
                        },
                    },
                },
            },
        }),
        [esTimeRange]
    )

    const esClient = useElasticStore((state) => state.client)
    const esIndexPath = useElasticStore((state) => state.indexPath)
    const checkExpiration = useElasticStore((state) => state.checkExpiration)

    useEffect(() => {
        checkExpiration()
    }, [checkExpiration])

    const queryFn = useCallback(async () => {
        try {
            const response = (await esClient.post(
                esIndexPath,
                esQuery
            )) as ElasticsearchResponse

            const chartDataShape: { key: Date; data: number }[] = []

            const startDate = convertEsRangeToDate(esTimeRange.from)
            const endDate = convertEsRangeToDate(esTimeRange.to)

            const dayDifference = Math.round(
                (endDate.getTime() - startDate.getTime()) /
                    (1000 * 60 * 60 * 24)
            )

            for (let i = 0; i <= dayDifference; i++) {
                const date = new Date(startDate)
                date.setDate(date.getDate() + i)
                chartDataShape.push({
                    key: date,
                    data: 0,
                })
            }

            response.data.aggregations.group_by_date.buckets.forEach(
                (bucket) => {
                    const dataShape: { key: Date; data: number } = {
                        key: new Date(bucket.key_as_string),
                        data: bucket.total_session_volume.value,
                    }
                    dataShape.key.setHours(0, 0, 0, 0)

                    const index = chartDataShape.findIndex((d) => {
                        return (
                            d.key.toLocaleDateString('en-US', {
                                month: 'numeric',
                                day: 'numeric',
                            }) ===
                            dataShape.key.toLocaleDateString('en-US', {
                                month: 'numeric',
                                day: 'numeric',
                            })
                        )
                    })

                    if (index !== -1) {
                        chartDataShape[index] = dataShape
                    }
                }
            )

            setPlaceholderData(
                chartDataShape.sort((a, b) => a.key.getTime() - b.key.getTime())
            )

            return chartDataShape.sort(
                (a, b) => a.key.getTime() - b.key.getTime()
            )
        } catch (error) {
            Sentry.captureException(error)
            return []
        }
    }, [esTimeRange, esQuery, esClient, esIndexPath])

    const initialData: { key: Date; data: number }[] = []

    const today = new Date()
    today.setHours(0, 0, 0, 0)
    for (let i = 0; i < 7; i++) {
        const date = new Date(today)
        date.setDate(today.getDate() - i)
        initialData.push({ key: date, data: 0 })
    }

    const [placeholderData, setPlaceholderData] = useState(
        initialData.sort((a, b) => a.key.getTime() - b.key.getTime())
    )

    return useQuery({
        queryKey: elasticSearchQueryKeyStore.trafficOverTime({
            query: esQuery,
        }),
        queryFn,
        initialData: placeholderData,
        gcTime: 1000 * 60 * 1,
    })
}
