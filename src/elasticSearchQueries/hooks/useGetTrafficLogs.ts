/* eslint-disable import/named */
import { EsTimeRange } from '@/customHooks/useElasticSearchTimeRange'
import { convertVolume, getVolumeUnit } from '@/utils/elasticSearchTimeUtils/volumeConvertors'
import useElasticStore from '@/zustandStores/useElasticStore'
import { useInfiniteQuery } from '@tanstack/react-query'
// eslint-disable-next-line import/named
import * as Sentry from '@sentry/react'
import { format } from 'date-fns'
import { capitalize } from 'lodash'
import { useEffect, useMemo } from 'react'

type Row = {
    key: number
    remote_addr: string
    local_addr: string
    timestamp: string
    egressVolume: string
    ingressVolume: string
    direction: 'Egress' | 'Ingress' | 'Unkown'
    port: string
}

type Fields = { remote_addr: [string]; local_addr: [string] }
type Source = {
    'start-time': string
    'egress-volume': number
    'ingress-volume': number
    initiation?: {
        'flow-direction': 'ingress' | 'egress'
        port: number
    }
}
type Hit = { fields: Fields; _source: Source; _index: string }
type Result = {
    data: {
        hits: { hits: Array<Hit> }
    }
}
export type Sorter = {
    field: 'start-time' | 'egress-volume' | 'ingress-volume'
    direction: 'asc' | 'desc'
}
type Args = {
    pageSize: number
    sorter: Sorter
    esTimeRange: EsTimeRange
    filter?: string
    type: 'widget' | 'page'
}

export default ({ pageSize, sorter, esTimeRange, filter, type }: Args) => {
    const esClient = useElasticStore((state) => state.client)
    const esIndexPath = useElasticStore((state) => state.indexPath)
    const checkExpiration = useElasticStore((state) => state.checkExpiration)

    useEffect(() => {
        checkExpiration()
    }, [checkExpiration])

    const esQuery = useMemo(
        () => (page: number) => {
            const query: any = {
                query: {
                    bool: {
                        filter: [
                            {
                                range: {
                                    'start-time': {
                                        gte: esTimeRange.from,
                                        lte: esTimeRange.to,
                                    },
                                },
                            },
                        ],
                        must:
                            filter && filter.length > 0
                                ? [
                                      {
                                          bool: {
                                              should: [
                                                  {
                                                      script: {
                                                          script: {
                                                              source: `
                                                            if (doc['initiation.local-addr.keyword'].size() == 0) {
                                                                return false;
                                                            } else {
                                                                return doc['initiation.local-addr.keyword'].value.contains(params.filter);
                                                            }
                                                        `,
                                                              params: {
                                                                  filter,
                                                              },
                                                          },
                                                      },
                                                  },
                                                  {
                                                      script: {
                                                          script: {
                                                              source: `
                                                            if (doc['initiation.remote-addr.keyword'].size() == 0) {
                                                                return false;
                                                            } else {
                                                                return doc['initiation.remote-addr.keyword'].value.contains(params.filter);
                                                            }
                                                        `,
                                                              params: {
                                                                  filter,
                                                              },
                                                          },
                                                      },
                                                  },
                                              ],
                                          },
                                      },
                                  ]
                                : [],
                    },
                },
                script_fields: {
                    local_addr: {
                        script: {
                            source: "if (doc['initiation.local-addr.keyword'].size() == 0) return ''; else return doc['initiation.local-addr.keyword'].value",
                        },
                    },
                    remote_addr: {
                        script: {
                            source: "if (doc['initiation.remote-addr.keyword'].size() == 0) return ''; else return doc['initiation.remote-addr.keyword'].value",
                        },
                    },
                },
                _source: [
                    'start-time',
                    'ingress-volume',
                    'egress-volume',
                    'initiation.port',
                    'initiation.flow-direction',
                ],
                sort: { [sorter.field]: { order: sorter.direction } },
                from: (page - 1) * pageSize,
                size: pageSize,
            }

            return query
        },
        [sorter, pageSize, esTimeRange, filter]
    )

    const fetchRows = async ({ pageParam = 1 }) => {
        try {
            const esQueryObj = esQuery(pageParam)
            const { data } = (await esClient.post(esIndexPath, esQueryObj)) as Result

            const rows: Row[] = []
            data.hits.hits.forEach((hit, index) => {
                const dateTimestamp = new Date(hit._source['start-time'])
                const timestamp = format(dateTimestamp, 'dd/MM/yyyy hh:mm a')

                const convertedEgressVolume = convertVolume(hit._source['egress-volume'])
                const convertedIngressVolume = convertVolume(hit._source['ingress-volume'])

                const egressVolume = `${convertedEgressVolume.toFixed(0)}${getVolumeUnit(hit._source['egress-volume'])}`
                const ingressVolume = `${convertedIngressVolume.toFixed(0)}${getVolumeUnit(
                    hit._source['ingress-volume']
                )}`

                const row: Row = {
                    key: index + (pageParam - 1) * pageSize,
                    local_addr: hit.fields.local_addr[0] ?? 'Unkown',
                    remote_addr: hit.fields.remote_addr[0] ?? 'Unkown',
                    timestamp,
                    egressVolume,
                    ingressVolume,
                    port: hit._source.initiation ? String(hit._source.initiation.port) : 'Unknown',
                    direction: hit._source.initiation
                        ? (capitalize(hit._source.initiation['flow-direction']) as 'Egress' | 'Ingress')
                        : 'Unkown',
                }
                rows.push(row)
            })

            const hasMore = data.hits.hits.length === pageSize

            return {
                rows,
                nextPage: hasMore ? pageParam + 1 : undefined,
                hasMore,
            }
        } catch (error) {
            Sentry.captureException(error)
            return { rows: [], nextPage: undefined, hasMore: false }
        }
    }

    return useInfiniteQuery({
        queryKey: ['trafficLogs', type, sorter, esTimeRange, filter],
        queryFn: fetchRows,
        // staleTime: 1000 * 60 * 1, // 1 minute
        gcTime: 1000 * 60 * 1, // 1 minute
        getNextPageParam: (lastPage) => lastPage.nextPage,
        initialPageParam: 1,
        select: (data) => ({
            rows: data.pages.flatMap((page) => page.rows),
            hasMore: data.pages[data.pages.length - 1]?.hasMore,
        }),
        refetchOnMount: true,
        refetchOnWindowFocus: true,
    })
}
