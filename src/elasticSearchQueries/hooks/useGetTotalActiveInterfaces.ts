import { EsTimeRange } from '@/customHooks/useElasticSearchTimeRange'
import useElasticStore from '@/zustandStores/useElasticStore'
import { useQuery } from '@tanstack/react-query'
import { AxiosInstance } from 'axios'
import { Dispatch, SetStateAction, useEffect, useMemo, useState } from 'react'
import * as Sentry from '@sentry/react'

type InnerBucket = {
    key: string
    doc_count: number
}

type UniqueInterfaceIds = {
    buckets: InnerBucket[]
}

type OuterBucket = {
    unique_interface_ids: UniqueInterfaceIds
}

type Aggregation = {
    buckets: OuterBucket[]
}

type Response = {
    data: {
        aggregations: {
            current_week: Aggregation
            previous_week: Aggregation
        }
    }
}

type DataType = {
    prevWeekCount: number
    currWeekCount: number
    diff: number
}

type Args = {
    esTimeRange: EsTimeRange
}

const useGetTotalActiveInterfaces = ({ esTimeRange }: Args) => {
    const esClient = useElasticStore((state) => state.client)
    const esIndexPath = useElasticStore((state) => state.indexPath)
    const checkExpiration = useElasticStore((state) => state.checkExpiration)

    useEffect(() => {
        checkExpiration()
    }, [checkExpiration])

    const esQuery = useMemo(
        () => ({
            size: 0,
            aggs: {
                previous_week: {
                    date_range: {
                        field: 'start-time',
                        ranges: [
                            {
                                from: esTimeRange.oldFrom,
                                to: esTimeRange.oldTo,
                            },
                        ],
                    },
                    aggs: {
                        unique_interface_ids: {
                            terms: {
                                field: 'interface-id',
                                size: 10000,
                            },
                        },
                    },
                },
                current_week: {
                    date_range: {
                        field: 'start-time',
                        ranges: [
                            {
                                from: esTimeRange.from,
                                to: esTimeRange.to,
                            },
                        ],
                    },
                    aggs: {
                        unique_interface_ids: {
                            terms: {
                                field: 'interface-id',
                                size: 10000,
                            },
                        },
                    },
                },
            },
        }),
        [esTimeRange]
    )

    const [initialData, setInitialData] = useState<DataType>({
        prevWeekCount: 0,
        currWeekCount: 0,
        diff: 0,
    })

    return useQuery({
        queryKey: ['total-interfaces', esQuery],
        queryFn: async () =>
            await queryFn({
                esClient,
                esIndexPath,
                esQuery,
                initialData,
                setInitialData,
            }),
        initialData,
        gcTime: 1000 * 60 * 1,
    })
}
export default useGetTotalActiveInterfaces
//------------------------------------------------------------------------------------------------------------------------------------------------
//------------------------------------------------------------------------------------------------------------------------------------------------

const queryFn = async ({
    esClient,
    esIndexPath,
    esQuery,
    setInitialData,
    initialData,
}: {
    esClient: AxiosInstance
    esIndexPath: string
    esQuery: object
    setInitialData: Dispatch<SetStateAction<DataType>>
    initialData: DataType
}) => {
    try {
        const {
            data: { aggregations },
        } = (await esClient.post(esIndexPath, esQuery)) as Response

        const prevWeekCount =
            aggregations.previous_week.buckets[0].unique_interface_ids.buckets
                .length

        const currWeekCount =
            aggregations.current_week.buckets[0].unique_interface_ids.buckets
                .length

        const diff = currWeekCount - prevWeekCount
        setInitialData({ prevWeekCount, currWeekCount, diff })
        return { prevWeekCount, currWeekCount, diff }
    } catch (error) {
        Sentry.captureException(error)
        return initialData
    }
}
