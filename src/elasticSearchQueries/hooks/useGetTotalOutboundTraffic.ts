import { EsTimeRange } from '@/customHooks/useElasticSearchTimeRange'
import useElasticStore from '@/zustandStores/useElasticStore'
import { useQuery } from '@tanstack/react-query'
import { AxiosInstance } from 'axios'
import { Dispatch, SetStateAction, useMemo, useState } from 'react'
import * as Sentry from '@sentry/react'

type DataType = {
    prevWeekSum: number
    currWeekSum: number
    precentageDiff: number
}

type Args = {
    esTimeRange: EsTimeRange
}

const useGetTotalOutboundTraffic = ({ esTimeRange }: Args) => {
    const esClient = useElasticStore((state) => state.client)
    const esIndexPath = useElasticStore((state) => state.indexPath)

    const esQuery = useMemo(
        () => ({
            size: 0,
            aggs: {
                prev_week_sum: {
                    filter: {
                        bool: {
                            must: [
                                {
                                    range: {
                                        'start-time': {
                                            gte: esTimeRange.oldFrom,
                                            lt: esTimeRange.oldTo,
                                        },
                                    },
                                },
                                {
                                    term: {
                                        'initiation.flow-direction.keyword': 'egress',
                                    },
                                },
                            ],
                        },
                    },
                    aggs: {
                        sum: {
                            sum: {
                                field: 'egress-volume',
                            },
                        },
                    },
                },
                curr_week_sum: {
                    filter: {
                        bool: {
                            must: [
                                {
                                    range: {
                                        'start-time': {
                                            gte: esTimeRange.from,
                                            lt: esTimeRange.to,
                                        },
                                    },
                                },
                                {
                                    term: {
                                        'initiation.flow-direction.keyword': 'egress',
                                    },
                                },
                            ],
                        },
                    },
                    aggs: {
                        sum: {
                            sum: {
                                field: 'egress-volume',
                            },
                        },
                    },
                },
            },
            fields: [
                {
                    field: 'end-time',
                    format: 'date_time',
                },
                {
                    field: 'start-time',
                    format: 'date_time',
                },
            ],
            _source: {
                excludes: [],
            },
            query: {
                bool: {
                    filter: [
                        {
                            range: {
                                'start-time': {
                                    gte: esTimeRange.from,
                                    lte: esTimeRange.to,
                                },
                            },
                        },
                        {
                            term: {
                                'initiation.flow-direction.keyword': 'egress',
                            },
                        },
                    ],
                },
            },
        }),
        [esTimeRange]
    )

    const [initialData, setInitialData] = useState<DataType>({
        prevWeekSum: 0,
        currWeekSum: 0,
        precentageDiff: 0,
    })

    return useQuery({
        queryKey: ['totalOutboundTraffic', esQuery],
        queryFn: async () => await queryFn({ esClient, esIndexPath, setInitialData, esQuery }),
        initialData,
        gcTime: 1000 * 60 * 1,
    })
}
export default useGetTotalOutboundTraffic

//------------------------------------------------------------------------------------------------------------------------------------------------
//------------------------------------------------------------------------------------------------------------------------------------------------
const getPrecentageDiff = (prevWeekSum: number, currWeekSum: number) => {
    return prevWeekSum === 0 ? 0 : (currWeekSum / prevWeekSum) * 100
}
//------------------------------------------------------------------------------------------------------------------------------------------------
//------------------------------------------------------------------------------------------------------------------------------------------------
const queryFn = async ({
    esQuery,
    setInitialData,
    esClient,
    esIndexPath,
}: {
    esClient: AxiosInstance
    esIndexPath: string
    esQuery: object
    setInitialData: Dispatch<SetStateAction<DataType>>
}) => {
    try {
        const response = (await esClient.post(esIndexPath, esQuery)) as {
            data: {
                aggregations: {
                    curr_week_sum: { sum: { value: number } }
                    prev_week_sum: { sum: { value: number } }
                }
            }
        }
        const prevWeekSum = response.data.aggregations.prev_week_sum.sum.value
        const currWeekSum = response.data.aggregations.curr_week_sum.sum.value

        const precentageDiff = getPrecentageDiff(prevWeekSum, currWeekSum)

        setInitialData({ prevWeekSum, currWeekSum, precentageDiff })
        return { prevWeekSum, currWeekSum, precentageDiff }
    } catch (error) {
        Sentry.captureException(error)
        return { prevWeekSum: 0, currWeekSum: 0, precentageDiff: 0 }
    }
}
