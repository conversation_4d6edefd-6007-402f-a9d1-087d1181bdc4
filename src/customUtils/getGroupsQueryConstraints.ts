import { DocumentReference, or, QueryFieldFilterConstraint, where } from 'firebase/firestore'

export function getQueryConstraints(groupsIds: Array<string>, userId: string) {
  const constraints: Array<QueryFieldFilterConstraint> = []
  const userOwnerConstraint = where(`roles.Owners`, 'array-contains', userId)
  const userEditorConstraint = where(`roles.Editors`, 'array-contains', userId)
  const userViewerConstraint = where(`roles.Viewers`, 'array-contains', userId)
  const groupEditorConstraint = where(`roles.Editors`, 'array-contains-any', groupsIds)
  const groupViewerConstraint = where(`roles.Viewers`, 'array-contains-any', groupsIds)
  const defaultRequesterConstraint = where(`defaultRole`, '==', 'Requester')
  const defaultViewerConstraint = where(`defaultRole`, '==', 'Viewer')
  const defaultEditorConstraint = where(`defaultRole`, '==', 'Editor')
  const defaultOwnerConstraint = where(`defaultRole`, '==', 'Owner')

  constraints.push(
    userOwnerConstraint,
    userEditorConstraint,
    userViewerConstraint,
    defaultViewerConstraint,
    defaultRequesterConstraint,
    defaultEditorConstraint,
    defaultOwnerConstraint,
  )
  constraints.push(userOwnerConstraint, userEditorConstraint, userViewerConstraint)

  if (groupsIds.length > 0) {
    constraints.push(groupEditorConstraint, groupViewerConstraint)
  }

  return or(...constraints)
}

export function getPortalQueryConstraints({
  groupsIds,
  resourcesRef,
  userId,
}: {
  groupsIds: Array<string>
  userId: string
  resourcesRef: Array<DocumentReference>
}) {
  const constraints: Array<QueryFieldFilterConstraint> = []
  const userOwnerConstraint = where(`roles.Owners`, 'array-contains', userId)
  const userEditorConstraint = where(`roles.Editors`, 'array-contains', userId)
  const userViewerConstraint = where(`roles.Viewers`, 'array-contains', userId)
  const groupEditorConstraint = where(`roles.Editors`, 'array-contains-any', groupsIds)
  const groupViewerConstraint = where(`roles.Viewers`, 'array-contains-any', groupsIds)
  const defaultRequesterConstraint = where(`defaultRole`, '==', 'Requester')
  const defaultViewerConstraint = where(`defaultRole`, '==', 'Viewer')
  const defaultEditorConstraint = where(`defaultRole`, '==', 'Editor')
  const defaultOwnerConstraint = where(`defaultRole`, '==', 'Owner')

  constraints.push(
    userOwnerConstraint,
    userEditorConstraint,
    userViewerConstraint,
    defaultViewerConstraint,
    defaultRequesterConstraint,
    defaultEditorConstraint,
    defaultOwnerConstraint,
  )

  if (groupsIds.length > 0) {
    constraints.push(groupEditorConstraint, groupViewerConstraint)
  }

  for (const ref of resourcesRef) {
    const constraint = where('resourceRef', '==', ref)
    constraints.push(constraint)
  }

  return or(...constraints)
}
export function getResourceQueryConstraints({ groupsIds, userId }: { groupsIds: Array<string>; userId: string }) {
  const constraints: Array<QueryFieldFilterConstraint> = []
  const userOwnerConstraint = where(`roles.Owners`, 'array-contains', userId)
  const userEditorConstraint = where(`roles.Editors`, 'array-contains', userId)
  const userViewerConstraint = where(`roles.Viewers`, 'array-contains', userId)
  const groupEditorConstraint = where(`roles.Editors`, 'array-contains-any', groupsIds)
  const groupViewerConstraint = where(`roles.Viewers`, 'array-contains-any', groupsIds)
  const defaultRequesterConstraint = where(`defaultRole`, '==', 'Requester')
  const defaultViewerConstraint = where(`defaultRole`, '==', 'Viewer')
  const defaultEditorConstraint = where(`defaultRole`, '==', 'Editor')
  const defaultOwnerConstraint = where(`defaultRole`, '==', 'Owner')

  constraints.push(
    userOwnerConstraint,
    userEditorConstraint,
    userViewerConstraint,
    defaultViewerConstraint,
    defaultRequesterConstraint,
    defaultEditorConstraint,
    defaultOwnerConstraint,
  )

  if (groupsIds.length > 0) {
    constraints.push(groupEditorConstraint, groupViewerConstraint)
  }

  return or(...constraints)
}
