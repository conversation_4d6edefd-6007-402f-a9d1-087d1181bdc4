import { Severity } from '@/customComponents/SeverityBars'
import { severityToScoreMap } from './severityToScoreMap'

const MAGIC_NUMBER = 500;

export function getRiskScore(severityArray: Severity[]) {
    const total = severityArray.reduce((acc, severity) => {
        return acc + severityToScoreMap[severity]
    }, 0)

    const riskScore = (total / MAGIC_NUMBER) * 100

    if (riskScore > 100) {
        return 100
    }

    return riskScore
}
