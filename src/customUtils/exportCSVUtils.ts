import { TrafficData } from "@/components/ExportCSVButton/ExportCSVButton"
import { ColumnDef } from "@tanstack/react-table"

export const downloadCSV = (csvContent: string, filename?: string) => {
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', filename || `hunter-traffic-export-${new Date().toISOString()}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
  
  export const generateFileName = (organizationName?: string) => {
    const now = new Date()
    const formattedDate = now
      .toLocaleDateString('en-US', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
      })
      .replace(/[/:,]/g, '-')
      .replace(/\s/g, '_')
  
    const orgName = organizationName || 'organization'
    return `${orgName}-traffic-export-${formattedDate}.csv`
  }
  
  // Helper functions for export
  export const getExportColumns = (columns: ColumnDef<any>[], visibleColumns: string[]) => {
    const blackListColumns = ['firewallEnabled']
    const visibleCols = columns.filter(
      (col: any) =>
        col.accessorKey && visibleColumns.includes(col.accessorKey) && !blackListColumns.includes(col.accessorKey),
    )
    return visibleCols.map((col: any) => {
      if (typeof col.header === 'function') {
        const rendered = col.header({}) as any
        if (rendered?.props?.title) return rendered.props.title
        if (typeof rendered === 'string') return rendered
        return col.accessorKey
      }
      return col.header || col.accessorKey
    })
  }
  
  export const getExportData = (data: any[], visibleColumns: string[]) => {
    return data.map((row) => {
      const rowData: Record<string, any> = {}
      visibleColumns.forEach((colId) => {
        const keys = colId.split('.')
        let value = row
        for (const key of keys) {
          value = value?.[key]
        }
        rowData[colId] = value ?? ''
      })
      return rowData
    })
  }
  
  export const convertToCSV = (data: TrafficData[], columns: string[]) => {
    const csvRows = data.map((item) =>
      columns.map((col) => {
        const value = item[col]
        return `"${value === 'N/A' ? 'N/A' : String(value ?? '').replace(/"/g, '""')}"`
      }),
    )
    return [columns.join(','), ...csvRows.map((row) => row.join(','))].join('\n')
  }
  