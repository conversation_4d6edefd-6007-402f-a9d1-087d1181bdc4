const names = [
  'DirectAd<PERSON>',
  '<PERSON>min',
  'VestaCP',
  'LiteSpeed Web Server',
  'OpenStack',
  '<PERSON>deck',
  '<PERSON><PERSON><PERSON> Virtuozzo',
  '<PERSON><PERSON><PERSON>',
  'Nutanix',
  'MA<PERSON> Stack',
  'cPanel',
  'Prometheus',
  'Nginx Server',
  '<PERSON>',
  'Subversion (SVN)',
  'Cacti',
  'Apache TomEE',
  'Docker',
  'KVM (Kernel-based Virtual Machine)',
  'Icinga',
  'OpenVZ',
  'CentOS',
  'Rocket.Chat',
  'SaltStack',
  'GitLab CI/CD',
  'ManageEngine OpManager',
  'Perforce Helix Core',
  'MantisBT',
  'Phabricator',
  'MediaWiki',
  'Plesk',
  'Redmine',
  'SUSE Linux Enterprise Server (SLES)',
  'ApisCP',
  'Chef',
  'Graylog',
  'Proxmox VE',
  'Red Hat Enterprise Linux (RHEL)',
  'Datadog',
  'CloudPanel',
  'HP-UX (Hewlett-Packard)',
  'Zabbix',
  'InterWorx',
  '<PERSON><PERSON>lunk',
  '<PERSON>',
  '<PERSON><PERSON><PERSON>',
  'Tomcat Server',
  'Ubuntu Server',
  'ISPConfig',
  'Mattermost',
  'FreeBSD Server',
  'TFS (Team Foundation Server)',
  'CentOS Web Panel',
  'aaPanel',
  'SolarWinds',
  'CyberPanel',
  'Virtualmin',
  'Kubernetes',
  'XAMPP',
  'HestiaCP',
  'PRTG Network Monitor',
  'Node.js Server',
  'Zenoss',
  'GitLab CI/CD',
  'Bitbucket',
  'Fedora Server',
  'Sentora',
  'VMware vSphere',
  'ELK Stack',
  'Trac',
  'BlueOnyx',
  'Netdata',
  'Zulip',
  'Ansible',
  'Debian Server',
  'Apache HTTP Server',
  'Nginx Unit',
  'XenServer',
  'LibreNMS',
  'Bugzilla',
  'Munin',
  'WildFly (formerly JBoss)',
  'Rancher',
  'OpenNMS',
  'CircleCI',
  'ZPanel',
  'Grafana',
]

const basePath = '@/assets/marketplaceIntegrationsLogos'
