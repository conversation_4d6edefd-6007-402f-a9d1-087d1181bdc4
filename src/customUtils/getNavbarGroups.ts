const demoMenuItems = [
  {
    key: 'access',
    icon: 'carbon:connect',
    title: 'Access',
    items: [
      { key: 'Dashboard', title: 'Dashboard', icon: 'ic:round-dashboard', path: '/access-dashboard' },
      { key: 'shortcuts', title: 'Shortcuts', icon: 'carbon:lightning', path: '/shortcuts' },
      { key: 'portals', title: 'Portals', icon: 'carbon:connect', path: '/portals' },
      { key: 'resources', title: 'Resources', icon: 'carbon:software-resource-cluster', path: '/resources' },
    ],
  },
  {
    key: 'ndr',
    icon: 'carbon:content-delivery-network',
    title: 'Network',
    items: [
      { key: 'Dashboard', title: 'Dashboard', icon: 'ic:round-dashboard', path: '/ndr-dashboard' },
      { key: 'overview', title: 'Overview', icon: 'material-symbols:error-outline-rounded', path: '/ndr-overview' },
      { key: 'insights', title: 'Insights', icon: 'material-symbols:error-outline-rounded', path: '/insights' },
      {
        key: 'inventory',
        title: 'Inventory',
        icon: 'material-symbols:inventory-2-outline-rounded',
        path: '/inventory',
      },
      { key: 'explore', title: 'Explore', icon: 'material-symbols:explore-outline-rounded', path: '/explore' },
      { key: 'controls', title: 'Controls', icon: 'material-symbols:tune-rounded', path: '/controls' },
    ],
  },
  {
    key: 'hunterx',
    icon: 'carbon:content-delivery-network',
    title: 'HunterX',
    items: [
      { key: 'Dashboard', title: 'Dashboard', icon: 'ic:round-dashboard', path: '/ndr-dashboard' },
      { key: 'overview', title: 'Overview', icon: 'material-symbols:error-outline-rounded', path: '/ndr-overview' },
      { key: 'issues', title: 'Issues', icon: 'material-symbols:error-outline-rounded', path: '/issues' },
      {
        key: 'inventory',
        title: 'Inventory',
        icon: 'material-symbols:inventory-2-outline-rounded',
        path: '/inventory',
      },
      { key: 'explore', title: 'Explore', icon: 'material-symbols:explore-outline-rounded', path: '/explore' },
      { key: 'controls', title: 'Controls', icon: 'material-symbols:tune-rounded', path: '/controls' },
    ],
  },
  {
    key: 'scanner',
    icon: 'carbon:radar',
    title: 'External Attack Surface',
    items: [{ key: 'Scans', title: 'Scans', icon: 'carbon:list-boxes', path: '/scanner' }],
  },
  {
    key: 'organization',
    icon: 'icons8:organization',
    title: 'Organization',
    items: [
      {
        key: 'users',
        title: 'Users',
        icon: 'material-symbols:person-outline-rounded',
        path: '/users',
        count: 10,
      },
      {
        key: 'groups',
        title: 'Groups',
        icon: 'material-symbols:group-outline-rounded',
        path: '/groups',
        count: 2,
      },
      { key: 'integrations', title: 'Integrations', icon: 'ri:apps-2-add-line', path: '/integrations' },
      { key: 'jitAccess', title: 'JIT Access', icon: 'basil:lock-time-solid', path: '/jit-access' },
      { key: 'portflow', title: 'Portflow', icon: 'basil:lock-time-solid', path: '/port-flow' },
      {
        key: 'schedules',
        title: 'Schedules',
        icon: 'material-symbols:calendar-month-outline-rounded',
        path: '/schedules',
      },
      {
        key: 'activities',
        title: 'Activities',
        icon: 'material-symbols:group-outline-rounded',
        path: '/activities',
        count: 2,
      },
      { key: 'billing', title: 'Billing', icon: 'material-symbols:payments-outline-rounded', path: '/billing' },
      {
        key: 'settings',
        title: 'Settings',
        icon: 'material-symbols:settings-outline-rounded',
        path: '/settings',
      },
    ],
  },
]

const prodItems = {
  access: {
    key: 'access',
    icon: 'carbon:connect',
    title: 'Access',
    items: [
      { key: 'portals', title: 'Portals', icon: 'carbon:connect', path: '/portals' },
      { key: 'resources', title: 'Resources', icon: 'carbon:software-resource-cluster', path: '/resources' },
    ],
  },
  organization: {
    key: 'organization',
    icon: 'icons8:organization',
    title: 'Organization',
    items: [
      {
        key: 'groups',
        title: 'Groups',
        icon: 'material-symbols:group-outline-rounded',
        path: '/groups',
        count: 2,
      },
    ],
  },
}

const prodAdminItems = {
  access: {
    key: 'access',
    icon: 'carbon:connect',
    title: 'Access',
    items: [
      { key: 'portals', title: 'Portals', icon: 'carbon:connect', path: '/portals' },
      { key: 'resources', title: 'Resources', icon: 'carbon:software-resource-cluster', path: '/resources' },
      {
        key: 'Dashboard',
        title: 'Dashboard',
        icon: 'ic:round-dashboard',
        path: '/access-dashboard',
      },
    ],
  },
  organization: {
    key: 'organization',
    icon: 'icons8:organization',
    title: 'Organization',
    items: [
      {
        key: 'users',
        title: 'Users',
        icon: 'material-symbols:person-outline-rounded',
        path: '/users',
        count: 10,
      },
      {
        key: 'groups',
        title: 'Groups',
        icon: 'material-symbols:group-outline-rounded',
        path: '/groups',
        count: 2,
      },
      {
        key: 'activities',
        title: 'Activities',
        icon: 'material-symbols:group-outline-rounded',
        path: '/activities',
        count: 2,
      },
      // { key: 'integrations', title: 'Integrations', icon: 'ri:apps-2-add-line', path: '/integrations' },
    ],
  },
  ndr: {
    key: 'ndr',
    icon: 'carbon:content-delivery-network',
    title: 'ShieldX',
    items: [
      { key: 'ndr-dashboard', title: 'Dashboard', path: '/ndr-dashboard' },
      { key: 'overview', title: 'Overview', icon: 'material-symbols:error-outline-rounded', path: '/ndr-overview' },
      { key: 'insights', title: 'Insights', icon: 'material-symbols:error-outline-rounded', path: '/insights' },
      { key: 'port-flow', title: 'Graph', icon: 'material-symbols:error-outline-rounded', path: '/graph' },
      {
        key: 'events',
        title: 'Events',
        icon: 'material-symbols:error-outline-rounded',
        path: '/events',
      },
      {
        key: 'inventory',
        title: 'Inventory',
        icon: 'material-symbols:inventory-2-outline-rounded',
        path: '/inventory',
      },
      // {
      //   key: 'auto-policy',
      //   title: 'Auto Policy',
      //   icon: 'material-symbols:inventory-2-outline-rounded',
      //   path: '/auto-policy',
      // },
    ],
  },
  hunterx: {
    key: 'hunterx',
    icon: 'carbon:api',
    title: 'HunterX',
    items: [
      { key: 'hunterx-dashboard', title: 'Dashboard', path: '/hunterx-dashboard' },
      { key: 'port-flow', title: 'Graph', icon: 'material-symbols:error-outline-rounded', path: '/hunterx-graph' },
      {
        key: 'detections',
        title: 'Detections',
        icon: 'material-symbols:security-rounded',
        path: '/hunterx-detections',
      },
      {
        key: 'hunterx-detections',
        title: 'Controls',
        icon: 'material-symbols:security-rounded',
        path: '/hunterx-controls',
      },
    ],
  },
  scanner: {
    key: 'scanner',
    icon: 'carbon:radar',
    title: 'External Attack Surface',
    items: [{ key: 'Scans', title: 'Scans', icon: 'carbon:list-boxes', path: '/scanner' }],
  },
}

export function getNavbarObject(
  isMock: boolean,
  isAdmin: boolean,
  showAccessModule: boolean,
  showScannerModule: boolean,
  showOrganizationModule: boolean,
  showHunterXModule: boolean,
) {
  if (isMock) return demoMenuItems
  if (isAdmin) {
    const items = []

    if (showAccessModule) items.push(prodAdminItems.access)

    items.push(prodAdminItems.ndr)
    if (showHunterXModule) items.push(prodAdminItems.hunterx)

    if (showScannerModule) items.push(prodAdminItems.scanner)

    if (showOrganizationModule) items.push(prodAdminItems.organization)

    return items
  }

  const items = []
  if (showAccessModule) {
    items.push(prodItems.access)
  }
  items.push(prodItems.organization)
  return items
}
