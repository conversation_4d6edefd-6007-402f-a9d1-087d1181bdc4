import npmLogo from '@/assets/temp/npm-logo.png'
import { EntityType } from '@/firestoreQueries/trafficIssues/trafficIssuesTypes'
import awsLogo from '@/assets/integrationLogos/aws.png'
import awsVpcLogo from '@/assets/images/vpc-icon.png'
import msDefenderEndpointIcon from '@/assets/integrationLogos/msdefender.png'
import ec2Logo from '@/assets/images/EC2-logo.png'
import s3Logo from '@/assets/images/S3-logo.png'
import apiGatewayLogo from '@/assets/entitiesIcons/aws-api-gateway.png'
import cloudfrontLogo from '@/assets/entitiesIcons/aws-cloudfront.webp'
import cloud9Logo from '@/assets/entitiesIcons/aws-cloud9.png'
import dynamoDBLogo from '@/assets/entitiesIcons/aws-dynamodb.jpg'
import route53Logo from '@/assets/entitiesIcons/aws-route53.png'
import { Icon } from '@iconify/react'
import { ReactNode } from 'react'
import rdsLogo from '@/assets/integrationLogos/rdsLogo.png'
import { AWSInstanceInfo, AWSSecurityGroupInfo, BaseEntityType } from '@globalTypes/ndrBenchmarkTypes/entityTypes'
import awsEC2EntityIcon from '@/assets/entitiesIcons/amazon-ec2.png'
import awsEndpointIcon from '@/assets/integrationLogos/aws.png'
import awsRDSEntityIcon from '@/assets/entitiesIcons/aws-rds-instance.png'
import awsLoadBalancerEntityIcon from '@/assets/entitiesIcons/aws-load-balancer.png'
import awsLambdaFunctionEntityIcon from '@/assets/entitiesIcons/aws-lambda-function.png'
import awsSecurityGroupEntityIcon from '@/assets/entitiesIcons/aws-security-group.png'
import awsUnrecognizedNetworkInterfaceIcon from '@/assets/entitiesIcons/unrecognized_network_interface.png'
import gcpGlobalLoadBalancerIcon from '@/assets/entitiesIcons/gcp-cloud-load-balancing.png'
import gcpFirewallRuleIcon from '@/assets/entitiesIcons/gcp-cloud-firewall-rules.png'
import gcpCloudFunctionIcon from '@/assets/entitiesIcons/gcp-cloud-function.png'
import gcpVPCNetworkIcon from '@/assets/entitiesIcons/gcp-cloud-network.png'
import gcpCloudRunIcon from '@/assets/entitiesIcons/gcp-cloud-run.png'
import gcpCloudSQLIcon from '@/assets/entitiesIcons/gcp-cloud-sql.png'
import gcpGkeNodeIcon from '@/assets/entitiesIcons/gcp-gke-node.png'
import gcpComputeInstanceIcon from '@/assets/entitiesIcons/gpc-compute-instance.png'
import { FlowlogVPCNodeService } from '@/firestoreQueries/ndr/trafficPatterns/queries/getIssueTrafficPatterns'
import sentinelOne_agentIcon from '@/assets/entitiesIcons/sentinelone.png'
import crowdstrike_agentIcon from '@/assets/entitiesIcons/crowdstrike.png'
import { BiQuestionMark } from 'react-icons/bi'
import awsNetworkLoadBalancer from '@/assets/entitiesIcons/Res_Elastic-Load-Balancing_Network-Load-Balancer_48.png'
import awsVpcEndpoint from '@/assets/entitiesIcons/Res_Amazon-VPC_Endpoints_48.png'
import awsRdsImage from '@/assets/entitiesIcons/aws-rds-instance.png'
import awsVpcLink from '@/assets/entitiesIcons/aws_Amazon-API-Gateway_64.png'
import awsTransit from '@/assets/entitiesIcons/Arch_AWS-Transit-Gateway_64.png'
import awsElasticache from '@/assets/entitiesIcons/Res_Amazon-ElastiCache.png'
import awsElasticFileSystem from '@/assets/entitiesIcons/Res_Amazon-Elastic-File-System_EFS-Standard_48.png'
import awsEksNode from '@/assets/entitiesIcons/Arch_Amazon-EKS-Anywhere_48.png'
import awsAmazon from '@/assets/entitiesIcons/Arch_Amazon-EC2_48.png'
import awsAppFlow from '@/assets/entitiesIcons/Arch_Amazon-AppFlow_48.png'
import awsConnect from '@/assets/entitiesIcons/Arch_Amazon-Connect_48.png'
import awsApiGateway from '@/assets/entitiesIcons/aws-api-gateway.png'
import awsChime from '@/assets/entitiesIcons/Arch_Amazon-Chime_48.png'
import awsCloud9 from '@/assets/entitiesIcons/Arch_AWS-Cloud9_48.png'
import awsCloudFront from '@/assets/entitiesIcons/aws-cloudfront.webp'
import awsEdgeLocation from '@/assets/entitiesIcons/Res_Amazon-CloudFront_Edge-Location_48.png'
import awsCodeBuild from '@/assets/entitiesIcons/Arch_AWS-CodeBuild_48.png'
import awsDynamoDB from '@/assets/entitiesIcons/aws-dynamodb.jpg'
import awsInstanceConnect from '@/assets/entitiesIcons/EC2-instance-contents_32.png'
import awsAcc from '@/assets/entitiesIcons/Arch_AWS-Global-Accelerator_48.png'
import awsVideoStreaming from '@/assets/entitiesIcons/Arch_Amazon-Kinesis-Video-Streams_48.png'
import awsRoute53 from '@/assets/entitiesIcons/aws-route53.png'
import awsS3 from '@/assets/entitiesIcons/aws-s3-instance.png'
import _ from 'lodash'

export const entityImageMap: { [K in EntityType]: ReactNode } = {
  EC2: (
    <img
      src={ec2Logo}
      alt="EC2"
      style={{ width: 24, height: 24 }}
      className="object-contain rounded-full overflow-hidden"
    />
  ),
  RDS: (
    <img
      src={rdsLogo}
      alt="RDS"
      style={{ width: 24, height: 24 }}
      className="object-contain rounded-full overflow-hidden"
    />
  ),
  S3: (
    <img
      src={s3Logo}
      alt="S3"
      style={{ width: 24, height: 24 }}
      className="object-contain rounded-full overflow-hidden"
    />
  ),
  VPC_RESOURCE: (
    <img
      src={awsVpcLogo}
      alt="VPC"
      style={{ width: 24, height: 24 }}
      className="object-contain rounded-full overflow-hidden"
    />
  ),
  INTERNET: <Icon icon="mdi:internet" fontSize={24} />,
  PORT: <Icon icon="fluent:usb-port-20-filled" fontSize={24} />,
  SECURITY_GROUP: <Icon icon="f7:lock-shield-fill" fontSize={24} />,
  VOLUME: <Icon icon="mingcute:chip-fill" fontSize={24} />,
  // SentinelOne Entities
  sentinelOne_agent: (
    <img
      src={sentinelOne_agentIcon}
      alt="SentinelOne"
      style={{ width: 24, height: 24 }}
      className="object-contain rounded-full overflow-hidden"
    />
  ),
  ms_defender_endpoint: (
    <img
      src={msDefenderEndpointIcon}
      alt="MS Defender Endpoint"
      style={{ width: 24, height: 24 }}
      className="object-contain rounded-full overflow-hidden"
    />
  ),
}

export const entityNameMap: { [K in EntityType]: string } = {
  EC2: 'EC2 Instance',
  INTERNET: 'Internet',
  PORT: 'Port',
  RDS: 'RDS',
  S3: 'S3 Bucket',
  SECURITY_GROUP: 'Security Group',
  VOLUME: 'Volume',
  VPC_RESOURCE: 'VPC Resource',
  // SentinelOne Entities
  sentinelOne_agent: 'SentinelOne',
  ms_defender_endpoint: 'MS Defender Endpoint',
}

export const ndrEntityNameMap: { [K in BaseEntityType]: string } = {
  // GCP Entities
  gcp_cloud_function: 'GCP Cloud Function',
  gcp_cloud_run_service: 'GCP Cloud Run',
  gcp_cloud_sql_instance: 'GCP Cloud SQL',
  gcp_compute_instance: 'GCP Compute Instance',
  gcp_firewall_rule: 'GCP Firewall Rule',
  gcp_global_load_balancer: 'GCP Global Load Balancer',
  gcp_gke_node: 'GCP GKE Node',
  gcp_vpc_network: 'GCP VPC Network',
  ms_defender_endpoint: 'MS Defender Endpoint',

  // AWS Entities
  aws_vpc: 'AWS VPC',
  aws_ec2_application_load_balancer: 'AWS Load Balancer',
  aws_ec2_instance: 'EC2',
  aws_lambda_function: 'AWS Lambda',
  aws_rds_db_cluster: 'RDS',
  aws_security_group: 'AWS Security Group',
  unrecognized_network_interface: 'Network Interface',

  // Azure Entities
  azure_virtual_machine: 'Azure Virtual Machine',
  azure_sql_database: 'Azure SQL Database',
  azure_function_app: 'Azure Function App',
  azure_application_gateway: 'Azure Application Gateway',
  azure_network_security_group: 'Azure Network Security Group',
  azure_virtual_network: 'Azure Virtual Network',
  azure_kubernetes_service: 'Azure Kubernetes Service',
  azure_storage_account: 'Azure Storage Account',

  // Internet Entities
  internet: 'Internet',
  internet_scanner: 'Scanner',

  // SentinelOne Entities
  sentinelOne_agent: 'SentinelOne',

  // CrowdStrike Entities
  crowdstrike_agent: 'CrowdStrike',
  aws_endpoint: 'AWS',

  // Other
  none: 'None',

  // Unrecognized
  unrecognized: 'Unrecognized',
}

export type InfoKeys = keyof AWSInstanceInfo | keyof AWSSecurityGroupInfo | 'securityGroupIds'

export const ndrEntityInfotoTitlesMap: { [K in InfoKeys]: string } = {
  accountId: 'Account ID',
  networkInterfaceId: 'Network Interface ID',
  region: 'Region',
  availabilityZone: 'Availability Zone',
  vpcId: 'VPC ID',
  privateIpAddress: 'Private IP Address',
  publicIpAddress: 'Public IP Address',
  egress: 'Egress',
  ingress: 'Ingress',
  securityGroupIds: 'Security Group IDs',
  attachedInstanceARNs: 'Attached Instance ARNs',
  arn: 'ARN',
}

export const ndrEntityIconMap: { [K in BaseEntityType]: ReactNode } = {
  gcp_global_load_balancer: <img src={gcpGlobalLoadBalancerIcon} alt="Load Balancer" className="ndr-icon-24" />,
  gcp_vpc_network: <img src={gcpVPCNetworkIcon} alt="Load Balancer" className="ndr-icon-24" />,
  gcp_cloud_function: <img src={gcpCloudFunctionIcon} alt="Load Balancer" className="ndr-icon-24" />,
  gcp_cloud_run_service: <img src={gcpCloudRunIcon} alt="Load Balancer" className="ndr-icon-24" />,
  gcp_cloud_sql_instance: <img src={gcpCloudSQLIcon} alt="Load Balancer" className="ndr-icon-24" />,
  gcp_compute_instance: <img src={gcpComputeInstanceIcon} alt="Load Balancer" className="ndr-icon-24" />,
  gcp_firewall_rule: <img src={gcpFirewallRuleIcon} alt="Load Balancer" className="ndr-icon-24" />,
  gcp_gke_node: <img src={gcpGkeNodeIcon} alt="Load Balancer" className="ndr-icon-24" />,
  aws_ec2_application_load_balancer: (
    <img src={awsLoadBalancerEntityIcon} alt="Load Balancer" className="ndr-icon-24" />
  ),
  aws_vpc: <img src={awsVpcLogo} alt="VPC" className="ndr-icon-24" />,
  aws_ec2_instance: <img src={awsEC2EntityIcon} alt="EC2" className="ndr-icon-24" />,
  aws_lambda_function: <img src={awsLambdaFunctionEntityIcon} alt="Lambda" className="ndr-icon-24" />,
  aws_rds_db_cluster: <img src={awsRDSEntityIcon} alt="RDS" className="ndr-icon-24" />,
  aws_security_group: <img src={awsSecurityGroupEntityIcon} alt="Security Group" className="ndr-icon-24" />,
  none: <Icon icon="mdi:close" fontSize={24} />,
  internet: <Icon icon="mdi:internet" fontSize={24} />,
  internet_scanner: <Icon icon="tabler:world-search" fontSize={24} />,
  // SentinelOne Entities
  sentinelOne_agent: <img src={sentinelOne_agentIcon} alt="SentinelOne" className="ndr-icon-24" />,
  ms_defender_endpoint: <img src={msDefenderEndpointIcon} alt="MS Defender Endpoint" className="ndr-icon-24" />,
}

export const bwNdrEntityIconMap: { [K in BaseEntityType]: ReactNode } = {
  unrecognized: (
    <div style={{ filter: 'grayscale(100%)' }} className="ndr-icon-24 flex justify-center items-center">
      <BiQuestionMark className="h-full w-full" />
    </div>
  ),
  gcp_global_load_balancer: (
    <img
      src={gcpGlobalLoadBalancerIcon}
      alt="Load Balancer"
      style={{ filter: 'grayscale(100%)' }}
      className="ndr-icon-24"
    />
  ),
  gcp_vpc_network: (
    <img src={gcpVPCNetworkIcon} alt="Load Balancer" style={{ filter: 'grayscale(100%)' }} className="ndr-icon-24" />
  ),
  gcp_cloud_function: (
    <img src={gcpCloudFunctionIcon} alt="Load Balancer" style={{ filter: 'grayscale(100%)' }} className="ndr-icon-24" />
  ),
  gcp_cloud_run_service: (
    <img src={gcpCloudRunIcon} alt="Load Balancer" style={{ filter: 'grayscale(100%)' }} className="ndr-icon-24" />
  ),
  gcp_cloud_sql_instance: (
    <img src={gcpCloudSQLIcon} alt="Load Balancer" style={{ filter: 'grayscale(100%)' }} className="ndr-icon-24" />
  ),
  gcp_compute_instance: (
    <img
      src={gcpComputeInstanceIcon}
      alt="Load Balancer"
      style={{ filter: 'grayscale(100%)' }}
      className="ndr-icon-24"
    />
  ),
  gcp_firewall_rule: (
    <img src={gcpFirewallRuleIcon} alt="Load Balancer" style={{ filter: 'grayscale(100%)' }} className="ndr-icon-24" />
  ),
  gcp_gke_node: (
    <img src={gcpGkeNodeIcon} alt="Load Balancer" style={{ filter: 'grayscale(100%)' }} className="ndr-icon-24" />
  ),
  aws_ec2_application_load_balancer: (
    <img
      src={awsLoadBalancerEntityIcon}
      alt="Load Balancer"
      style={{ filter: 'grayscale(100%)' }}
      className="ndr-icon-24"
    />
  ),
  aws_vpc: <img src={awsVpcLogo} alt="VPC" style={{ filter: 'grayscale(100%)' }} className="ndr-icon-24" />,
  aws_ec2_instance: (
    <img src={awsEC2EntityIcon} alt="EC2" style={{ filter: 'grayscale(100%)' }} className="ndr-icon-24" />
  ),
  aws_lambda_function: (
    <img src={awsLambdaFunctionEntityIcon} alt="Lambda" style={{ filter: 'grayscale(100%)' }} className="ndr-icon-24" />
  ),
  aws_rds_db_cluster: (
    <img src={awsRDSEntityIcon} alt="RDS" style={{ filter: 'grayscale(100%)' }} className="ndr-icon-24" />
  ),
  aws_security_group: (
    <img
      src={awsSecurityGroupEntityIcon}
      alt="Security Group"
      style={{ filter: 'grayscale(100%)' }}
      className="ndr-icon-24"
    />
  ),

  aws_ec2_network_load_balancer: (
    <img
      src={awsNetworkLoadBalancer}
      alt="Network Load Balancer"
      style={{ filter: 'grayscale(100%)' }}
      className="ndr-icon-24"
    />
  ),

  aws_transit_gateway: (
    <img src={awsTransit} alt="AWS Transit Gateway" style={{ filter: 'grayscale(100%)' }} className="ndr-icon-24" />
  ),

  aws_elasticache_node: (
    <img
      src={awsElasticache}
      alt="AWS Elasticache Node"
      style={{ filter: 'grayscale(100%)' }}
      className="ndr-icon-24"
    />
  ),

  aws_ec2_gateway_load_balancer: (
    <img
      src={awsNetworkLoadBalancer}
      alt="Gateway Load Balancer"
      style={{ filter: 'grayscale(100%)' }}
      className="ndr-icon-24"
    />
  ),

  aws_vpc_endpoint: (
    <img src={awsVpcEndpoint} alt="VPC Endpoint" style={{ filter: 'grayscale(100%)' }} className="ndr-icon-24" />
  ),

  aws_rds_db_instance: (
    <img src={awsRdsImage} alt="RDS DB Instance" style={{ filter: 'grayscale(100%)' }} className="ndr-icon-24" />
  ),

  aws_nat_gateway: (
    <img src={apiGatewayLogo} alt="AWS NAT Gateway" style={{ filter: 'grayscale(100%)' }} className="ndr-icon-24" />
  ),

  aws_vpc_link: <img src={awsVpcLink} alt="VPC Link" style={{ filter: 'grayscale(100%)' }} className="ndr-icon-24" />,

  aws_elastic_filesystem: (
    <img
      src={awsElasticFileSystem}
      alt="AWS Elastic File System"
      style={{ filter: 'grayscale(100%)' }}
      className="ndr-icon-24"
    />
  ),

  aws_ec2_instance_eks_node: (
    <img src={awsEksNode} alt="EC2 Instance EKS Node" style={{ filter: 'grayscale(100%)' }} className="ndr-icon-24" />
  ),

  aws_ec2_redshift_cluster: (
    <img src={awsRDSEntityIcon} alt="RDS" style={{ filter: 'grayscale(100%)' }} className="ndr-icon-24" />
  ),

  aws: <img src={awsEC2EntityIcon} alt="EC2" style={{ filter: 'grayscale(100%)' }} className="ndr-icon-24" />,

  unrecognized_network_interface: (
    <img
      src={awsUnrecognizedNetworkInterfaceIcon}
      alt="Unrecognized Network Interface"
      style={{ filter: 'grayscale(100%)' }}
      className="ndr-icon-24"
    />
  ),

  //
  none: <Icon icon="mdi:close" fontSize={24} />,
  internet: <Icon icon="mdi:internet" fontSize={24} />,
  internet_scanner: <Icon icon="tabler:world-search" fontSize={24} />,

  // SentinelOne Entities
  sentinelOne_agent: <img src={sentinelOne_agentIcon} alt="SentinelOne" className="ndr-icon-24" />,
  crowdstrike_agent: <img src={crowdstrike_agentIcon} alt="CrowdStrike" className="ndr-icon-24" />,

  aws_endpoint: <img src={awsEndpointIcon} alt="AWS Endpoint" className="ndr-icon-24" />,
  aws_service: <img src={awsEndpointIcon} alt="AWS Service" className="ndr-icon-24" />,

  aws_service_amazon: <img src={awsAmazon} alt="Amazon" className="ndr-icon-24" />,
  aws_service_appflow: <img src={awsAppFlow} alt="Amazon AppFlow" className="ndr-icon-24" />,
  aws_service_connect: <img src={awsConnect} alt="Amazon Connect" className="ndr-icon-24" />,
  aws_service_api_gateway: <img src={awsApiGateway} alt="API Gateway" className="ndr-icon-24" />,
  aws_service_chime: <img src={awsChime} alt="Amazon Chime" className="ndr-icon-24" />,
  aws_service_cloud9: <img src={awsCloud9} alt="AWS Cloud9" className="ndr-icon-24" />,
  aws_service_cloudfront: <img src={awsCloudFront} alt="CloudFront" className="ndr-icon-24" />,
  aws_service_cloudfront_edge: <img src={awsEdgeLocation} alt="CloudFront Edge Location" className="ndr-icon-24" />,
  aws_service_codebuild: <img src={awsCodeBuild} alt="AWS CodeBuild" className="ndr-icon-24" />,
  aws_service_dynamodb: <img src={awsDynamoDB} alt="DynamoDB" className="ndr-icon-24" />,
  aws_service_instance_connect: <img src={awsInstanceConnect} alt="EC2 Instance Connect" className="ndr-icon-24" />,
  aws_service_global_accelerator: <img src={awsAcc} alt="Global Accelerator" className="ndr-icon-24" />,
  aws_service_kinesis_video: <img src={awsVideoStreaming} alt="Kinesis Video Streams" className="ndr-icon-24" />,
  aws_service_route53: <img src={awsRoute53} alt="Route 53" className="ndr-icon-24" />,
  aws_service_s3: <img src={awsS3} alt="S3" className="ndr-icon-24" />,
  ms_defender_endpoint: <img src={msDefenderEndpointIcon} alt="MS Defender Endpoint" className="ndr-icon-24" />,
}

export function getNDREntityIcon(trafficPattern: any, path: string, alternativePath = '') {
  const iconRef = _.get(trafficPattern, path)
  const chainedFallbackPathList = (alternativePath ?? '')
    .split('.')
    .map((key) => trafficPattern[key])
    .join('_')
    .toLowerCase()

  const IconComponent = bwNdrEntityIconMap?.[chainedFallbackPathList] || bwNdrEntityIconMap?.[iconRef]

  return IconComponent
}

export const iconImage = (src: string, alt: string) => {
  return <img src={src} alt={alt} className="h-5 w-5 object-contain rounded-full bg-white" />
}

export const inventoryEntityMap: { [K in BaseEntityType]: ReactNode } = {
  gcp_cloud_function: iconImage(gcpCloudFunctionIcon, 'GCP Cloud Network'),
  gcp_cloud_run_service: iconImage(gcpCloudRunIcon, 'GCP Cloud Run'),
  gcp_cloud_sql_instance: iconImage(gcpCloudSQLIcon, 'GCP Cloud SQL'),
  gcp_compute_instance: iconImage(gcpComputeInstanceIcon, 'GCP Compute Instance'),
  gcp_firewall_rule: iconImage(gcpFirewallRuleIcon, 'GCP Firewall Rule'),
  gcp_global_load_balancer: iconImage(gcpGlobalLoadBalancerIcon, 'GCP Global Load Balancer'),
  gcp_gke_node: iconImage(gcpGkeNodeIcon, 'GCP GKE Node'),
  gcp_vpc_network: iconImage(gcpVPCNetworkIcon, 'GCP VPC Network'),
  aws_vpc: iconImage(awsVpcLogo, 'AWS VPC'),
  aws_ec2_application_load_balancer: iconImage(awsLoadBalancerEntityIcon, 'AWS Load Balancer'),
  aws_ec2_instance: iconImage(awsEC2EntityIcon, 'EC2'),
  aws_lambda_function: iconImage(awsLambdaFunctionEntityIcon, 'AWS Lambda'),
  aws_rds_db_cluster: iconImage(awsRDSEntityIcon, 'RDS'),
  aws_security_group: iconImage(awsSecurityGroupEntityIcon, 'AWS Security Group'),
  aws_transit_gateway: iconImage(awsTransit, 'AWS Transit Gateway'),
  aws_elasticache_node: iconImage(awsElasticache, 'AWS Elasticache Node'),
  unrecognized_network_interface: iconImage(awsUnrecognizedNetworkInterfaceIcon, 'Unrecognized Network Interface'),
  none: <Icon icon="mdi:close" fontSize={24} />,
  internet: <Icon icon="mdi:internet" fontSize={24} />,
  internet_scanner: <Icon icon="tabler:world-search" fontSize={24} />,
  // SentinelOne Entities
  sentinelOne_agent: iconImage(sentinelOne_agentIcon, 'sentinelOne_agent'),
  crowdstrike_agent: iconImage(crowdstrike_agentIcon, 'crowdstrike_agent'),
  aws_endpoint: iconImage(awsEndpointIcon, 'aws_endpoint'),
  aws: iconImage(awsEC2EntityIcon, 'EC2'),
  aws_ec2_redshift_cluster: iconImage(awsRDSEntityIcon, 'RDS'),
  aws_ec2_network_load_balancer: iconImage(awsNetworkLoadBalancer, 'Network Load Balancer'),
  aws_ec2_gateway_load_balancer: iconImage(awsNetworkLoadBalancer, 'Gateway Load Balancer'),
  aws_vpc_endpoint: iconImage(awsVpcEndpoint, 'VPC Endpoint'),
  aws_rds_db_instance: iconImage(awsRdsImage, 'RDS DB Instance'),
  aws_nat_gateway: iconImage(apiGatewayLogo, 'AWS NAT Gateway'),
  aws_vpc_link: iconImage(awsVpcLink, 'VPC Link'),
  aws_elastic_filesystem: iconImage(awsElasticFileSystem, 'aws_elastic_filesystem'),
  aws_ec2_instance_eks_node: iconImage(awsEksNode, 'EC2 Instance EKS Node'),
  ms_defender_endpoint: iconImage(msDefenderEndpointIcon, 'MS Defender Endpoint'),
}

const bwIconImage = (src: string, alt: string) => {
  return (
    <img
      src={src}
      alt={alt}
      className="h-5 w-5 object-contain rounded-full bg-white "
      style={{ filter: 'grayscale(100%)' }}
    />
  )
}

export const bwIventoryEntityMap: { [K in BaseEntityType]: ReactNode } = {
  gcp_cloud_function: bwIconImage(gcpCloudFunctionIcon, 'GCP Cloud Network'),
  gcp_cloud_run_service: bwIconImage(gcpCloudRunIcon, 'GCP Cloud Run'),
  gcp_cloud_sql_instance: bwIconImage(gcpCloudSQLIcon, 'GCP Cloud SQL'),
  gcp_compute_instance: bwIconImage(gcpComputeInstanceIcon, 'GCP Compute Instance'),
  gcp_firewall_rule: bwIconImage(gcpFirewallRuleIcon, 'GCP Firewall Rule'),
  gcp_global_load_balancer: bwIconImage(gcpGlobalLoadBalancerIcon, 'GCP Global Load Balancer'),
  gcp_gke_node: bwIconImage(gcpGkeNodeIcon, 'GCP GKE Node'),
  gcp_vpc_network: bwIconImage(gcpVPCNetworkIcon, 'GCP VPC Network'),
  aws_vpc: bwIconImage(awsVpcLogo, 'AWS VPC'),
  aws_ec2_application_load_balancer: bwIconImage(awsLoadBalancerEntityIcon, 'AWS Load Balancer'),
  aws_ec2_instance: bwIconImage(awsEC2EntityIcon, 'EC2'),
  aws_lambda_function: bwIconImage(awsLambdaFunctionEntityIcon, 'AWS Lambda'),
  aws_rds_db_cluster: bwIconImage(awsRDSEntityIcon, 'RDS'),
  aws_security_group: bwIconImage(awsSecurityGroupEntityIcon, 'AWS Security Group'),
  none: <Icon icon="mdi:close" style={{ filter: 'grayscale(100%)' }} fontSize={24} />,
  internet: <Icon icon="mdi:internet" style={{ filter: 'grayscale(100%)' }} fontSize={24} />,
  internet_scanner: <Icon icon="tabler:world-search" style={{ filter: 'grayscale(100%)' }} fontSize={24} />,
  // SentinelOne Entities (black-and-white)
  sentinelOne_agent: bwIconImage(sentinelOne_agentIcon, 'sentinelOne_agent'),
  ms_defender_endpoint: bwIconImage(msDefenderEndpointIcon, 'MS Defender Endpoint'),
}

export const flowlogNodesIcons: { [K in FlowlogVPCNodeService]: string } = {
  alb: awsLambdaFunctionEntityIcon,
  ec2: ec2Logo,
  ec2_connect: ec2Logo,
  s3: s3Logo,
  lambda: awsLambdaFunctionEntityIcon,
  generic_amazon: awsLogo,
  vpc: awsVpcLogo,
  npm: npmLogo,
  rds: rdsLogo,
  api_gateway: apiGatewayLogo,
  cloud9: cloud9Logo,
  cloudfront: cloudfrontLogo,
  dynamodb: dynamoDBLogo,
  route53: route53Logo,
  internet: '',
  scanner: '',
  none: '',
}
