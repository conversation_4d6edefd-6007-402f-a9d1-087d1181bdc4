import { RolesType, RoleType } from '@/firestoreQueries/utils/generalTypes'

export function getMemberRole(id: string, roles?: RolesType): RoleType | undefined {
  if (!roles) return undefined
  if (roles.Editors.includes(id)) return 'Editor'
  if (roles.Owners.includes(id)) return 'Owner'
  if (roles.Viewers.includes(id)) return 'Viewer'
  return undefined
}

export function getPermittedIds(roles: RolesType): string[] {
  return [...roles.Owners, ...roles.Editors, ...roles.Viewers]
}
