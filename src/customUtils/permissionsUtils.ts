import { DefaultRoleType, RolesType } from '@/firestoreQueries/utils/generalTypes'

export function getUserRole(
  defaultRole: DefaultRoleType | undefined,
  roles: RolesType | undefined,
  id: string,
  groupsIds: Array<string>,
): DefaultRoleType | undefined {
  if (!roles) return undefined
  if (roles['Owners'].includes(id)) return 'Owner'
  if (roles['Owners'].some((ownerId: string) => groupsIds.includes(ownerId))) return 'Owner'
  if (defaultRole === 'Owner') return 'Owner'

  if (roles['Editors'].includes(id)) return 'Editor'
  if (roles['Editors'].some((editorId: string) => groupsIds.includes(editorId))) return 'Editor'
  if (defaultRole === 'Editor') return 'Editor'

  if (roles['Viewers'].includes(id)) return 'Viewer'
  if (roles['Viewers'].some((viewerId: string) => groupsIds.includes(viewerId))) return 'Viewer'
  if (defaultRole === 'Viewer') return 'Viewer'

  if (defaultRole === 'Requester') return 'Requester'
  if (defaultRole === 'Hidden') return 'Hidden'

  return undefined
}

// from top to bottom: Owner, Editor, Viewer
export function getUserRoleForPortal(
  defaultRole: DefaultRoleType | undefined,
  portalRole: DefaultRoleType | undefined,
  resourceRoles: RolesType | undefined,
  userId: string,
  groupsIds: Array<string>,
) {
  const resourceRole = getUserRole(defaultRole, resourceRoles, userId, groupsIds)
  if (portalRole === 'Owner' || resourceRole === 'Owner') return 'Owner'
  if (portalRole === 'Editor' || resourceRole === 'Editor') return 'Editor'
  if (portalRole === 'Viewer' || resourceRole === 'Viewer') return 'Viewer'
  return undefined
}
