import { AvailableIntegrations } from '@/firestoreQueries/integrations/IntegrationTypes'
import { BaseEntity, BaseEntityType, CloudEntity } from '@globalTypes/ndrBenchmarkTypes/entityTypes'

export function getEntityCloudProvider(entityType: BaseEntityType): AvailableIntegrations | undefined {
  switch (entityType) {
    case 'aws_ec2_application_load_balancer':
    case 'aws_ec2_instance':
    case 'aws_lambda_function':
    case 'aws_rds_db_cluster':
    case 'aws_security_group':
    case 'aws_service':
    case 'aws_vpc':
    case 'aws_account':
    case 'aws_region':
    case 'aws_subnet':
      return 'aws'
    case 'azure_application_gateway':
    case 'azure_function_app':
    case 'azure_kubernetes_service':
    case 'azure_network_security_group':
    case 'azure_sql_database':
    case 'azure_storage_account':
    case 'azure_virtual_machine':
    case 'azure_virtual_network':
      return 'azure'
    case 'gcp_cloud_function':
    case 'gcp_cloud_run_service':
    case 'gcp_cloud_sql_instance':
    case 'gcp_compute_instance':
    case 'gcp_firewall_rule':
    case 'gcp_gke_node':
    case 'gcp_global_load_balancer':
    case 'gcp_vpc_network':
      return 'gcp'
    case 'sentinelOne_agent':
      return 'sentinelOne'
    case 'crowdstrike_agent':
      return 'crowdstrike'
    case 'ms_defender_endpoint':
      return 'msDefender'
    case 'internet':
    case 'internet_scanner':
      return undefined
    case 'none':
      return undefined
  }
}

export function getEntityId(entity: BaseEntity) {
  const cloudProvider = getEntityCloudProvider(entity.type)
  switch (cloudProvider) {
    case 'aws': {
      const length = (entity as CloudEntity).id.split('/').length
      return (entity as CloudEntity).id.split('/')[length - 1]
    }
    case 'azure':
    case 'gcp':
      return (entity as CloudEntity).id
    case 'crowdstrike':
    case 'msDefender':
    case 'sentinelOne':
      return (entity as BaseEntity).id
    default:
      ''
  }
}
