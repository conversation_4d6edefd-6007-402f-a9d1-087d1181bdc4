import { SlotsToClasses } from '@nextui-org/react'

type TableSlots = SlotsToClasses<
    | 'base'
    | 'table'
    | 'tbody'
    | 'td'
    | 'tfoot'
    | 'th'
    | 'thead'
    | 'tr'
    | 'wrapper'
    | 'sortIcon'
    | 'emptyWrapper'
    | 'loadingWrapper'
>

export const tableClassNames: TableSlots = {
    base: `h-full overflow-y-scroll`,
    wrapper: 'p-0',
}

export const infTableStyles: {
    container: {
        className: string
        style: {
            compact: React.CSSProperties
            midSize: React.CSSProperties
            fullSize: React.CSSProperties
        }
    }
    table: TableSlots
} = {
    container: {
        className: 'overflow-hidden shadow-primary-shadow rounded-tertiary bg-white dark:bg-[#111628]',
        style: {
            fullSize: {
                height: `calc(100vh / 1.45)`,
            },
            midSize: {
                height: 'calc(100vh / 1.6)',
            },
            compact: {
                height: 'calc(100vh / 2.8)',
            },
        },
    },
    table: tableClassNames,
}

export const newContainerClassName = 'bg-white dark:bg-dark-blue w-full pt-4 rounded-primary'
