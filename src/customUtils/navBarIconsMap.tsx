import { ReactNode, SVGProps } from 'react'

function SettingsIcon(props: SVGProps<SVGSVGElement>) {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" {...props}>
            <path
                fill="currentColor"
                d="M10.825 22q-.675 0-1.162-.45t-.588-1.1L8.85 18.8q-.325-.125-.612-.3t-.563-.375l-1.55.65q-.625.275-1.25.05t-.975-.8l-1.175-2.05q-.35-.575-.2-1.225t.675-1.075l1.325-1Q4.5 12.5 4.5 12.337v-.675q0-.162.025-.337l-1.325-1Q2.675 9.9 2.525 9.25t.2-1.225L3.9 5.975q.35-.575.975-.8t1.25.05l1.55.65q.275-.2.575-.375t.6-.3l.225-1.65q.1-.65.588-1.1T10.825 2h2.35q.675 0 1.163.45t.587 1.1l.225 1.65q.325.125.613.3t.562.375l1.55-.65q.625-.275 1.25-.05t.975.8l1.175 2.05q.35.575.2 1.225t-.675 1.075l-1.325 1q.025.175.025.338v.674q0 .163-.05.338l1.325 1q.525.425.675 1.075t-.2 1.225l-1.2 2.05q-.35.575-.975.8t-1.25-.05l-1.5-.65q-.275.2-.575.375t-.6.3l-.225 1.65q-.1.65-.587 1.1t-1.163.45zM11 20h1.975l.35-2.65q.775-.2 1.438-.587t1.212-.938l2.475 1.025l.975-1.7l-2.15-1.625q.125-.35.175-.737T17.5 12t-.05-.787t-.175-.738l2.15-1.625l-.975-1.7l-2.475 1.05q-.55-.575-1.212-.962t-1.438-.588L13 4h-1.975l-.35 2.65q-.775.2-1.437.588t-1.213.937L5.55 7.15l-.975 1.7l2.15 1.6q-.125.375-.175.75t-.05.8q0 .4.05.775t.175.75l-2.15 1.625l.975 1.7l2.475-1.05q.55.575 1.213.963t1.437.587zm1.05-4.5q1.45 0 2.475-1.025T15.55 12t-1.025-2.475T12.05 8.5q-1.475 0-2.487 1.025T8.55 12t1.013 2.475T12.05 15.5M12 12"
            ></path>
        </svg>
    )
}

function BillingsIcon(props: SVGProps<SVGSVGElement>) {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" {...props}>
            <path
                fill="currentColor"
                d="M14 13q-1.25 0-2.125-.875T11 10t.875-2.125T14 7t2.125.875T17 10t-.875 2.125T14 13m-7 3q-.825 0-1.412-.587T5 14V6q0-.825.588-1.412T7 4h14q.825 0 1.413.588T23 6v8q0 .825-.587 1.413T21 16zm2-2h10q0-.825.588-1.412T21 12V8q-.825 0-1.412-.587T19 6H9q0 .825-.587 1.413T7 8v4q.825 0 1.413.588T9 14m10 6H3q-.825 0-1.412-.587T1 18V8q0-.425.288-.712T2 7t.713.288T3 8v10h16q.425 0 .713.288T20 19t-.288.713T19 20M7 14V6z"
            ></path>
        </svg>
    )
}

function SchedulesIcon(props: SVGProps<SVGSVGElement>) {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" {...props}>
            <path
                fill="currentColor"
                d="M5 22q-.825 0-1.412-.587T3 20V6q0-.825.588-1.412T5 4h1V3q0-.425.288-.712T7 2t.713.288T8 3v1h8V3q0-.425.288-.712T17 2t.713.288T18 3v1h1q.825 0 1.413.588T21 6v14q0 .825-.587 1.413T19 22zm0-2h14V10H5zM5 8h14V6H5zm0 0V6zm7 6q-.425 0-.712-.288T11 13t.288-.712T12 12t.713.288T13 13t-.288.713T12 14m-4 0q-.425 0-.712-.288T7 13t.288-.712T8 12t.713.288T9 13t-.288.713T8 14m8 0q-.425 0-.712-.288T15 13t.288-.712T16 12t.713.288T17 13t-.288.713T16 14m-4 4q-.425 0-.712-.288T11 17t.288-.712T12 16t.713.288T13 17t-.288.713T12 18m-4 0q-.425 0-.712-.288T7 17t.288-.712T8 16t.713.288T9 17t-.288.713T8 18m8 0q-.425 0-.712-.288T15 17t.288-.712T16 16t.713.288T17 17t-.288.713T16 18"
            ></path>
        </svg>
    )
}

function JITAccessIcon(props: SVGProps<SVGSVGElement>) {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" {...props}>
            <path
                fill="currentColor"
                fillRule="evenodd"
                d="m5.306 6.758l.347 3.122l-.476.042a2.39 2.39 0 0 0-2.154 2.028a24.1 24.1 0 0 0 0 7.1a2.39 2.39 0 0 0 2.154 2.028l1.134.1q1.831.16 3.668.162c.175 0 .266-.212.154-.346a7.002 7.002 0 0 1 3.947-11.35a.42.42 0 0 0 .333-.357l.28-2.529q.062-.548 0-1.095l-.022-.205a4.7 4.7 0 0 0-9.342 0l-.023.205a5 5 0 0 0 0 1.095M10.374 2.8A3.2 3.2 0 0 0 6.82 5.624l-.023.205a3.5 3.5 0 0 0 0 .764l.352 3.164a42 42 0 0 1 5.702 0l.352-3.164a3.5 3.5 0 0 0 0-.764l-.023-.205a3.2 3.2 0 0 0-2.806-2.825"
                clipRule="evenodd"
            ></path>
            <path
                fill="currentColor"
                d="M16.25 15a.75.75 0 0 0-1.5 0v1.773c0 .24.115.465.309.606l1 .727a.75.75 0 1 0 .882-1.213l-.691-.502z"
            ></path>
            <path
                fill="currentColor"
                fillRule="evenodd"
                d="M15.5 22a5.5 5.5 0 1 0 0-11a5.5 5.5 0 0 0 0 11m0-1.5a4 4 0 1 0 0-8a4 4 0 0 0 0 8"
                clipRule="evenodd"
            ></path>
        </svg>
    )
}

function IntegrationsIcon(props: SVGProps<SVGSVGElement>) {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" {...props}>
            <path
                fill="currentColor"
                d="M2.5 7a4.5 4.5 0 1 0 9 0a4.5 4.5 0 0 0-9 0m0 10a4.5 4.5 0 1 0 9 0a4.5 4.5 0 0 0-9 0m10 0a4.5 4.5 0 1 0 9 0a4.5 4.5 0 0 0-9 0m-3-10a2.5 2.5 0 1 1-5 0a2.5 2.5 0 0 1 5 0m0 10a2.5 2.5 0 1 1-5 0a2.5 2.5 0 0 1 5 0m10 0a2.5 2.5 0 1 1-5 0a2.5 2.5 0 0 1 5 0M16 11V8h-3V6h3V3h2v3h3v2h-3v3z"
            ></path>
        </svg>
    )
}

function GroupsIcon(props: SVGProps<SVGSVGElement>) {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" {...props}>
            <path
                fill="currentColor"
                d="M1 17.2q0-.85.438-1.562T2.6 14.55q1.55-.775 3.15-1.162T9 13t3.25.388t3.15 1.162q.725.375 1.163 1.088T17 17.2v.8q0 .825-.587 1.413T15 20H3q-.825 0-1.412-.587T1 18zM21 20h-2.55q.275-.45.413-.962T19 18v-1q0-1.1-.612-2.113T16.65 13.15q1.275.15 2.4.513t2.1.887q.9.5 1.375 1.112T23 17v1q0 .825-.587 1.413T21 20M9 12q-1.65 0-2.825-1.175T5 8t1.175-2.825T9 4t2.825 1.175T13 8t-1.175 2.825T9 12m10-4q0 1.65-1.175 2.825T15 12q-.275 0-.7-.062t-.7-.138q.675-.8 1.038-1.775T15 8t-.362-2.025T13.6 4.2q.35-.125.7-.163T15 4q1.65 0 2.825 1.175T19 8M3 18h12v-.8q0-.275-.137-.5t-.363-.35q-1.35-.675-2.725-1.012T9 15t-2.775.338T3.5 16.35q-.225.125-.363.35T3 17.2zm6-8q.825 0 1.413-.587T11 8t-.587-1.412T9 6t-1.412.588T7 8t.588 1.413T9 10m0-2"
            ></path>
        </svg>
    )
}

function UsersIcon(props: SVGProps<SVGSVGElement>) {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" {...props}>
            <path
                fill="currentColor"
                d="M12 12q-1.65 0-2.825-1.175T8 8t1.175-2.825T12 4t2.825 1.175T16 8t-1.175 2.825T12 12m-8 6v-.8q0-.85.438-1.562T5.6 14.55q1.55-.775 3.15-1.162T12 13t3.25.388t3.15 1.162q.725.375 1.163 1.088T20 17.2v.8q0 .825-.587 1.413T18 20H6q-.825 0-1.412-.587T4 18m2 0h12v-.8q0-.275-.137-.5t-.363-.35q-1.35-.675-2.725-1.012T12 15t-2.775.338T6.5 16.35q-.225.125-.363.35T6 17.2zm6-8q.825 0 1.413-.587T14 8t-.587-1.412T12 6t-1.412.588T10 8t.588 1.413T12 10m0 8"
            ></path>
        </svg>
    )
}

function OrganizationsIcon(props: SVGProps<SVGSVGElement>) {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" {...props}>
            <path
                fill="currentColor"
                d="M4 3v26h11v-4h2v4h11V3zm2 2h20v22h-7v-4h-6v4H6zm2 2v2h4V7zm6 0v2h4V7zm6 0v2h4V7zM8 11v2h4v-2zm6 0v2h4v-2zm6 0v2h4v-2zM8 15v2h4v-2zm6 0v2h4v-2zm6 0v2h4v-2zM8 19v2h4v-2zm6 0v2h4v-2zm6 0v2h4v-2zM8 23v2h4v-2zm12 0v2h4v-2z"
            ></path>
        </svg>
    )
}

function ScansIcon(props: SVGProps<SVGSVGElement>) {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" {...props}>
            <path
                fill="currentColor"
                d="M16 8h14v2H16zm0 14h14v2H16zm-6-8H4a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v6a2 2 0 0 1-2 2M4 6v6h6.001L10 6zm6 22H4a2 2 0 0 1-2-2v-6a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v6a2 2 0 0 1-2 2m-6-8v6h6.001L10 20z"
            ></path>
        </svg>
    )
}

function ExternalAttackSurfaceIcon(props: SVGProps<SVGSVGElement>) {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" {...props}>
            <path
                fill="currentColor"
                d="M30 3.414L28.586 2L15.293 15.293a1 1 0 0 0 1.414 1.414l4.18-4.18A5.996 5.996 0 1 1 16 10V8a8.011 8.011 0 1 0 6.316 3.098l2.847-2.847A11.88 11.88 0 0 1 28 16A12 12 0 1 1 16 4V2a14 14 0 1 0 14 14a13.86 13.86 0 0 0-3.422-9.164Z"
            ></path>
        </svg>
    )
}

function ControlsIcon(props: SVGProps<SVGSVGElement>) {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" {...props}>
            <path
                fill="currentColor"
                d="M12 21q-.425 0-.712-.288T11 20v-4q0-.425.288-.712T12 15t.713.288T13 16v1h7q.425 0 .713.288T21 18t-.288.713T20 19h-7v1q0 .425-.288.713T12 21m-8-2q-.425 0-.712-.288T3 18t.288-.712T4 17h4q.425 0 .713.288T9 18t-.288.713T8 19zm4-4q-.425 0-.712-.288T7 14v-1H4q-.425 0-.712-.288T3 12t.288-.712T4 11h3v-1q0-.425.288-.712T8 9t.713.288T9 10v4q0 .425-.288.713T8 15m4-2q-.425 0-.712-.288T11 12t.288-.712T12 11h8q.425 0 .713.288T21 12t-.288.713T20 13zm4-4q-.425 0-.712-.288T15 8V4q0-.425.288-.712T16 3t.713.288T17 4v1h3q.425 0 .713.288T21 6t-.288.713T20 7h-3v1q0 .425-.288.713T16 9M4 7q-.425 0-.712-.288T3 6t.288-.712T4 5h8q.425 0 .713.288T13 6t-.288.713T12 7z"
            ></path>
        </svg>
    )
}

function ExploreIcon(props: SVGProps<SVGSVGElement>) {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" {...props}>
            <path
                fill="currentColor"
                d="M12 13q-.425 0-.712-.288T11 12t.288-.712T12 11t.713.288T13 12t-.288.713T12 13m0 9q-2.075 0-3.9-.788t-3.175-2.137T2.788 15.9T2 12t.788-3.9t2.137-3.175T8.1 2.788T12 2t3.9.788t3.175 2.137T21.213 8.1T22 12t-.788 3.9t-2.137 3.175t-3.175 2.138T12 22m0-2q3.35 0 5.675-2.325T20 12t-2.325-5.675T12 4T6.325 6.325T4 12t2.325 5.675T12 20m-4.575-2.925l6.25-2.925q.15-.075.275-.2t.2-.275l2.925-6.25q.125-.25-.062-.438t-.438-.062l-6.25 2.925q-.15.075-.275.2t-.2.275l-2.925 6.25q-.125.25.063.438t.437.062"
            ></path>
        </svg>
    )
}

function RecommendationsIcon(props: SVGProps<SVGSVGElement>) {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" {...props}>
            <path
                fill="currentColor"
                d="M12 22q-.825 0-1.412-.587T10 20h4q0 .825-.587 1.413T12 22m-3-3q-.425 0-.712-.288T8 18t.288-.712T9 17h6q.425 0 .713.288T16 18t-.288.713T15 19zm-.75-3q-1.725-1.025-2.738-2.75T4.5 9.5q0-3.125 2.188-5.312T12 2t5.313 2.188T19.5 9.5q0 2.025-1.012 3.75T15.75 16zm.6-2h6.3q1.125-.8 1.738-1.975T17.5 9.5q0-2.3-1.6-3.9T12 4T8.1 5.6T6.5 9.5q0 1.35.613 2.525T8.85 14M12 14"
            ></path>
        </svg>
    )
}

function IssuesIcon(props: SVGProps<SVGSVGElement>) {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" {...props}>
            <path
                fill="currentColor"
                d="M12 17q.425 0 .713-.288T13 16t-.288-.712T12 15t-.712.288T11 16t.288.713T12 17m0-4q.425 0 .713-.288T13 12V8q0-.425-.288-.712T12 7t-.712.288T11 8v4q0 .425.288.713T12 13m0 9q-2.075 0-3.9-.788t-3.175-2.137T2.788 15.9T2 12t.788-3.9t2.137-3.175T8.1 2.788T12 2t3.9.788t3.175 2.137T21.213 8.1T22 12t-.788 3.9t-2.137 3.175t-3.175 2.138T12 22m0-2q3.35 0 5.675-2.325T20 12t-2.325-5.675T12 4T6.325 6.325T4 12t2.325 5.675T12 20m0-8"
            ></path>
        </svg>
    )
}

function NdrDashboardIcon(props: SVGProps<SVGSVGElement>) {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" {...props}>
            <path
                fill="currentColor"
                d="M4 13h6c.55 0 1-.45 1-1V4c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1v8c0 .55.45 1 1 1m0 8h6c.55 0 1-.45 1-1v-4c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1v4c0 .55.45 1 1 1m10 0h6c.55 0 1-.45 1-1v-8c0-.55-.45-1-1-1h-6c-.55 0-1 .45-1 1v8c0 .55.45 1 1 1M13 4v4c0 .55.45 1 1 1h6c.55 0 1-.45 1-1V4c0-.55-.45-1-1-1h-6c-.55 0-1 .45-1 1"
            ></path>
        </svg>
    )
}

function NdrIcon(props: SVGProps<SVGSVGElement>) {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" {...props}>
            <circle cx={21} cy={21} r={2} fill="currentColor"></circle>
            <circle cx={7} cy={7} r={2} fill="currentColor"></circle>
            <path
                fill="currentColor"
                d="M27 31a4 4 0 1 1 4-4a4.01 4.01 0 0 1-4 4m0-6a2 2 0 1 0 2 2a2.006 2.006 0 0 0-2-2"
            ></path>
            <path
                fill="currentColor"
                d="M30 16A14.04 14.04 0 0 0 16 2a13.04 13.04 0 0 0-6.8 1.8l1.1 1.7a24 24 0 0 1 2.4-1A25.1 25.1 0 0 0 10 15H4a11.15 11.15 0 0 1 1.4-4.7L3.9 9A13.84 13.84 0 0 0 2 16a14 14 0 0 0 14 14a13.4 13.4 0 0 0 5.2-1l-.6-1.9a11.44 11.44 0 0 1-5.2.9A21.07 21.07 0 0 1 12 17h17.9a3.4 3.4 0 0 0 .1-1M12.8 27.6a13 13 0 0 1-5.3-3.1A12.5 12.5 0 0 1 4 17h6a25 25 0 0 0 2.8 10.6M12 15a21.45 21.45 0 0 1 3.3-11h1.4A21.45 21.45 0 0 1 20 15Zm10 0a23.3 23.3 0 0 0-2.8-10.6A12.09 12.09 0 0 1 27.9 15Z"
            ></path>
        </svg>
    )
}

function HunterXIcon(props: SVGProps<SVGSVGElement>) {
    return (
       <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 512 512"><path fill="currentColor" d="M27.48 25.695C37 62.802 51.945 100.233 69.07 137.86c17.496-31.598 41.214-52.96 71.563-70.473C102.823 50.575 65.097 36.27 27.48 25.695m456.24 0c-37.62 10.575-75.347 24.88-113.156 41.692c30.35 17.514 54.067 38.875 71.563 70.472c17.125-37.627 32.07-75.058 41.592-112.165zm-367.1 81.315a146 146 0 0 0-10.224 10.117L232.12 242.85l10.257-10.243zm277.956 0L28.018 473.11l10.54 10.26L404.8 117.126a145 145 0 0 0-10.224-10.117zm-138.963 26.81c-24.338 0-47.014 7.245-65.998 19.682l13.494 13.477c15.33-9.19 33.285-14.472 52.503-14.472c19.214 0 37.16 5.28 52.483 14.465l13.492-13.477c-18.975-12.433-41.64-19.676-65.975-19.676zm-.004 45.08a75.1 75.1 0 0 0-32.967 7.588l14.246 14.23a57.2 57.2 0 0 1 18.72-3.138c6.56 0 12.848 1.11 18.702 3.13l14.25-14.228a75.1 75.1 0 0 0-32.953-7.582zm102.27 11.58l-13.556 13.55c8.464 14.877 13.297 32.102 13.297 50.488c0 19.172-5.255 37.087-14.403 52.392l13.496 13.48c12.386-18.958 19.598-41.59 19.598-65.872c0-23.51-6.76-45.467-18.43-64.04zm-204.56 0c-11.677 18.573-18.443 40.527-18.443 64.038c0 24.282 7.217 46.912 19.61 65.87l13.493-13.478c-9.154-15.305-14.416-33.22-14.416-52.392c0-18.386 4.838-35.61 13.307-50.487zm171.315 33.24l-14.457 14.458a57.3 57.3 0 0 1 2.373 16.343a57.2 57.2 0 0 1-3.113 18.654l14.25 14.23a75.1 75.1 0 0 0 7.543-32.883c0-10.962-2.37-21.38-6.595-30.8zm-138.072.003a75 75 0 0 0-6.598 30.798a75.1 75.1 0 0 0 7.547 32.882l14.25-14.23a57.2 57.2 0 0 1-3.117-18.65c0-5.69.837-11.17 2.375-16.344l-14.458-14.455zm92.523 45.547l-10.274 10.273l203.83 203.826l10.54-10.26zm-39.84 39.84l-14.453 14.452a75.1 75.1 0 0 0 30.816 6.604a75 75 0 0 0 30.798-6.6l-14.453-14.453a57.3 57.3 0 0 1-16.346 2.375a57.3 57.3 0 0 1-16.364-2.38zM81.87 341.3l-68.024 68.026h51.588l68.11-68.025H81.872zm295.78 0l68.112 68.026h51.59L429.326 341.3zm-172.546 1.95l-13.55 13.553c18.58 11.68 40.544 18.45 64.06 18.45c23.51 0 45.464-6.768 64.036-18.444l-13.55-13.552c-14.875 8.47-32.102 13.306-50.487 13.306c-18.39 0-35.625-4.84-50.51-13.314zm-34.88 34.883l-68.03 68.025l.003 51.52l68.026-68.024v-51.52zm170.75 0v51.52L409 497.68l.002-51.52l-68.027-68.025z"/></svg>
    )
}


function ResourcesIcon(props: SVGProps<SVGSVGElement>) {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" {...props}>
            <path
                fill="currentColor"
                d="M24 21c-.5 0-1-.2-1.4-.6l-3-3c-.4-.4-.6-.9-.6-1.4s.2-1 .6-1.4l3-3c.4-.4.9-.6 1.4-.6s1 .2 1.4.6l3 3c.4.4.6.9.6 1.4s-.2 1-.6 1.4l-3 3c-.4.4-.9.6-1.4.6m0-8l-3 3l3 3l3-3zm-8 0c-.5 0-1-.2-1.4-.6l-3-3C11.2 9 11 8.5 11 8s.2-1 .6-1.4l3-3c.4-.4.9-.6 1.4-.6s1 .2 1.4.6l3 3c.4.4.6.9.6 1.4s-.2 1-.6 1.4l-3 3c-.4.4-.9.6-1.4.6m0-8l-3 3l3 3l3-3zm0 24c-.5 0-1-.2-1.4-.6l-3-3c-.4-.4-.6-.9-.6-1.4s.2-1 .6-1.4l3-3c.4-.4.9-.6 1.4-.6s1 .2 1.4.6l3 3c.4.4.6.9.6 1.4s-.2 1-.6 1.4l-3 3c-.4.4-.9.6-1.4.6m0-8l-3 3l3 3l3-3zm-8 0c-.5 0-1-.2-1.4-.6l-3-3C3.2 17 3 16.5 3 16s.2-1 .6-1.4l3-3c.4-.4.9-.6 1.4-.6s1 .2 1.4.6l3 3c.4.4.6.9.6 1.4s-.2 1-.6 1.4l-3 3c-.4.4-.9.6-1.4.6m0-8l-3 3l3 3l3-3z"
            ></path>
        </svg>
    )
}

function PortalsIcon(props: SVGProps<SVGSVGElement>) {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" {...props}>
            <path
                fill="currentColor"
                d="M23 16.01a7 7 0 0 0-4.18 1.39l-4.22-4.22A6.86 6.86 0 0 0 16 9.01a7 7 0 1 0-2.81 5.59l4.21 4.22a7 7 0 1 0 5.6-2.81m-19-7a5 5 0 1 1 5 5a5 5 0 0 1-5-5"
            ></path>
        </svg>
    )
}

function ShortcutsIcon(props: SVGProps<SVGSVGElement>) {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" {...props}>
            <path
                fill="currentColor"
                d="M11.61 29.92a1 1 0 0 1-.6-1.07L12.83 17H8a1 1 0 0 1-1-1.23l3-13A1 1 0 0 1 11 2h10a1 1 0 0 1 .78.37a1 1 0 0 1 .2.85L20.25 11H25a1 1 0 0 1 .9.56a1 1 0 0 1-.11 1l-13 17A1 1 0 0 1 12 30a1.1 1.1 0 0 1-.39-.08M17.75 13l2-9H11.8L9.26 15h5.91l-1.59 10.28L23 13Z"
            ></path>
        </svg>
    )
}
function AccessIcon(props: SVGProps<SVGSVGElement>) {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" {...props}>
            <path
                fill="currentColor"
                d="M23 16.01a7 7 0 0 0-4.18 1.39l-4.22-4.22A6.86 6.86 0 0 0 16 9.01a7 7 0 1 0-2.81 5.59l4.21 4.22a7 7 0 1 0 5.6-2.81m-19-7a5 5 0 1 1 5 5a5 5 0 0 1-5-5"
            ></path>
        </svg>
    )
}

const smallClassName = 'w-5 h-5 mr-3'
const largeClassName = 'w-6 h-6'

export const parentIcons: { [key: string]: ReactNode } = {
    'carbon:connect': <AccessIcon className={largeClassName} />, // Access
    'carbon:content-delivery-network': <NdrIcon className={largeClassName} />, // NDR
    'carbon:radar': <ExternalAttackSurfaceIcon className={largeClassName} />, // external attack surface
    'icons8:organization': <OrganizationsIcon className={largeClassName} />, // organzation
    'carbon:api': <HunterXIcon className={largeClassName} />, // hunterx
}

export const childIcons: Record<string, ReactNode> = {
    'carbon:lightning': <ShortcutsIcon className={smallClassName} />, // shortcuts
    'carbon:connect': <PortalsIcon className={smallClassName} />, //poortals
    'carbon:software-resource-cluster': <ResourcesIcon className={smallClassName} />, // resources
    'ic:round-dashboard': <NdrDashboardIcon className={smallClassName} />, // ndr dashboard
    'material-symbols:error-outline-rounded': <IssuesIcon className={smallClassName} />, // issues
    'material-symbols:lightbulb-outline-rounded': <RecommendationsIcon className={smallClassName} />, // recommendations
    'material-symbols:inventory-2-outline-rounded': <IssuesIcon className={smallClassName} />, // issues
    'material-symbols:explore-outline-rounded': <ExploreIcon className={smallClassName} />, // explore
    'material-symbols:tune-rounded': <ControlsIcon className={smallClassName} />, // controls
    'carbon:list-boxes': <ScansIcon className={smallClassName} />, // scans
    'material-symbols:person-outline-rounded': <UsersIcon className={smallClassName} />, // users
    'material-symbols:group-outline-rounded': <GroupsIcon className={smallClassName} />, // groups
    'ri:apps-2-add-line': <IntegrationsIcon className={smallClassName} />, // integrations
    'basil:lock-time-solid': <JITAccessIcon className={smallClassName} />, // jit access
    'material-symbols:calendar-month-outline-rounded': <SchedulesIcon className={smallClassName} />, //schedule
    'material-symbols:payments-outline-rounded': <BillingsIcon className={smallClassName} />, //billing
    'material-symbols:settings-outline-rounded': <SettingsIcon className={smallClassName} />, //settings
}
