import type { SVGProps } from 'react'

const RuleIcon: React.FC<SVGProps<SVGSVGElement>> = ({ width = '1rem', height = '1rem', ...props }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      aria-hidden="true"
      role="img"
      className="iconify iconify--solar text-lg text-primary"
      width={width}
      height={height}
      viewBox="0 0 24 24"
      {...props}
    >
      <g fill="none" stroke="currentColor" stroke-width="1.5">
        <path d="m3 11l9-3l9 3m-9-9v19.5"></path>
        <path
          stroke-linecap="round"
          d="M3.193 14c.857 4.298 4.383 6.513 6.706 7.527c.721.315 1.082.473 2.101.473c1.02 0 1.38-.158 2.101-.473c.579-.252 1.231-.58 1.899-.994m3-2.629c1.163-1.476 2-3.408 2-5.913v-1.574c0-3.198 0-4.797-.378-5.335c-.377-.537-1.88-1.052-4.887-2.081l-.573-.196C13.595 2.268 12.812 2 12 2s-1.595.268-3.162.805L8.265 3c-3.007 1.03-4.51 1.545-4.887 2.082C3 5.62 3 7.22 3 10.417V11"
        ></path>
      </g>
    </svg>
  )
}

export default RuleIcon
