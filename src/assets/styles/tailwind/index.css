@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
    body {
        @apply text-gray-500 dark:text-gray-400 text-sm bg-white dark:bg-[#111827] leading-normal;
        -webkit-font-smoothing: antialiased;
        overscroll-behavior-y: none;
    }

    @media (prefers-color-scheme: dark) {
    }
    ::-webkit-scrollbar-thumb {
        @apply bg-gray-500/50;
        border-radius: 50px;
        background-clip: content-box;
    }

    ::-webkit-scrollbar {
        width: 3px;
        height: 3px;
    }
}


@layer utilities {
    .animate-shimmer {
        @apply relative overflow-hidden before:absolute before:inset-0 before:-translate-x-full before:animate-[shimmer_2s_infinite] before:bg-gradient-to-r before:from-transparent before:via-white/60 before:to-transparent;
    }
}

@keyframes shimmer {
    100% {
        transform: translateX(100%);
    }
}
