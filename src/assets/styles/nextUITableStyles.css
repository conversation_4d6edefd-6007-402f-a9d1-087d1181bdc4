.bg-content1 {
    @apply dark:bg-dark-blue !important;
    @apply bg-white !p-0;
    @apply !rounded-none;
}

.bg-content1[role="dialog"] {
    @apply !rounded-primary;
    @apply !bg-white 
}

.bg-content1:not(.force-light-mode){
   @apply dark:!bg-gray-800
}

.bg-content1 * > th {
    @apply !font-extralight
}

.members-table > .bg-content1 {
    @apply !rounded-primary
}


.members-table * > th:nth-child(1)   {
    @apply !rounded-tl-none
}
.members-table * > th:nth-last-child(1)   {
    @apply !rounded-tr-none
}

.bg-content1 * > td, tr, th {
    @apply !rounded-none
}

.members-table > .bg-content1 {
    @apply !rounded-primary
}

.members-table * > th:nth-child(1)   {
    @apply !rounded-tl-primary
}
.members-table * > th:nth-last-child(1)   {
    @apply !rounded-tr-primary
}


.bg-content1 * > td, tr, th {
    @apply !rounded-none
}
.bg-content1 * > td::before {
    @apply !rounded-none
}


.table-header::before {
  content: "";
  height: 2rem;
  z-index: -1 ;
  width: 100%;
  position: absolute;
  top: -1rem;
  left: 0;
  @apply bg-white dark:bg-dark-blue 
}

.custom-tr td:nth-child(1) {
    @apply !rounded-l-none before:!rounded-l-none;
}

.custom-tr td:nth-last-child(1) {
    @apply !rounded-l-none before:!rounded-r-none;
}

.hidden-top-selector {
    [data-key*="row-header-column"] > label {
     display: none;
}
}
    

