import _ from 'lodash'
import { getFunctions, httpsCallable } from 'firebase/functions'
import { QueryRules } from '@/trafficPatternsQuery/evaluateQueryRules'
import { ColumnFiltersState } from '@tanstack/react-table'
import { SQLQueryBuilder } from './SQLQueryBuilder'

import { TRAFFIC_SCHEMA } from '@/services/schemas/traffic'
import { DateRangeValue } from '@/views/HunterX/Dashboard/store/store'

export type EntitiesIntegrationsTypes = 'msDefender' | 'aws' | 'sentinelOne' | 'crowdstrike'

export const static_keys = Object.keys(TRAFFIC_SCHEMA).filter((key) => !key.includes('_common'))

function isUniqueFieldInQueryRules(field: string, queryRules: QueryRules) {
  const listOfRules = queryRules.rules.reduce((acc: any[], condition) => acc.concat(condition.rules), [])
  const uniqFieldsInList = new Set(_.map(listOfRules, 'field'))

  return uniqFieldsInList.has(field) && uniqFieldsInList.size === 1
}

export async function getSuggestionsCloudFn({
  organizationId,
  params,
  queryRules,
  searchBy,
  pageSize,
  offset,
  table = 'traffic_patterns',
  dateRange,
}: {
  organizationId: string
  params: Record<string, string>
  queryRules: QueryRules
  table?: 'traffic_patterns' | 'traffic'
  dateRange?: { startDate: Date; endDate: Date }
  searchBy?: Record<string, string>
  pageSize: number
  offset: number
}) {
  const functions = getFunctions()
  const queryBigQuery = httpsCallable<any, any>(functions, 'query0')

  const fieldName = SQLQueryBuilder.toSchemaName(params.field)
  const fieldType = typeof _.get(TRAFFIC_SCHEMA, fieldName)

  const maybeWithQueryRules =
    queryRules.condition === 'and' && !isUniqueFieldInQueryRules(params.field, queryRules) ? queryRules : undefined

  const queryBuilder = new SQLQueryBuilder({
    organizationId,
    queryRules: maybeWithQueryRules,
    searchBy,
    dateRange,
    table,
  })

  queryBuilder.select([fieldName]).from(table).distinct().where().pagination(pageSize, offset)

  if (['string', 'number'].includes(fieldType)) {
    queryBuilder.orderBy(fieldName, 'ASC', true)
  }

  const { query, params: sqlParams, pageSize: size, offset: skip } = queryBuilder.build()

  const response = await queryBigQuery({
    query,
    pageSize: size,
    offset: skip,
    params: sqlParams,
    organizationId,
  })

  if (response.data.error) {
    return Promise.reject(`Get suggestions failed :: ${response.data.error}`)
  }

  return { rows: enrichWithEntity(response.data.rows), pagination: response.data.pagination }
}

export interface GetTableDataParams {
  organizationId: string
  pageSize: number
  orderBy?: { id: string; desc: boolean }
  columnFilters?: { id: string; value: any }[]
  queryRules: QueryRules
  offset: number
  table?: 'traffic' | 'traffic_patterns' | 'recent_traffic'
  dateRange?: { startDate: Date; endDate: Date }
  fields?: string[]
}

export async function getTableData({
  organizationId,
  pageSize,
  orderBy,
  columnFilters,
  queryRules,
  dateRange,
  offset,
  table = 'traffic_patterns',
  fields,
}: GetTableDataParams) {
  const functions = getFunctions()
  const queryBigQuery = httpsCallable<any, any>(functions, 'query0')

  const queryBuilder = new SQLQueryBuilder({
    organizationId,
    queryRules,
    columnFilters: columnFilters as ColumnFiltersState,
    dateRange,
    table,
  })

  if (table === 'traffic_patterns') {
    queryBuilder.withRowNumber(orderBy?.id, orderBy?.desc ? 'DESC' : 'ASC')
  } else {
    queryBuilder.select(fields ? fields : ['*'])
  }

  queryBuilder.from(table).where().pagination(pageSize, offset)

  const { query, params: sqlParams, pageSize: size, offset: skip } = queryBuilder.build()

  const response = await queryBigQuery({
    query,
    pageSize: size,
    offset: skip,
    params: sqlParams,
    organizationId,
  })

  return { ...response, query, sqlParams }
}

export async function getVisibleNodesByQuery({
  queryRules,
  organizationId,
  table = 'traffic_patterns',
  dateRange,
}: {
  queryRules: QueryRules
  organizationId: string
  table: 'traffic_patterns' | 'traffic'
  dateRange?: { startDate: Date; endDate: Date }
}) {
  try {
    const functions = getFunctions()
    const queryBigQuery = httpsCallable<any, any>(functions, 'query0')

    const queryBuilder = new SQLQueryBuilder({
      organizationId,
      queryRules,
      dateRange,
      table,
    })
      .from(table as 'traffic_patterns' | 'traffic')
      .where()
      .withUniqueNodes()
      .pagination(50_000, 0)

    const { query, params: sqlParams, pageSize, offset } = queryBuilder.build()

    const response = await queryBigQuery({
      query,
      pageSize,
      offset,
      params: sqlParams,
      organizationId,
    })
    return response.data.rows
  } catch (error) {
    console.error({ error })
    throw new Error('getVisibleNodesByQuery::Failed', { cause: error })
  }
}

export async function getEdgesForNodes({
  queryRules,
  organizationId,
  pageIndex,
  table = 'traffic_patterns',
  dateRange,
}: {
  queryRules: QueryRules
  organizationId: string
  pageIndex: number
  table: 'traffic_patterns' | 'traffic'
  dateRange?: { startDate: Date; endDate: Date }
}) {
  try {
    const functions = getFunctions()
    const queryBigQuery = httpsCallable<any, any>(functions, 'query0')

    const queryBuilder = new SQLQueryBuilder({
      organizationId,
      queryRules,
      dateRange,
      table,
    })
      .select(['src_id', 'dst_id'])
      .distinct()
      .from(table)
      .where()
      .pagination(500, pageIndex * 500)

    const { query, params: sqlParams, pageSize, offset: skip } = queryBuilder.build()

    const response = await queryBigQuery({
      query,
      pageSize,
      offset: skip,
      params: sqlParams,
      organizationId,
    })

    const enriched = enrichWithEntity(response.data.rows)
    return { enriched, pagination: response.data.pagination }
  } catch (error) {
    console.error({ error })

    throw new Error('getTrafficPatternPrevForGraphPage::Failed', { cause: error })
  }
}

export async function getAvailableFields({
  organizationId,
  tableId = 'traffic_patterns',
}: {
  organizationId: string
  tableId?: 'traffic_patterns' | 'traffic'
}) {
  try {
    const functions = getFunctions()
    const queryBigQuery = httpsCallable<any, any>(functions, 'query0')

    const queryBuilder = new SQLQueryBuilder({
      organizationId,
      table: tableId,
    })
      .select(['*'])
      .from(tableId)
      .where('src_common IS NOT null OR dst_common IS NOT null')

    const { query } = queryBuilder.build()

    const response = await queryBigQuery({
      query,
      pageSize: 1,
      offset: 0,
      params: {},
      organizationId,
    })
    return response.data.rows
  } catch (error) {
    console.log({ error })
  }
}

export async function freeSearchCloudFn({
  organizationId,
  search,
  queryRules,
  table,
}: {
  organizationId: string
  search: string
  queryRules: QueryRules
  table: 'traffic' | 'traffic_patterns'
}) {
  const functions = getFunctions()
  const freeSearch = httpsCallable<
    {
      organizationId: string
      tableId: 'traffic_patterns' | 'traffic'
      search: string
      query: string
      params: Record<string, string | number>
    },
    any
  >(functions, 'freeSearch')

  const queryBuilder = new SQLQueryBuilder({
    organizationId,
    queryRules: queryRules.condition === 'and' ? queryRules : undefined,
    table,
  })
    .select(['*'])
    .from(table)
    .where()

  const { query, params: sqlParams } = queryBuilder.build()

  const response = await freeSearch({
    organizationId,
    tableId: table as 'traffic' | 'traffic_patterns',
    search,
    query,
    params: sqlParams as Record<string, string | number>,
  })

  return response.data
}

export async function getRecentTraffics({
  organizationId,
  dateRange,
  tableId,
  pageSize,
  offset,
  queryRules,
  fields,
}: {
  organizationId: string
  dateRange: { startDate: Date; endDate: Date }
  tableId: 'traffic' | 'traffic_patterns'
  pageSize: number
  offset: number
  queryRules?: QueryRules
  fields?: string[]
}) {
  try {
    const functions = getFunctions()
    const queryBigQuery = httpsCallable<any, any>(functions, 'query0')
    const selectedFields = fields
      ? fields
      : [
          'src_id',
          'dst_id',
          'src_name',
          'dst_name',
          'src_process',
          'port',
          'src_addr',
          'dst_addr',
          'time',
          // 'risk_score',
        ]

    const queryBuilder = new SQLQueryBuilder({
      organizationId,
      dateRange,
      queryRules,
      table: tableId,
    })
      .select(selectedFields)
      .distinct()
      .from(tableId)
      .where()

    const { query, params } = queryBuilder.build()

    const response = await queryBigQuery({
      query,
      pageSize,
      offset,
      params,
      organizationId,
    })

    return response.data
  } catch (error) {
    console.log({ error })
  }
}

export async function getFrequentQuery({
  organizationId,
  tableId = 'traffic_patterns',
  fieldName,
  queryRules,
  pageSize = 5,
  orderBy,
  dateRange,
  offset = 0,
}: {
  organizationId: string
  tableId: 'traffic' | 'traffic_patterns'
  fieldName: string
  queryRules?: QueryRules
  pageSize?: number
  orderBy: 'DESC' | 'ASC'
  dateRange?: { startDate: Date; endDate: Date }
  offset?: number
}) {
  try {
    const functions = getFunctions()
    const queryBigQuery = httpsCallable<any, any>(functions, 'query0')

    const queryBuilder = new SQLQueryBuilder({
      organizationId,
      queryRules,
      dateRange,
      table: tableId,
    }).buildTopFrequenciesQuery(fieldName, tableId, orderBy)

    const { query, params } = queryBuilder.pagination(pageSize, offset).build()

    const response = await queryBigQuery({
      query,
      params,
      organizationId,
      pageSize,
      offset,
    })

    return response.data
  } catch (error) {
    console.log({ error })
  }
}

const getHoursBetweenDates = ({ endDate, startDate }: DateRangeValue) => {
  const diffInMilliseconds = endDate.getTime() - startDate.getTime()
  return Math.ceil(diffInMilliseconds / (1000 * 60 * 60))
}

export type ChartInfoArg = {
  organizationId: string
  tableId: 'traffic' | 'traffic_patterns'
  dateRange: DateRangeValue
  queryRules?: QueryRules
}

export async function getChartInfo({ organizationId, tableId, dateRange, queryRules }: ChartInfoArg) {
  try {
    const functions = getFunctions()
    const queryBigQuery = httpsCallable<any, any>(functions, 'query0')

    const queryBuilder = new SQLQueryBuilder({
      organizationId,
      dateRange,
      queryRules,
      table: tableId,
    }).getHourlyTrafficCounts(tableId)

    const { query, params } = queryBuilder.build()

    const response = await queryBigQuery({
      query,
      params,
      organizationId,
      pageSize: getHoursBetweenDates(dateRange),
      offset: 0,
    })

    return response.data.rows
  } catch (error) {
    console.log({ error })
  }
}

export async function getTrafficTimeline({
  organizationId,
  tableId,
  src_id,
  dst_id,
  pageSize,
  offset,
}: {
  organizationId: string
  tableId: 'traffic' | 'traffic_patterns'
  src_id: string
  dst_id: string
  pageSize: number
  offset: number
}) {
  try {
    const functions = getFunctions()
    const queryBigQuery = httpsCallable<any, any>(functions, 'query0')

    const queryBuilder = new SQLQueryBuilder({
      organizationId,
      table: tableId,
    }).getTimeSeriesCount({
      timeUnit: 'MINUTE',
      srcId: src_id,
      dstId: dst_id,
    })

    const { query, params } = queryBuilder.build()

    const response = await queryBigQuery({
      query,
      params,
      organizationId,
      pageSize,
      offset,
    })

    return response.data
  } catch (error) {
    console.log({ error })
  }
}

export async function getUniqCount({ organizationId, queryRules }: { organizationId: string; queryRules: QueryRules }) {
  try {
    const functions = getFunctions()
    const queryBigQuery = httpsCallable<any, any>(functions, 'query0')
    const queryBuilder = new SQLQueryBuilder({
      organizationId,
      queryRules,
      table: 'traffic_patterns',
    })
      .selectUniqSrcAdds()
      .from('traffic_patterns')
      .where()

    const { query, params } = queryBuilder.build()
    const response = await queryBigQuery({
      query,
      params,
      organizationId,
      queryRules,
    })

    return _.get(response.data, 'rows[0].total_count', 0)
  } catch (error) {
    console.log({ error })
  }
}

export async function getSuggestedNames({
  organizationId,
  pageSize,
  offset,
  searchTerm,
  dstName,
  srcName,
}: {
  organizationId: string
  pageSize: number
  offset: number
  searchTerm: string
  dstName: string
  srcName: string
}) {
  try {
    const functions = getFunctions()
    const queryBigQuery = httpsCallable<any, any>(functions, 'query0')
    const queryBuilder = new SQLQueryBuilder({
      organizationId,
      table: 'traffic_patterns',
    })
      .getNames(searchTerm, { dstName, srcName })
      .where()
      .pagination(pageSize, offset)

    const { query, params, offset: skip, pageSize: size } = queryBuilder.build()
    const response = await queryBigQuery({
      query,
      params,
      organizationId,
      pageSize: size,
      offset: skip,
    })

    return response.data
  } catch (error) {
    console.log({ error })
  }
}

export async function checkRawSQL({
  organizationId,
  query,
  table,
}: {
  organizationId: string
  query: string
  table: 'traffic' | 'traffic_patterns' | 'recent_traffic'
}) {
  const functions = getFunctions()
  const queryBigQuery = httpsCallable<any, any>(functions, 'query0')

  const response = await queryBigQuery({
    query,
    params: {},
    organizationId,
    pageSize: 1_000,
    offset: 0,
    table,
  })

  if (response?.data.error) {
    throw new Error(response.data.error)
  }

  return response
}

export async function getListOfEntities({
  organizationId,
  integrationType,
  queryRules,
}: {
  organizationId: string
  integrationType: EntitiesIntegrationsTypes
  queryRules?: QueryRules
}) {
  try {
    const functions = getFunctions()
    const queryBigQuery = httpsCallable<any, any>(functions, 'query0')
    const { fields, mapper } = entitiesSelector(integrationType)

    const queryBuilder = new SQLQueryBuilder({
      organizationId,
      table: 'entities',
      queryRules: queryRules
        ? queryRules
        : {
            condition: 'and',
            rules: [],
          },
    })
      .select(fields)
      .distinct()
      .from('entities')
      .where()

    const { query, params } = queryBuilder.build()

    const response = await queryBigQuery({
      query,
      pageSize: 25_000,
      offset: 0,
      params,
      organizationId,
    })

    return response.data.rows.map(mapper)
  } catch (error) {
    console.error({ error })

    throw new Error('getTrafficPatternPrevForGraphPage::Failed', { cause: error })
  }
}

export const flatInfoObject = (obj: any, prefix = '') => {
  const flatObject = {} as Record<string, string | Array<string | number>>

  function recurse(currentObj: any, currentPrefix = '') {
    for (const key in currentObj) {
      const value = currentObj[key]
      const newKey = currentPrefix ? `${currentPrefix}_${key}` : key

      if (Array.isArray(value)) {
        // Always initialize array fields, even if empty
        flatObject[newKey] = flatObject[newKey] || []

        if (key === 'computerMemberOf' || key === 'lastUserMemberOf') {
          if (!_.isNil(value)) {
            value.forEach((item) => {
              const split = item.split(',') as Array<string>
              split.forEach((part) => {
                const [key, value] = part.split('=')
                flatObject[`${newKey}_${key}`] = flatObject[`${newKey}_${key}`] || []
                ;(flatObject[`${newKey}_${key}`] as string[]).push(value)
              })
            })
          }
        } else if (value?.length > 0 && value?.every((item) => typeof item === 'object' && item !== null)) {
          // Array of objects
          value.forEach((item) => {
            for (const subKey in item) {
              const arrayKey = `${newKey}_${subKey}`
              flatObject[arrayKey] = flatObject[arrayKey] || []
              if (Array.isArray(item[subKey])) {
                ;(flatObject[arrayKey] as Array<string | number>).push(...item[subKey])
              } else {
                ;(flatObject[arrayKey] as Array<string | number>).push(item[subKey])
              }
            }
          })
        } else {
          // Simple array: flatten nested arrays into a single-level array
          if (value?.length > 0) {
            ;(flatObject[newKey] as Array<string | number>).push(...value.flat())
          }
        }
      } else if (typeof value === 'object' && value !== null) {
        // Recurse for nested objects
        recurse(value, newKey)
      } else {
        if (key === 'computerDistinguishedName' || key === 'lastUserDistinguishedName') {
          if (!_.isNil(value)) {
            const split = value.split(',') as Array<string>
            split.forEach((part) => {
              const [key, value] = part.split('=')
              flatObject[`${newKey}_${key}`] = flatObject[`${newKey}_${key}`] || []
              ;(flatObject[`${newKey}_${key}`] as string[]).push(value)
            })
          } else {
            flatObject[`${newKey}`] = []
          }
        } else {
          // Set primitive values directly
          flatObject[`${newKey}`] = value
        }
      }
    }
  }

  recurse(obj, prefix)

  const integrations = ['crowdstrike', 'sentinelOne', 'aws']

  for (const key in flatObject) {
    if (integrations.includes(key) && _.isNull(flatObject[key])) {
      delete flatObject[key]
    }
  }

  return flatObject
}

export function enrichWithEntity(trafficPatterns: any[]) {
  return trafficPatterns.reduce((acc, item) => {
    const { dst_common, src_common, ...restItemValues } = item

    const dstCommon: Record<string, any> = flatInfoObject(_.get(item, 'dst_common', {}) ?? {})
    const srcCommon: Record<string, any> = flatInfoObject(_.get(item, 'src_common', {}) ?? {})

    const flatQueryTrafficPattern: Record<string, any> = {}

    Object.keys(srcCommon).length &&
      Object.keys(srcCommon).forEach((key: string) => {
        flatQueryTrafficPattern[`src_${key}`] = srcCommon[key]
      })

    Object.keys(dstCommon).length &&
      Object.keys(dstCommon).forEach((key: string) => {
        flatQueryTrafficPattern[`dst_${key}`] = dstCommon[key]
      })

    const enrichedEntity = {
      ...restItemValues,
      ...flatQueryTrafficPattern,
      ...(item.src_id && { src_id: item.src_id }),
      ...(item.dst_id && { dst_id: item.dst_id }),
    }

    return acc.concat(enrichedEntity)
  }, [])
}

export function getPrefix(fieldKey: string): 'src' | 'dst' {
  return fieldKey.slice(0, 3) as 'src' | 'dst'
}

export async function searchEntities({
  organizationId,
  searchQuery,
  pageSize,
  offset,
}: {
  organizationId: string
  searchQuery: string
  pageSize: number
  offset: number
}) {
  try {
    const functions = getFunctions()
    const queryBigQuery = httpsCallable<any, any>(functions, 'query0')

    const query = `
    SELECT id
          FROM \`${organizationId}.entities\` AS t
    WHERE REGEXP_CONTAINS(TO_JSON_STRING(t), r'(?i){{seachQuery}}')
  `

    const response = await queryBigQuery({
      query,
      pageSize,
      offset,
      params: {
        searchQuery: `%${searchQuery}%`,
        pageSize,
        offset,
      },
      organizationId,
    })

    if (response.data.error) {
      throw new Error(`Search entities failed: ${response.data.error}`)
    }

    return response.data.rows
  } catch (error) {
    console.error('searchEntities error:', error)
    throw error
  }
}

function entitiesSelector(type: EntitiesIntegrationsTypes) {
  switch (type) {
    case 'msDefender': {
      return {
        fields: [
          'id',
          'name',
          'type',
          'common.cloudProvider',
          'info.msDefender.awsResourceName',
          'info.msDefender.tenantId',
          'common.activeDirectoryDomain',
          'common.activeDirectoryOu',
          'integration_type',
          'info.msDefender.ips', // firewall
        ],
        mapper: (entity: any) => ({
          id: entity.id,
          name: entity.name,
          type: entity.type,
          integrationType: entity.integration_type,
          info: {
            awsResourceName: entity.awsResourceName,
            tenantId: entity.tenantId,
            ips: entity.ips,
          },
          common: {
            cloudProvider: entity.cloudProvider,
            activeDirectoryDomain: entity.activeDirectoryDomain,
            activeDirectoryOu: entity.activeDirectoryOu,
          },
        }),
      }
    }

    case 'aws': {
      return {
        fields: [
          'id',
          'name',
          'type',
          'info.aws.accountId',
          'info.aws.region',
          'info.aws.vpcId',
          'info.aws.endpointType',
          'sub_types',
          'info.aws.ips', // firewall
          'info.aws.securityGroups', // firewall
        ],
        mapper: (entity: any) => ({
          id: entity.id,
          name: entity.name,
          type: entity.type,
          subTypes: entity.sub_types,
          integrationType: entity.integration_type,
          info: {
            accountId: entity.accountId,
            region: entity.region,
            vpcId: entity.vpcId,
            endpointType: entity.endpointType,
            ips: entity.ips,
            securityGroups: entity.securityGroups,
          },
        }),
      }
    }

    case 'sentinelOne': {
      return {
        fields: [
          'id',
          'name',
          'type',
          'integration_type',
          'info.sentinelOne.accountId',
          'info.sentinelOne.activeDirectory',
          'info.sentinelOne.domain',
          'info.sentinelOne.machineType',
          'info.sentinelOne.osType',
          'info.sentinelOne.cloudProviders',
          'info.sentinelOne.computerName',
          'info.sentinelOne.siteName',
          'info.sentinelOne.groupId',
          'info.sentinelOne.ips',
        ],
        mapper: (entity: any) => ({
          id: entity.id,
          name: entity.name,
          type: entity.type,
          integrationType: entity.integration_type,
          info: {
            accountId: entity.accountId,
            activeDirectory: entity.activeDirectory,
            domain: entity.domain,
            machineType: entity.machineType,
            osType: entity.osType,
            cloudProviders: JSON.parse(entity.cloudProviders),
            computerName: entity.computerName,
            siteName: entity.siteName,
            groupId: entity.groupId,
            ips: entity.ips,
          },
        }),
      }
    }

    case 'crowdstrike': {
      return {
        fields: [
          'id',
          'name',
          'type',
          'sub_types',
          'integration_type',
          'info.crowdstrike.serviceProviderAccountId',
          'info.crowdstrike.machineDomain',
          'info.crowdstrike.ou',
          'info.crowdstrike.platformName',
          'info.crowdstrike.zoneGroup',
          'info.crowdstrike.cid',
          'info.crowdstrike.ips',
        ],
        mapper: (entity: any) => {
          return {
            id: entity.id,
            name: entity.name,
            type: entity.type,
            integrationType: entity.integration_type,
            subTypes: entity.sub_types,
            info: {
              serviceProviderAccountId: entity.serviceProviderAccountId,
              machineDomain: entity.machineDomain,
              ou: entity.ou,
              platformName: entity.platformName,
              zoneGroup: entity.zoneGroup,
              cid: entity.cid,
              ips: entity.ips,
            },
          }
        },
      }
    }

    default:
      console.error(`entitiesSelector:: Unknown type ${type}`)

      return {
        fields: ['name', 'id', 'type', 'sub_types', 'integration_type'],
        mapper: (entity: any) => entity,
      }
  }
}
