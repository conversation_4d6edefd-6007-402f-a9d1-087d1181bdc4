import { EntityTypeWithId } from '@/firestoreQueries/ndr/entities/customEntitiesTypes'
import { Label } from '@/firestoreQueries/ndr/labels/hooks/useLabels'
import { 
  GroupedEntity, 
  GroupedEdge, 
  GroupViewKey 
} from '@globalTypes/ndrBenchmarkTypes/groupViews/groupViewTypes'

// Extract available label keys from entities
export function getAvailableLabelKeys(
  entities: EntityTypeWithId[], 
  entityLabels: Map<string, Label[]>
): GroupViewKey[] {
  const keySet = new Set<string>()
  
  entities.forEach(entity => {
    const labels = entityLabels.get(entity.id) || []
    labels.forEach(label => {
      keySet.add(label.key)
    })
  })
  
  return Array.from(keySet).map(key => ({
    key,
    displayName: formatKeyDisplayName(key),
  }))
}

// Format label key for display
function formatKeyDisplayName(key: string): string {
  return key
    .split(/[-_]/)
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ')
}

// Group entities by label keys
export function groupEntitiesByLabels(
  entities: EntityTypeWithId[],
  entityLabels: Map<string, Label[]>,
  primaryKey: string,
  secondaryKey?: string,
  additionalKeys?: string[]
): GroupedEntity[] {
  const groups = new Map<string, GroupedEntity>()
  
  entities.forEach(entity => {
    const labels = entityLabels.get(entity.id) || []
    const labelMap = new Map(labels.map(label => [label.key, label.values]))
    
    // Create group key based on primary and secondary keys
    const groupKey = createGroupKey(labelMap, primaryKey, secondaryKey, additionalKeys)
    const groupName = createGroupName(labelMap, primaryKey, secondaryKey, additionalKeys)
    
    if (!groups.has(groupKey)) {
      groups.set(groupKey, {
        groupId: groupKey,
        groupName,
        groupKeys: createGroupKeysObject(labelMap, primaryKey, secondaryKey, additionalKeys),
        entities: [],
        entityCount: 0,
        aggregatedLabels: {},
      })
    }
    
    const group = groups.get(groupKey)!
    group.entities.push(entity.id)
    group.entityCount++
    
    // Aggregate all labels for this group
    labels.forEach(label => {
      if (!group.aggregatedLabels[label.key]) {
        group.aggregatedLabels[label.key] = []
      }
      label.values.forEach(value => {
        if (!group.aggregatedLabels[label.key].includes(value)) {
          group.aggregatedLabels[label.key].push(value)
        }
      })
    })
  })
  
  return Array.from(groups.values())
}

// Create a unique group key
function createGroupKey(
  labelMap: Map<string, string[]>,
  primaryKey: string,
  secondaryKey?: string,
  additionalKeys?: string[]
): string {
  const keyParts: string[] = []
  
  // Add primary key value
  const primaryValue = labelMap.get(primaryKey)?.[0] || 'unknown'
  keyParts.push(`${primaryKey}:${primaryValue}`)
  
  // Add secondary key value if provided
  if (secondaryKey) {
    const secondaryValue = labelMap.get(secondaryKey)?.[0] || 'unknown'
    keyParts.push(`${secondaryKey}:${secondaryValue}`)
  }
  
  // Add additional keys if provided
  if (additionalKeys) {
    additionalKeys.forEach(key => {
      const value = labelMap.get(key)?.[0] || 'unknown'
      keyParts.push(`${key}:${value}`)
    })
  }
  
  return keyParts.join('|')
}

// Create a human-readable group name
function createGroupName(
  labelMap: Map<string, string[]>,
  primaryKey: string,
  secondaryKey?: string,
  additionalKeys?: string[]
): string {
  const nameParts: string[] = []
  
  // Add primary key value
  const primaryValue = labelMap.get(primaryKey)?.[0] || 'Unknown'
  nameParts.push(primaryValue)
  
  // Add secondary key value if provided
  if (secondaryKey) {
    const secondaryValue = labelMap.get(secondaryKey)?.[0] || 'Unknown'
    nameParts.push(secondaryValue)
  }
  
  // Add additional keys if provided
  if (additionalKeys && additionalKeys.length > 0) {
    const additionalValues = additionalKeys
      .map(key => labelMap.get(key)?.[0] || 'Unknown')
      .join(', ')
    nameParts.push(`(${additionalValues})`)
  }
  
  return nameParts.join(' - ')
}

// Create group keys object for metadata
function createGroupKeysObject(
  labelMap: Map<string, string[]>,
  primaryKey: string,
  secondaryKey?: string,
  additionalKeys?: string[]
): Record<string, string> {
  const groupKeys: Record<string, string> = {}
  
  groupKeys[primaryKey] = labelMap.get(primaryKey)?.[0] || 'unknown'
  
  if (secondaryKey) {
    groupKeys[secondaryKey] = labelMap.get(secondaryKey)?.[0] || 'unknown'
  }
  
  if (additionalKeys) {
    additionalKeys.forEach(key => {
      groupKeys[key] = labelMap.get(key)?.[0] || 'unknown'
    })
  }
  
  return groupKeys
}

// Aggregate edges between groups
export function aggregateEdgesBetweenGroups(
  trafficPatterns: any[], // Replace with proper type
  entityToGroupMap: Map<string, string>
): GroupedEdge[] {
  const edgeMap = new Map<string, GroupedEdge>()
  
  trafficPatterns.forEach(pattern => {
    const sourceGroupId = entityToGroupMap.get(pattern.src_id)
    const targetGroupId = entityToGroupMap.get(pattern.dst_id)
    
    if (!sourceGroupId || !targetGroupId) return
    
    const edgeKey = `${sourceGroupId}->${targetGroupId}`
    
    if (!edgeMap.has(edgeKey)) {
      edgeMap.set(edgeKey, {
        id: edgeKey,
        sourceGroupId,
        targetGroupId,
        edgeCount: 0,
        aggregatedData: {
          totalTraffic: 0,
          ports: [],
          protocols: [],
          isDanger: false,
        },
      })
    }
    
    const edge = edgeMap.get(edgeKey)!
    edge.edgeCount++
    edge.aggregatedData.totalTraffic += pattern.traffic || 1
    
    if (pattern.port && !edge.aggregatedData.ports.includes(pattern.port)) {
      edge.aggregatedData.ports.push(pattern.port)
    }
    
    if (pattern.protocol && !edge.aggregatedData.protocols.includes(pattern.protocol)) {
      edge.aggregatedData.protocols.push(pattern.protocol)
    }
    
    if (pattern.isDanger) {
      edge.aggregatedData.isDanger = true
    }
  })
  
  return Array.from(edgeMap.values())
}

// Create entity to group mapping
export function createEntityToGroupMap(groupedEntities: GroupedEntity[]): Map<string, string> {
  const map = new Map<string, string>()
  
  groupedEntities.forEach(group => {
    group.entities.forEach(entityId => {
      map.set(entityId, group.groupId)
    })
  })
  
  return map
}

// Search label keys
export function searchLabelKeys(keys: GroupViewKey[], searchTerm: string): GroupViewKey[] {
  if (!searchTerm.trim()) return keys
  
  const lowerSearchTerm = searchTerm.toLowerCase()
  return keys.filter(key => 
    key.key.toLowerCase().includes(lowerSearchTerm) ||
    (key.displayName && key.displayName.toLowerCase().includes(lowerSearchTerm))
  )
}
