import {
  getAvailableLabelKeys,
  groupEntitiesByLabels,
  aggregateEdgesBetweenGroups,
  createEntityToGroupMap,
  searchLabelKeys,
} from '../groupViewUtils'

// Mock data
const mockEntities = [
  {
    id: 'entity1',
    name: 'Entity 1',
    type: 'instance',
  },
  {
    id: 'entity2',
    name: 'Entity 2',
    type: 'instance',
  },
  {
    id: 'entity3',
    name: 'Entity 3',
    type: 'instance',
  },
]

const mockEntityLabels = new Map([
  ['entity1', [
    { key: 'environment', values: ['production'] },
    { key: 'application', values: ['web-app'] },
  ]],
  ['entity2', [
    { key: 'environment', values: ['staging'] },
    { key: 'application', values: ['web-app'] },
  ]],
  ['entity3', [
    { key: 'environment', values: ['production'] },
    { key: 'application', values: ['api'] },
  ]],
])

const mockTrafficPatterns = [
  {
    src_id: 'entity1',
    dst_id: 'entity2',
    traffic: 100,
    port: 80,
    protocol: 'HTTP',
    isDanger: false,
  },
  {
    src_id: 'entity2',
    dst_id: 'entity3',
    traffic: 50,
    port: 443,
    protocol: 'HTTPS',
    isDanger: true,
  },
]

describe('groupViewUtils', () => {
  describe('getAvailableLabelKeys', () => {
    it('should extract unique label keys from entities', () => {
      const result = getAvailableLabelKeys(mockEntities, mockEntityLabels)
      
      expect(result).toHaveLength(2)
      expect(result.map(k => k.key)).toContain('environment')
      expect(result.map(k => k.key)).toContain('application')
    })

    it('should return empty array when no entities', () => {
      const result = getAvailableLabelKeys([], new Map())
      expect(result).toEqual([])
    })

    it('should format display names correctly', () => {
      const result = getAvailableLabelKeys(mockEntities, mockEntityLabels)
      const environmentKey = result.find(k => k.key === 'environment')
      
      expect(environmentKey?.displayName).toBe('Environment')
    })
  })

  describe('groupEntitiesByLabels', () => {
    it('should group entities by primary key', () => {
      const result = groupEntitiesByLabels(
        mockEntities,
        mockEntityLabels,
        'environment'
      )

      expect(result).toHaveLength(2) // production and staging groups
      
      const productionGroup = result.find(g => g.groupName.includes('production'))
      const stagingGroup = result.find(g => g.groupName.includes('staging'))
      
      expect(productionGroup?.entities).toHaveLength(2) // entity1 and entity3
      expect(stagingGroup?.entities).toHaveLength(1) // entity2
    })

    it('should group entities by primary and secondary keys', () => {
      const result = groupEntitiesByLabels(
        mockEntities,
        mockEntityLabels,
        'environment',
        'application'
      )

      expect(result).toHaveLength(3) // production-web-app, staging-web-app, production-api
      
      const groups = result.map(g => g.groupName)
      expect(groups).toContain('production - web-app')
      expect(groups).toContain('staging - web-app')
      expect(groups).toContain('production - api')
    })

    it('should handle entities without labels', () => {
      const entitiesWithoutLabels = [{ id: 'entity4', name: 'Entity 4', type: 'instance' }]
      const result = groupEntitiesByLabels(
        entitiesWithoutLabels,
        new Map(),
        'environment'
      )

      expect(result).toHaveLength(1)
      expect(result[0].groupName).toContain('Unknown')
    })
  })

  describe('createEntityToGroupMap', () => {
    it('should create correct entity to group mapping', () => {
      const groupedEntities = [
        {
          groupId: 'group1',
          groupName: 'Group 1',
          entities: ['entity1', 'entity2'],
          entityCount: 2,
          groupKeys: {},
          aggregatedLabels: {},
        },
        {
          groupId: 'group2',
          groupName: 'Group 2',
          entities: ['entity3'],
          entityCount: 1,
          groupKeys: {},
          aggregatedLabels: {},
        },
      ]

      const result = createEntityToGroupMap(groupedEntities)

      expect(result.get('entity1')).toBe('group1')
      expect(result.get('entity2')).toBe('group1')
      expect(result.get('entity3')).toBe('group2')
    })
  })

  describe('aggregateEdgesBetweenGroups', () => {
    it('should aggregate edges between groups', () => {
      const entityToGroupMap = new Map([
        ['entity1', 'group1'],
        ['entity2', 'group2'],
        ['entity3', 'group2'],
      ])

      const result = aggregateEdgesBetweenGroups(mockTrafficPatterns, entityToGroupMap)

      expect(result).toHaveLength(1) // Only one group-to-group connection
      
      const edge = result[0]
      expect(edge.sourceGroupId).toBe('group1')
      expect(edge.targetGroupId).toBe('group2')
      expect(edge.edgeCount).toBe(1)
      expect(edge.aggregatedData.totalTraffic).toBe(100)
    })

    it('should handle multiple edges between same groups', () => {
      const multiplePatterns = [
        ...mockTrafficPatterns,
        {
          src_id: 'entity1',
          dst_id: 'entity3',
          traffic: 75,
          port: 22,
          protocol: 'SSH',
          isDanger: false,
        },
      ]

      const entityToGroupMap = new Map([
        ['entity1', 'group1'],
        ['entity2', 'group2'],
        ['entity3', 'group2'],
      ])

      const result = aggregateEdgesBetweenGroups(multiplePatterns, entityToGroupMap)

      expect(result).toHaveLength(1)
      
      const edge = result[0]
      expect(edge.edgeCount).toBe(2) // Two edges between group1 and group2
      expect(edge.aggregatedData.totalTraffic).toBe(175) // 100 + 75
      expect(edge.aggregatedData.ports).toContain(80)
      expect(edge.aggregatedData.ports).toContain(22)
    })
  })

  describe('searchLabelKeys', () => {
    const labelKeys = [
      { key: 'environment', displayName: 'Environment' },
      { key: 'application', displayName: 'Application' },
      { key: 'team-owner', displayName: 'Team Owner' },
    ]

    it('should filter keys by search term', () => {
      const result = searchLabelKeys(labelKeys, 'env')
      expect(result).toHaveLength(1)
      expect(result[0].key).toBe('environment')
    })

    it('should search in display names', () => {
      const result = searchLabelKeys(labelKeys, 'owner')
      expect(result).toHaveLength(1)
      expect(result[0].key).toBe('team-owner')
    })

    it('should be case insensitive', () => {
      const result = searchLabelKeys(labelKeys, 'APP')
      expect(result).toHaveLength(1)
      expect(result[0].key).toBe('application')
    })

    it('should return all keys when search term is empty', () => {
      const result = searchLabelKeys(labelKeys, '')
      expect(result).toHaveLength(3)
    })
  })
})
