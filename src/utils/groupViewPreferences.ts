import { GroupViewWithId } from '@globalTypes/ndrBenchmarkTypes/groupViews/groupViewTypes'

export interface GroupViewPreferences {
  currentViewId: string | null
  isGrouped: boolean
  lastUpdated: string
  autoApplyDefault: boolean
}

const PREFERENCES_KEY_PREFIX = 'groupViewPreferences'
const PREFERENCES_VERSION = '1.0'

/**
 * Get the localStorage key for group view preferences
 */
function getPreferencesKey(organizationId: string, userId: string): string {
  return `${PREFERENCES_KEY_PREFIX}_${organizationId}_${userId}_v${PREFERENCES_VERSION}`
}

/**
 * Save group view preferences to localStorage
 */
export function saveGroupViewPreferences(
  organizationId: string,
  userId: string,
  preferences: Partial<GroupViewPreferences>
): void {
  try {
    const key = getPreferencesKey(organizationId, userId)
    const existing = getGroupViewPreferences(organizationId, userId)
    
    const updated: GroupViewPreferences = {
      ...existing,
      ...preferences,
      lastUpdated: new Date().toISOString(),
    }
    
    localStorage.setItem(key, JSON.stringify(updated))
  } catch (error) {
    console.warn('Failed to save group view preferences:', error)
  }
}

/**
 * Load group view preferences from localStorage
 */
export function getGroupViewPreferences(
  organizationId: string,
  userId: string
): GroupViewPreferences {
  const defaultPreferences: GroupViewPreferences = {
    currentViewId: null,
    isGrouped: false,
    lastUpdated: new Date().toISOString(),
    autoApplyDefault: true,
  }

  try {
    const key = getPreferencesKey(organizationId, userId)
    const stored = localStorage.getItem(key)
    
    if (!stored) {
      return defaultPreferences
    }
    
    const parsed = JSON.parse(stored) as GroupViewPreferences
    
    // Check if preferences are recent (within last 7 days)
    const lastUpdated = new Date(parsed.lastUpdated)
    const now = new Date()
    const daysDiff = (now.getTime() - lastUpdated.getTime()) / (1000 * 60 * 60 * 24)
    
    if (daysDiff > 7) {
      // Preferences are too old, return defaults
      return defaultPreferences
    }
    
    return {
      ...defaultPreferences,
      ...parsed,
    }
  } catch (error) {
    console.warn('Failed to load group view preferences:', error)
    return defaultPreferences
  }
}

/**
 * Clear group view preferences from localStorage
 */
export function clearGroupViewPreferences(organizationId: string, userId: string): void {
  try {
    const key = getPreferencesKey(organizationId, userId)
    localStorage.removeItem(key)
  } catch (error) {
    console.warn('Failed to clear group view preferences:', error)
  }
}

/**
 * Check if a group view should be auto-applied based on preferences
 */
export function shouldAutoApplyGroupView(
  organizationId: string,
  userId: string,
  groupView: GroupViewWithId
): boolean {
  const preferences = getGroupViewPreferences(organizationId, userId)
  
  return (
    preferences.autoApplyDefault &&
    groupView.isDefault &&
    preferences.currentViewId !== groupView.id
  )
}

/**
 * Update the current view in preferences
 */
export function updateCurrentViewPreference(
  organizationId: string,
  userId: string,
  viewId: string | null,
  isGrouped: boolean
): void {
  saveGroupViewPreferences(organizationId, userId, {
    currentViewId: viewId,
    isGrouped,
  })
}

/**
 * Toggle auto-apply default setting
 */
export function toggleAutoApplyDefault(
  organizationId: string,
  userId: string
): boolean {
  const preferences = getGroupViewPreferences(organizationId, userId)
  const newValue = !preferences.autoApplyDefault
  
  saveGroupViewPreferences(organizationId, userId, {
    autoApplyDefault: newValue,
  })
  
  return newValue
}

/**
 * Get recently used group views from preferences
 * This could be extended to track view usage history
 */
export function getRecentlyUsedViews(
  organizationId: string,
  userId: string,
  allViews: GroupViewWithId[]
): GroupViewWithId[] {
  const preferences = getGroupViewPreferences(organizationId, userId)
  
  if (!preferences.currentViewId) {
    return []
  }
  
  const currentView = allViews.find(view => view.id === preferences.currentViewId)
  return currentView ? [currentView] : []
}

/**
 * Migrate old preferences to new format if needed
 */
export function migrateGroupViewPreferences(organizationId: string, userId: string): void {
  try {
    // Check for old format preferences and migrate if needed
    const oldKey = `groupViewPreferences_${organizationId}_${userId}`
    const oldData = localStorage.getItem(oldKey)
    
    if (oldData) {
      const parsed = JSON.parse(oldData)
      
      // Migrate to new format
      saveGroupViewPreferences(organizationId, userId, {
        currentViewId: parsed.currentViewId || null,
        isGrouped: parsed.isGrouped || false,
        autoApplyDefault: true, // Default for migrated preferences
      })
      
      // Remove old format
      localStorage.removeItem(oldKey)
    }
  } catch (error) {
    console.warn('Failed to migrate group view preferences:', error)
  }
}
