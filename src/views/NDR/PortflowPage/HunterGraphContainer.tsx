import { useSearchParams } from 'react-router-dom'
import { useOrganization } from '@clerk/clerk-react'
import { usePortflowFilters, usePortflowInteractions } from '@/views/NDR/PortflowPage/hooks'
import useUpdateQueryParam from '@/hooks/useUpdateQueryParam'
import { SHORTCUTS } from '@/constants/ui-url_search_params.constant'
import { useCallback, useEffect } from 'react'
import { RuleChangeEventArgs, RuleModel } from '@syncfusion/ej2-react-querybuilder'
import {
  convertSimpleQueryToComplexQuery,
  convertToURLSearchParams,
  searchParamsDeseriazlier,
} from '@/views/NDR/PortflowPage/utils/queryConvertors'
import isEmpty from 'lodash/isEmpty'
import useListenerClickBackBrowserButton from '@/views/NDR/PortflowPage/hooks/useListenerClickBackBrowserButton'
import { PortflowQueryType } from '@/firestoreQueries/ndr/portflowShortcuts/portflowShortcutsTypes'
import { TConditionOperator } from '@/views/NDR/PortflowPage/utils/types'
import PortFlowHeader from '@/views/NDR/PortflowPage/components/PortFlowHeader/PortFlowHeader'
import EntityProfileModal from '@/views/NDR/Inventory/entityProfile/EntityProfileModal'
import ShortcutsContainer from '@/views/NDR/PortflowPage/ShortcutsContainer'
import ReactflowContainer from '@/views/NDR/PortflowPage/ReactflowContainer'
import { useGraphStore } from '@/views/NDR/PortflowPage/enteties/store'
import PickDateRangeContainer from '@/views/NDR/PortflowPage/components/PickDateRangeContainer'
import { deserializeQueryUrlToSimpleQueryObj } from '@/views/NDR/PortflowPage/hooks/useFilteredOptions'
import { getDateRange } from '@/views/HunterX/Dashboard/utils'
import GroupViewInitializer from '@/components/GroupViewInitializer'

function HunterGraphContainer() {
  const [searchParams, setSearchParams] = useSearchParams()

  const { queryRules, setQueryRules, setFilterOptions, filterOptions, setDate } = useGraphStore()

  const { organization } = useOrganization()
  const organizationId = organization?.id as string

  const {
    activeNode,
    selectedNodes,
    isOpen,
    onOpenChange,
    entityData,
    openModalWithNode,
    setActiveNode,
    setSelectedNodes,
  } = usePortflowInteractions(organizationId)

  const { isShortcutsVisibleTrue, toggleShortcutsVisible } = useUpdateQueryParam({
    keyName: SHORTCUTS,
    options: ['true', 'false'],
  })

  const { dynamicFilterSuggestions, showAllTrafficPatterns, updateHistory, timerRef, toggleTrafficPatterns } =
    usePortflowFilters({ setFilterOptions, filterOptions })

  const handleAdvancedQueryFilter = useCallback(
    (updatedRule: RuleModel) => {
      const rule = isEmpty(updatedRule)
        ? {
            condition: searchParams.get('operator') ?? 'and',
            rules: [],
          }
        : updatedRule

      setQueryRules(rule)
    },
    [setQueryRules, searchParams],
  )
  const updateRule = (args: RuleChangeEventArgs) => {
    const params = convertToURLSearchParams(args.rule)
    setSearchParams(params, { replace: false })
    handleAdvancedQueryFilter(args.rule)
  }

  useListenerClickBackBrowserButton<RuleModel>({
    deserializeQueryParamFn: searchParamsDeseriazlier,
    optimisticUpdateFn: setQueryRules,
    debouncedFn: handleAdvancedQueryFilter,
    debouncedTimeout: 800,
  })

  const handleCleanTags = async () => {
    const params = new URLSearchParams()

    params.set('shortcutsVisible', 'true')
    params.set('operator', 'and')

    setSearchParams(params, { replace: false })
    setFilterOptions([])

    await handleAdvancedQueryFilter({
      condition: 'and',
      rules: [],
    })
  }

  const handleSimpleFilterChange = (newFilters: PortflowQueryType[]) => {
    setFilterOptions(newFilters)

    let defaultOperator = searchParams.get('operator') as TConditionOperator

    const queryRulesData = convertSimpleQueryToComplexQuery(newFilters, defaultOperator)

    const params = convertToURLSearchParams(queryRulesData)

    setSearchParams(params, { replace: false })
    handleAdvancedQueryFilter(queryRulesData)

    if (newFilters.length > 0) {
      updateHistory(newFilters)
    }
  }

  const onShortcutsChange = (tags: PortflowQueryType[]) => {
    if (timerRef.current) clearTimeout(timerRef.current)
    const query: RuleModel = convertSimpleQueryToComplexQuery(tags, searchParams.get('operator') as TConditionOperator)

    handleAdvancedQueryFilter(query)
    setFilterOptions(tags)

    if (tags.length > 0) {
      const timer = setTimeout(() => updateHistory(tags), 5000)
      timerRef.current = timer
    }
  }

  const onChangeFilter = (tags: PortflowQueryType[]) => {
    // Note: used for focus on Node
    const query: RuleModel = convertSimpleQueryToComplexQuery(tags, 'or')

    handleAdvancedQueryFilter(query)
    setFilterOptions(tags)
  }

  useEffect(() => {
    const rules = searchParamsDeseriazlier(searchParams)
    const filterOptions = deserializeQueryUrlToSimpleQueryObj(searchParams)

    setQueryRules(rules)
    setFilterOptions(filterOptions)
    setDate(
      getDateRange({
        days: 1,
        hours: 0,
        minutes: 0,
      }),
    )

    const params = new URLSearchParams(searchParams.toString())
    if (rules?.rules?.length === 0) {
      params.set('shortcutsVisible', 'true')
    } else {
      params.set('shortcutsVisible', 'false')
    }

    if (!searchParams.has('operator')) {
      params.set('operator', rules?.condition ?? 'and')
    }

    if (params.size > 0) {
      setSearchParams(params, { replace: true })
    }

    return () => {
      setDate({ startDate: null, endDate: null })
    }
  }, [])

  return (
    <GroupViewInitializer>
      <div style={{ height: '100%' }}>
        <ReactflowContainer
        queryRules={queryRules}
        onChangeQueryFilter={onChangeFilter}
        filterOptions={filterOptions}
        showAllTrafficPatterns={showAllTrafficPatterns}
        activeNode={activeNode}
        selectedNodes={selectedNodes}
        openModalWithNode={openModalWithNode}
        setActiveNode={setActiveNode}
        setSelectedNodes={setSelectedNodes}
      >
        <PortFlowHeader
          organizationId={organizationId}
          filterOptions={filterOptions}
          dynamicSuggestions={dynamicFilterSuggestions}
          onFilterChange={handleSimpleFilterChange}
          onCleanTags={handleCleanTags}
          onToggleShortcuts={toggleShortcutsVisible}
          onToggleTrafficPatterns={toggleTrafficPatterns}
          showAllTrafficPatterns={showAllTrafficPatterns}
          isShortcutsVisible={isShortcutsVisibleTrue}
          queryRules={queryRules}
          updateRule={updateRule}
        >
          <div className="mt-4">
            <PickDateRangeContainer className="!rounded-primary" />
          </div>
        </PortFlowHeader>
        <ShortcutsContainer onChangeQueryFilter={onShortcutsChange} />
      </ReactflowContainer>

        {entityData && <EntityProfileModal entity={entityData} isOpen={isOpen} onOpenChange={onOpenChange} />}
      </div>
    </GroupViewInitializer>
  )
}

export default HunterGraphContainer
