import { useOrganization } from '@clerk/clerk-react'
import { useSearchParams } from 'react-router-dom'
import useUpdateQueryParam from '@/hooks/useUpdateQueryParam'
import { SHORTCUTS } from '@/constants/ui-url_search_params.constant'
import { usePortflowLayout } from '@/views/NDR/PortflowPage/hooks'
import React, { startTransition, useEffect, useMemo, useRef, useState } from 'react'
import ReactFlow, { Background, ReactFlowInstance } from 'reactflow'
import { RawTypes } from '@/views/NDR/PortflowPage/utils/helpers'
import {
  CustomEdgeComponent,
  CustomNodeComponent,
  InstanceNode,
  InternetNode,
  UnrecognizedNode,
  withCustomActionEdge,
  withCustomActionNode,
} from '@/views/NDR/PortflowPage/components'
import GroupNode from '@/views/NDR/PortflowPage/components/GroupNode'
import GroupEdge from '@/views/NDR/PortflowPage/components/GroupEdge'
import GroupContainerNode from '@/views/NDR/PortflowPage/components/GroupContainerNode'
import { usePortflowGroupingAdapter } from '@/hooks/usePortflowGroupingAdapter'
import useDeepCompareEffect from '@/hooks/useDeepCompareEffect'
import useShuffleVisibleEdgesContainer from '@/views/NDR/PortflowPage/components/ShuffleVisibleEdgesContainer'
import { registerLicense } from '@syncfusion/ej2-base'
import treeNodeManipulator from '@/views/NDR/PortflowPage/utils/treeNodeManipulator'
import { PortflowQueryType } from '@/firestoreQueries/ndr/portflowShortcuts/portflowShortcutsTypes'
import { QueryRules } from '@/trafficPatternsQuery/evaluateQueryRules'
import useEdgesPagination from '@/views/NDR/PortflowPage/hooks/useEdgesPagination'
import { useGraphStore } from '@/views/NDR/PortflowPage/enteties/store'
import { AWSserviceNode } from '@/views/NDR/PortflowPage/components/AWSserviceNode'

registerLicense('Ngo9BigBOggjHTQxAR8/V1NDaF5cWWtCf1FpRmJGdld5fUVHYVZUTXxaS00DNHVRdkdnWH1ceXVXRGlYVkNwX0s=')

//@ts-ignore
const awsTreeManipulator = new treeNodeManipulator(null)

const edgeTypes = {
  customEdge: withCustomActionEdge(CustomEdgeComponent, [], []),
  groupEdge: GroupEdge,
}

function ReactflowContainer({
  queryRules,
  onChangeQueryFilter,
  dynamicFilterSuggestions,
  filterOptions,
  showAllTrafficPatterns,
  openModalWithNode,
  children,
}: {
  queryRules: QueryRules
  onChangeQueryFilter: (tags: PortflowQueryType[]) => void
  dynamicFilterSuggestions: PortflowQueryType[]
  filterOptions: PortflowQueryType[]
  showAllTrafficPatterns: boolean
  children: React.ReactNode
  openModalWithNode: any
}) {
  const { setFilterOptions, setQueryRules } = useGraphStore()

  const { organization } = useOrganization()
  const organizationId = organization?.id as string

  const [searchParams, setSearchParams] = useSearchParams()



  const { isShortcutsVisibleTrue } = useUpdateQueryParam({
    keyName: SHORTCUTS,
    options: ['true', 'false'],
  })

  // usePortflowData(organizationId, '')
  const { nodes } = usePortflowLayout(
    //@ts-ignore
    [], //treeData,
    [], //  entities,
    filterOptions,
    dynamicFilterSuggestions,
    showAllTrafficPatterns,
    awsTreeManipulator,
    isShortcutsVisibleTrue,
    // setTrafficPatterns,
    // setDynamicFilterSuggestions,
    queryRules, //queryRulesProps, // queryRules,
    false, //isRendering,
    true, //isDataLoaded,
    // entityIssuesMap,
  )

  const edges = useEdgesPagination({
    organizationId,
    queryRules,
  })

  // Use grouping adapter to handle grouped views
  const {
    nodes: groupedNodes,
    edges: groupedEdges,
    isGrouped,
    handleNodeClick: handleGroupedNodeClick,
  } = usePortflowGroupingAdapter({
    organizationId,
    originalNodes: nodes,
    originalEdges: edges,
  })

  // Use grouped nodes/edges when grouping is enabled, otherwise use original
  const finalNodes = isGrouped ? groupedNodes : nodes
  const finalEdges = isGrouped ? groupedEdges : edges

  // Add debug logging for node/edge changes
  useEffect(() => {
    console.log('ReactflowContainer - nodes/edges update:', {
      isGrouped,
      originalNodes: nodes.length,
      groupedNodes: groupedNodes.length,
      finalNodes: finalNodes.length,
      originalEdges: edges.length,
      groupedEdges: groupedEdges.length,
      finalEdges: finalEdges.length
    })
  }, [isGrouped, nodes.length, groupedNodes.length, finalNodes.length, edges.length, groupedEdges.length, finalEdges.length])

  // Only use finalEdges if they're valid, otherwise fall back to original edges
  // Also ensure edges have the required structure for the shuffle container
  const edgesForShuffle = useMemo(() => {
    const edgesToUse = (finalEdges && Array.isArray(finalEdges) && finalEdges.length >= 0) ? finalEdges : edges

    // Ensure all edges have the required properties
    return edgesToUse.filter(edge =>
      edge &&
      typeof edge === 'object' &&
      edge.id &&
      edge.source &&
      edge.target
    )
  }, [finalEdges, edges])

  const visibleEdges = useShuffleVisibleEdgesContainer({ edges: edgesForShuffle })

  const [reactFlowInstance, setReactFlowInstance] = useState<ReactFlowInstance | null>(null)

  const zoomingRefQue = useRef<any>([])

  useEffect(() => {
    return () => {
      setFilterOptions([])
      setQueryRules({
        condition: 'and',
        rules: [],
      })
    }
  }, [])

  const handleFilterByNodeLabel = (filters: { key: string; values: string[]; operator?: 'equal' | 'notequal' }[]) => {
    const params = new URLSearchParams()

    filters.forEach((filter) => {
      params.set(filter.key, filter.values.join(','))
    })

    setSearchParams([...params], { replace: false })
    onChangeQueryFilter(filters)
  }

  const nodeTypes = useMemo(
    () => ({
      // AWS types
      //@ts-ignore
      [RawTypes.ROOT]: withCustomActionNode(CustomNodeComponent),
      //@ts-ignore
      [RawTypes.ACCOUNT_NODE]: withCustomActionNode(CustomNodeComponent),
      //@ts-ignore
      [RawTypes.REGION_NODE]: withCustomActionNode(CustomNodeComponent),
      //@ts-ignore
      [RawTypes.VPC_NODE]: withCustomActionNode(CustomNodeComponent),
      //@ts-ignore
      [RawTypes.SUBNET_NODE]: withCustomActionNode(CustomNodeComponent),
      [RawTypes.INSTANCE_NODE]: withCustomActionNode(InstanceNode, openModalWithNode, handleFilterByNodeLabel),
      // SentinelOne one types
      //@ts-ignore
      [RawTypes.SITE]: withCustomActionNode(CustomNodeComponent),
      //@ts-ignore
      [RawTypes.TENANT_NODE]: withCustomActionNode(CustomNodeComponent),
      //@ts-ignore
      [RawTypes.GROUP]: withCustomActionNode(CustomNodeComponent),
      //@ts-ignore
      [RawTypes.DOMAIN_NODE]: withCustomActionNode(CustomNodeComponent),
      //@ts-ignore
      [RawTypes.AVAILABILITY_ZONE]: withCustomActionNode(CustomNodeComponent),
      [RawTypes.MACHINE]: withCustomActionNode(InstanceNode, openModalWithNode, handleFilterByNodeLabel),
      // Internet types
      //@ts-ignore
      [RawTypes.INTERNET_PARENT]: withCustomActionNode(CustomNodeComponent),
      [RawTypes.AWS_SERVICE_PARENT]: withCustomActionNode(CustomNodeComponent),
      //@ts-ignore
      [RawTypes.INTERNET_CHILD]: withCustomActionNode(InternetNode, null as any, handleFilterByNodeLabel),
      //@ts-ignore
      [RawTypes.UNRECOGNIZED_PARENT]: withCustomActionNode(CustomNodeComponent),
      //@ts-ignore
      [RawTypes.UNRECOGNIZED_SUBNET]: withCustomActionNode(CustomNodeComponent),
      //@ts-ignore
      [RawTypes.UNRECOGNIZED_CHILD]: withCustomActionNode(UnrecognizedNode, null as any, handleFilterByNodeLabel),
      // Active Directory
      //@ts-ignore
      [RawTypes.ACTIVE_DIRECTORY]: withCustomActionNode(CustomNodeComponent),
      //@ts-ignore
      [RawTypes.ACTIVE_DIRECTORY_OU]: withCustomActionNode(CustomNodeComponent),
      [RawTypes.ACTIVE_DIRECTORY_COMPUTER]: withCustomActionNode(
        InstanceNode,
        openModalWithNode,
        handleFilterByNodeLabel,
      ),
      //@ts-ignore
      [RawTypes.SUBTYPE_NODE]: withCustomActionNode(CustomNodeComponent),
      [RawTypes.AWS_SERVICE_NODE]: withCustomActionNode(AWSserviceNode, null as any, handleFilterByNodeLabel),
      // Group node types
      groupNode: GroupNode,
      groupContainer: GroupContainerNode,
    }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [],
  )

  const handleInit = (instance: ReactFlowInstance) => {
    startTransition(() => {
      setReactFlowInstance(instance)
    })
  }

  const queryRulesRef = useRef(queryRules)
  queryRulesRef.current = queryRules

  useDeepCompareEffect(() => {
    if (zoomingRefQue.current) {
      // Stop prev zooming, and start the new one
      clearTimeout(zoomingRefQue.current)
    }

    zoomingRefQue.current = setTimeout(() => {
      // const FirstMarkedNode = reactFlowInstance?.getNodes().filter((node) => node?.data?.marked === true)
      if (queryRulesRef.current?.rules && queryRulesRef.current.rules.length > 0) {
        reactFlowInstance?.fitView({ nodes: reactFlowInstance?.getNodes(), duration: 500 })
      } else {
        reactFlowInstance?.setViewport({ x: 100, y: 250, zoom: 0.25 }, { duration: 500 })
      }
    }, 800)

    return () => clearTimeout(zoomingRefQue.current) // Clean up the timeout to avoid memory leaks.
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [nodes, reactFlowInstance])

  return (
    <>
      <ReactFlow
        key={`reactflow-${isGrouped ? 'grouped' : 'normal'}`}
        onInit={handleInit}
        nodes={finalNodes}
        edges={visibleEdges}
        onNodeClick={(_event, node) => {
          // Handle grouped node clicks when grouping is enabled
          if (isGrouped && handleGroupedNodeClick) {
            const handled = handleGroupedNodeClick(node.id, node.data)
            if (handled) {
              return // Don't proceed with default behavior
            }
          }
          // Default node click behavior can go here if needed
        }}
        onMouseDownCapture={() => {
          document.dispatchEvent(new CustomEvent('react-flow__mousedown', { bubbles: true }))
        }}
        nodeTypes={nodeTypes}
        edgeTypes={edgeTypes}
        fitView
        attributionPosition="top-right"
        minZoom={0.05}
        maxZoom={1.2}
        zoomOnDoubleClick={false}
        onlyRenderVisibleElements
        proOptions={{ hideAttribution: true }}
      >
        <Background />
        {children}
      </ReactFlow>
    </>
  )
}

export default ReactflowContainer
