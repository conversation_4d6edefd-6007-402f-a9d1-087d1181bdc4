import React, { useEffect } from 'react'
import { useUser } from '@clerk/clerk-react'
import _ from 'lodash'
import Tooltip from '@/components/ui/Tooltip'
import {
  PortflowQueryType,
  PortflowShortcutType,
} from '@/firestoreQueries/ndr/portflowShortcuts/portflowShortcutsTypes'
import useCreateSavedPortflowQuery from '@/firestoreQueries/ndr/savedPortflowQueries/hooks/useCreateSavedPortflowQuery'
import { Icon } from '@iconify/react'
import { Button, cn, colors } from '@nextui-org/react'
import { DynamicFiltersInterface } from '../../utils/types'
import CreateSaveQuery from '../CreateSaveQuery'
import MultiSelect from '../MultiSelect'
import PortflowTrafficPatternsTable from './PortflowTrafficPatternsTable'
import { useSearchParams } from 'react-router-dom'
import { RuleChangeEventArgs } from '@syncfusion/ej2-react-querybuilder'
import AndOr<PERSON>uttons, { TAndOrUnion } from '@/views/NDR/PortflowPage/components/PortFlowHeader/AndOrButtons'
import { SHORTCUTS } from '@/constants/ui-url_search_params.constant'

import './querybuilder.css'
import { useAvailableFields } from '@/views/NDR/PortflowPage/components/MultiSelect/MultiSelectContainer'
import useGraphConfig from '@/views/NDR/PortflowPage/hooks/useGraphConfig'
import MultiSelectContainerErrorBoundary from '../MultiSelect/MultiSelectContainerErrorBoundary'
import { useGraphStore } from '@/views/NDR/PortflowPage/enteties/store'
import GroupedByButton from '../GroupedByButton/GroupedByButton'
import GroupViewModal from '../GroupViewModal/GroupViewModal'

interface PortflowHeaderProps {
  filterOptions: PortflowQueryType[]
  dynamicSuggestions: DynamicFiltersInterface
  onFilterChange: (filters: PortflowQueryType[]) => void
  onCleanTags: () => void
  onToggleShortcuts: () => void
  onToggleTrafficPatterns: () => void
  showAllTrafficPatterns: boolean
  isShortcutsVisible: boolean
  organizationId: string | undefined
  queryRules?: RuleChangeEventArgs
  updateRule: (rule: RuleChangeEventArgs) => void
  children: React.ReactNode
}

const PortflowHeader: React.FC<PortflowHeaderProps> = ({
  filterOptions,
  onFilterChange,
  onCleanTags,
  onToggleShortcuts,
  onToggleTrafficPatterns,
  showAllTrafficPatterns,
  isShortcutsVisible,
  organizationId,
  queryRules,
  updateRule,
  children,
}) => {
  const { tableId } = useGraphConfig()
  const { availableFields } = useAvailableFields({ tableId })

  const { isQueryBuilderVisible } = useGraphStore()
  const {
    mutate: createSavedPortflowQuery,
    isPending: isCreateQueryPending,
    isSuccess: isCreateQuerySuccess,
    reset: resetCreateQueryStates,
  } = useCreateSavedPortflowQuery()
  const { user } = useUser()

  const [searchParams] = useSearchParams()
  const [isGroupViewModalOpen, setIsGroupViewModalOpen] = React.useState(false)

  useEffect(() => {
    if (isCreateQuerySuccess) {
      resetCreateQueryStates()
    }
  }, [isCreateQuerySuccess])

  function handleCreateQuery(name: string) {
    const correct: PortflowShortcutType = filterOptions.reduce(
      (acc, filter) => {
        if (!acc.filter) {
          acc.filter = []
        }
        acc.filter.push(filter)
        return acc
      },
      {
        userID: user!.id,
        image: '/shortcutsAssets/saved.png',
        title: name,
        subTitile: 'Saved query',
        filter: [],
        category: 'Saved Queries',
      } as PortflowShortcutType,
    )

    createSavedPortflowQuery({ organizationId, savedQuery: correct })
  }

  // TODO: temp solution.
  // Maybe better to have omitted query.condition in usePortflowFilters()
  const withQueryRules = _.filter(filterOptions, (param) => !['operator', SHORTCUTS].includes(param.key))

  return (
    <>
      <div
        className="block flex-row gap-4 p-4 bg-transperent"
        style={{
          position: 'absolute',
          zIndex: '41',
          left: '0',
        }}
      >
        {isQueryBuilderVisible && (
          <div className="flex gap-4 items-start">
            <MultiSelectContainerErrorBoundary>
              <div className="p-4">
                <MultiSelect
                  initialTags={withQueryRules}
                  onTagsChange={onFilterChange}
                  dynamicSuggestions={[]} // dynamicSuggestions
                  availableFields={availableFields}
                  table="traffic"
                  style={{ minWidth: 400, height: 28 }}
                >
                  <AndOrButtons
                    value={(searchParams.get('operator') as TAndOrUnion) ?? 'and'}
                    queryRules={queryRules as RuleChangeEventArgs}
                    ruleChange={updateRule}
                  />

                  <div className={'flex flex-grow'}>
                    {withQueryRules.length > 0 && (
                      <Tooltip title="Reset Filter">
                        <Button isIconOnly onClick={onCleanTags} className="bg-background border-default-200">
                          <Icon
                            color={colors.blue['500']}
                            fontSize={22}
                            icon="fluent:text-clear-formatting-24-filled"
                          />
                        </Button>
                      </Tooltip>
                    )}
                    {withQueryRules.length > 0 && (
                      <CreateSaveQuery
                        isSuccessful={isCreateQuerySuccess}
                        isLoading={isCreateQueryPending}
                        onSaveQuery={handleCreateQuery}
                        queries={withQueryRules}
                      />
                    )}
                  </div>
                </MultiSelect>
              </div>
            </MultiSelectContainerErrorBoundary>

            <div className="flex gap-4 items-start mt-4">
              <Tooltip title={'Shortcuts'} className="z-[10000]">
                <Button
                  isIconOnly
                  className={cn('h-[42px] w-[42px] border', {
                    'dark:bg-[#111827] dark:border-gray-700 bg-white border-gray-300 shadow-primary-shadow':
                      !isShortcutsVisible, // Inactive state styles
                    'dark:bg-gray-200 dark:border-gray-200 bg-blue-600 border-blue-600 shadow-lg': isShortcutsVisible, // Active state styles
                  })}
                  onClick={() => {
                    onToggleShortcuts()
                  }}
                >
                  <Icon
                    icon="fe:magic"
                    fontSize={24}
                    className={cn({
                      'dark:text-gray-200 text-gray-500 opacity-100': !isShortcutsVisible, // Inactive icon styles
                      'dark:text-gray-800 text-white opacity-100': isShortcutsVisible, // Active icon styles
                    })}
                  />
                </Button>
              </Tooltip>

              <GroupedByButton onOpenModal={() => setIsGroupViewModalOpen(true)} />
            </div>
            <>{children}</>
          </div>
        )}
        <PortflowTrafficPatternsTable queryRules={queryRules} onFilterChange={onFilterChange} />
      </div>

      {/* Group View Modal */}
      <GroupViewModal
        isOpen={isGroupViewModalOpen}
        onClose={() => setIsGroupViewModalOpen(false)}
      />
    </>
  )
}

export default PortflowHeader
