import { memo } from 'react'
import { Handle, Position } from 'reactflow'
import styled from 'styled-components'
import useGraphConfig from '@/views/NDR/PortflowPage/hooks/useGraphConfig'

const StyledGroupContainer = styled.div<{
  theme: 'dark' | 'default'
}>`
  background: ${({ theme }) => (theme === 'dark' ? '#1f2937' : '#f9fafb')};
  border: 2px solid ${({ theme }) => (theme === 'dark' ? '#374151' : '#d1d5db')};
  border-radius: 12px;
  padding: 20px;
  min-width: 400px;
  min-height: 300px;
  position: relative;

  .group-header {
    background: ${({ theme }) => (theme === 'dark' ? '#111827' : '#ffffff')};
    border: 1px solid ${({ theme }) => (theme === 'dark' ? '#374151' : '#e5e7eb')};
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 16px;
    
    .group-title {
      font-size: 16px;
      font-weight: 600;
      color: ${({ theme }) => (theme === 'dark' ? '#f9fafb' : '#111827')};
      margin-bottom: 4px;
    }
    
    .group-subtitle {
      font-size: 12px;
      color: ${({ theme }) => (theme === 'dark' ? '#9ca3af' : '#6b7280')};
    }
  }

  .entities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 12px;
    max-height: 200px;
    overflow-y: auto;
  }

  .entity-item {
    background: ${({ theme }) => (theme === 'dark' ? '#111827' : '#ffffff')};
    border: 1px solid ${({ theme }) => (theme === 'dark' ? '#374151' : '#e5e7eb')};
    border-radius: 6px;
    padding: 8px;
    text-align: center;
    font-size: 11px;
    color: ${({ theme }) => (theme === 'dark' ? '#f9fafb' : '#111827')};
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: ${({ theme }) => (theme === 'dark' ? '#374151' : '#f3f4f6')};
      transform: translateY(-1px);
    }

    .entity-name {
      font-weight: 500;
      margin-bottom: 2px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .entity-type {
      color: ${({ theme }) => (theme === 'dark' ? '#9ca3af' : '#6b7280')};
      font-size: 10px;
    }
  }

  .back-button {
    position: absolute;
    top: -15px;
    right: -15px;
    background: ${({ theme }) => (theme === 'dark' ? '#ef4444' : '#dc2626')};
    color: white;
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: bold;
    transition: all 0.2s ease;

    &:hover {
      background: ${({ theme }) => (theme === 'dark' ? '#dc2626' : '#b91c1c')};
      transform: scale(1.1);
    }
  }
`

interface GroupContainerNodeProps {
  data: {
    label: string
    entities: any[]
    groupKeys: Record<string, string>
    isBackButton?: boolean
    onEntityClick?: (entityId: string) => void
    onBackClick?: () => void
  }
}

export const GroupContainerNode = memo(({ data }: GroupContainerNodeProps) => {
  const { theme } = useGraphConfig()
  const { label, entities, groupKeys, onEntityClick, onBackClick } = data

  const handleEntityClick = (entity: any) => {
    if (onEntityClick) {
      onEntityClick(entity.id)
    }
  }

  const handleBackClick = () => {
    if (onBackClick) {
      onBackClick()
    }
  }

  return (
    <>
      <StyledGroupContainer theme={theme}>
        <button className="back-button" onClick={handleBackClick} title="Back to Groups">
          ×
        </button>
        
        <div className="group-header">
          <div className="group-title">{label}</div>
          <div className="group-subtitle">
            {entities.length} entities
            {Object.keys(groupKeys).length > 0 && (
              <span> • {Object.entries(groupKeys).map(([key, value]) => `${key}: ${value}`).join(', ')}</span>
            )}
          </div>
        </div>

        <div className="entities-grid">
          {entities.map((entity, index) => (
            <div
              key={entity.id || index}
              className="entity-item"
              onClick={() => handleEntityClick(entity)}
              title={entity.name || entity.id}
            >
              <div className="entity-name">{entity.name || entity.id}</div>
              <div className="entity-type">{entity.subType || entity.type || 'unknown'}</div>
            </div>
          ))}
        </div>
      </StyledGroupContainer>

      <Handle
        type="target"
        position={Position.Bottom}
        style={{
          width: '15px',
          height: '15px',
          borderRadius: '50%',
          border: theme === 'default' ? '2px solid #eaeaea' : '2px solid #374151',
          background: theme === 'default' ? 'white' : '#111827',
        }}
      />
      <Handle
        type="source"
        position={Position.Top}
        style={{
          width: '15px',
          height: '15px',
          borderRadius: '50%',
          border: theme === 'default' ? '2px solid #eaeaea' : '2px solid #374151',
          background: theme === 'default' ? 'white' : '#111827',
        }}
      />
    </>
  )
})

export default GroupContainerNode
