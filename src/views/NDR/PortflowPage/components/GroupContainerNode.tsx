import { memo } from 'react'
import { Handle, Position } from 'reactflow'
import styled from 'styled-components'
import useGraphConfig from '@/views/NDR/PortflowPage/hooks/useGraphConfig'

// Helper function to get entity icon based on type
const getEntityIcon = (entityType: string) => {
  switch (entityType?.toLowerCase()) {
    case 'sentinelone':
    case 'activedirectory':
      return 'S1'
    case 'crowdstrike':
      return 'CS'
    case 'msdefender':
      return 'MD'
    case 'aws':
    case 'ec2':
      return 'AWS'
    case 'machine':
    case 'instance':
      return '💻'
    case 'server':
      return '🖥️'
    case 'workstation':
      return '🖥️'
    default:
      return '📱'
  }
}

const StyledGroupContainer = styled.div<{
  theme: 'dark' | 'default'
  entityCount: number
}>`
  background: ${({ theme }) => (theme === 'dark' ? '#1f2937' : '#f9fafb')};
  border: 2px solid ${({ theme }) => (theme === 'dark' ? '#374151' : '#d1d5db')};
  border-radius: 12px;
  padding: 20px;
  position: relative;
  
  /* Dynamic sizing based on entity count */
  width: ${({ entityCount }) => {
    if (entityCount === 0) return '400px'
    const cols = Math.min(Math.ceil(Math.sqrt(entityCount)), 6) // Max 6 columns
    const minWidth = 400
    const itemWidth = 140 // 120px item + 12px gap + padding
    return Math.max(minWidth, cols * itemWidth + 40) + 'px' // +40 for padding
  }};
  
  height: ${({ entityCount }) => {
    if (entityCount === 0) return '200px'
    const cols = Math.min(Math.ceil(Math.sqrt(entityCount)), 6)
    const rows = Math.ceil(entityCount / cols)
    const headerHeight = 80 // Header + margins
    const itemHeight = 72 // Item height (60px) + gap (12px)
    const minHeight = 200
    return Math.max(minHeight, headerHeight + (rows * itemHeight) + 40) + 'px' // +40 for padding
  }};

  .group-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 16px;
    color: white;

    .group-title {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 4px;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .group-subtitle {
      font-size: 12px;
      opacity: 0.9;
    }

    .group-icon {
      font-size: 18px;
    }
  }

  .entities-grid {
    display: grid;
    grid-template-columns: ${({ entityCount }) => {
      const cols = Math.min(Math.ceil(Math.sqrt(entityCount)), 6) // Max 6 columns
      return `repeat(${cols}, 1fr)`
    }};
    gap: 12px;
    width: 100%;
    justify-items: center;
    /* No max-height or overflow - let it expand naturally */
  }

  .entity-item {
    /* Match InstanceNode styling */
    background: ${({ theme }) => (theme === 'dark' ? '#111827' : '#ffffff')};
    border-radius: 8px;
    padding: 15px 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 140px;
    height: 80px;
    position: relative;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    .entity-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 35px;
      margin-bottom: 8px;
    }

    .entity-name {
      font-size: 14px;
      font-weight: 500;
      color: ${({ theme }) => (theme === 'dark' ? '#f9fafb' : '#000000')};
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      flex: 1;
      margin-right: 8px;
    }

    .entity-icon {
      width: 24px;
      height: 24px;
      background: ${({ theme }) => (theme === 'dark' ? '#374151' : '#f3f4f6')};
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      color: ${({ theme }) => (theme === 'dark' ? '#9ca3af' : '#6b7280')};
    }

    .entity-type {
      font-size: 12px;
      color: ${({ theme }) => (theme === 'dark' ? '#9ca3af' : '#6b7280')};
      text-transform: capitalize;
    }

    .entity-labels {
      margin-top: 4px;
      display: flex;
      flex-wrap: wrap;
      gap: 2px;
    }

    .label-chip {
      background: ${({ theme }) => (theme === 'dark' ? '#374151' : '#e5e7eb')};
      color: ${({ theme }) => (theme === 'dark' ? '#d1d5db' : '#374151')};
      border-radius: 4px;
      padding: 1px 4px;
      font-size: 9px;
      font-weight: 500;
    }
  }

  .back-button {
    position: absolute;
    top: -15px;
    right: -15px;
    background: ${({ theme }) => (theme === 'dark' ? '#ef4444' : '#dc2626')};
    color: white;
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: bold;
    transition: all 0.2s ease;

    &:hover {
      background: ${({ theme }) => (theme === 'dark' ? '#dc2626' : '#b91c1c')};
      transform: scale(1.1);
    }
  }
`

interface GroupContainerNodeProps {
  data: {
    label: string
    entities: any[]
    groupKeys: Record<string, string>
    isBackButton?: boolean
    onEntityClick?: (entityId: string) => void
    onBackClick?: () => void
  }
}

export const GroupContainerNode = memo(({ data }: GroupContainerNodeProps) => {
  const { theme } = useGraphConfig()
  const { label, entities, groupKeys, onEntityClick, onBackClick } = data

  // Calculate layout for debugging
  const entityCount = entities.length
  const cols = Math.min(Math.ceil(Math.sqrt(entityCount)), 6)
  const rows = Math.ceil(entityCount / cols)
  
  console.log('GroupContainerNode - layout calculation:', {
    entityCount,
    cols,
    rows,
    label
  })

  const handleEntityClick = (entity: any) => {
    if (onEntityClick) {
      onEntityClick(entity.id)
    }
  }

  const handleBackClick = () => {
    if (onBackClick) {
      onBackClick()
    }
  }

  return (
    <>
      <StyledGroupContainer theme={theme} entityCount={entities.length}>
        <button className="back-button" onClick={handleBackClick} title="Back to Groups">
          ×
        </button>
        
        <div className="group-header">
          <div className="group-title">
            <span className="group-icon">👥</span>
            {label}
          </div>
          <div className="group-subtitle">
            {entities.length} {entities.length === 1 ? 'entity' : 'entities'}
            {Object.keys(groupKeys).length > 0 && (
              <span> • {Object.entries(groupKeys).map(([key, value]) => `${key}: ${value}`).join(', ')}</span>
            )}
          </div>
        </div>

        <div className="entities-grid">
          {entities.map((entity, index) => (
            <div
              key={entity.id || index}
              className="entity-item"
              onClick={() => handleEntityClick(entity)}
              title={entity.name || entity.id}
            >
              <div className="entity-header">
                <div className="entity-name">{entity.name || entity.id}</div>
                <div className="entity-icon">
                  {getEntityIcon(entity.subType || entity.type)}
                </div>
              </div>
              <div className="entity-type">{entity.subType || entity.type || 'unknown'}</div>
              {entity.labels && entity.labels.length > 0 && (
                <div className="entity-labels">
                  {entity.labels.slice(0, 2).map((label: any, labelIndex: number) => (
                    <div key={labelIndex} className="label-chip">
                      {label.key}: {label.values?.[0] || 'N/A'}
                    </div>
                  ))}
                  {entity.labels.length > 2 && (
                    <div className="label-chip">+{entity.labels.length - 2}</div>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>
      </StyledGroupContainer>

      <Handle
        type="target"
        position={Position.Bottom}
        style={{
          width: '15px',
          height: '15px',
          borderRadius: '50%',
          border: theme === 'default' ? '2px solid #eaeaea' : '2px solid #374151',
          background: theme === 'default' ? 'white' : '#111827',
        }}
      />
      <Handle
        type="source"
        position={Position.Top}
        style={{
          width: '15px',
          height: '15px',
          borderRadius: '50%',
          border: theme === 'default' ? '2px solid #eaeaea' : '2px solid #374151',
          background: theme === 'default' ? 'white' : '#111827',
        }}
      />
    </>
  )
})

export default GroupContainerNode
