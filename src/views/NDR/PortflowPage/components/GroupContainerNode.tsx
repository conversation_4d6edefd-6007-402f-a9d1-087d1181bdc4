import { memo } from 'react'
import { StyledInstanceNode } from './InstanceNode'
import useGraphConfig from '@/views/NDR/PortflowPage/hooks/useGraphConfig'

interface GroupContainerNodeProps {
  data: {
    label: string
    entities: any[]
    groupKeys: Record<string, string>
    isBackButton?: boolean
    onEntityClick?: (entityId: string) => void
    onBackClick?: () => void
  }
}

// This component now just shows a header node - individual entities will be separate InstanceNodes
export const GroupContainerNode = memo(({ data }: GroupContainerNodeProps) => {
  const { theme } = useGraphConfig()
  const { label, entities, groupKeys, onBackClick } = data

  const handleBackClick = () => {
    if (onBackClick) {
      onBackClick()
    }
  }

  return (
    <StyledInstanceNode
      marked={false}
      isZoomedOut={false}
      theme={theme}
      withIssues={false}
      isFocusNode={false}
      onClick={handleBackClick}
    >
      <div className="header">
        <div className="node-name truncate dark:text-gray-100">
          ← Back to Groups
        </div>
        <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
          <span>🔙</span>
        </div>
      </div>

      <div className="content">
        <div className="sub-title">
          {label} ({entities.length} {entities.length === 1 ? 'entity' : 'entities'})
          {Object.keys(groupKeys).length > 0 && (
            <div style={{ marginTop: '4px', fontSize: '10px' }}>
              {Object.entries(groupKeys).map(([key, value]) => `${key}: ${value}`).join(', ')}
            </div>
          )}
        </div>
      </div>
    </StyledInstanceNode>
  )
})

export default GroupContainerNode
