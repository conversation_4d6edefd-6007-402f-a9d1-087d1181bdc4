import React, { useState } from 'react'
import { 
  Table, 
  TableHeader, 
  TableColumn, 
  TableBody, 
  TableRow, 
  TableCell,
  Button,
  Chip,
  Dropdown,
  DropdownTrigger,
  DropdownMenu,
  DropdownItem,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  useDisclosure
} from '@nextui-org/react'
import { Icon } from '@iconify/react'
import { useUser, useOrganization } from '@clerk/clerk-react'
import { 
  useGetUserGroupViews, 
  useDeleteGroupView, 
  useSetDefaultGroupView 
} from '@/firestoreQueries/ndr/groupViews/hooks/useGroupViews'
import { useGroupViewStore } from '@/zustandStores/useGroupViewStore'
import { GroupViewWithId } from '@globalTypes/ndrBenchmarkTypes/groupViews/groupViewTypes'
import { toast } from '@/components/ui'
import ToastComponent from '@/generalComponents/ToastComponent'

interface SavedViewsTabProps {
  onViewSelected: (view: GroupViewWithId) => void
  onViewDeleted: () => void
  currentView: GroupViewWithId | null
}

const SavedViewsTab: React.FC<SavedViewsTabProps> = ({ 
  onViewSelected, 
  onViewDeleted, 
  currentView 
}) => {
  const { user } = useUser()
  const { organization } = useOrganization()
  const organizationId = organization?.id
  const userId = user?.id

  const [viewToDelete, setViewToDelete] = useState<GroupViewWithId | null>(null)
  const { isOpen: isDeleteModalOpen, onOpen: onDeleteModalOpen, onClose: onDeleteModalClose } = useDisclosure()

  const { resetGrouping } = useGroupViewStore()
  
  const { data: userGroupViews = [], isLoading } = useGetUserGroupViews(organizationId, userId)
  const deleteGroupView = useDeleteGroupView(organizationId)
  const setDefaultGroupView = useSetDefaultGroupView(organizationId, userId)

  const handleDeleteClick = (view: GroupViewWithId) => {
    setViewToDelete(view)
    onDeleteModalOpen()
  }

  const handleDeleteConfirm = async () => {
    if (!viewToDelete) return

    try {
      await deleteGroupView.mutateAsync(viewToDelete.id)
      
      toast.push(
        ToastComponent({
          title: 'Success',
          message: `Group view "${viewToDelete.name}" deleted successfully`,
          type: 'success'
        })
      )

      // If the deleted view was the current view, reset grouping
      if (currentView?.id === viewToDelete.id) {
        resetGrouping()
        onViewDeleted()
      }

      onDeleteModalClose()
      setViewToDelete(null)
    } catch (error) {
      toast.push(
        ToastComponent({
          title: 'Error',
          message: 'Failed to delete group view',
          type: 'danger'
        })
      )
    }
  }

  const handleSetDefault = async (view: GroupViewWithId) => {
    try {
      await setDefaultGroupView.mutateAsync(view.id)
      
      toast.push(
        ToastComponent({
          title: 'Success',
          message: `"${view.name}" set as default view`,
          type: 'success'
        })
      )
    } catch (error) {
      toast.push(
        ToastComponent({
          title: 'Error',
          message: 'Failed to set default view',
          type: 'danger'
        })
      )
    }
  }

  const formatKeys = (view: GroupViewWithId) => {
    const keys = [view.primaryKey]
    if (view.secondaryKey) keys.push(view.secondaryKey)
    if (view.additionalKeys?.length) {
      keys.push(`+${view.additionalKeys.length} more`)
    }
    return keys.join(' → ')
  }

  const renderActions = (view: GroupViewWithId) => (
    <Dropdown>
      <DropdownTrigger>
        <Button
          isIconOnly
          size="sm"
          variant="light"
        >
          <Icon icon="material-symbols:more-vert" fontSize={16} />
        </Button>
      </DropdownTrigger>
      <DropdownMenu aria-label="View actions">
        <DropdownItem
          key="select"
          startContent={<Icon icon="material-symbols:play-arrow" fontSize={16} />}
          onPress={() => onViewSelected(view)}
        >
          Apply View
        </DropdownItem>
        <DropdownItem
          key="set-default"
          startContent={<Icon icon="material-symbols:star" fontSize={16} />}
          onPress={() => handleSetDefault(view)}
          isDisabled={view.isDefault}
        >
          Set as Default
        </DropdownItem>
        <DropdownItem
          key="delete"
          className="text-danger"
          color="danger"
          startContent={<Icon icon="material-symbols:delete" fontSize={16} />}
          onPress={() => handleDeleteClick(view)}
        >
          Delete
        </DropdownItem>
      </DropdownMenu>
    </Dropdown>
  )

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-8">
        <Icon icon="material-symbols:progress-activity" fontSize={24} className="animate-spin" />
      </div>
    )
  }

  if (userGroupViews.length === 0) {
    return (
      <div className="text-center py-8">
        <Icon icon="material-symbols:bookmark-border" fontSize={48} className="text-gray-300 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No saved views</h3>
        <p className="text-gray-500">Create your first group view in the "New View" tab</p>
      </div>
    )
  }

  return (
    <>
      <Table aria-label="Saved group views">
        <TableHeader>
          <TableColumn>NAME</TableColumn>
          <TableColumn>KEYS</TableColumn>
          <TableColumn>STATUS</TableColumn>
          <TableColumn>ACTIONS</TableColumn>
        </TableHeader>
        <TableBody>
          {userGroupViews.map((view) => (
            <TableRow key={view.id}>
              <TableCell>
                <div className="flex items-center gap-2">
                  <span className="font-medium">{view.name}</span>
                  {currentView?.id === view.id && (
                    <Chip size="sm" color="success" variant="flat">
                      Active
                    </Chip>
                  )}
                </div>
              </TableCell>
              <TableCell>
                <span className="text-sm text-gray-600">{formatKeys(view)}</span>
              </TableCell>
              <TableCell>
                <div className="flex items-center gap-2">
                  {view.isDefault && (
                    <Chip
                      size="sm"
                      color="warning"
                      variant="flat"
                      startContent={<Icon icon="material-symbols:star" fontSize={12} />}
                    >
                      Default
                    </Chip>
                  )}
                </div>
              </TableCell>
              <TableCell>
                {renderActions(view)}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {/* Delete Confirmation Modal */}
      <Modal isOpen={isDeleteModalOpen} onClose={onDeleteModalClose}>
        <ModalContent>
          <ModalHeader>
            <div className="flex items-center gap-2">
              <Icon icon="material-symbols:warning" fontSize={24} className="text-red-500" />
              <span>Delete Group View</span>
            </div>
          </ModalHeader>
          <ModalBody>
            <p>
              Are you sure you want to delete the group view "{viewToDelete?.name}"? 
              This action cannot be undone.
            </p>
            {viewToDelete?.isDefault && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                <div className="flex items-center gap-2">
                  <Icon icon="material-symbols:info" fontSize={16} className="text-yellow-600" />
                  <span className="text-sm text-yellow-800">
                    This is your default view. Deleting it will remove the default setting.
                  </span>
                </div>
              </div>
            )}
          </ModalBody>
          <ModalFooter>
            <Button variant="light" onPress={onDeleteModalClose}>
              Cancel
            </Button>
            <Button 
              color="danger" 
              onPress={handleDeleteConfirm}
              isLoading={deleteGroupView.isPending}
            >
              Delete
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  )
}

export default SavedViewsTab
