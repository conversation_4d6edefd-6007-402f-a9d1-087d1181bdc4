# Group View Components

This directory contains the components for the Dynamic Grouping feature in the PortFlow page.

## Components Overview

### GroupViewModal
The main modal component that provides the interface for creating and managing group views.

**Props:**
- `isOpen: boolean` - Controls modal visibility
- `onClose: () => void` - Callback when modal is closed

**Features:**
- Tabbed interface with "New View" and "Saved Views" tabs
- Responsive design with proper scrolling
- Integration with the group view store

### NewViewTab
Component for creating new group views.

**Props:**
- `availableKeys: string[]` - Available label keys for grouping
- `onViewCreated: (view: GroupViewWithId) => void` - Callback when view is created
- `currentView: GroupViewWithId | null` - Currently active view

**Features:**
- Form validation for required fields
- Search functionality for label keys
- Support for primary, secondary, and additional keys
- Preview of grouping configuration
- Default view setting

### SavedViewsTab
Component for managing existing group views.

**Props:**
- `onViewSelected: (view: GroupViewWithId) => void` - Callback when view is selected
- `onViewDeleted: () => void` - Callback when view is deleted
- `currentView: GroupViewWithId | null` - Currently active view

**Features:**
- Table display of saved views
- Actions menu for each view (Apply, Set as Default, Delete)
- Delete confirmation modal
- Status indicators (Active, Default)

## Usage Example

```tsx
import GroupViewModal from './GroupViewModal'

function MyComponent() {
  const [isModalOpen, setIsModalOpen] = useState(false)

  return (
    <>
      <button onClick={() => setIsModalOpen(true)}>
        Open Group Views
      </button>
      
      <GroupViewModal 
        isOpen={isModalOpen} 
        onClose={() => setIsModalOpen(false)} 
      />
    </>
  )
}
```

## State Management

The components integrate with the `useGroupViewStore` Zustand store for state management:

```tsx
const {
  currentView,
  setCurrentView,
  setIsGrouped,
  availableKeys
} = useGroupViewStore()
```

## Data Flow

1. **Creating Views**: NewViewTab → useCreateGroupView → Firestore → Store Update
2. **Loading Views**: SavedViewsTab → useGetUserGroupViews → Display
3. **Applying Views**: View Selection → Store Update → Graph Re-render
4. **Deleting Views**: Delete Action → useDeleteGroupView → Firestore → Store Update

## Styling

Components use NextUI components for consistent styling:
- `Modal`, `ModalContent`, `ModalHeader`, `ModalBody`, `ModalFooter`
- `Tabs`, `Tab` for tabbed interface
- `Input`, `Select`, `Button`, `Checkbox` for form elements
- `Table` components for saved views display

## Error Handling

- Form validation with user-friendly error messages
- Toast notifications for success/error states
- Loading states during async operations
- Graceful handling of missing data

## Accessibility

- Proper ARIA labels and roles
- Keyboard navigation support
- Focus management in modal
- Screen reader friendly content

## Testing

Components can be tested using React Testing Library:

```tsx
import { render, screen, fireEvent } from '@testing-library/react'
import GroupViewModal from './GroupViewModal'

test('opens modal and displays tabs', () => {
  render(<GroupViewModal isOpen={true} onClose={jest.fn()} />)
  
  expect(screen.getByText('New View')).toBeInTheDocument()
  expect(screen.getByText('Saved Views')).toBeInTheDocument()
})
```

## Performance Considerations

- Components use React.memo where appropriate
- Expensive calculations are memoized
- Form state is managed efficiently
- Debounced search for label keys

## Future Enhancements

- Drag and drop for key ordering
- Bulk operations for saved views
- Import/export functionality
- Advanced filtering options
- Custom grouping algorithms
