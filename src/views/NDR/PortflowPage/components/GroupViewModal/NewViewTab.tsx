import React, { useState, useMemo, useEffect } from 'react'
import { 
  Input, 
  Select, 
  SelectItem, 
  Button, 
  Checkbox, 
  Card, 
  CardBody,
  Chip,
  Divider
} from '@nextui-org/react'
import { Icon } from '@iconify/react'
import { useUser, useOrganization } from '@clerk/clerk-react'
import { useCreateGroupView } from '@/firestoreQueries/ndr/groupViews/hooks/useGroupViews'
import { useGroupViewStore } from '@/zustandStores/useGroupViewStore'
import { searchLabelKeys } from '@/utils/groupViewUtils'
import { GroupViewKey, GroupViewWithId } from '@globalTypes/ndrBenchmarkTypes/groupViews/groupViewTypes'
import { toast } from '@/components/ui'

interface NewViewTabProps {
  availableKeys: string[]
  onViewCreated: (view: GroupViewWithId) => void
  currentView: GroupViewWithId | null
}

const NewViewTab: React.FC<NewViewTabProps> = ({ 
  availableKeys, 
  onViewCreated, 
  currentView 
}) => {
  const { user } = useUser()
  const { organization } = useOrganization()
  const organizationId = organization?.id
  const userId = user?.id

  const [viewName, setViewName] = useState('')
  const [primaryKey, setPrimaryKey] = useState('')
  const [secondaryKey, setSecondaryKey] = useState('')
  const [additionalKeys, setAdditionalKeys] = useState<string[]>([])
  const [isDefault, setIsDefault] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')

  const createGroupView = useCreateGroupView(organizationId, userId)

  // Convert available keys to GroupViewKey format
  const groupViewKeys: GroupViewKey[] = useMemo(() => {
    return availableKeys.map(key => ({
      key,
      displayName: key.split(/[-_]/).map(word => 
        word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
      ).join(' ')
    }))
  }, [availableKeys])

  // Filter keys based on search term
  const filteredKeys = useMemo(() => {
    return searchLabelKeys(groupViewKeys, searchTerm)
  }, [groupViewKeys, searchTerm])

  // Get available keys for secondary selection (excluding primary)
  const availableSecondaryKeys = useMemo(() => {
    return filteredKeys.filter(key => key.key !== primaryKey)
  }, [filteredKeys, primaryKey])

  // Get available keys for additional selection (excluding primary and secondary)
  const availableAdditionalKeys = useMemo(() => {
    return filteredKeys.filter(key => 
      key.key !== primaryKey && key.key !== secondaryKey
    )
  }, [filteredKeys, primaryKey, secondaryKey])

  // Reset form when modal opens
  useEffect(() => {
    setViewName('')
    setPrimaryKey('')
    setSecondaryKey('')
    setAdditionalKeys([])
    setIsDefault(false)
    setSearchTerm('')
  }, [])

  const handleSubmit = async () => {
    if (!viewName.trim() || !primaryKey) {
      toast.push({
        title: 'Validation Error',
        message: 'Please provide a view name and select a primary key',
        type: 'danger'
      })
      return
    }

    try {
      const newView = await createGroupView.mutateAsync({
        name: viewName.trim(),
        primaryKey,
        secondaryKey: secondaryKey || undefined,
        additionalKeys: additionalKeys.length > 0 ? additionalKeys : undefined,
        isDefault,
      })

      toast.push({
        title: 'Success',
        message: `Group view "${newView.name}" created successfully`,
        type: 'success'
      })

      onViewCreated(newView)
    } catch (error) {
      toast.push({
        title: 'Error',
        message: 'Failed to create group view',
        type: 'danger'
      })
    }
  }

  const handleAddAdditionalKey = (key: string) => {
    if (!additionalKeys.includes(key)) {
      setAdditionalKeys([...additionalKeys, key])
    }
  }

  const handleRemoveAdditionalKey = (key: string) => {
    setAdditionalKeys(additionalKeys.filter(k => k !== key))
  }

  const isFormValid = viewName.trim() && primaryKey

  return (
    <div className="space-y-6">
      {/* View Name */}
      <Input
        label="View Name"
        placeholder="Enter a name for this view"
        value={viewName}
        onValueChange={setViewName}
        isRequired
        startContent={<Icon icon="material-symbols:label" className="text-gray-400" />}
      />

      {/* Search Keys */}
      <Input
        label="Search Label Keys"
        placeholder="Search available label keys..."
        value={searchTerm}
        onValueChange={setSearchTerm}
        startContent={<Icon icon="material-symbols:search" className="text-gray-400" />}
        description="Search to filter available label keys"
      />

      {/* Primary Key Selection */}
      <Select
        label="Primary Label Key"
        placeholder="Select the primary grouping key"
        selectedKeys={primaryKey ? [primaryKey] : []}
        onSelectionChange={(keys) => {
          const key = Array.from(keys)[0] as string
          setPrimaryKey(key || '')
          // Reset secondary and additional if they conflict
          if (key === secondaryKey) setSecondaryKey('')
          setAdditionalKeys(additionalKeys.filter(k => k !== key))
        }}
        isRequired
      >
        {filteredKeys.map((key) => (
          <SelectItem key={key.key} value={key.key}>
            {key.displayName || key.key}
          </SelectItem>
        ))}
      </Select>

      {/* Secondary Key Selection */}
      <Select
        label="Secondary Label Key (Optional)"
        placeholder="Select a secondary grouping key"
        selectedKeys={secondaryKey ? [secondaryKey] : []}
        onSelectionChange={(keys) => {
          const key = Array.from(keys)[0] as string
          setSecondaryKey(key || '')
          // Remove from additional keys if selected
          setAdditionalKeys(additionalKeys.filter(k => k !== key))
        }}
        isDisabled={!primaryKey}
      >
        {availableSecondaryKeys.map((key) => (
          <SelectItem key={key.key} value={key.key}>
            {key.displayName || key.key}
          </SelectItem>
        ))}
      </Select>

      {/* Additional Keys */}
      <div className="space-y-3">
        <label className="text-sm font-medium text-gray-700">
          Additional Keys (Optional)
        </label>
        
        {additionalKeys.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {additionalKeys.map((key) => (
              <Chip
                key={key}
                onClose={() => handleRemoveAdditionalKey(key)}
                variant="flat"
                color="primary"
              >
                {groupViewKeys.find(k => k.key === key)?.displayName || key}
              </Chip>
            ))}
          </div>
        )}

        <Select
          placeholder="Add additional grouping keys"
          selectedKeys={[]}
          onSelectionChange={(keys) => {
            const key = Array.from(keys)[0] as string
            if (key) handleAddAdditionalKey(key)
          }}
          isDisabled={!primaryKey}
        >
          {availableAdditionalKeys.map((key) => (
            <SelectItem key={key.key} value={key.key}>
              {key.displayName || key.key}
            </SelectItem>
          ))}
        </Select>
      </div>

      <Divider />

      {/* Default View Checkbox */}
      <Checkbox
        isSelected={isDefault}
        onValueChange={setIsDefault}
      >
        <div className="flex flex-col">
          <span className="text-sm font-medium">Set as default view</span>
          <span className="text-xs text-gray-500">
            This view will be automatically applied when opening the map
          </span>
        </div>
      </Checkbox>

      {/* Preview */}
      {primaryKey && (
        <Card>
          <CardBody className="space-y-2">
            <h4 className="text-sm font-medium text-gray-700">Preview</h4>
            <div className="text-sm text-gray-600">
              <span className="font-medium">Grouping:</span> {primaryKey}
              {secondaryKey && ` → ${secondaryKey}`}
              {additionalKeys.length > 0 && ` + ${additionalKeys.length} more`}
            </div>
          </CardBody>
        </Card>
      )}

      {/* Action Buttons */}
      <div className="flex justify-end gap-3 pt-4">
        <Button
          color="primary"
          onPress={handleSubmit}
          isLoading={createGroupView.isPending}
          isDisabled={!isFormValid}
          startContent={<Icon icon="material-symbols:save" />}
        >
          Save View
        </Button>
      </div>
    </div>
  )
}

export default NewViewTab
