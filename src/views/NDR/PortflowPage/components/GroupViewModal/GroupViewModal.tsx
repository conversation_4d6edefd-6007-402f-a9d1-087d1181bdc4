import React, { useState } from 'react'
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON>dalBody, 
  <PERSON>dal<PERSON>oot<PERSON>, 
  <PERSON><PERSON>, 
  <PERSON><PERSON>, 
  <PERSON><PERSON> 
} from '@nextui-org/react'
import { Icon } from '@iconify/react'
import NewViewTab from './NewViewTab'
import SavedViewsTab from './SavedViewsTab'
import { useGroupViewStore } from '@/zustandStores/useGroupViewStore'
import { CreateGroupViewRequest } from '@globalTypes/ndrBenchmarkTypes/groupViews/groupViewTypes'

interface GroupViewModalProps {
  isOpen: boolean
  onClose: () => void
}

const GroupViewModal: React.FC<GroupViewModalProps> = ({ isOpen, onClose }) => {
  const [activeTab, setActiveTab] = useState<string>('new-view')
  const [isLoading, setIsLoading] = useState(false)
  
  const {
    currentView,
    setCurrentView,
    setIsGrouped
  } = useGroupViewStore()

  const handleClose = () => {
    setActiveTab('new-view')
    onClose()
  }

  const handleViewCreated = (view: any) => {
    setCurrentView(view)
    setIsGrouped(true)
    handleClose()
  }

  const handleViewSelected = (view: any) => {
    setCurrentView(view)
    setIsGrouped(true)
    handleClose()
  }

  const handleViewDeleted = () => {
    // If the deleted view was the current view, reset grouping
    // This will be handled in the SavedViewsTab component
  }

  return (
    <Modal 
      isOpen={isOpen} 
      onClose={handleClose}
      size="2xl"
      scrollBehavior="inside"
      classNames={{
        base: "max-h-[90vh]",
        body: "py-6",
        header: "border-b border-gray-200",
      }}
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <div className="flex items-center gap-2">
            <Icon icon="material-symbols:group" fontSize={24} className="text-blue-600" />
            <h2 className="text-xl font-semibold">Group View Management</h2>
          </div>
          <p className="text-sm text-gray-600 font-normal">
            Create and manage custom grouping views for your network map
          </p>
        </ModalHeader>
        
        <ModalBody>
          <Tabs 
            selectedKey={activeTab} 
            onSelectionChange={(key) => setActiveTab(key as string)}
            variant="underlined"
            classNames={{
              tabList: "gap-6 w-full relative rounded-none p-0 border-b border-divider",
              cursor: "w-full bg-blue-600",
              tab: "max-w-fit px-0 h-12",
              tabContent: "group-data-[selected=true]:text-blue-600"
            }}
          >
            <Tab 
              key="new-view" 
              title={
                <div className="flex items-center gap-2">
                  <Icon icon="material-symbols:add" fontSize={18} />
                  <span>New View</span>
                </div>
              }
            >
              <div className="py-4">
                <NewViewTab
                  onViewCreated={handleViewCreated}
                  currentView={currentView}
                />
              </div>
            </Tab>
            
            <Tab 
              key="saved-views" 
              title={
                <div className="flex items-center gap-2">
                  <Icon icon="material-symbols:bookmark" fontSize={18} />
                  <span>Saved Views</span>
                </div>
              }
            >
              <div className="py-4">
                <SavedViewsTab 
                  onViewSelected={handleViewSelected}
                  onViewDeleted={handleViewDeleted}
                  currentView={currentView}
                />
              </div>
            </Tab>
          </Tabs>
        </ModalBody>
        
        <ModalFooter className="border-t border-gray-200">
          <Button 
            variant="light" 
            onPress={handleClose}
            className="font-medium"
          >
            Close
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  )
}

export default GroupViewModal
