import React from 'react'
import { <PERSON>le, Position, NodeProps } from 'reactflow'
import { Icon } from '@iconify/react'
import { useGroupViewStore } from '@/zustandStores/useGroupViewStore'
import { StyledInstanceNode } from './InstanceNode'
import useGraphConfig from '@/views/NDR/PortflowPage/hooks/useGraphConfig'

interface GroupNodeData {
  label: string
  entityCount: number
  entities: string[]
  groupKeys: Record<string, string>
  aggregatedLabels: Record<string, string[]>
  isGroup: boolean
}

const GroupNode: React.FC<NodeProps<GroupNodeData>> = ({ data, id, selected }) => {
  const { zoomedGroup, setZoomedGroup } = useGroupViewStore()
  const { theme } = useGraphConfig()
  const isZoomed = zoomedGroup === id

  const handleClick = (event: React.MouseEvent) => {
    event.stopPropagation()
    if (isZoomed) {
      setZoomedGroup(null)
    } else {
      setZoomedGroup(id)
    }
  }

  const formatGroupKeys = () => {
    const keys = Object.entries(data.groupKeys)
    if (keys.length === 0) return ''

    if (keys.length === 1) {
      return `${keys[0][1]}`
    }

    return `${keys[0][1]} • ${keys[1][1]}${keys.length > 2 ? ` +${keys.length - 2}` : ''}`
  }

  return (
    <>
      <StyledInstanceNode
        marked={false}
        isZoomedOut={false}
        theme={theme}
        withIssues={false}
        isFocusNode={isZoomed}
        onClick={handleClick}
      >
        <div className="header">
          <div className="node-name truncate dark:text-gray-100">
            👥 {data.label}
          </div>
          <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
            <Icon icon="material-symbols:group" fontSize={20} />
          </div>
        </div>

        <div className="content">
          <div className="sub-title">
            {data.entityCount} {data.entityCount === 1 ? 'entity' : 'entities'}
            {formatGroupKeys() && (
              <div style={{ marginTop: '4px', fontSize: '10px' }}>
                {formatGroupKeys()}
              </div>
            )}
          </div>
        </div>
      </StyledInstanceNode>

      <Handle
        type="target"
        position={Position.Bottom}
        style={{
          width: '15px',
          height: '15px',
          borderRadius: '50%',
          border: isZoomed
            ? '2px solid #3B82F6'
            : theme === 'default'
              ? '2px solid #eaeaea'
              : '2px solid #374151',
          background: theme === 'default' ? 'white' : '#111827',
        }}
      />
      <Handle
        type="source"
        position={Position.Top}
        style={{
          width: '15px',
          height: '15px',
          borderRadius: '50%',
          border: isZoomed
            ? '2px solid #3B82F6'
            : theme === 'default'
              ? '2px solid #eaeaea'
              : '2px solid #374151',
          background: theme === 'default' ? 'white' : '#111827',
        }}
      />
    </>
  )
}

export default GroupNode
