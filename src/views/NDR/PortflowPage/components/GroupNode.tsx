import React from 'react'
import { <PERSON><PERSON>, <PERSON>sition, NodeProps } from 'reactflow'
import { Icon } from '@iconify/react'
import styled from 'styled-components'
import { useGroupViewStore } from '@/zustandStores/useGroupViewStore'

const StyledGroupNode = styled.div<{ isZoomed?: boolean }>`
  padding: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  border: 2px solid ${props => props.isZoomed ? '#3B82F6' : '#E5E7EB'};
  box-shadow: ${props => props.isZoomed 
    ? '0 10px 25px rgba(59, 130, 246, 0.3)' 
    : '0 4px 12px rgba(0, 0, 0, 0.1)'
  };
  color: white;
  min-width: 200px;
  max-width: 300px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
  }

  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
  }

  .title {
    font-size: 16px;
    font-weight: 600;
    margin: 0;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .entity-count {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 4px 8px;
    font-size: 12px;
    font-weight: 500;
    margin-left: 8px;
  }

  .group-keys {
    font-size: 12px;
    opacity: 0.9;
    margin-bottom: 8px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .labels-preview {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    margin-top: 8px;
  }

  .label-chip {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 2px 6px;
    font-size: 10px;
    font-weight: 500;
  }

  .expand-icon {
    position: absolute;
    top: 8px;
    right: 8px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
  }

  .expand-icon:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
  }
`

interface GroupNodeData {
  label: string
  entityCount: number
  entities: string[]
  groupKeys: Record<string, string>
  aggregatedLabels: Record<string, string[]>
  isGroup: boolean
}

const GroupNode: React.FC<NodeProps<GroupNodeData>> = ({ data, id, selected }) => {
  const { zoomedGroup, setZoomedGroup } = useGroupViewStore()
  const isZoomed = zoomedGroup === id

  const handleClick = (event: React.MouseEvent) => {
    event.stopPropagation()
    if (isZoomed) {
      // If already zoomed, zoom out
      setZoomedGroup(null)
    } else {
      // Zoom into this group
      setZoomedGroup(id)
    }
  }

  const formatGroupKeys = () => {
    const keys = Object.entries(data.groupKeys)
    if (keys.length === 0) return ''
    
    if (keys.length === 1) {
      return `${keys[0][1]}`
    }
    
    return `${keys[0][1]} • ${keys[1][1]}${keys.length > 2 ? ` +${keys.length - 2}` : ''}`
  }

  const getTopLabels = () => {
    const labels = Object.entries(data.aggregatedLabels)
    return labels.slice(0, 3).map(([key, values]) => ({
      key,
      value: values[0] || key
    }))
  }

  return (
    <>
      <Handle
        type="target"
        position={Position.Left}
        style={{ background: '#fff', border: '2px solid #667eea' }}
      />
      
      <StyledGroupNode isZoomed={isZoomed} onClick={handleClick}>
        <div className="expand-icon">
          <Icon 
            icon={isZoomed ? "material-symbols:zoom-out" : "material-symbols:zoom-in"} 
            fontSize={14} 
          />
        </div>
        
        <div className="header">
          <Icon icon="material-symbols:group" fontSize={20} />
          <div className="entity-count">
            {data.entityCount} {data.entityCount === 1 ? 'entity' : 'entities'}
          </div>
        </div>
        
        <h3 className="title" title={data.label}>
          {data.label}
        </h3>
        
        <div className="group-keys" title={formatGroupKeys()}>
          {formatGroupKeys()}
        </div>
        
        <div className="labels-preview">
          {getTopLabels().map((label, index) => (
            <div key={index} className="label-chip" title={`${label.key}: ${label.value}`}>
              {label.value}
            </div>
          ))}
          {Object.keys(data.aggregatedLabels).length > 3 && (
            <div className="label-chip">
              +{Object.keys(data.aggregatedLabels).length - 3}
            </div>
          )}
        </div>
      </StyledGroupNode>
      
      <Handle
        type="source"
        position={Position.Right}
        style={{ background: '#fff', border: '2px solid #667eea' }}
      />
    </>
  )
}

export default GroupNode
