import React from 'react'
import { EdgeProps, getBezierPath, EdgeLabelRenderer } from 'reactflow'
import { Icon } from '@iconify/react'

interface GroupEdgeData {
  edgeCount: number
  aggregatedData: {
    totalTraffic: number
    ports: number[]
    protocols: string[]
    isDanger: boolean
  }
  isGroupEdge: boolean
}

const GroupEdge: React.FC<EdgeProps<GroupEdgeData>> = ({
  id,
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  data,
  markerEnd,
}) => {
  const [edgePath, labelX, labelY] = getBezierPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition,
  })

  const getEdgeColor = () => {
    if (data?.aggregatedData?.isDanger) {
      return '#EF4444' // Red for dangerous traffic
    }
    return '#3B82F6' // Blue for normal traffic
  }

  const getEdgeWidth = () => {
    if (!data?.edgeCount) return 2
    
    // Scale edge width based on edge count (min 2, max 8)
    const baseWidth = 2
    const maxWidth = 8
    const scaleFactor = Math.min(data.edgeCount / 10, 1) // Normalize to 0-1
    return baseWidth + (maxWidth - baseWidth) * scaleFactor
  }

  const formatTrafficInfo = () => {
    if (!data?.aggregatedData) return ''
    
    const { totalTraffic, ports, protocols } = data.aggregatedData
    const parts = []
    
    if (totalTraffic > 0) {
      parts.push(`${totalTraffic} flows`)
    }
    
    if (ports.length > 0) {
      if (ports.length === 1) {
        parts.push(`port ${ports[0]}`)
      } else if (ports.length <= 3) {
        parts.push(`ports ${ports.join(', ')}`)
      } else {
        parts.push(`${ports.length} ports`)
      }
    }
    
    if (protocols.length > 0) {
      if (protocols.length <= 2) {
        parts.push(protocols.join(', '))
      } else {
        parts.push(`${protocols.length} protocols`)
      }
    }
    
    return parts.join(' • ')
  }

  return (
    <>
      <path
        id={id}
        style={{
          stroke: getEdgeColor(),
          strokeWidth: getEdgeWidth(),
          fill: 'none',
          strokeDasharray: data?.aggregatedData?.isDanger ? '5,5' : 'none',
        }}
        className="react-flow__edge-path"
        d={edgePath}
        markerEnd={markerEnd}
      />
      
      {data && (
        <EdgeLabelRenderer>
          <div
            style={{
              position: 'absolute',
              transform: `translate(-50%, -50%) translate(${labelX}px,${labelY}px)`,
              fontSize: 11,
              pointerEvents: 'all',
            }}
            className="nodrag nopan"
          >
            <div
              style={{
                background: 'rgba(255, 255, 255, 0.95)',
                border: `1px solid ${getEdgeColor()}`,
                borderRadius: '8px',
                padding: '4px 8px',
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                display: 'flex',
                alignItems: 'center',
                gap: '4px',
                maxWidth: '200px',
              }}
            >
              {data.aggregatedData?.isDanger && (
                <Icon 
                  icon="material-symbols:warning" 
                  fontSize={12} 
                  style={{ color: '#EF4444' }} 
                />
              )}
              <div style={{ 
                fontWeight: 600, 
                color: getEdgeColor(),
                marginRight: '4px'
              }}>
                {data.edgeCount}
              </div>
              <div style={{ 
                color: '#6B7280',
                fontSize: '10px',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap'
              }}>
                {formatTrafficInfo()}
              </div>
            </div>
          </div>
        </EdgeLabelRenderer>
      )}
    </>
  )
}

export default GroupEdge
