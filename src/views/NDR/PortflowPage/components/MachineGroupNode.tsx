import React from 'react'
import { NodeProps } from 'reactflow'
import { useGroupViewStore } from '@/zustandStores/useGroupViewStore'
import { StyledInstanceNode } from './InstanceNode'
import useGraphConfig from '@/views/NDR/PortflowPage/hooks/useGraphConfig'

interface MachineGroupNodeData {
  label: string
  subType: string
  entityCount: number
  entities: string[]
  groupKeys: Record<string, string>
  aggregatedLabels: Record<string, string[]>
  isGroup: boolean
  children: any[]
  issues: any[]
  marked: boolean
  riskScore: number
  isFocusNode: boolean
  labels: any[]
}

const MachineGroupNode: React.FC<NodeProps<MachineGroupNodeData>> = ({ data, id }) => {
  const { zoomedGroup, setZoomedGroup } = useGroupViewStore()
  const { theme } = useGraphConfig()
  const isZoomed = zoomedGroup === id

  const handleClick = (event: React.MouseEvent) => {
    event.stopPropagation()
    if (isZoomed) {
      setZoomedGroup(null)
    } else {
      setZoomedGroup(id)
    }
  }

  const formatGroupKeys = () => {
    const keys = Object.entries(data.groupKeys)
    if (keys.length === 0) return ''
    
    if (keys.length === 1) {
      return `${keys[0][1]}`
    }
    
    return `${keys[0][1]} • ${keys[1][1]}${keys.length > 2 ? ` +${keys.length - 2}` : ''}`
  }

  return (
    // No handles - this makes it look like a regular machine node without connection points
    <StyledInstanceNode
      marked={data.marked}
      isZoomedOut={false}
      theme={theme}
      withIssues={data.issues.length > 0}
      isFocusNode={isZoomed}
      onClick={handleClick}
    >
      <div className="header">
        <div className="node-name truncate dark:text-gray-100">
          👥 {data.label}
        </div>
        <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
          <div style={{ 
            fontSize: '12px', 
            background: theme === 'dark' ? '#374151' : '#f3f4f6',
            padding: '2px 6px',
            borderRadius: '4px',
            color: theme === 'dark' ? '#d1d5db' : '#374151'
          }}>
            {data.entityCount}
          </div>
        </div>
      </div>

      <div className="content">
        <div className="sub-title">
          Group • {data.entityCount} {data.entityCount === 1 ? 'entity' : 'entities'}
          {formatGroupKeys() && (
            <div style={{ marginTop: '4px', fontSize: '10px', opacity: 0.7 }}>
              {formatGroupKeys()}
            </div>
          )}
        </div>

        {/* Show some labels like a regular machine node */}
        {Object.keys(data.aggregatedLabels).length > 0 && (
          <div style={{ marginTop: '8px', display: 'flex', flexWrap: 'wrap', gap: '4px' }}>
            {Object.entries(data.aggregatedLabels).slice(0, 2).map(([key, values], index) => (
              <div
                key={index}
                style={{
                  fontSize: '9px',
                  background: theme === 'dark' ? '#374151' : '#e5e7eb',
                  color: theme === 'dark' ? '#d1d5db' : '#374151',
                  padding: '1px 4px',
                  borderRadius: '3px',
                }}
              >
                {key}: {values[0]}
              </div>
            ))}
            {Object.keys(data.aggregatedLabels).length > 2 && (
              <div
                style={{
                  fontSize: '9px',
                  background: theme === 'dark' ? '#374151' : '#e5e7eb',
                  color: theme === 'dark' ? '#d1d5db' : '#374151',
                  padding: '1px 4px',
                  borderRadius: '3px',
                }}
              >
                +{Object.keys(data.aggregatedLabels).length - 2}
              </div>
            )}
          </div>
        )}
      </div>
    </StyledInstanceNode>
  )
}

export default MachineGroupNode
