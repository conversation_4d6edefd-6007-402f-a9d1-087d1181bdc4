import React, { useState } from 'react'
import { Button, cn, colors, Dropdown, DropdownTrigger, DropdownMenu, DropdownItem } from '@nextui-org/react'
import { Icon } from '@iconify/react'
import Tooltip from '@/components/ui/Tooltip'
import { useGroupViewStore } from '@/zustandStores/useGroupViewStore'
import { useGetUserGroupViews } from '@/firestoreQueries/ndr/groupViews/hooks/useGroupViews'
import { useUser, useOrganization } from '@clerk/clerk-react'
import { GroupViewWithId } from '@globalTypes/ndrBenchmarkTypes/groupViews/groupViewTypes'

interface GroupedByButtonProps {
  onOpenModal: () => void
}

const GroupedByButton: React.FC<GroupedByButtonProps> = ({ onOpenModal }) => {
  const { user } = useUser()
  const { organization } = useOrganization()
  const organizationId = organization?.id
  const userId = user?.id

  const {
    currentView,
    isGrouped,
    setCurrentView,
    setIsGrouped,
    resetGrouping,
  } = useGroupViewStore()

  const { data: userGroupViews = [] } = useGetUserGroupViews(organizationId, userId)

  const [isDropdownOpen, setIsDropdownOpen] = useState(false)

  const handleViewSelect = (view: GroupViewWithId | null) => {
    if (view) {
      setCurrentView(view)
      setIsGrouped(true)
    } else {
      resetGrouping()
    }
    setIsDropdownOpen(false)
  }

  const handleOpenModal = () => {
    setIsDropdownOpen(false)
    onOpenModal()
  }

  const getCurrentViewLabel = () => {
    if (!isGrouped || !currentView) {
      return 'No Grouping'
    }
    return currentView.name
  }

  const getButtonIcon = () => {
    if (isGrouped && currentView) {
      return 'material-symbols:check-circle'
    }
    return 'material-symbols:group'
  }

  return (
    <div className="flex items-center gap-2">
      <Dropdown 
        isOpen={isDropdownOpen} 
        onOpenChange={setIsDropdownOpen}
        placement="bottom-start"
      >
        <DropdownTrigger>
          <Button
            className={cn('h-[42px] border', {
              'dark:bg-[#111827] dark:border-gray-700 bg-white border-gray-300 shadow-primary-shadow':
                !isGrouped, // Inactive state styles
              'dark:bg-gray-200 dark:border-gray-200 bg-blue-600 border-blue-600 shadow-lg': isGrouped, // Active state styles
            })}
            startContent={
              <Icon
                icon={getButtonIcon()}
                fontSize={20}
                className={cn({
                  'dark:text-gray-200 text-gray-500': !isGrouped, // Inactive icon styles
                  'dark:text-gray-800 text-white': isGrouped, // Active icon styles
                })}
              />
            }
            endContent={
              <Icon
                icon="material-symbols:keyboard-arrow-down"
                fontSize={16}
                className={cn({
                  'dark:text-gray-200 text-gray-500': !isGrouped,
                  'dark:text-gray-800 text-white': isGrouped,
                })}
              />
            }
          >
            <span
              className={cn('text-sm font-medium', {
                'dark:text-gray-200 text-gray-700': !isGrouped,
                'dark:text-gray-800 text-white': isGrouped,
              })}
            >
              Grouped by: {getCurrentViewLabel()}
            </span>
          </Button>
        </DropdownTrigger>
        
        <DropdownMenu aria-label="Group view options">
          {/* No Grouping Option */}
          <DropdownItem
            key="no-grouping"
            onClick={() => handleViewSelect(null)}
            startContent={
              <Icon
                icon={!isGrouped ? 'material-symbols:check-circle' : 'material-symbols:radio-button-unchecked'}
                fontSize={16}
                className={!isGrouped ? 'text-green-500' : 'text-gray-400'}
              />
            }
          >
            No Grouping
          </DropdownItem>

          {/* Divider */}
          {userGroupViews.length > 0 && (
            <DropdownItem key="divider-1" className="border-t border-gray-200 mt-1 pt-1">
              <span className="text-xs text-gray-500 font-medium">SAVED VIEWS</span>
            </DropdownItem>
          )}

          {/* Saved Views */}
          {userGroupViews.map((view) => (
            <DropdownItem
              key={view.id}
              onClick={() => handleViewSelect(view)}
              startContent={
                <Icon
                  icon={
                    isGrouped && currentView?.id === view.id
                      ? 'material-symbols:check-circle'
                      : 'material-symbols:radio-button-unchecked'
                  }
                  fontSize={16}
                  className={
                    isGrouped && currentView?.id === view.id ? 'text-green-500' : 'text-gray-400'
                  }
                />
              }
              endContent={
                view.isDefault && (
                  <Icon
                    icon="material-symbols:star"
                    fontSize={14}
                    className="text-yellow-500"
                  />
                )
              }
            >
              <div className="flex flex-col">
                <span className="text-sm font-medium">{view.name}</span>
                <span className="text-xs text-gray-500">
                  {view.primaryKey}
                  {view.secondaryKey && ` → ${view.secondaryKey}`}
                </span>
              </div>
            </DropdownItem>
          ))}

          {/* Divider */}
          <DropdownItem key="divider-2" className="border-t border-gray-200 mt-1 pt-1">
            <span className="text-xs text-gray-500 font-medium">ACTIONS</span>
          </DropdownItem>

          {/* Manage Views Option */}
          <DropdownItem
            key="manage-views"
            onClick={handleOpenModal}
            startContent={
              <Icon
                icon="material-symbols:settings"
                fontSize={16}
                className="text-gray-600"
              />
            }
          >
            Manage Views...
          </DropdownItem>
        </DropdownMenu>
      </Dropdown>

      {/* Arrow button to open modal */}
      <Tooltip title="Manage Group Views">
        <Button
          isIconOnly
          className="h-[42px] w-[42px] border dark:bg-[#111827] dark:border-gray-700 bg-white border-gray-300 shadow-primary-shadow"
          onClick={handleOpenModal}
        >
          <Icon
            icon="material-symbols:arrow-forward-ios"
            fontSize={16}
            className="dark:text-gray-200 text-gray-500"
          />
        </Button>
      </Tooltip>
    </div>
  )
}

export default GroupedByButton
