import { useFlags } from 'launchdarkly-react-client-sdk'

export type FeatureToggleFlags = {
  isMock: boolean
  showAccessModule: boolean
  showScannerModule: boolean
  showOrganizationModule: boolean
  manualNotificationTriggering: boolean
  activityLogModule: boolean
  isFirewallViewOnly: boolean
  showHunterXModule: boolean
}

export function useTypedFlags() {
  return useFlags<FeatureToggleFlags>()
}
