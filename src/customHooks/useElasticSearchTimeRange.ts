import { DatePickerRangeValue } from '@/components/ui/DatePicker/DatePickerRange'
import {
    convertDateToEsRange,
    convertEsRangeToDate,
    getMirroredRange,
    getRangeString,
} from '@/utils/elasticSearchTimeUtils/esTimeRangeFns'
import { useEffect, useState } from 'react'

export type EsTimeRange = {
    from: string
    to: string
    oldFrom: string
    oldTo: string
}

export default function useElasticSeatchTimeRange(defaultDates?: DatePickerRangeValue) {
    const [esTimeRange, setEsTimeRange] = useState({
        from: 'now-7d/d',
        to: 'now/d',
        oldFrom: 'now-14d/d',
        oldTo: 'now-7d/d',
    })

    const [dates, setDates] = useState<DatePickerRangeValue>([
        convertEsRangeToDate(esTimeRange.from),
        convertEsRangeToDate(esTimeRange.to),
    ])

    const [rangeString, setRangeString] = useState<string>('last week')

    const handleDateChange = (value: DatePickerRangeValue) => {
        setDates(value)
    }

    useEffect(() => {
        if (!defaultDates) return

        setDates(defaultDates)
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [])

    useEffect(() => {
        const first = dates[0]
        const second = dates[1]

        if (first !== null && second !== null) {
            const currTimeRange = {
                from: convertDateToEsRange(first),
                to: convertDateToEsRange(second),
            }
            const mirroredRange = getMirroredRange({
                from: first,
                to: second,
            })

            const newTimeRange = {
                ...currTimeRange,
                oldFrom: mirroredRange.from,
                oldTo: mirroredRange.to,
            }

            setEsTimeRange(newTimeRange)

            const newRangeString = getRangeString({
                from: convertEsRangeToDate(newTimeRange.from),
                to: convertEsRangeToDate(newTimeRange.to),
            })

            setRangeString(newRangeString)
        }
    }, [dates])

    return { esTimeRange, handleDateChange, dates, rangeString }
}
