import { DatePickerRangeValue } from '@/components/ui/DatePicker/DatePickerRange'
import {
    convertDateToEsRange,
    getMirroredRange,
    getRangeString,
} from '@/utils/elasticSearchTimeUtils/esTimeRangeFns'
import { Timestamp } from 'firebase/firestore'
import { useEffect, useState } from 'react'

const today = new Date()
const lastWeek = new Date(new Date().setDate(today.getDate() - 7))
export default function useMixedTimeRange() {
    const [esTimestamp, setEsTimestamp] = useState({
        from: 'now-7d/d',
        to: 'now/d',
        oldFrom: 'now-14d/d',
        oldTo: 'now-7d/d',
    })

    const [fsTimestamp, setFsTimestamp] = useState<{
        from: Timestamp
        to: Timestamp
    }>({
        from: Timestamp.fromDate(lastWeek),
        to: Timestamp.fromDate(today),
    })
    const [dates, setDates] = useState<DatePickerRangeValue>([lastWeek, today])
    const [rangeString, setRangeString] = useState<string>('last week')

    const handleDateChange = (value: DatePickerRangeValue) => {
        setDates(value)
    }

    useEffect(() => {
        const first = dates[0]
        const second = dates[1]

        if (first !== null && second !== null) {
            const newRangeString = getRangeString({
                from: first,
                to: second,
            })

            const now = new Date()
            first.setHours(now.getHours(), now.getMinutes(), now.getSeconds())
            second.setHours(now.getHours(), now.getMinutes(), now.getSeconds())
            setRangeString(newRangeString)
            setFsTimestamp({
                from: Timestamp.fromDate(first),
                to: Timestamp.fromDate(second),
            })

            const currTimeRange = {
                from: convertDateToEsRange(first),
                to: convertDateToEsRange(second),
            }
            const mirroredRange = getMirroredRange({
                from: first,
                to: second,
            })

            const newTimeRange = {
                ...currTimeRange,
                oldFrom: mirroredRange.from,
                oldTo: mirroredRange.to,
            }

            setEsTimestamp(newTimeRange)

            setRangeString(newRangeString)
        }
    }, [dates])
    return { handleDateChange, dates, rangeString, fsTimestamp, esTimestamp }
}
