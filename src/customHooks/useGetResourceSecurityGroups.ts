import getIntegrationsSecurityGroups from '@/utils/belloUtils/getIntegrationsSecurityGroups.ts'
import { setupWebRTCConnection } from '@/utils/webRTCClient'
import { useQuery } from '@tanstack/react-query'
import * as Sentry from '@sentry/react'

type Args = {
    firestoreResourceId: string
    integrationIp: string
    integrationId: string
    organizationId: string
    awsResourceId: string
}

export default function ({ firestoreResourceId, integrationIp, integrationId, organizationId, awsResourceId }: Args) {
    return useQuery({
        queryKey: ['resourceSecurityGroups', firestoreResourceId],
        queryFn: async () => {
            try {
                const dataChannel = await setupWebRTCConnection({
                    ip: integrationIp,
                })

                if (!dataChannel) {
                    throw new Error('Error setting up WebRTC connection')
                }

                const allSecurityGroups = await getIntegrationsSecurityGroups({
                    dataChannel,
                    handleError: () => null,
                    integrationId,
                    organizationId,
                })
                const resourceSecurityGroups = allSecurityGroups.filter((sg) =>
                    sg.options?.associatedInstances.some((instance) => instance.id === awsResourceId)
                )

                return resourceSecurityGroups
            } catch (error) {
                Sentry.captureException(error)
                return []
            }
        },
    })
}
