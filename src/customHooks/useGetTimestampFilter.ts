import { DatePickerRangeValue } from '@/components/ui/DatePicker/DatePickerRange'
import dayjs from 'dayjs'
import React from 'react'

export const DATE_FORMAT = 'MMM DD, YYYY'
export default () => {
    const handleDateChange = (value: DatePickerRangeValue) => {
        const start = dayjs(value[0]).toDate()
        const end = dayjs(value[1]).toDate()

        setTimestampFilter((p) => {
            return {
                start: start ?? p.start,
                end: end ?? p.end,
            }
        })
    }
    const today = new Date()
    const lastWeek = new Date(new Date().setDate(today.getDate() - 7))
    const [timestampFilter, setTimestampFilter] = React.useState<{
        start: Date
        end: Date
    }>({
        start: lastWeek,
        end: today,
    })

    return {
        timestampFilter,
        setTimestampFilter,
        handleDateChange,
        lastWeek,
        today,
    }
}
