import { Severity } from '@/customComponents/SeverityBars'
import { IssueTypeWithId } from '@/firestoreQueries/ndr/issues/customIssuesTypes'
import capitalize from 'lodash/capitalize'
import { useEffect, useState } from 'react'

type Args = {
    issues: IssueTypeWithId[]
}

export default ({ issues }: Args) => {
    const [pieChartData, setPieChartData] = useState<
        {
            key: Severity
            data: number
        }[]
    >([])

    useEffect(() => {
        const unresolvedIssues = issues.filter((issue) => issue.status === 'open')

        const unresolvedIssuesMap = {
            info: 0,
            low: 0,
            medium: 0,
            high: 0,
            critical: 0,
        }

        unresolvedIssues.forEach((issue) => {
            unresolvedIssuesMap[issue.severity]++
        })

        const unresolvedIssuesData = Object.entries(unresolvedIssuesMap).map(([key, data]) => ({
            key: capitalize(key) as Severity,
            data: data,
        }))

        if (JSON.stringify(unresolvedIssuesData) !== JSON.stringify(pieChartData)) {
            setPieChartData(unresolvedIssuesData)
        }
    }, [issues, pieChartData])

    return { pieChartData }
}
