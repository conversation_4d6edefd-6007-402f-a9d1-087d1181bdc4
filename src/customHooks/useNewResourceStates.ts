import { useDisclosure } from '@nextui-org/react'
import { useState } from 'react'

export type NewResourceType = 'select' | 'aws' | 'standalone'

export default function (
  initialType: NewResourceType = 'select',
  initialIntegrationId: string | undefined = undefined,
) {
  const { isOpen, onOpen, onOpenChange, onClose } = useDisclosure()
  const [type, setType] = useState<'select' | 'aws' | 'standalone'>(initialType)
  const [integrationId, setIntegrationId] = useState<string | undefined>(initialIntegrationId)

  function resetNewResourceStates() {
    setType('select')
    setIntegrationId(undefined)
  }

  function onNewResourceCancel() {
    resetNewResourceStates()
    onClose()
  }

  return {
    isOpen,
    onOpen,
    onOpenChange,
    onClose,
    type,
    setType,
    integrationId,
    setIntegrationId,
    resetNewResourceStates,
    onNewResourceCancel,
  }
}
