import { useEffect, useState } from 'react'

export default () => {
  const [drawerWidth, setDrawerWidth] = useState(0)

  useEffect(() => {
    setDrawerWidth(window.innerWidth * 0.7)
    addEventListener('resize', () => {
      setDrawerWidth(window.innerWidth * 0.7)
    })

    return () =>
      removeEventListener('resize', () => {
        setDrawerWidth(window.innerWidth)
      })
  }, [])

  return { drawerWidth }
}
