import { DatePickerRangeValue } from '@/components/ui/DatePicker/DatePickerRange'
import { getRangeString } from '@/utils/elasticSearchTimeUtils/esTimeRangeFns'
import { Timestamp } from 'firebase/firestore'
import { useEffect, useState } from 'react'
import { isDate } from '@/utils/createDateTime'

const today = new Date()
const lastWeek = new Date(new Date().setDate(today.getDate() - 7))

export type FirestoreTimeRange = {
  from: Timestamp
  to: Timestamp
}

export default (defaultRange?: DatePickerRangeValue) => {
  const [dates, setDates] = useState<DatePickerRangeValue>(defaultRange ?? [lastWeek, today])
  const [timestamp, setTimestamp] = useState<{
    from: Timestamp
    to: Timestamp
  }>({
    from: Timestamp.fromDate(dates?.[0] ?? today),
    to: Timestamp.fromDate(dates?.[1] ?? lastWeek),
  })

  const [rangeString, setRangeString] = useState<string>('last week')

  const handleDateChange = (value: DatePickerRangeValue) => {
   setDates(value)
  }

  useEffect(() => {
    if (defaultRange && Array.isArray(defaultRange) && defaultRange.length === 2) {
      const [fromTimeRange, toTimeRange] = defaultRange

      if (fromTimeRange !== null && toTimeRange !== null) {
        const newRangeString = getRangeString({
          from: fromTimeRange,
          to: toTimeRange,
        })
        setRangeString(newRangeString)

        const newTimestamp = getFirestoreTimestamp(defaultRange)
        setTimestamp(newTimestamp)
      }
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  useEffect(() => {
    const [fromDate, toDate] = dates

    if (fromDate !== null && toDate !== null) {
      const newRangeString = getRangeString({
        from: fromDate,
        to: toDate,
      })

      setRangeString(newRangeString)

      const newTimestamp = getFirestoreTimestamp(dates)
      setTimestamp(newTimestamp)
    }
  }, [dates])
  
  return { handleDateChange, dates, rangeString, timestamp }
}

function transfromDateToTimestamp(date: Date): Timestamp {
  const clonedDate = new Date(date.getTime());
  
  return Timestamp.fromDate(clonedDate);
}

function getFirestoreTimestamp(dates: DatePickerRangeValue): FirestoreTimeRange {
  const first = dates[0]
  const second = dates[1]

  if (first === null || second === null) {
    return {
      from: Timestamp.fromDate(new Date()),
      to: Timestamp.fromDate(new Date()),
    }
  }
  const firstTimestamp = transfromDateToTimestamp(first)
  const secondTimestamp = transfromDateToTimestamp(second)

  return {
    from: firstTimestamp,
    to: secondTimestamp,
  }
}

export function isValidDateRange(dateRangeValue: DatePickerRangeValue): dateRangeValue is [Date, Date] {
  if (!dateRangeValue || !Array.isArray(dateRangeValue)) return false;
  const [fromDate, toDate] = dateRangeValue;
  return isDate(fromDate) && isDate(toDate)
}
