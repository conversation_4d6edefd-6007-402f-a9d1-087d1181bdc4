/* eslint-disable import/named */
import {
    ActivityLogTypeFromDB,
    ConnectToPortalActivityLogTypeFromDB,
} from '@/firestoreQueries/activities/activitiesTypes'
import { DashboardContext } from '@/views/Access/AccessDashboard/DashboardContext'
import dayjs from 'dayjs'
import { useContext, useEffect, useState } from 'react'
import { ChartDataShape } from 'reaviz'

export default ({ activities }: { activities: Array<ActivityLogTypeFromDB> }) => {
    const [chartData, setChartData] = useState<ChartDataShape[]>([])
    const { timestampFilter } = useContext(DashboardContext)

    useEffect(() => {
        const connectToPortalActivities = activities.filter(
            (act): act is ConnectToPortalActivityLogTypeFromDB => act.type === 'connectToPortal'
        )

        // Initialize a Map to track unique users per date
        const dateMap: Map<string, Set<string>> = new Map()

        const allDates = getAllDates(timestampFilter.from.toDate(), dayjs(timestampFilter.to.toDate()).toDate())

        allDates.forEach((date) => {
            const key = formatDate(date)
            dateMap.set(key, new Set())
        })

        // Populate the Map with unique user IDs for each date
        connectToPortalActivities.forEach((activity) => {
            const key = formatDate(activity.timestamp.toDate())
            const usersSet = dateMap.get(key)
            if (usersSet) {
                usersSet.add(activity.userRef.id)
            }
        })

        const formatBackToDate = (date: string): Date => {
            const [day, month, year] = date.split('/')
            return new Date(Number(year), Number(month) - 1, Number(day))
        }

        const data: ChartDataShape[] = [
            {
                key: 'Active Users',
                data: Array.from(dateMap.keys()).map((date, index) => {
                    const key = formatBackToDate(date)
                    const usersCount = dateMap.get(date)?.size || 0
                    return {
                        key,
                        id: String(index),
                        data: usersCount,
                    }
                }),
            },
        ]

        setChartData(data)
    }, [activities, timestampFilter])

    return { chartData }
}
const getAllDates = (start: Date, end: Date) => {
    const dates = []
    let currentDate = start
    while (currentDate <= end) {
        dates.push(currentDate)
        currentDate = new Date(currentDate)
        currentDate.setDate(currentDate.getDate() + 1)
    }
    return dates
}
const formatDate = (date: Date): string => {
    const day = date.getDate().toString().padStart(2, '0')
    const month = (date.getMonth() + 1).toString().padStart(2, '0') // javascript months are 0-based
    const year = date.getFullYear()
    return `${day}/${month}/${year}`
}
