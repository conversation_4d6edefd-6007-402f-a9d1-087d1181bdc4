import { queryClient } from '@/Providers/ReactQueryProvider'
import { signScannerToken } from '@/utils/cloudFunctions'
import { useQuery } from '@tanstack/react-query'

type Args = {
    organizationId: string
}

export const getToken = async ({ organizationId }: Args) => {
    const token = queryClient.getQueryData<string>([
        'scannerToken',
        organizationId,
    ])
    if (token && !isTokenExpired(token)) {
        return token
    } else {
        const { token: newToken } = await signScannerToken({
            organizationId,
        })
        queryClient.setQueryData(['scannerToken', organizationId], newToken)
        return newToken
    }
}

const useGetScannerToken = ({ organizationId }: Args) => {
    return useQuery({
        queryKey: ['scannerToken', organizationId],
        queryFn: () => getToken({ organizationId }),
        staleTime: 1000 * 60 * 14, //1 minutes
    })
}

export default useGetScannerToken

function isTokenExpired(jwt: string) {
    const base64Url = jwt.split('.')[1]

    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/')

    const payload = decodeURIComponent(
        atob(base64)
            .split('')
            .map((c) => {
                return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2)
            })
            .join(''),
    )
    const { exp } = JSON.parse(payload)

    return exp < Date.now() / 1000
}
