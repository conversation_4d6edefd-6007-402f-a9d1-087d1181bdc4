import { ResourceType } from '@/firestoreQueries/resources/resourcesTypes'
import { useDisclosure } from '@nextui-org/react'
import { useState } from 'react'

export default function () {
  const { isOpen, onOpen, onOpenChange, onClose } = useDisclosure()
  const [newPortalStep, setNewPortalStep] = useState(1)
  const [newPortalResource, setNewPortalResource] = useState<ResourceType | null>(null)

  function resetNewPortalStates() {
    setNewPortalResource(null)
    setNewPortalStep(1)
  }

  function handleNewPortalSuccess() {
    resetNewPortalStates()
    onClose()
  }

  return {
    isOpen,
    onOpen,
    onOpenChange,
    onClose,
    resetNewPortalStates,
    handleNewPortalSuccess,
    newPortalStep,
    newPortalResource,
    setNewPortalResource,
    setNewPortalStep,
  }
}
