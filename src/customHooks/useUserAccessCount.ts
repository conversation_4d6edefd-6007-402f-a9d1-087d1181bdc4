import { ActivityLogTypeFromDB } from '@/firestoreQueries/activities/activitiesTypes'
import { useEffect, useState } from 'react'

type Args = {
    activities: ActivityLogTypeFromDB[]
}

const useUserAccessCount = ({ activities }: Args) => {
    const [userAccessCount, setUserAccessCount] = useState<
        { userId: string; count: number }[]
    >([])
    useEffect(() => {
        const accessedPortsMap: { [key: string]: number } = {}
        activities.forEach((act) => {
            if (act.type === 'connectToPortal') {
                accessedPortsMap[act.userRef.id] =
                    accessedPortsMap[act.userRef.id] + 1 || 1
            }
        })

        const keys = Object.keys(accessedPortsMap)
        const userAccessCount: Array<{ userId: string; count: number }> = []
        for (const key in keys) {
            userAccessCount.push({
                userId: keys[key],
                count: accessedPortsMap[keys[key]],
            })
        }

        setUserAccessCount(userAccessCount)
    }, [activities])

    return userAccessCount
}
export default useUserAccessCount
