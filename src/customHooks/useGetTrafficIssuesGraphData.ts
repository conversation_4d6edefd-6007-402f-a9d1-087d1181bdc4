import { IssueTypeWithId } from '@/firestoreQueries/ndr/issues/customIssuesTypes'
import { IssueTableData } from '@/views/NDR/Issues/components/IssuesGraph'
import { Timestamp } from 'firebase/firestore'
import { useEffect, useState } from 'react'

type Args = {
  issues: IssueTypeWithId[]
  timestamp: { from: Timestamp; to: Timestamp }
}

export default ({ issues, timestamp }: Args) => {
  const [trafficIssuesGraphData, setGrafficIssuesGraphData] = useState<IssueTableData>([
    {
      key: 'Insights Resolved',
      data: [],
    },
    {
      key: 'Iinsights Reported',
      data: [],
    },
  ])

  const [totals, setTotals] = useState<{ resolved: number; reported: number }>({
    reported: issues.length,
    resolved: issues.filter((i) => i.status !== 'open').length,
  })

  const resolvedIssue = issues.filter((issue) => issue.status !== 'open')

  useEffect(() => {
    const dateMap: {
      [key: string]: { resolved: number; unresolved: number }
    } = {}

    const currentDate = new Date(timestamp.from.toDate())
    const toDate = new Date(timestamp.to.toDate())
    while (currentDate <= toDate) {
      const key = formatDate(currentDate)
      dateMap[key] = { resolved: 0, unresolved: 0 }
      currentDate.setDate(currentDate.getDate() + 1)
    }

    resolvedIssue.forEach((issue) => {
      const time = issue.updatedTime || issue.createdTime
      const date = time.toDate()
      const key = formatDate(date)
      if (!dateMap[key]) {
        dateMap[key] = { resolved: 0, unresolved: 0 }
      }
      dateMap[key].resolved++
    })

    issues.forEach((issue) => {
      const date = issue.createdTime.toDate()
      const key = formatDate(date)
      if (!dateMap[key]) {
        dateMap[key] = { resolved: 0, unresolved: 0 }
      }
      dateMap[key].unresolved++
    })
    const newTrafficIssuesGraphData: IssueTableData = [
      {
        key: 'Insights Resolved',
        data: Object.entries(dateMap).map(([key, data], index) => {
          const date = formatBackToDate(key)
          return {
            key: date,
            id: String(index),
            data: data.resolved,
          }
        }),
      },

      {
        key: 'Insights Reported',
        data: Object.entries(dateMap).map(([key, data], index) => {
          const date = formatBackToDate(key)
          return {
            key: date,
            id: String(index),
            data: data.unresolved,
          }
        }),
      },
    ]

    if (JSON.stringify(newTrafficIssuesGraphData) !== JSON.stringify(trafficIssuesGraphData)) {
      setGrafficIssuesGraphData(newTrafficIssuesGraphData)
    }

    setTotals({
      reported: issues.length,
      resolved: resolvedIssue.length,
    })

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [issues, timestamp.from, timestamp.to])

  return { trafficIssuesGraphData, totals }
}

const formatDate = (date: Date): string => {
  const day = date.getDate().toString().padStart(2, '0')
  const month = (date.getMonth() + 1).toString().padStart(2, '0') // javascript months are 0-based
  const year = date.getFullYear()
  return `${day}/${month}/${year}`
}

const formatBackToDate = (date: string): Date => {
  const [day, month, year] = date.split('/')
  return new Date(Number(year), Number(month) - 1, Number(day))
}
