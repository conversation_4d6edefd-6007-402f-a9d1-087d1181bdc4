import { AssociatedInstanceType } from '@/firestoreQueries/integrations/IntegrationTypes'
import { ResourceType } from '@/firestoreQueries/resources/resourcesTypes'
import { useEffect, useState } from 'react'
import * as Sentry from '@sentry/react'
import { connectAndGetAvailableResource } from '@/views/Access/Resources/utils'

type Args = {
    resources: ResourceType[] | undefined
    organizationId: string
}
export default ({ resources, organizationId }: Args) => {
    const [availableInstancesMap, setAvailableInstancesMap] = useState<
        Map<string, { associatedInstances: AssociatedInstanceType[]; region: string }>
    >(new Map())
    const [isLoadingInstances, setIsLoadingInstances] = useState(true)
    useEffect(() => {
        if (resources) {
            setIsLoadingInstances(true)
            const awsResources = resources.filter((r) => r.integration.integrationType === 'aws')

            const promises = awsResources.map(async (resource) => {
                const res = await connectAndGetAvailableResource({
                    organizationId,
                    resource,
                })
                if (res) {
                    const { associatedInstances, region } = res
                    setAvailableInstancesMap((prev) => {
                        return new Map(
                            prev.set(resource.id, {
                                associatedInstances,

                                region: region!,
                            })
                        )
                    })
                }
            })

            Promise.all(promises)
                .then(() => {
                    setIsLoadingInstances(false)
                })
                .catch((e) => {
                    Sentry.captureException(e)
                    setIsLoadingInstances(false)
                })
        }
    }, [resources, organizationId])
    return { availableInstancesMap, isLoadingInstances }
}
