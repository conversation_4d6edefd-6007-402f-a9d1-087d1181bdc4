import { useEffect, useRef } from 'react'

export default () => {
    const inputRef = useRef<HTMLInputElement>(null)
    useEffect(() => {
        const handleKeyDown = (e: KeyboardEvent) => {
            if (e.metaKey && e.key === 'k') {
                e.preventDefault()
                inputRef.current?.focus()
            }

            if (e.key === 'Escape') {
                inputRef.current?.blur()
            }
        }
        addEventListener('keydown', handleKeyDown)
        return () => {
            removeEventListener('keydown', handleKeyDown)
        }
    }, [])
    return inputRef
}
