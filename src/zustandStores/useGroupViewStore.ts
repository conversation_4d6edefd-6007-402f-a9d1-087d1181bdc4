import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { 
  GroupView, 
  GroupViewState, 
  GroupedEntity, 
  GroupedEdge 
} from '@globalTypes/ndrBenchmarkTypes/groupViews/groupViewTypes'

interface GroupViewStore extends GroupViewState {
  // Actions
  setCurrentView: (view: GroupView | null) => void
  setAvailableKeys: (keys: string[]) => void
  setGroupedEntities: (entities: GroupedEntity[]) => void
  setGroupedEdges: (edges: GroupedEdge[]) => void
  setIsGrouped: (isGrouped: boolean) => void
  setZoomedGroup: (groupId: string | null) => void
  
  // Computed getters
  getGroupById: (groupId: string) => GroupedEntity | undefined
  getEdgesForGroup: (groupId: string) => GroupedEdge[]
  
  // Reset functions
  resetGrouping: () => void
  clearZoom: () => void
}

const initialState: GroupViewState = {
  currentView: null,
  availableKeys: [],
  groupedEntities: [],
  groupedEdges: [],
  isGrouped: false,
  zoomedGroup: null,
}

export const useGroupViewStore = create<GroupViewStore>()(
  persist(
    (set, get) => ({
      ...initialState,
      
      // Actions
      setCurrentView: (view) => set({ currentView: view }),
      
      setAvailableKeys: (keys) => set({ availableKeys: keys }),
      
      setGroupedEntities: (entities) => set({ groupedEntities: entities }),
      
      setGroupedEdges: (edges) => set({ groupedEdges: edges }),
      
      setIsGrouped: (isGrouped) => set({ isGrouped }),
      
      setZoomedGroup: (groupId) => set({ zoomedGroup: groupId }),
      
      // Computed getters
      getGroupById: (groupId) => {
        const { groupedEntities } = get()
        return groupedEntities.find(entity => entity.groupId === groupId)
      },
      
      getEdgesForGroup: (groupId) => {
        const { groupedEdges } = get()
        return groupedEdges.filter(edge => 
          edge.sourceGroupId === groupId || edge.targetGroupId === groupId
        )
      },
      
      // Reset functions
      resetGrouping: () => set({
        currentView: null,
        groupedEntities: [],
        groupedEdges: [],
        isGrouped: false,
        zoomedGroup: null,
      }),
      
      clearZoom: () => set({ zoomedGroup: null }),
    }),
    {
      name: 'groupViewStore',
      partialize: (state) => ({
        currentView: state.currentView,
        isGrouped: state.isGrouped,
        zoomedGroup: state.zoomedGroup,
      }),
    }
  )
)
