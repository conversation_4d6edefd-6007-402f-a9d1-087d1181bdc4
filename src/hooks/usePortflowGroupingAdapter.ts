import { useMemo, useEffect } from 'react'
import { Node, Edge } from 'reactflow'
import { useGroupViewStore } from '@/zustandStores/useGroupViewStore'
import { useEntitiesWithLabels } from '@/hooks/useEntitiesWithLabels'
import { groupEntitiesByLabels } from '@/utils/groupViewUtils'

interface UsePortflowGroupingAdapterProps {
  organizationId: string | undefined
  originalNodes: Node[]
  originalEdges: Edge[]
}

/**
 * Adapter hook that integrates grouping with the existing Portflow system
 * This is a simplified version that works with the current data structure
 */
export function usePortflowGroupingAdapter({
  organizationId,
  originalNodes,
  originalEdges,
}: UsePortflowGroupingAdapterProps) {
  const {
    currentView,
    isGrouped,
    zoomedGroup,
    setZoomedGroup,
  } = useGroupViewStore()

  // Get all entities with their labels using the dedicated hook
  const {
    data: allEntities,
    isLoading: isLoadingData,
    error: entitiesError,
  } = useEntitiesWithLabels({
    organizationId,
    enabled: !!organizationId,
  })

  // Log any errors
  useEffect(() => {
    if (entitiesError) {
      console.error('usePortflowGroupingAdapter - entities fetch error:', entitiesError)
    }
  }, [entitiesError])

  // Filter to only entities that have labels (for grouping)
  const entities = useMemo(() => {
    if (!allEntities || !allEntities.length) {
      console.log('usePortflowGroupingAdapter - no entities fetched')
      return []
    }

    // Filter entities that have at least one label
    const entitiesWithLabels = allEntities.filter(entity =>
      entity.labels && Array.isArray(entity.labels) && entity.labels.length > 0
    )

    console.log('usePortflowGroupingAdapter - entities with labels:', entitiesWithLabels.length, '/', allEntities.length)

    if (entitiesWithLabels.length > 0) {
      console.log('usePortflowGroupingAdapter - sample entity with labels:', {
        id: entitiesWithLabels[0].id,
        name: entitiesWithLabels[0].name,
        labelsCount: entitiesWithLabels[0].labels?.length || 0,
        labels: entitiesWithLabels[0].labels?.map((l: any) => ({ key: l.key, values: l.values })) || []
      })
    } else {
      console.warn('usePortflowGroupingAdapter - No entities have valid labels for grouping!')
    }

    return entitiesWithLabels
  }, [allEntities])

  // Add effect to trigger re-calculation when view changes
  useEffect(() => {
    if (currentView) {
      console.log('usePortflowGroupingAdapter - view changed:', currentView.name)
      console.log('usePortflowGroupingAdapter - isGrouped:', isGrouped)
      console.log('usePortflowGroupingAdapter - entities available:', entities.length)
      console.log('usePortflowGroupingAdapter - isLoadingData:', isLoadingData)
    }
  }, [currentView, isGrouped, entities.length, isLoadingData])

  // Remove unused code - labels are now fetched directly in components that need them

  // Create grouped entities using the existing utility function
  const groupedEntities = useMemo(() => {
    if (!isGrouped || !currentView || !entities.length || isLoadingData) {
      console.log('usePortflowGroupingAdapter - skipping grouping:', {
        isGrouped,
        hasCurrentView: !!currentView,
        entitiesLength: entities.length,
        isLoadingData
      })
      return []
    }

    console.log('usePortflowGroupingAdapter - creating grouped entities')
    console.log('usePortflowGroupingAdapter - entities with labels:', entities.length)
    console.log('usePortflowGroupingAdapter - currentView:', {
      name: currentView.name,
      primaryKey: currentView.primaryKey,
      secondaryKey: currentView.secondaryKey
    })

    // Log sample entities to see their structure
    if (entities.length > 0) {
      console.log('usePortflowGroupingAdapter - sample entities:', entities.slice(0, 3).map(e => ({
        id: e.id,
        name: e.name,
        labelsCount: e.labels?.length || 0,
        labels: e.labels?.map((l: any) => ({ key: l.key, values: l.values })) || []
      })))
    }

    // Create entity labels map for the utility function
    const entityLabelsMap = new Map()
    entities.forEach(entity => {
      if (entity.labels && Array.isArray(entity.labels)) {
        entityLabelsMap.set(entity.id, entity.labels)
      }
    })

    console.log('usePortflowGroupingAdapter - entityLabelsMap size:', entityLabelsMap.size)

    try {
      // Use the existing grouping utility
      // Cast entities to the expected type since they have the same structure
      const grouped = groupEntitiesByLabels(
        entities as any[], // The entities have the same structure as EntityTypeWithId
        entityLabelsMap,
        currentView.primaryKey,
        currentView.secondaryKey,
        currentView.additionalKeys
      )

      console.log('usePortflowGroupingAdapter - grouped entities result:', grouped.length)
      if (grouped.length > 0) {
        console.log('usePortflowGroupingAdapter - sample groups:', grouped.slice(0, 2))
      }
      return grouped
    } catch (error) {
      console.error('usePortflowGroupingAdapter - grouping error:', error)
      return []
    }
  }, [isGrouped, currentView, entities, isLoadingData])

  // Transform nodes and edges based on grouping state
  const { nodes, edges } = useMemo(() => {
    // Always ensure we return valid arrays
    const safeOriginalNodes = originalNodes || []
    const safeOriginalEdges = originalEdges || []

    console.log('usePortflowGroupingAdapter - processing nodes/edges')
    console.log('usePortflowGroupingAdapter - isGrouped:', isGrouped)
    console.log('usePortflowGroupingAdapter - currentView:', currentView?.name)
    console.log('usePortflowGroupingAdapter - entities with labels:', entities.length)
    console.log('usePortflowGroupingAdapter - groupedEntities:', groupedEntities.length)

    if (!isGrouped || !currentView) {
      console.log('usePortflowGroupingAdapter - returning original nodes/edges (not grouped)')
      return { nodes: safeOriginalNodes, edges: safeOriginalEdges }
    }

    if (isLoadingData) {
      console.log('usePortflowGroupingAdapter - returning original nodes/edges (still loading data)')
      return { nodes: safeOriginalNodes, edges: safeOriginalEdges }
    }

    if (!entities.length) {
      console.log('usePortflowGroupingAdapter - returning original nodes/edges (no entities with labels)')
      return { nodes: safeOriginalNodes, edges: safeOriginalEdges }
    }

    if (zoomedGroup) {
      console.log('usePortflowGroupingAdapter - showing zoomed group view')
      // Show individual entities within the zoomed group
      return getZoomedGroupView(zoomedGroup, safeOriginalNodes, safeOriginalEdges, groupedEntities, entities)
    }

    if (!groupedEntities.length) {
      console.log('usePortflowGroupingAdapter - no grouped entities created, but grouping is enabled')
      console.log('usePortflowGroupingAdapter - returning empty nodes to show only groups')
      // When grouping is enabled but no groups are created, show empty graph
      return { nodes: [], edges: [] }
    }

    console.log('usePortflowGroupingAdapter - creating group nodes from grouped entities')
    console.log('usePortflowGroupingAdapter - groupedEntities sample:', groupedEntities[0])

    // Create group nodes using machine-group type (similar to so-group)
    const groupNodes: Node[] = groupedEntities.map((group, index) => ({
      id: group.groupId || `group-${index}`,
      type: 'machine-group',
      position: calculateGroupPosition(index, groupedEntities.length),
      data: {
        label: group.groupName || `Group ${index + 1}`,
        subType: 'group',
        treeType: 'group',
        cloudProvider: 'group',
        issues: [],
        marked: false,
        riskScore: 0,
        isFocusNode: false,
        entityCount: group.entityCount || group.entities?.length || 0,
        entities: group.entities || [],
        groupKeys: group.groupKeys || {},
        aggregatedLabels: group.aggregatedLabels || {},
        isGroup: true,
        labels: [],
        // Add children property like so-group
        children: group.entities?.map((entityId: string) => {
          const entity = entities.find(e => e.id === entityId)
          return entity ? {
            id: entity.id,
            type: 'machine',
            data: {
              ...entity,
              label: entity.name || entity.id,
              subType: entity.subType || entity.type || 'unknown',
              parentId: group.groupId || `group-${index}`,
            },
            position: { x: 0, y: 0 },
          } : null
        }).filter(Boolean) || [],
      },
      draggable: true,
    }))

    // Create simplified edges between groups
    const groupEdges = createGroupEdges(groupedEntities, safeOriginalEdges)

    console.log('usePortflowGroupingAdapter - final result:', {
      nodes: groupNodes.length,
      edges: groupEdges.length,
      groupNodes: groupNodes.map(n => ({ id: n.id, label: n.data.label })),
      sampleEdges: groupEdges.slice(0, 3).map(e => ({ id: e.id, source: e.source, target: e.target }))
    })

    return { nodes: groupNodes, edges: groupEdges }
  }, [isGrouped, currentView, zoomedGroup, originalNodes, originalEdges, entities, groupedEntities, isLoadingData])

  // Handle node clicks for grouping functionality
  const handleNodeClick = (nodeId: string, nodeData: any) => {
    console.log('handleNodeClick - node clicked:', {
      id: nodeId,
      isGroup: nodeData?.isGroup,
      isBackButton: nodeData?.isBackButton,
      isMachine: nodeData?.isMachine,
      currentZoomedGroup: zoomedGroup
    })

    if (nodeData?.isBackButton) {
      console.log('handleNodeClick - back button clicked, returning to group view')
      setZoomedGroup(null)
      return true
    }

    if (nodeData?.isGroup && !zoomedGroup) {
      console.log('handleNodeClick - group node clicked, zooming into group:', nodeId)
      setZoomedGroup(nodeId)
      return true
    }

    console.log('handleNodeClick - entity node clicked or already zoomed, no action')
    return false
  }

  return {
    nodes: nodes || [],
    edges: edges || [],
    isGrouped,
    currentView,
    zoomedGroup,
    handleNodeClick,
  }
}

// Helper function to create edges between groups
function createGroupEdges(groupedEntities: any[], originalEdges: Edge[]): Edge[] {
  console.log('createGroupEdges - input:', {
    groupedEntities: groupedEntities.length,
    originalEdges: originalEdges.length
  })

  if (!groupedEntities.length || !originalEdges.length) {
    console.log('createGroupEdges - no input data, returning empty edges')
    return []
  }

  // Create entity to group mapping
  const entityToGroupMap = new Map<string, string>()
  groupedEntities.forEach(group => {
    group.entities.forEach((entityId: string) => {
      entityToGroupMap.set(entityId, group.groupId)
    })
  })

  console.log('createGroupEdges - entity to group mapping:', entityToGroupMap.size)

  // Track connections between groups
  const groupConnections = new Map<string, Set<string>>()

  originalEdges.forEach((edge) => {
    const sourceGroup = entityToGroupMap.get(edge.source)
    const targetGroup = entityToGroupMap.get(edge.target)

    if (sourceGroup && targetGroup && sourceGroup !== targetGroup) {
      const connectionKey = `${sourceGroup}-${targetGroup}`
      if (!groupConnections.has(connectionKey)) {
        groupConnections.set(connectionKey, new Set())
      }
      groupConnections.get(connectionKey)!.add(edge.id)
    }
  })

  console.log('createGroupEdges - group connections found:', groupConnections.size)

  // Create group edges
  const groupEdges: Edge[] = []
  groupConnections.forEach((edgeIds, connectionKey) => {
    const [sourceGroup, targetGroup] = connectionKey.split('-')
    groupEdges.push({
      id: connectionKey,
      source: sourceGroup,
      target: targetGroup,
      type: 'customEdge', // Use customEdge for traffic animation
      data: {
        edgeCount: edgeIds.size,
        aggregatedData: {
          totalTraffic: edgeIds.size,
          ports: [],
          protocols: [],
          isDanger: false,
        },
        isGroupEdge: true,
      },
    })
  })

  console.log('createGroupEdges - created group edges:', groupEdges.length)
  if (groupEdges.length > 0) {
    console.log('createGroupEdges - sample edges:', groupEdges.slice(0, 3))
  }

  return groupEdges
}

// Helper function to calculate group positions
function calculateGroupPosition(index: number, totalGroups: number): { x: number; y: number } {
  const cols = Math.ceil(Math.sqrt(totalGroups))
  const row = Math.floor(index / cols)
  const col = index % cols

  const spacing = 350
  const offsetX = 100
  const offsetY = 100

  return {
    x: offsetX + col * spacing,
    y: offsetY + row * spacing,
  }
}

// Zoomed group view - shows individual entities within a group
function getZoomedGroupView(
  zoomedGroupId: string,
  originalNodes: Node[],
  originalEdges: Edge[],
  groupedEntities: any[],
  entities: any[]
) {
  console.log('getZoomedGroupView - zoomedGroupId:', zoomedGroupId)
  console.log('getZoomedGroupView - groupedEntities:', groupedEntities.length)

  // Safety check for inputs
  if (!originalNodes || !originalEdges || !groupedEntities.length) {
    return { nodes: [], edges: [] }
  }

  // Find the group being zoomed into
  const targetGroup = groupedEntities.find(group => group.groupId === zoomedGroupId)
  if (!targetGroup) {
    console.log('getZoomedGroupView - target group not found')
    return { nodes: [], edges: [] }
  }

  console.log('getZoomedGroupView - target group:', targetGroup)
  console.log('getZoomedGroupView - entities in group:', targetGroup.entities?.length || 0)

  // Filter entities to show only those in the target group
  const groupEntityIds = new Set(targetGroup.entities || [])
  const entitiesInGroup = entities.filter(entity => groupEntityIds.has(entity.id))

  console.log('getZoomedGroupView - filtered entities:', entitiesInGroup.length)

  // Create individual machine nodes for each entity (with full functionality)
  const machineNodes: Node[] = entitiesInGroup.map((entity, index) => ({
    id: entity.id,
    type: 'machine',
    position: calculateGroupPosition(index, entitiesInGroup.length),
    data: {
      ...entity,
      label: entity.name || entity.id,
      subType: entity.subType || entity.type || 'unknown',
      issues: entity.issues || [],
      marked: entity.marked || false,
      riskScore: entity.riskScore || 0,
      isFocusNode: entity.isFocusNode || false,
      labels: entity.labels || [],
      isMachine: true,
      parentId: `so-group-${zoomedGroupId}`, // Set parent to the so-group
    },
    draggable: true,
  }))

  // Create a so-group node that wraps all the machine nodes
  const soGroupNode: Node = {
    id: `so-group-${zoomedGroupId}`,
    type: 'so-group', // Use the existing so-group type
    position: { x: 0, y: 0 }, // Will be positioned by layout
    data: {
      label: targetGroup.groupName || 'Group Details',
      subType: 'group',
      treeType: 'group',
      cloudProvider: 'group',
      riskScore: 0,
      labels: [],
      isBackButton: true, // Mark this so the click handler knows to go back
      children: machineNodes, // Include the machine nodes as children
    },
    draggable: true,
  }

  // Filter edges to show only connections between entities in this group
  // Preserve original edge properties including type for traffic animation
  const entityEdges = originalEdges.filter(edge =>
    groupEntityIds.has(edge.source) && groupEntityIds.has(edge.target)
  ).map(edge => ({
    ...edge,
    // Ensure edge has proper type for animation
    type: edge.type || 'customEdge',
  }))

  console.log('getZoomedGroupView - result:', {
    soGroupNode: soGroupNode.id,
    machineNodes: machineNodes.length,
    edges: entityEdges.length
  })

  return {
    nodes: [soGroupNode, ...machineNodes],
    edges: entityEdges,
  }
}
