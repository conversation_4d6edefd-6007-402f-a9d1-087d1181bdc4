import { useMemo, useEffect } from 'react'
import { Node, Edge } from 'reactflow'
import { useGroupViewStore } from '@/zustandStores/useGroupViewStore'
import { useGetLabels } from '@/firestoreQueries/ndr/labels/hooks/useLabels'
import useGetEntities from '@/firestoreQueries/ndr/entities/hooks/useGetEntities'
import useFetchLabelAssigmentForEntities from '@/firestoreQueries/ndr/entities/queries/hooks/useFetchLabelAssigmentForEntities'
import useFetchLabelsByIds from '@/firestoreQueries/ndr/entities/queries/hooks/useFetchLabelForEntities'

interface UsePortflowGroupingAdapterProps {
  organizationId: string | undefined
  originalNodes: Node[]
  originalEdges: Edge[]
}

/**
 * Adapter hook that integrates grouping with the existing Portflow system
 * This is a simplified version that works with the current data structure
 */
export function usePortflowGroupingAdapter({
  organizationId,
  originalNodes,
  originalEdges,
}: UsePortflowGroupingAdapterProps) {
  const {
    currentView,
    isGrouped,
    zoomedGroup,
    setZoomedGroup,
  } = useGroupViewStore()

  // Get entities and labels for grouping
  const { data: rawEntities = [] } = useGetEntities({
    organizationId,
    leanedEntities: true,
    enabled: !!organizationId, // Always fetch entities when org is available
  })

  // Get entity IDs for label fetching
  const entityIds = useMemo(() => rawEntities.map((entity: any) => entity.id), [rawEntities])

  // Get label assignments for all entities
  const { data: labelAssignments } = useFetchLabelAssigmentForEntities({
    organizationId: organizationId!,
    entityIds,
    enabled: !!organizationId && entityIds.length > 0,
  })

  // Get all label IDs that are assigned to entities
  const assignedLabelIds = useMemo(() => {
    if (!labelAssignments) return []
    const labelIds = new Set<string>()
    labelAssignments.forEach((assignments: string[]) => {
      assignments.forEach(labelId => labelIds.add(labelId))
    })
    return Array.from(labelIds)
  }, [labelAssignments])

  // Get the actual label data
  const { data: labelsData } = useFetchLabelsByIds({
    organizationId: organizationId!,
    labelIds: assignedLabelIds,
    enabled: !!organizationId && assignedLabelIds.length > 0,
  })

  // Combine entities with their labels
  const entities = useMemo(() => {
    if (!rawEntities.length || !labelAssignments || !labelsData) {
      console.log('usePortflowGroupingAdapter - missing data for entity-label combination')
      return []
    }

    return rawEntities.map((entity: any) => {
      const entityLabelIds = labelAssignments.get(entity.id) || []
      const entityLabels = entityLabelIds
        .map((labelId: string) => labelsData.get(labelId))
        .filter(Boolean)

      return {
        ...entity,
        labels: entityLabels,
      }
    })
  }, [rawEntities, labelAssignments, labelsData])

  const { data: allLabels = [] } = useGetLabels(organizationId)

  // Extract available label keys from entities
  const availableKeys = useMemo(() => {
    if (!entities.length) return []
    
    const keySet = new Set<string>()
    entities.forEach((entity: any) => {
      if (entity.labels) {
        entity.labels.forEach((label: any) => {
          if (label.key) {
            keySet.add(label.key)
          }
        })
      }
    })
    
    return Array.from(keySet)
  }, [entities])

  // TODO: Re-enable automatic key updates after fixing infinite loop
  // For now, keys will need to be updated manually when creating views
  // useEffect(() => {
  //   setAvailableKeys(availableKeys)
  // }, [availableKeys])

  // Transform nodes and edges based on grouping state
  const { nodes, edges } = useMemo(() => {
    // Always ensure we return valid arrays
    const safeOriginalNodes = originalNodes || []
    const safeOriginalEdges = originalEdges || []

    console.log('usePortflowGroupingAdapter - isGrouped:', isGrouped)
    console.log('usePortflowGroupingAdapter - currentView:', currentView)
    console.log('usePortflowGroupingAdapter - entities:', entities.length)
    console.log('usePortflowGroupingAdapter - rawEntities:', rawEntities.length)
    console.log('usePortflowGroupingAdapter - labelAssignments:', labelAssignments?.size || 0)
    console.log('usePortflowGroupingAdapter - labelsData:', labelsData?.size || 0)
    console.log('usePortflowGroupingAdapter - originalNodes:', safeOriginalNodes.length)

    if (!isGrouped || !currentView) {
      console.log('usePortflowGroupingAdapter - returning original nodes/edges')
      return { nodes: safeOriginalNodes, edges: safeOriginalEdges }
    }

    if (zoomedGroup) {
      console.log('usePortflowGroupingAdapter - showing zoomed group view')
      // Show individual entities within the zoomed group
      return getZoomedGroupView(zoomedGroup, safeOriginalNodes, safeOriginalEdges)
    }

    console.log('usePortflowGroupingAdapter - creating grouped view')
    // Show grouped view - for now, return original nodes with group indicators
    const result = getSimpleGroupedView(safeOriginalNodes, safeOriginalEdges, entities, currentView)
    console.log('usePortflowGroupingAdapter - grouped result:', result)
    return result
  }, [isGrouped, currentView, zoomedGroup, originalNodes, originalEdges, entities, rawEntities, labelAssignments, labelsData])

  // Handle node clicks for grouping functionality
  const handleNodeClick = (nodeId: string, nodeData: any) => {
    if (nodeData?.isBackButton) {
      setZoomedGroup(null)
      return true
    }

    if (nodeData?.isGroup && !zoomedGroup) {
      setZoomedGroup(nodeId)
      return true
    }

    return false
  }

  return {
    nodes: nodes || [],
    edges: edges || [],
    isGrouped,
    currentView,
    zoomedGroup,
    availableKeys,
    handleNodeClick,
  }
}

// Simplified grouped view that works with existing node structure
function getSimpleGroupedView(
  originalNodes: Node[],
  originalEdges: Edge[],
  entities: any[],
  currentView: any
) {
  console.log('getSimpleGroupedView - entities:', entities.length)
  console.log('getSimpleGroupedView - currentView:', currentView)
  console.log('getSimpleGroupedView - originalNodes:', originalNodes.length)

  if (!entities.length || !currentView || !originalNodes || !originalEdges) {
    console.log('getSimpleGroupedView - early return due to missing data')
    return { nodes: originalNodes || [], edges: originalEdges || [] }
  }

  // Create a map of entity ID to group
  const entityToGroupMap = new Map<string, string>()
  const groups = new Map<string, { entities: string[], label: string }>()

  console.log('getSimpleGroupedView - processing entities...')
  entities.forEach((entity: any, index: number) => {
    if (index < 3) { // Log first 3 entities for debugging
      console.log('getSimpleGroupedView - entity structure:', {
        id: entity.id,
        name: entity.name,
        labels: entity.labels,
        labelCount: entity.labels?.length || 0
      })
    }

    if (!entity.labels || !Array.isArray(entity.labels) || entity.labels.length === 0) {
      if (index < 3) console.log('getSimpleGroupedView - entity has no labels:', entity.id)
      return
    }

    // Find the primary key value for this entity
    const primaryLabel = entity.labels.find((label: any) => label.key === currentView.primaryKey)
    if (!primaryLabel || !primaryLabel.values || primaryLabel.values.length === 0) {
      if (index < 3) {
        console.log('getSimpleGroupedView - no primary label found for entity:', entity.id, 'looking for key:', currentView.primaryKey)
        console.log('getSimpleGroupedView - available label keys:', entity.labels.map((l: any) => l.key))
      }
      return
    }

    const groupKey = primaryLabel.values[0]
    const groupId = `group-${currentView.primaryKey}-${groupKey}`

    entityToGroupMap.set(entity.id, groupId)

    if (!groups.has(groupId)) {
      groups.set(groupId, {
        entities: [],
        label: `${currentView.primaryKey}: ${groupKey}`,
      })
    }

    groups.get(groupId)!.entities.push(entity.id)
  })

  console.log('getSimpleGroupedView - created groups:', groups.size)
  console.log('getSimpleGroupedView - groups:', Array.from(groups.keys()))

  // Create group nodes
  const groupNodes: Node[] = Array.from(groups.entries()).map(([groupId, group], index) => ({
    id: groupId,
    type: 'groupNode',
    position: calculateGroupPosition(index, groups.size),
    data: {
      label: group.label,
      entityCount: group.entities.length,
      entities: group.entities,
      groupKeys: { [currentView.primaryKey]: group.label.split(': ')[1] },
      aggregatedLabels: {},
      isGroup: true,
    },
    draggable: true,
  }))

  // Create simplified group edges based on original edges
  const groupEdges: Edge[] = []
  const groupConnections = new Map<string, Set<string>>()

  originalEdges.forEach((edge) => {
    const sourceGroup = entityToGroupMap.get(edge.source)
    const targetGroup = entityToGroupMap.get(edge.target)

    if (sourceGroup && targetGroup && sourceGroup !== targetGroup) {
      const connectionKey = `${sourceGroup}-${targetGroup}`
      if (!groupConnections.has(connectionKey)) {
        groupConnections.set(connectionKey, new Set())
      }
      groupConnections.get(connectionKey)!.add(edge.id)
    }
  })

  groupConnections.forEach((edgeIds, connectionKey) => {
    const [sourceGroup, targetGroup] = connectionKey.split('-')
    groupEdges.push({
      id: connectionKey,
      source: sourceGroup,
      target: targetGroup,
      type: 'groupEdge',
      data: {
        edgeCount: edgeIds.size,
        aggregatedData: {
          totalTraffic: edgeIds.size,
          ports: [],
          protocols: [],
          isDanger: false,
        },
        isGroupEdge: true,
      },
    })
  })

  return { nodes: groupNodes, edges: groupEdges }
}

// Simplified zoomed group view
function getZoomedGroupView(
  zoomedGroupId: string,
  originalNodes: Node[],
  originalEdges: Edge[]
) {
  // Safety check for inputs
  if (!originalNodes || !originalEdges) {
    return { nodes: [], edges: [] }
  }

  // For now, just show all original nodes with a back button
  // In a full implementation, this would filter to show only entities in the group
  
  const backNode: Node = {
    id: 'back-to-groups',
    type: 'default',
    position: { x: 50, y: 50 },
    data: {
      label: '← Back to Groups',
      isBackButton: true,
    },
    style: {
      background: '#f3f4f6',
      border: '2px solid #d1d5db',
      borderRadius: '8px',
      padding: '8px 16px',
      cursor: 'pointer',
    },
  }

  return {
    nodes: [backNode, ...originalNodes],
    edges: originalEdges,
  }
}

// Helper function to calculate group positions
function calculateGroupPosition(index: number, totalGroups: number): { x: number; y: number } {
  const cols = Math.ceil(Math.sqrt(totalGroups))
  const row = Math.floor(index / cols)
  const col = index % cols
  
  const spacing = 350
  const offsetX = 100
  const offsetY = 100
  
  return {
    x: offsetX + col * spacing,
    y: offsetY + row * spacing,
  }
}
