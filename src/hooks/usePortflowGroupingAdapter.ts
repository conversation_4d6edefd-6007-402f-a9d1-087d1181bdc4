import { useMemo, useEffect } from 'react'
import { Node, Edge } from 'reactflow'
import { useGroupViewStore } from '@/zustandStores/useGroupViewStore'
import { useEntitiesWithLabels } from '@/hooks/useEntitiesWithLabels'
import { groupEntitiesByLabels } from '@/utils/groupViewUtils'

interface UsePortflowGroupingAdapterProps {
  organizationId: string | undefined
  originalNodes: Node[]
  originalEdges: Edge[]
}

/**
 * Adapter hook that integrates grouping with the existing Portflow system
 * This is a simplified version that works with the current data structure
 */
export function usePortflowGroupingAdapter({
  organizationId,
  originalNodes,
  originalEdges,
}: UsePortflowGroupingAdapterProps) {
  const {
    currentView,
    isGrouped,
    zoomedGroup,
    setZoomedGroup,
  } = useGroupViewStore()

  // Get all entities with their labels using the dedicated hook
  const {
    data: allEntities,
    isLoading: isLoadingData,
    error: entitiesError,
  } = useEntitiesWithLabels({
    organizationId,
    enabled: !!organizationId,
  })

  // Log any errors
  useEffect(() => {
    if (entitiesError) {
      console.error('usePortflowGroupingAdapter - entities fetch error:', entitiesError)
    }
  }, [entitiesError])

  // Filter to only entities that have labels (for grouping)
  const entities = useMemo(() => {
    if (!allEntities || !allEntities.length) {
      console.log('usePortflowGroupingAdapter - no entities fetched')
      return []
    }

    // Filter entities that have at least one label
    const entitiesWithLabels = allEntities.filter(entity =>
      entity.labels && Array.isArray(entity.labels) && entity.labels.length > 0
    )

    console.log('usePortflowGroupingAdapter - entities with labels:', entitiesWithLabels.length, '/', allEntities.length)

    if (entitiesWithLabels.length > 0) {
      console.log('usePortflowGroupingAdapter - sample entity with labels:', {
        id: entitiesWithLabels[0].id,
        name: entitiesWithLabels[0].name,
        labelsCount: entitiesWithLabels[0].labels?.length || 0,
        labels: entitiesWithLabels[0].labels?.map((l: any) => ({ key: l.key, values: l.values })) || []
      })
    } else {
      console.warn('usePortflowGroupingAdapter - No entities have valid labels for grouping!')
    }

    return entitiesWithLabels
  }, [allEntities])

  // Add effect to trigger re-calculation when view changes
  useEffect(() => {
    if (currentView) {
      console.log('usePortflowGroupingAdapter - view changed:', currentView.name)
      console.log('usePortflowGroupingAdapter - isGrouped:', isGrouped)
      console.log('usePortflowGroupingAdapter - entities available:', entities.length)
      console.log('usePortflowGroupingAdapter - isLoadingData:', isLoadingData)
    }
  }, [currentView, isGrouped, entities.length, isLoadingData])

  // Remove unused code - labels are now fetched directly in components that need them

  // Create grouped entities using the existing utility function
  const groupedEntities = useMemo(() => {
    if (!isGrouped || !currentView || !entities.length || isLoadingData) {
      console.log('usePortflowGroupingAdapter - skipping grouping:', {
        isGrouped,
        hasCurrentView: !!currentView,
        entitiesLength: entities.length,
        isLoadingData
      })
      return []
    }

    console.log('usePortflowGroupingAdapter - creating grouped entities')
    console.log('usePortflowGroupingAdapter - entities with labels:', entities.length)
    console.log('usePortflowGroupingAdapter - currentView:', {
      name: currentView.name,
      primaryKey: currentView.primaryKey,
      secondaryKey: currentView.secondaryKey
    })

    // Log sample entities to see their structure
    if (entities.length > 0) {
      console.log('usePortflowGroupingAdapter - sample entities:', entities.slice(0, 3).map(e => ({
        id: e.id,
        name: e.name,
        labelsCount: e.labels?.length || 0,
        labels: e.labels?.map((l: any) => ({ key: l.key, values: l.values })) || []
      })))
    }

    // Create entity labels map for the utility function
    const entityLabelsMap = new Map()
    entities.forEach(entity => {
      if (entity.labels && Array.isArray(entity.labels)) {
        entityLabelsMap.set(entity.id, entity.labels)
      }
    })

    console.log('usePortflowGroupingAdapter - entityLabelsMap size:', entityLabelsMap.size)

    try {
      // Use the existing grouping utility
      // Cast entities to the expected type since they have the same structure
      const grouped = groupEntitiesByLabels(
        entities as any[], // The entities have the same structure as EntityTypeWithId
        entityLabelsMap,
        currentView.primaryKey,
        currentView.secondaryKey,
        currentView.additionalKeys
      )

      console.log('usePortflowGroupingAdapter - grouped entities result:', grouped.length)
      if (grouped.length > 0) {
        console.log('usePortflowGroupingAdapter - sample groups:', grouped.slice(0, 2))
      }
      return grouped
    } catch (error) {
      console.error('usePortflowGroupingAdapter - grouping error:', error)
      return []
    }
  }, [isGrouped, currentView, entities, isLoadingData])

  // Transform nodes and edges based on grouping state
  const { nodes, edges } = useMemo(() => {
    // Always ensure we return valid arrays
    const safeOriginalNodes = originalNodes || []
    const safeOriginalEdges = originalEdges || []

    console.log('usePortflowGroupingAdapter - processing nodes/edges')
    console.log('usePortflowGroupingAdapter - isGrouped:', isGrouped)
    console.log('usePortflowGroupingAdapter - currentView:', currentView?.name)
    console.log('usePortflowGroupingAdapter - entities with labels:', entities.length)
    console.log('usePortflowGroupingAdapter - groupedEntities:', groupedEntities.length)

    if (!isGrouped || !currentView) {
      console.log('usePortflowGroupingAdapter - returning original nodes/edges (not grouped)')
      return { nodes: safeOriginalNodes, edges: safeOriginalEdges }
    }

    if (isLoadingData) {
      console.log('usePortflowGroupingAdapter - returning original nodes/edges (still loading data)')
      return { nodes: safeOriginalNodes, edges: safeOriginalEdges }
    }

    if (!entities.length) {
      console.log('usePortflowGroupingAdapter - returning original nodes/edges (no entities with labels)')
      return { nodes: safeOriginalNodes, edges: safeOriginalEdges }
    }

    if (zoomedGroup) {
      console.log('usePortflowGroupingAdapter - showing zoomed group view')
      // Show individual entities within the zoomed group
      return getZoomedGroupView(zoomedGroup, safeOriginalNodes, safeOriginalEdges, groupedEntities, entities)
    }

    if (!groupedEntities.length) {
      console.log('usePortflowGroupingAdapter - no grouped entities created, but grouping is enabled')
      console.log('usePortflowGroupingAdapter - returning empty nodes to show only groups')
      // When grouping is enabled but no groups are created, show empty graph
      return { nodes: [], edges: [] }
    }

    console.log('usePortflowGroupingAdapter - creating group nodes from grouped entities')
    console.log('usePortflowGroupingAdapter - groupedEntities sample:', groupedEntities[0])

    // Create a root node to manage the ELK layout
    const rootNode: Node = {
      id: 'groups-root',
      type: 'machine',
      position: { x: 0, y: 0 },
      width: 200,
      height: 80,
      data: {
        label: `${currentView.name} (${groupedEntities.length} groups)`,
        subType: 'root',
        issues: [],
        marked: false,
        riskScore: 0,
        isFocusNode: false,
        labels: [],
        isRoot: true,
      },
      style: {
        background: '#e0f2fe',
        border: '2px solid #0284c7',
        borderRadius: '8px',
        fontWeight: 'bold',
      },
      draggable: true,
    }

    // Create group nodes directly from grouped entities
    const groupNodes: Node[] = groupedEntities.map((group, index) => ({
      id: group.groupId || `group-${index}`,
      type: 'groupNode',
      position: { x: 0, y: 0 }, // ELK will calculate positions
      width: 200, // Larger width to prevent overlapping
      height: 120, // Larger height to prevent overlapping
      data: {
        label: group.groupName || `Group ${index + 1}`,
        subType: 'group', // Required by InstanceNode
        issues: [], // Required by InstanceNode - groups don't have issues
        marked: false, // Required by InstanceNode
        riskScore: 0, // Required by InstanceNode - groups don't have risk scores
        isFocusNode: false, // Required by InstanceNode
        entityCount: group.entityCount || group.entities?.length || 0,
        entities: group.entities || [],
        groupKeys: group.groupKeys || {},
        aggregatedLabels: group.aggregatedLabels || {},
        isGroup: true,
        labels: [], // Required for LabelsColumn component
      },
      draggable: true,
    }))

    // Create edges from root to all groups for proper ELK hierarchy
    const rootToGroupEdges: Edge[] = groupNodes.map((group) => ({
      id: `root-to-${group.id}`,
      source: 'groups-root',
      target: group.id,
      type: 'default',
      style: { stroke: '#94a3b8', strokeWidth: 1, strokeDasharray: '5,5' },
      data: { isHierarchical: true },
    }))

    // Create simplified edges between groups
    const groupEdges = createGroupEdges(groupedEntities, safeOriginalEdges)

    // Combine all edges
    const allEdges = [...rootToGroupEdges, ...groupEdges]

    console.log('usePortflowGroupingAdapter - final result:', {
      totalNodes: groupNodes.length + 1, // +1 for root
      groupNodes: groupNodes.length,
      edges: allEdges.length,
      rootToGroupEdges: rootToGroupEdges.length,
      groupToGroupEdges: groupEdges.length,
      sampleEdges: allEdges.slice(0, 3).map(e => ({ id: e.id, source: e.source, target: e.target }))
    })

    return { nodes: [rootNode, ...groupNodes], edges: allEdges }
  }, [isGrouped, currentView, zoomedGroup, originalNodes, originalEdges, entities, groupedEntities, isLoadingData])

  // Handle node clicks for grouping functionality
  const handleNodeClick = (nodeId: string, nodeData: any) => {
    console.log('handleNodeClick - node clicked:', {
      id: nodeId,
      isGroup: nodeData?.isGroup,
      isBackButton: nodeData?.isBackButton,
      isMachine: nodeData?.isMachine,
      currentZoomedGroup: zoomedGroup
    })

    if (nodeData?.isBackButton) {
      console.log('handleNodeClick - back button clicked, returning to group view')
      setZoomedGroup(null)
      return true
    }

    if (nodeData?.isGroup && !zoomedGroup) {
      console.log('handleNodeClick - group node clicked, zooming into group:', nodeId)
      setZoomedGroup(nodeId)
      return true
    }

    console.log('handleNodeClick - entity node clicked or already zoomed, no action')
    return false
  }

  return {
    nodes: nodes || [],
    edges: edges || [],
    isGrouped,
    currentView,
    zoomedGroup,
    handleNodeClick,
  }
}

// Helper function to create edges between groups
function createGroupEdges(groupedEntities: any[], originalEdges: Edge[]): Edge[] {
  console.log('createGroupEdges - input:', {
    groupedEntities: groupedEntities.length,
    originalEdges: originalEdges.length
  })

  if (!groupedEntities.length || !originalEdges.length) {
    console.log('createGroupEdges - no input data, returning empty edges')
    return []
  }

  // Create entity to group mapping
  const entityToGroupMap = new Map<string, string>()
  groupedEntities.forEach(group => {
    group.entities.forEach((entityId: string) => {
      entityToGroupMap.set(entityId, group.groupId)
    })
  })

  console.log('createGroupEdges - entity to group mapping:', entityToGroupMap.size)

  // Track connections between groups
  const groupConnections = new Map<string, Set<string>>()

  originalEdges.forEach((edge) => {
    const sourceGroup = entityToGroupMap.get(edge.source)
    const targetGroup = entityToGroupMap.get(edge.target)

    if (sourceGroup && targetGroup && sourceGroup !== targetGroup) {
      const connectionKey = `${sourceGroup}-${targetGroup}`
      if (!groupConnections.has(connectionKey)) {
        groupConnections.set(connectionKey, new Set())
      }
      groupConnections.get(connectionKey)!.add(edge.id)
    }
  })

  console.log('createGroupEdges - group connections found:', groupConnections.size)

  // Create group edges
  const groupEdges: Edge[] = []
  groupConnections.forEach((edgeIds, connectionKey) => {
    const [sourceGroup, targetGroup] = connectionKey.split('-')
    groupEdges.push({
      id: connectionKey,
      source: sourceGroup,
      target: targetGroup,
      type: 'groupEdge',
      data: {
        edgeCount: edgeIds.size,
        aggregatedData: {
          totalTraffic: edgeIds.size,
          ports: [],
          protocols: [],
          isDanger: false,
        },
        isGroupEdge: true,
      },
    })
  })

  console.log('createGroupEdges - created group edges:', groupEdges.length)
  if (groupEdges.length > 0) {
    console.log('createGroupEdges - sample edges:', groupEdges.slice(0, 3))
  }

  return groupEdges
}

// Note: ELK layout is handled by the existing usePortflowLayout system
// We just need to provide nodes with proper width/height for ELK to work with

// Zoomed group view - shows individual entities within a group
function getZoomedGroupView(
  zoomedGroupId: string,
  originalNodes: Node[],
  originalEdges: Edge[],
  groupedEntities: any[],
  entities: any[]
) {
  console.log('getZoomedGroupView - zoomedGroupId:', zoomedGroupId)
  console.log('getZoomedGroupView - groupedEntities:', groupedEntities.length)

  // Safety check for inputs
  if (!originalNodes || !originalEdges || !groupedEntities.length) {
    return { nodes: [], edges: [] }
  }

  // Find the group being zoomed into
  const targetGroup = groupedEntities.find(group => group.groupId === zoomedGroupId)
  if (!targetGroup) {
    console.log('getZoomedGroupView - target group not found')
    return { nodes: [], edges: [] }
  }

  console.log('getZoomedGroupView - target group:', targetGroup)
  console.log('getZoomedGroupView - entities in group:', targetGroup.entities?.length || 0)

  // Filter entities to show only those in the target group
  const groupEntityIds = new Set(targetGroup.entities || [])
  const entitiesInGroup = entities.filter(entity => groupEntityIds.has(entity.id))

  console.log('getZoomedGroupView - filtered entities:', entitiesInGroup.length)

  // Create a root node for this group view
  const groupRootNode: Node = {
    id: `group-root-${zoomedGroupId}`,
    type: 'machine',
    position: { x: 0, y: 0 },
    width: 250,
    height: 80,
    data: {
      label: `${targetGroup.groupName || 'Group'} (${entitiesInGroup.length} entities)`,
      subType: 'group-root',
      issues: [],
      marked: false,
      riskScore: 0,
      isFocusNode: false,
      labels: [],
      isGroupRoot: true,
    },
    style: {
      background: '#fef3c7',
      border: '2px solid #f59e0b',
      borderRadius: '8px',
      fontWeight: 'bold',
    },
    draggable: true,
  }

  // Create back button using existing design
  const backNode: Node = {
    id: 'back-to-groups',
    type: 'machine',
    position: { x: 0, y: 0 },
    width: 150,
    height: 60,
    data: {
      label: '← Back to Groups',
      subType: 'back-button',
      issues: [],
      marked: false,
      riskScore: 0,
      isFocusNode: false,
      labels: [],
      isBackButton: true,
    },
    style: {
      background: '#fee2e2',
      border: '2px solid #ef4444',
      borderRadius: '8px',
      cursor: 'pointer',
      fontWeight: 'bold',
    },
    draggable: true,
  }

  // Create individual entity nodes using existing machine type and design
  const entityNodes: Node[] = entitiesInGroup.map((entity) => ({
    id: entity.id,
    type: 'machine',
    position: { x: 0, y: 0 },
    width: 160,
    height: 100,
    data: {
      ...entity,
      label: entity.name || entity.id,
      subType: entity.subType || entity.type || 'unknown',
      issues: entity.issues || [],
      marked: false,
      riskScore: entity.riskScore || 0,
      isFocusNode: false,
      labels: entity.labels || [],
      isMachine: true,
    },
    draggable: true,
  }))

  // Create hierarchical edges from group root to back button and entities
  const hierarchicalEdges: Edge[] = [
    {
      id: `group-root-to-back`,
      source: groupRootNode.id,
      target: backNode.id,
      type: 'default',
      style: { stroke: '#ef4444', strokeWidth: 2, strokeDasharray: '5,5' },
      data: { isHierarchical: true },
    },
    ...entityNodes.map((entity) => ({
      id: `group-root-to-${entity.id}`,
      source: groupRootNode.id,
      target: entity.id,
      type: 'default',
      style: { stroke: '#94a3b8', strokeWidth: 1, strokeDasharray: '3,3' },
      data: { isHierarchical: true },
    })),
  ]

  // Filter edges to show only connections between entities in this group
  const entityEdges = originalEdges.filter(edge =>
    groupEntityIds.has(edge.source) && groupEntityIds.has(edge.target)
  )

  // Combine all edges
  const allEdges = [...hierarchicalEdges, ...entityEdges]

  console.log('getZoomedGroupView - result:', {
    totalNodes: entityNodes.length + 2, // +2 for group root and back button
    entityNodes: entityNodes.length,
    hierarchicalEdges: hierarchicalEdges.length,
    entityEdges: entityEdges.length,
    totalEdges: allEdges.length
  })

  return {
    nodes: [groupRootNode, backNode, ...entityNodes],
    edges: allEdges,
  }
}
