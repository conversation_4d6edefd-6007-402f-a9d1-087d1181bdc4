import { useMemo, useEffect } from 'react'
import { Node, Edge } from 'reactflow'
import { useGroupViewStore } from '@/zustandStores/useGroupViewStore'
import { useGetLabels } from '@/firestoreQueries/ndr/labels/hooks/useLabels'
import useGetEntities from '@/firestoreQueries/ndr/entities/hooks/useGetEntities'
import useFetchLabelAssigmentForEntities from '@/firestoreQueries/ndr/entities/queries/hooks/useFetchLabelAssigmentForEntities'
import useFetchLabelsByIds from '@/firestoreQueries/ndr/entities/queries/hooks/useFetchLabelForEntities'
import { GroupingProcessor } from '@/firestoreQueries/ndr/entities/nodeTreeUtils/processors/grouping/GroupingProcessor'
import { groupEntitiesByLabels } from '@/utils/groupViewUtils'

interface UsePortflowGroupingAdapterProps {
  organizationId: string | undefined
  originalNodes: Node[]
  originalEdges: Edge[]
}

/**
 * Adapter hook that integrates grouping with the existing Portflow system
 * This is a simplified version that works with the current data structure
 */
export function usePortflowGroupingAdapter({
  organizationId,
  originalNodes,
  originalEdges,
}: UsePortflowGroupingAdapterProps) {
  const {
    currentView,
    isGrouped,
    zoomedGroup,
    setZoomedGroup,
  } = useGroupViewStore()

  // Get entities and labels for grouping
  const { data: rawEntities = [], isLoading: isEntitiesLoading } = useGetEntities({
    organizationId,
    leanedEntities: true,
    enabled: !!organizationId, // Always fetch entities when org is available
  })

  // Log when entities are fetched
  useEffect(() => {
    if (!isEntitiesLoading && rawEntities.length > 0) {
      console.log('usePortflowGroupingAdapter - fetched raw entities:', rawEntities.length)
      console.log('usePortflowGroupingAdapter - sample raw entity:', rawEntities[0])
    }
  }, [isEntitiesLoading, rawEntities.length])

  // Get entity IDs for label fetching
  const entityIds = useMemo(() => rawEntities.map((entity: any) => entity.id), [rawEntities])

  // Get label assignments for all entities
  const assignedLabels = useFetchLabelAssigmentForEntities({
    organizationId: organizationId!,
    entityIds,
    enabled: !!organizationId && entityIds.length > 0,
  })

  // Get all label IDs that are assigned to entities
  const computedLabelIds = useMemo(() => {
    if (!assignedLabels || !assignedLabels.data) return []

    return entityIds.map((id) => assignedLabels.data?.get(id) ?? []).flat()
  }, [entityIds, assignedLabels])

  // Get the actual label data
  const allLabels = useFetchLabelsByIds({
    organizationId: organizationId!,
    labelIds: computedLabelIds,
    enabled: !!organizationId && assignedLabels.isFetched && computedLabelIds.length > 0,
  })

  // Combine entities with their labels (following the pattern from useFetchNDREntitiesList)
  const entities = useMemo(() => {
    if (!allLabels.data || !rawEntities.length || !assignedLabels.data) {
      console.log('usePortflowGroupingAdapter - missing data for entity-label combination')
      console.log('usePortflowGroupingAdapter - rawEntities:', rawEntities.length)
      console.log('usePortflowGroupingAdapter - assignedLabels.data:', assignedLabels.data ? 'exists' : 'missing')
      console.log('usePortflowGroupingAdapter - allLabels.data:', allLabels.data ? 'exists' : 'missing')
      return []
    }

    const entitiesWithLabels = rawEntities.map((entity: any) => {
      const entityLabelIds = assignedLabels.data.get(entity.id) || []
      const entityLabels = entityLabelIds
        .map((labelId: string) => allLabels.data.get(labelId))
        .filter(Boolean)

      return {
        ...entity,
        labels: entityLabels,
      }
    })

    console.log('usePortflowGroupingAdapter - created entities with labels:', entitiesWithLabels.length)
    if (entitiesWithLabels.length > 0) {
      console.log('usePortflowGroupingAdapter - sample entity:', {
        id: entitiesWithLabels[0].id,
        name: entitiesWithLabels[0].name,
        labelsCount: entitiesWithLabels[0].labels?.length || 0,
        labels: entitiesWithLabels[0].labels?.map((l: any) => ({ key: l.key, values: l.values })) || []
      })
    }

    return entitiesWithLabels
  }, [rawEntities, assignedLabels.data, allLabels.data])

  // Check loading states
  const isLoadingData = isEntitiesLoading || assignedLabels.isLoading || allLabels.isLoading

  // Add effect to trigger re-calculation when view changes
  useEffect(() => {
    if (currentView) {
      console.log('usePortflowGroupingAdapter - view changed:', currentView.name)
      console.log('usePortflowGroupingAdapter - isGrouped:', isGrouped)
      console.log('usePortflowGroupingAdapter - entities available:', entities.length)
      console.log('usePortflowGroupingAdapter - isLoadingData:', isLoadingData)
    }
  }, [currentView, isGrouped, entities.length, isLoadingData])

  const { data: allLabels = [] } = useGetLabels(organizationId)

  // Extract available label keys from entities
  const availableKeys = useMemo(() => {
    if (!entities.length) return []
    
    const keySet = new Set<string>()
    entities.forEach((entity: any) => {
      if (entity.labels) {
        entity.labels.forEach((label: any) => {
          if (label.key) {
            keySet.add(label.key)
          }
        })
      }
    })
    
    return Array.from(keySet)
  }, [entities])

  // TODO: Re-enable automatic key updates after fixing infinite loop
  // For now, keys will need to be updated manually when creating views
  // useEffect(() => {
  //   setAvailableKeys(availableKeys)
  // }, [availableKeys])

  // Create grouped entities using the existing utility function
  const groupedEntities = useMemo(() => {
    if (!isGrouped || !currentView || !entities.length || isLoadingData) {
      console.log('usePortflowGroupingAdapter - skipping grouping:', {
        isGrouped,
        hasCurrentView: !!currentView,
        entitiesLength: entities.length,
        isLoadingData
      })
      return []
    }

    console.log('usePortflowGroupingAdapter - creating grouped entities')
    console.log('usePortflowGroupingAdapter - entities with labels:', entities.length)
    console.log('usePortflowGroupingAdapter - currentView:', {
      name: currentView.name,
      primaryKey: currentView.primaryKey,
      secondaryKey: currentView.secondaryKey
    })

    // Log sample entities to see their structure
    if (entities.length > 0) {
      console.log('usePortflowGroupingAdapter - sample entities:', entities.slice(0, 3).map(e => ({
        id: e.id,
        name: e.name,
        labelsCount: e.labels?.length || 0,
        labels: e.labels?.map((l: any) => ({ key: l.key, values: l.values })) || []
      })))
    }

    // Create entity labels map for the utility function
    const entityLabelsMap = new Map()
    entities.forEach(entity => {
      if (entity.labels && Array.isArray(entity.labels)) {
        entityLabelsMap.set(entity.id, entity.labels)
      }
    })

    console.log('usePortflowGroupingAdapter - entityLabelsMap size:', entityLabelsMap.size)

    try {
      // Use the existing grouping utility
      const grouped = groupEntitiesByLabels(
        entities,
        entityLabelsMap,
        currentView.primaryKey,
        currentView.secondaryKey,
        currentView.additionalKeys
      )

      console.log('usePortflowGroupingAdapter - grouped entities result:', grouped.length)
      if (grouped.length > 0) {
        console.log('usePortflowGroupingAdapter - sample groups:', grouped.slice(0, 2))
      }
      return grouped
    } catch (error) {
      console.error('usePortflowGroupingAdapter - grouping error:', error)
      return []
    }
  }, [isGrouped, currentView, entities, isLoadingData])

  // Transform nodes and edges based on grouping state
  const { nodes, edges } = useMemo(() => {
    // Always ensure we return valid arrays
    const safeOriginalNodes = originalNodes || []
    const safeOriginalEdges = originalEdges || []

    console.log('usePortflowGroupingAdapter - processing nodes/edges')
    console.log('usePortflowGroupingAdapter - isGrouped:', isGrouped)
    console.log('usePortflowGroupingAdapter - currentView:', currentView?.name)
    console.log('usePortflowGroupingAdapter - entities with labels:', entities.length)
    console.log('usePortflowGroupingAdapter - groupedEntities:', groupedEntities.length)

    if (!isGrouped || !currentView) {
      console.log('usePortflowGroupingAdapter - returning original nodes/edges (not grouped)')
      return { nodes: safeOriginalNodes, edges: safeOriginalEdges }
    }

    if (isLoadingData) {
      console.log('usePortflowGroupingAdapter - returning original nodes/edges (still loading data)')
      return { nodes: safeOriginalNodes, edges: safeOriginalEdges }
    }

    if (!entities.length) {
      console.log('usePortflowGroupingAdapter - returning original nodes/edges (no entities with labels)')
      return { nodes: safeOriginalNodes, edges: safeOriginalEdges }
    }

    if (zoomedGroup) {
      console.log('usePortflowGroupingAdapter - showing zoomed group view')
      // Show individual entities within the zoomed group
      return getZoomedGroupView(zoomedGroup, safeOriginalNodes, safeOriginalEdges)
    }

    if (!groupedEntities.length) {
      console.log('usePortflowGroupingAdapter - no grouped entities created, but grouping is enabled')
      console.log('usePortflowGroupingAdapter - returning empty nodes to show only groups')
      // When grouping is enabled but no groups are created, show empty graph
      return { nodes: [], edges: [] }
    }

    console.log('usePortflowGroupingAdapter - creating group nodes from grouped entities')
    console.log('usePortflowGroupingAdapter - groupedEntities sample:', groupedEntities[0])

    // Create group nodes directly from grouped entities (simpler approach)
    const groupNodes: Node[] = groupedEntities.map((group, index) => ({
      id: group.groupId || `group-${index}`,
      type: 'groupNode',
      position: calculateGroupPosition(index, groupedEntities.length),
      data: {
        label: group.label || `Group ${index + 1}`,
        entityCount: group.entities?.length || 0,
        entities: group.entities || [],
        groupKeys: group.groupKeys || {},
        aggregatedLabels: group.aggregatedLabels || {},
        isGroup: true,
      },
      draggable: true,
    }))

    // Create simplified edges between groups
    const groupEdges = createGroupEdges(groupedEntities, safeOriginalEdges)

    console.log('usePortflowGroupingAdapter - final result:', {
      nodes: groupNodes.length,
      edges: groupEdges.length,
      groupNodes: groupNodes.map(n => ({ id: n.id, label: n.data.label }))
    })

    return { nodes: groupNodes, edges: groupEdges }
  }, [isGrouped, currentView, zoomedGroup, originalNodes, originalEdges, entities, groupedEntities])

  // Handle node clicks for grouping functionality
  const handleNodeClick = (nodeId: string, nodeData: any) => {
    if (nodeData?.isBackButton) {
      setZoomedGroup(null)
      return true
    }

    if (nodeData?.isGroup && !zoomedGroup) {
      setZoomedGroup(nodeId)
      return true
    }

    return false
  }

  return {
    nodes: nodes || [],
    edges: edges || [],
    isGrouped,
    currentView,
    zoomedGroup,
    handleNodeClick,
  }
}

// Helper function to create edges between groups
function createGroupEdges(groupedEntities: any[], originalEdges: Edge[]): Edge[] {
  if (!groupedEntities.length || !originalEdges.length) {
    return []
  }

  // Create entity to group mapping
  const entityToGroupMap = new Map<string, string>()
  groupedEntities.forEach(group => {
    group.entities.forEach((entityId: string) => {
      entityToGroupMap.set(entityId, group.groupId)
    })
  })

  // Track connections between groups
  const groupConnections = new Map<string, Set<string>>()

  originalEdges.forEach((edge) => {
    const sourceGroup = entityToGroupMap.get(edge.source)
    const targetGroup = entityToGroupMap.get(edge.target)

    if (sourceGroup && targetGroup && sourceGroup !== targetGroup) {
      const connectionKey = `${sourceGroup}-${targetGroup}`
      if (!groupConnections.has(connectionKey)) {
        groupConnections.set(connectionKey, new Set())
      }
      groupConnections.get(connectionKey)!.add(edge.id)
    }
  })

  // Create group edges
  const groupEdges: Edge[] = []
  groupConnections.forEach((edgeIds, connectionKey) => {
    const [sourceGroup, targetGroup] = connectionKey.split('-')
    groupEdges.push({
      id: connectionKey,
      source: sourceGroup,
      target: targetGroup,
      type: 'groupEdge',
      data: {
        edgeCount: edgeIds.size,
        aggregatedData: {
          totalTraffic: edgeIds.size,
          ports: [],
          protocols: [],
          isDanger: false,
        },
        isGroupEdge: true,
      },
    })
  })

  return groupEdges
}

// Simplified zoomed group view
function getZoomedGroupView(
  zoomedGroupId: string,
  originalNodes: Node[],
  originalEdges: Edge[]
) {
  // Safety check for inputs
  if (!originalNodes || !originalEdges) {
    return { nodes: [], edges: [] }
  }

  // For now, just show all original nodes with a back button
  // In a full implementation, this would filter to show only entities in the group
  
  const backNode: Node = {
    id: 'back-to-groups',
    type: 'default',
    position: { x: 50, y: 50 },
    data: {
      label: '← Back to Groups',
      isBackButton: true,
    },
    style: {
      background: '#f3f4f6',
      border: '2px solid #d1d5db',
      borderRadius: '8px',
      padding: '8px 16px',
      cursor: 'pointer',
    },
  }

  return {
    nodes: [backNode, ...originalNodes],
    edges: originalEdges,
  }
}

// Helper function to calculate group positions
function calculateGroupPosition(index: number, totalGroups: number): { x: number; y: number } {
  const cols = Math.ceil(Math.sqrt(totalGroups))
  const row = Math.floor(index / cols)
  const col = index % cols
  
  const spacing = 350
  const offsetX = 100
  const offsetY = 100
  
  return {
    x: offsetX + col * spacing,
    y: offsetY + row * spacing,
  }
}
