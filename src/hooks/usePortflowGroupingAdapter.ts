import { useMemo, useEffect } from 'react'
import { Node, Edge } from 'reactflow'
import { useGroupViewStore } from '@/zustandStores/useGroupViewStore'
import { useGetLabels } from '@/firestoreQueries/ndr/labels/hooks/useLabels'
import useGetEntities from '@/firestoreQueries/ndr/entities/hooks/useGetEntities'
import useFetchLabelAssigmentForEntities from '@/firestoreQueries/ndr/entities/queries/hooks/useFetchLabelAssigmentForEntities'
import useFetchLabelsByIds from '@/firestoreQueries/ndr/entities/queries/hooks/useFetchLabelForEntities'
import { GroupingProcessor } from '@/firestoreQueries/ndr/entities/nodeTreeUtils/processors/grouping/GroupingProcessor'
import { groupEntitiesByLabels } from '@/utils/groupViewUtils'

interface UsePortflowGroupingAdapterProps {
  organizationId: string | undefined
  originalNodes: Node[]
  originalEdges: Edge[]
}

/**
 * Adapter hook that integrates grouping with the existing Portflow system
 * This is a simplified version that works with the current data structure
 */
export function usePortflowGroupingAdapter({
  organizationId,
  originalNodes,
  originalEdges,
}: UsePortflowGroupingAdapterProps) {
  const {
    currentView,
    isGrouped,
    zoomedGroup,
    setZoomedGroup,
  } = useGroupViewStore()

  // Get entities and labels for grouping
  const { data: rawEntities = [] } = useGetEntities({
    organizationId,
    leanedEntities: true,
    enabled: !!organizationId, // Always fetch entities when org is available
  })

  // Get entity IDs for label fetching
  const entityIds = useMemo(() => rawEntities.map((entity: any) => entity.id), [rawEntities])

  // Get label assignments for all entities
  const { data: labelAssignments } = useFetchLabelAssigmentForEntities({
    organizationId: organizationId!,
    entityIds,
    enabled: !!organizationId && entityIds.length > 0,
  })

  // Get all label IDs that are assigned to entities
  const assignedLabelIds = useMemo(() => {
    if (!labelAssignments) return []
    const labelIds = new Set<string>()
    labelAssignments.forEach((assignments: string[]) => {
      assignments.forEach(labelId => labelIds.add(labelId))
    })
    return Array.from(labelIds)
  }, [labelAssignments])

  // Get the actual label data
  const { data: labelsData } = useFetchLabelsByIds({
    organizationId: organizationId!,
    labelIds: assignedLabelIds,
    enabled: !!organizationId && assignedLabelIds.length > 0,
  })

  // Combine entities with their labels
  const entities = useMemo(() => {
    if (!rawEntities.length || !labelAssignments || !labelsData) {
      console.log('usePortflowGroupingAdapter - missing data for entity-label combination')
      return []
    }

    return rawEntities.map((entity: any) => {
      const entityLabelIds = labelAssignments.get(entity.id) || []
      const entityLabels = entityLabelIds
        .map((labelId: string) => labelsData.get(labelId))
        .filter(Boolean)

      return {
        ...entity,
        labels: entityLabels,
      }
    })
  }, [rawEntities, labelAssignments, labelsData])

  const { data: allLabels = [] } = useGetLabels(organizationId)

  // Extract available label keys from entities
  const availableKeys = useMemo(() => {
    if (!entities.length) return []
    
    const keySet = new Set<string>()
    entities.forEach((entity: any) => {
      if (entity.labels) {
        entity.labels.forEach((label: any) => {
          if (label.key) {
            keySet.add(label.key)
          }
        })
      }
    })
    
    return Array.from(keySet)
  }, [entities])

  // TODO: Re-enable automatic key updates after fixing infinite loop
  // For now, keys will need to be updated manually when creating views
  // useEffect(() => {
  //   setAvailableKeys(availableKeys)
  // }, [availableKeys])

  // Create grouped entities using the existing utility function
  const groupedEntities = useMemo(() => {
    if (!isGrouped || !currentView || !entities.length) {
      return []
    }

    console.log('usePortflowGroupingAdapter - creating grouped entities')
    console.log('usePortflowGroupingAdapter - entities with labels:', entities.length)
    console.log('usePortflowGroupingAdapter - currentView:', currentView)

    // Create entity labels map for the utility function
    const entityLabelsMap = new Map()
    entities.forEach(entity => {
      if (entity.labels && Array.isArray(entity.labels)) {
        entityLabelsMap.set(entity.id, entity.labels)
      }
    })

    console.log('usePortflowGroupingAdapter - entityLabelsMap size:', entityLabelsMap.size)

    // Use the existing grouping utility
    const grouped = groupEntitiesByLabels(
      entities,
      entityLabelsMap,
      currentView.primaryKey,
      currentView.secondaryKey,
      currentView.additionalKeys
    )

    console.log('usePortflowGroupingAdapter - grouped entities:', grouped.length)
    return grouped
  }, [isGrouped, currentView, entities])

  // Transform nodes and edges based on grouping state
  const { nodes, edges } = useMemo(() => {
    // Always ensure we return valid arrays
    const safeOriginalNodes = originalNodes || []
    const safeOriginalEdges = originalEdges || []

    console.log('usePortflowGroupingAdapter - processing nodes/edges')
    console.log('usePortflowGroupingAdapter - isGrouped:', isGrouped)
    console.log('usePortflowGroupingAdapter - groupedEntities:', groupedEntities.length)

    if (!isGrouped || !currentView || !groupedEntities.length) {
      console.log('usePortflowGroupingAdapter - returning original nodes/edges')
      return { nodes: safeOriginalNodes, edges: safeOriginalEdges }
    }

    if (zoomedGroup) {
      console.log('usePortflowGroupingAdapter - showing zoomed group view')
      // Show individual entities within the zoomed group
      return getZoomedGroupView(zoomedGroup, safeOriginalNodes, safeOriginalEdges)
    }

    console.log('usePortflowGroupingAdapter - using GroupingProcessor')
    // Use the existing GroupingProcessor
    const groupingProcessor = new GroupingProcessor(groupedEntities)
    const groupNodes = groupingProcessor.process(entities)

    console.log('usePortflowGroupingAdapter - generated group nodes:', groupNodes.length)

    // Convert SuperNodes to ReactFlow nodes
    const reactFlowNodes: Node[] = groupNodes.map((superNode, index) => ({
      id: superNode.id,
      type: 'groupNode',
      position: calculateGroupPosition(index, groupNodes.length),
      data: {
        ...superNode.data,
        isGroup: true,
      },
      draggable: true,
    }))

    // Create simplified edges between groups
    const groupEdges = createGroupEdges(groupedEntities, safeOriginalEdges)

    console.log('usePortflowGroupingAdapter - final result:', {
      nodes: reactFlowNodes.length,
      edges: groupEdges.length
    })

    return { nodes: reactFlowNodes, edges: groupEdges }
  }, [isGrouped, currentView, zoomedGroup, originalNodes, originalEdges, entities, groupedEntities])

  // Handle node clicks for grouping functionality
  const handleNodeClick = (nodeId: string, nodeData: any) => {
    if (nodeData?.isBackButton) {
      setZoomedGroup(null)
      return true
    }

    if (nodeData?.isGroup && !zoomedGroup) {
      setZoomedGroup(nodeId)
      return true
    }

    return false
  }

  return {
    nodes: nodes || [],
    edges: edges || [],
    isGrouped,
    currentView,
    zoomedGroup,
    handleNodeClick,
  }
}

// Helper function to create edges between groups
function createGroupEdges(groupedEntities: any[], originalEdges: Edge[]): Edge[] {
  if (!groupedEntities.length || !originalEdges.length) {
    return []
  }

  // Create entity to group mapping
  const entityToGroupMap = new Map<string, string>()
  groupedEntities.forEach(group => {
    group.entities.forEach((entityId: string) => {
      entityToGroupMap.set(entityId, group.groupId)
    })
  })

  // Track connections between groups
  const groupConnections = new Map<string, Set<string>>()

  originalEdges.forEach((edge) => {
    const sourceGroup = entityToGroupMap.get(edge.source)
    const targetGroup = entityToGroupMap.get(edge.target)

    if (sourceGroup && targetGroup && sourceGroup !== targetGroup) {
      const connectionKey = `${sourceGroup}-${targetGroup}`
      if (!groupConnections.has(connectionKey)) {
        groupConnections.set(connectionKey, new Set())
      }
      groupConnections.get(connectionKey)!.add(edge.id)
    }
  })

  // Create group edges
  const groupEdges: Edge[] = []
  groupConnections.forEach((edgeIds, connectionKey) => {
    const [sourceGroup, targetGroup] = connectionKey.split('-')
    groupEdges.push({
      id: connectionKey,
      source: sourceGroup,
      target: targetGroup,
      type: 'groupEdge',
      data: {
        edgeCount: edgeIds.size,
        aggregatedData: {
          totalTraffic: edgeIds.size,
          ports: [],
          protocols: [],
          isDanger: false,
        },
        isGroupEdge: true,
      },
    })
  })

  return groupEdges
}

// Simplified zoomed group view
function getZoomedGroupView(
  zoomedGroupId: string,
  originalNodes: Node[],
  originalEdges: Edge[]
) {
  // Safety check for inputs
  if (!originalNodes || !originalEdges) {
    return { nodes: [], edges: [] }
  }

  // For now, just show all original nodes with a back button
  // In a full implementation, this would filter to show only entities in the group
  
  const backNode: Node = {
    id: 'back-to-groups',
    type: 'default',
    position: { x: 50, y: 50 },
    data: {
      label: '← Back to Groups',
      isBackButton: true,
    },
    style: {
      background: '#f3f4f6',
      border: '2px solid #d1d5db',
      borderRadius: '8px',
      padding: '8px 16px',
      cursor: 'pointer',
    },
  }

  return {
    nodes: [backNode, ...originalNodes],
    edges: originalEdges,
  }
}

// Helper function to calculate group positions
function calculateGroupPosition(index: number, totalGroups: number): { x: number; y: number } {
  const cols = Math.ceil(Math.sqrt(totalGroups))
  const row = Math.floor(index / cols)
  const col = index % cols
  
  const spacing = 350
  const offsetX = 100
  const offsetY = 100
  
  return {
    x: offsetX + col * spacing,
    y: offsetY + row * spacing,
  }
}
