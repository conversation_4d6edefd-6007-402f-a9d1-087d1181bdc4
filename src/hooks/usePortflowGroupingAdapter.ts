import { useMemo, useEffect } from 'react'
import { Node, Edge } from 'reactflow'
import { useGroupViewStore } from '@/zustandStores/useGroupViewStore'
import { useGetLabels } from '@/firestoreQueries/ndr/labels/hooks/useLabels'
import useGetEntities from '@/firestoreQueries/ndr/entities/hooks/useGetEntities'

interface UsePortflowGroupingAdapterProps {
  organizationId: string | undefined
  originalNodes: Node[]
  originalEdges: Edge[]
}

/**
 * Adapter hook that integrates grouping with the existing Portflow system
 * This is a simplified version that works with the current data structure
 */
export function usePortflowGroupingAdapter({
  organizationId,
  originalNodes,
  originalEdges,
}: UsePortflowGroupingAdapterProps) {
  const {
    currentView,
    isGrouped,
    zoomedGroup,
    setAvailableKeys,
    setZoomedGroup,
  } = useGroupViewStore()

  // Get entities and labels for grouping
  const { data: entities = [] } = useGetEntities({
    organizationId,
    leanedEntities: true,
    enabled: !!organizationId && isGrouped,
  })

  const { data: allLabels = [] } = useGetLabels(organizationId)

  // Extract available label keys from entities
  const availableKeys = useMemo(() => {
    if (!entities.length) return []
    
    const keySet = new Set<string>()
    entities.forEach((entity: any) => {
      if (entity.labels) {
        entity.labels.forEach((label: any) => {
          if (label.key) {
            keySet.add(label.key)
          }
        })
      }
    })
    
    return Array.from(keySet)
  }, [entities])

  // Update available keys in store
  useEffect(() => {
    setAvailableKeys(availableKeys)
  }, [availableKeys, setAvailableKeys])

  // Transform nodes and edges based on grouping state
  const { nodes, edges } = useMemo(() => {
    if (!isGrouped || !currentView) {
      return { nodes: originalNodes, edges: originalEdges }
    }

    if (zoomedGroup) {
      // Show individual entities within the zoomed group
      return getZoomedGroupView(zoomedGroup, originalNodes, originalEdges)
    }

    // Show grouped view - for now, return original nodes with group indicators
    return getSimpleGroupedView(originalNodes, originalEdges, entities, currentView)
  }, [isGrouped, currentView, zoomedGroup, originalNodes, originalEdges, entities])

  // Handle node clicks for grouping functionality
  const handleNodeClick = (nodeId: string, nodeData: any) => {
    if (nodeData?.isBackButton) {
      setZoomedGroup(null)
      return true
    }

    if (nodeData?.isGroup && !zoomedGroup) {
      setZoomedGroup(nodeId)
      return true
    }

    return false
  }

  return {
    nodes,
    edges,
    isGrouped,
    currentView,
    zoomedGroup,
    availableKeys,
    handleNodeClick,
  }
}

// Simplified grouped view that works with existing node structure
function getSimpleGroupedView(
  originalNodes: Node[],
  originalEdges: Edge[],
  entities: any[],
  currentView: any
) {
  if (!entities.length || !currentView) {
    return { nodes: originalNodes, edges: originalEdges }
  }

  // Create a map of entity ID to group
  const entityToGroupMap = new Map<string, string>()
  const groups = new Map<string, { entities: string[], label: string }>()

  entities.forEach((entity: any) => {
    if (!entity.labels) return

    // Find the primary key value for this entity
    const primaryLabel = entity.labels.find((label: any) => label.key === currentView.primaryKey)
    if (!primaryLabel || !primaryLabel.values || primaryLabel.values.length === 0) return

    const groupKey = primaryLabel.values[0]
    const groupId = `group-${currentView.primaryKey}-${groupKey}`

    entityToGroupMap.set(entity.id, groupId)

    if (!groups.has(groupId)) {
      groups.set(groupId, {
        entities: [],
        label: `${currentView.primaryKey}: ${groupKey}`,
      })
    }

    groups.get(groupId)!.entities.push(entity.id)
  })

  // Create group nodes
  const groupNodes: Node[] = Array.from(groups.entries()).map(([groupId, group], index) => ({
    id: groupId,
    type: 'groupNode',
    position: calculateGroupPosition(index, groups.size),
    data: {
      label: group.label,
      entityCount: group.entities.length,
      entities: group.entities,
      groupKeys: { [currentView.primaryKey]: group.label.split(': ')[1] },
      aggregatedLabels: {},
      isGroup: true,
    },
    draggable: true,
  }))

  // Create simplified group edges based on original edges
  const groupEdges: Edge[] = []
  const groupConnections = new Map<string, Set<string>>()

  originalEdges.forEach((edge) => {
    const sourceGroup = entityToGroupMap.get(edge.source)
    const targetGroup = entityToGroupMap.get(edge.target)

    if (sourceGroup && targetGroup && sourceGroup !== targetGroup) {
      const connectionKey = `${sourceGroup}-${targetGroup}`
      if (!groupConnections.has(connectionKey)) {
        groupConnections.set(connectionKey, new Set())
      }
      groupConnections.get(connectionKey)!.add(edge.id)
    }
  })

  groupConnections.forEach((edgeIds, connectionKey) => {
    const [sourceGroup, targetGroup] = connectionKey.split('-')
    groupEdges.push({
      id: connectionKey,
      source: sourceGroup,
      target: targetGroup,
      type: 'groupEdge',
      data: {
        edgeCount: edgeIds.size,
        aggregatedData: {
          totalTraffic: edgeIds.size,
          ports: [],
          protocols: [],
          isDanger: false,
        },
        isGroupEdge: true,
      },
    })
  })

  return { nodes: groupNodes, edges: groupEdges }
}

// Simplified zoomed group view
function getZoomedGroupView(
  zoomedGroupId: string,
  originalNodes: Node[],
  originalEdges: Edge[]
) {
  // For now, just show all original nodes with a back button
  // In a full implementation, this would filter to show only entities in the group
  
  const backNode: Node = {
    id: 'back-to-groups',
    type: 'default',
    position: { x: 50, y: 50 },
    data: {
      label: '← Back to Groups',
      isBackButton: true,
    },
    style: {
      background: '#f3f4f6',
      border: '2px solid #d1d5db',
      borderRadius: '8px',
      padding: '8px 16px',
      cursor: 'pointer',
    },
  }

  return {
    nodes: [backNode, ...originalNodes],
    edges: originalEdges,
  }
}

// Helper function to calculate group positions
function calculateGroupPosition(index: number, totalGroups: number): { x: number; y: number } {
  const cols = Math.ceil(Math.sqrt(totalGroups))
  const row = Math.floor(index / cols)
  const col = index % cols
  
  const spacing = 350
  const offsetX = 100
  const offsetY = 100
  
  return {
    x: offsetX + col * spacing,
    y: offsetY + row * spacing,
  }
}
