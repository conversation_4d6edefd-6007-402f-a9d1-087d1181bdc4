import { useMemo } from 'react'
import { Node, Edge } from 'reactflow'
import { useGroupViewStore } from '@/zustandStores/useGroupViewStore'
import { useEntityGrouping } from './useEntityGrouping'
import { EntityTypeWithId } from '@/firestoreQueries/ndr/entities/customEntitiesTypes'
import { Label } from '@/firestoreQueries/ndr/labels/hooks/useLabels'

interface UseGroupedPortflowLayoutProps {
  organizationId: string | undefined
  entities: EntityTypeWithId[]
  trafficPatterns: any[]
  entityLabels: Map<string, Label[]>
  originalNodes: Node[]
  originalEdges: Edge[]
}

export function useGroupedPortflowLayout({
  organizationId,
  entities,
  trafficPatterns,
  entityLabels,
  originalNodes,
  originalEdges,
}: UseGroupedPortflowLayoutProps) {
  const { 
    isGrouped, 
    zoomedGroup, 
    currentView 
  } = useGroupViewStore()

  const {
    groupedEntities,
    groupedEdges,
    entityToGroupMap,
  } = useEntityGrouping({
    organizationId,
    entities: entities || [],
    trafficPatterns: trafficPatterns || [],
    entityLabels: entityLabels || new Map(),
  })

  // Transform grouped entities and edges into ReactFlow nodes and edges
  const { nodes, edges } = useMemo(() => {
    if (!isGrouped || !currentView || !originalNodes || !originalEdges) {
      return { nodes: originalNodes || [], edges: originalEdges || [] }
    }

    if (zoomedGroup) {
      // Show individual entities within the zoomed group
      return getZoomedGroupView(zoomedGroup, entities || [], originalNodes, originalEdges, entityToGroupMap)
    }

    // Show grouped view
    return getGroupedView(groupedEntities, groupedEdges)
  }, [
    isGrouped,
    currentView,
    zoomedGroup,
    groupedEntities,
    groupedEdges,
    originalNodes,
    originalEdges,
    entities,
    entityToGroupMap,
  ])

  return { nodes, edges }
}

function getGroupedView(groupedEntities: any[], groupedEdges: any[]) {
  const nodes: Node[] = groupedEntities.map((group, index) => ({
    id: group.groupId,
    type: 'groupNode',
    position: calculateGroupPosition(index, groupedEntities.length),
    data: {
      label: group.groupName,
      entityCount: group.entityCount,
      entities: group.entities,
      groupKeys: group.groupKeys,
      aggregatedLabels: group.aggregatedLabels,
      isGroup: true,
    },
    draggable: true,
  }))

  const edges: Edge[] = groupedEdges.map((edge) => ({
    id: edge.id,
    source: edge.sourceGroupId,
    target: edge.targetGroupId,
    type: 'groupEdge',
    data: {
      edgeCount: edge.edgeCount,
      aggregatedData: edge.aggregatedData,
      isGroupEdge: true,
    },
    animated: edge.aggregatedData.isDanger,
  }))

  return { nodes, edges }
}

function getZoomedGroupView(
  zoomedGroupId: string,
  entities: EntityTypeWithId[],
  originalNodes: Node[],
  originalEdges: Edge[],
  entityToGroupMap: Map<string, string>
) {
  // Get entities that belong to the zoomed group
  const groupEntityIds = new Set<string>()
  entityToGroupMap.forEach((groupId, entityId) => {
    if (groupId === zoomedGroupId) {
      groupEntityIds.add(entityId)
    }
  })

  // Filter original nodes to show only entities in the zoomed group
  const filteredNodes = originalNodes.filter(node => 
    groupEntityIds.has(node.id)
  )

  // Filter original edges to show only edges between entities in the zoomed group
  const filteredEdges = originalEdges.filter(edge => 
    groupEntityIds.has(edge.source) && groupEntityIds.has(edge.target)
  )

  // Add a "back" node to return to grouped view
  const backNode: Node = {
    id: 'back-to-groups',
    type: 'default',
    position: { x: 50, y: 50 },
    data: {
      label: '← Back to Groups',
      isBackButton: true,
    },
    style: {
      background: '#f3f4f6',
      border: '2px solid #d1d5db',
      borderRadius: '8px',
      padding: '8px 16px',
      cursor: 'pointer',
    },
  }

  return {
    nodes: [backNode, ...filteredNodes],
    edges: filteredEdges,
  }
}

function calculateGroupPosition(index: number, totalGroups: number): { x: number; y: number } {
  // Simple grid layout for groups
  const cols = Math.ceil(Math.sqrt(totalGroups))
  const row = Math.floor(index / cols)
  const col = index % cols
  
  const spacing = 350 // Space between group nodes
  const offsetX = 100
  const offsetY = 100
  
  return {
    x: offsetX + col * spacing,
    y: offsetY + row * spacing,
  }
}

// Hook to handle node clicks in grouped view
export function useGroupedNodeClick() {
  const { setZoomedGroup, zoomedGroup } = useGroupViewStore()

  return (nodeId: string, nodeData: any) => {
    if (nodeData?.isBackButton) {
      // Return to grouped view
      setZoomedGroup(null)
      return
    }

    if (nodeData?.isGroup && !zoomedGroup) {
      // Zoom into group
      setZoomedGroup(nodeId)
      return
    }

    // Handle regular node clicks (entity modals, etc.)
    // This would integrate with existing node click handlers
  }
}
