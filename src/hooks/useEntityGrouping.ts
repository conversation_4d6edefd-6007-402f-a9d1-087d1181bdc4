import { useMemo } from 'react'
import { useQuery } from '@tanstack/react-query'
import { useGroupViewStore } from '@/zustandStores/useGroupViewStore'
import { useGetLabels } from '@/firestoreQueries/ndr/labels/hooks/useLabels'
import { 
  getAvailableLabelKeys, 
  groupEntitiesByLabels, 
  aggregateEdgesBetweenGroups, 
  createEntityToGroupMap 
} from '@/utils/groupViewUtils'
import { EntityTypeWithId } from '@/firestoreQueries/ndr/entities/customEntitiesTypes'
import { Label } from '@/firestoreQueries/ndr/labels/hooks/useLabels'

interface UseEntityGroupingProps {
  organizationId: string | undefined
  entities: EntityTypeWithId[]
  trafficPatterns: any[] // Replace with proper type
  entityLabels: Map<string, Label[]>
}

export function useEntityGrouping({
  organizationId,
  entities,
  trafficPatterns,
  entityLabels,
}: UseEntityGroupingProps) {
  const {
    currentView,
    isGrouped,
    setAvailableKeys,
    setGroupedEntities,
    setGroupedEdges,
  } = useGroupViewStore()

  // Get all available labels to extract keys
  const { data: allLabels = [] } = useGetLabels(organizationId)

  // Calculate available label keys
  const availableKeys = useMemo(() => {
    if (!entities.length || !entityLabels.size) return []
    return getAvailableLabelKeys(entities, entityLabels)
  }, [entities, entityLabels])

  // Update available keys in store when they change
  useMemo(() => {
    setAvailableKeys(availableKeys.map(k => k.key))
  }, [availableKeys, setAvailableKeys])

  // Group entities when current view changes
  const groupingResult = useMemo(() => {
    if (!currentView || !isGrouped || !entities.length || !entityLabels.size) {
      return {
        groupedEntities: [],
        groupedEdges: [],
        entityToGroupMap: new Map(),
      }
    }

    const groupedEntities = groupEntitiesByLabels(
      entities,
      entityLabels,
      currentView.primaryKey,
      currentView.secondaryKey,
      currentView.additionalKeys
    )

    const entityToGroupMap = createEntityToGroupMap(groupedEntities)
    
    const groupedEdges = aggregateEdgesBetweenGroups(
      trafficPatterns,
      entityToGroupMap
    )

    return {
      groupedEntities,
      groupedEdges,
      entityToGroupMap,
    }
  }, [currentView, isGrouped, entities, entityLabels, trafficPatterns])

  // Update store when grouping result changes
  useMemo(() => {
    setGroupedEntities(groupingResult.groupedEntities)
    setGroupedEdges(groupingResult.groupedEdges)
  }, [groupingResult, setGroupedEntities, setGroupedEdges])

  return {
    availableKeys,
    groupedEntities: groupingResult.groupedEntities,
    groupedEdges: groupingResult.groupedEdges,
    entityToGroupMap: groupingResult.entityToGroupMap,
    isGrouped,
    currentView,
  }
}

// Hook to get entity labels for all entities
export function useEntityLabelsMap(organizationId: string | undefined, entities: EntityTypeWithId[]) {
  return useQuery({
    queryKey: ['entityLabelsMap', organizationId, entities.map(e => e.id)],
    queryFn: async () => {
      if (!organizationId || !entities.length) return new Map()
      
      // This would need to be implemented to fetch labels for all entities
      // For now, return empty map - this should be replaced with actual implementation
      const labelMap = new Map<string, Label[]>()
      
      // TODO: Implement batch fetching of entity labels
      // This could be done by:
      // 1. Fetching all label assignments for the organization
      // 2. Fetching all labels for the organization
      // 3. Creating a map of entityId -> Label[]
      
      return labelMap
    },
    enabled: !!organizationId && entities.length > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

// Hook to apply grouping to existing graph data
export function useApplyGrouping(
  nodes: any[], // Replace with proper ReactFlow node type
  edges: any[], // Replace with proper ReactFlow edge type
  groupedEntities: any[],
  groupedEdges: any[],
  isGrouped: boolean
) {
  return useMemo(() => {
    if (!isGrouped || !groupedEntities.length) {
      return { nodes, edges }
    }

    // Transform grouped entities into graph nodes
    const groupNodes = groupedEntities.map(group => ({
      id: group.groupId,
      type: 'groupNode',
      position: { x: 0, y: 0 }, // Will be calculated by layout algorithm
      data: {
        label: group.groupName,
        entityCount: group.entityCount,
        entities: group.entities,
        groupKeys: group.groupKeys,
        aggregatedLabels: group.aggregatedLabels,
        isGroup: true,
      },
    }))

    // Transform grouped edges into graph edges
    const groupEdges = groupedEdges.map(edge => ({
      id: edge.id,
      source: edge.sourceGroupId,
      target: edge.targetGroupId,
      type: 'groupEdge',
      data: {
        edgeCount: edge.edgeCount,
        aggregatedData: edge.aggregatedData,
        isGroupEdge: true,
      },
    }))

    return {
      nodes: groupNodes,
      edges: groupEdges,
    }
  }, [nodes, edges, groupedEntities, groupedEdges, isGrouped])
}
