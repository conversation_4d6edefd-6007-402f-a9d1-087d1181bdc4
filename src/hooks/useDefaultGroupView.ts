import { useEffect } from 'react'
import { useUser, useOrganization } from '@clerk/clerk-react'
import { useGetDefaultGroupView } from '@/firestoreQueries/ndr/groupViews/hooks/useGroupViews'
import { useGroupViewStore } from '@/zustandStores/useGroupViewStore'
import {
  getGroupViewPreferences,
  updateCurrentViewPreference,
  shouldAutoApplyGroupView,
  migrateGroupViewPreferences
} from '@/utils/groupViewPreferences'

/**
 * Hook to automatically load and apply the user's default group view
 * when the application starts or when the user changes
 */
export function useDefaultGroupView() {
  const { user } = useUser()
  const { organization } = useOrganization()
  const organizationId = organization?.id
  const userId = user?.id

  const { 
    setCurrentView, 
    setIsGrouped, 
    currentView,
    isGrouped 
  } = useGroupViewStore()

  const { 
    data: defaultGroupView, 
    isLoading, 
    error 
  } = useGetDefaultGroupView(organizationId, userId)

  // Migrate old preferences on mount
  useEffect(() => {
    if (organizationId && userId) {
      migrateGroupViewPreferences(organizationId, userId)
    }
  }, [organizationId, userId])

  // Apply default group view when it's loaded and should be auto-applied
  useEffect(() => {
    if (defaultGroupView && organizationId && userId) {
      const shouldApply = shouldAutoApplyGroupView(organizationId, userId, defaultGroupView)

      if (shouldApply && !currentView && !isGrouped) {
        setCurrentView(defaultGroupView)
        setIsGrouped(true)
        updateCurrentViewPreference(organizationId, userId, defaultGroupView.id, true)
      }
    }
  }, [defaultGroupView, currentView, isGrouped, setCurrentView, setIsGrouped, organizationId, userId])

  return {
    defaultGroupView,
    isLoading,
    error,
    hasDefaultView: !!defaultGroupView,
  }
}

/**
 * Hook to manage user preferences for group views
 */
export function useGroupViewPreferences() {
  const { user } = useUser()
  const { organization } = useOrganization()
  const organizationId = organization?.id
  const userId = user?.id

  const { 
    currentView,
    isGrouped,
    resetGrouping,
  } = useGroupViewStore()

  // Save current state to preferences when it changes
  useEffect(() => {
    if (userId && organizationId && (currentView || isGrouped !== undefined)) {
      updateCurrentViewPreference(organizationId, userId, currentView?.id || null, isGrouped)
    }
  }, [currentView, isGrouped, userId, organizationId])

  const clearPreferences = () => {
    if (userId && organizationId) {
      const { clearGroupViewPreferences } = require('@/utils/groupViewPreferences')
      clearGroupViewPreferences(organizationId, userId)
    }
    resetGrouping()
  }

  return {
    clearPreferences,
  }
}

/**
 * Hook to handle group view initialization and cleanup
 */
export function useGroupViewInitialization() {
  const defaultGroupView = useDefaultGroupView()
  const preferences = useGroupViewPreferences()

  // Cleanup when user logs out or organization changes
  useEffect(() => {
    return () => {
      // Cleanup function - could be used for saving state before unmount
    }
  }, [])

  return {
    ...defaultGroupView,
    ...preferences,
  }
}
