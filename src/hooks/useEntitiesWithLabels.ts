import { useMemo, useEffect } from 'react'
import { useQuery } from '@tanstack/react-query'
import useGetEntities from '@/firestoreQueries/ndr/entities/hooks/useGetEntities'
import useFetchLabelAssigmentForEntities from '@/firestoreQueries/ndr/entities/queries/hooks/useFetchLabelAssigmentForEntities'
import useFetchLabelsByIds from '@/firestoreQueries/ndr/entities/queries/hooks/useFetchLabelForEntities'
import { ndrQueryKeyStore } from '@/configs/reactQueryKeyStore'

interface EntityWithLabels {
  id: string
  name: string
  labels: Array<{
    id: string
    key: string
    values: string[]
    [key: string]: any
  }>
  [key: string]: any
}

interface UseEntitiesWithLabelsArgs {
  organizationId: string | undefined
  enabled?: boolean
}

/**
 * Hook to fetch all entities with their associated labels
 * This combines entities, label assignments, and label data into a single result
 */
export function useEntitiesWithLabels({ 
  organizationId, 
  enabled = true 
}: UseEntitiesWithLabelsArgs) {
  
  // Step 1: Get all entities
  const entitiesQuery = useGetEntities({
    organizationId: organizationId || '',
    leanedEntities: true,
    enabled: enabled && !!organizationId,
  })

  // Debug logging for entities
  useEffect(() => {
    if (entitiesQuery.data) {
      console.log('useEntitiesWithLabels - fetched entities:', entitiesQuery.data.length)
      if (entitiesQuery.data.length > 0) {
        console.log('useEntitiesWithLabels - sample entity:', entitiesQuery.data[0])
      }
    }
  }, [entitiesQuery.data])

  // Step 2: Get entity IDs for label fetching
  const entityIds = useMemo(() => {
    return entitiesQuery.data?.map((entity: any) => entity.id) || []
  }, [entitiesQuery.data])

  // Step 3: Get label assignments for all entities
  const labelAssignmentsQuery = useFetchLabelAssigmentForEntities({
    organizationId: organizationId || '',
    entityIds,
    enabled: enabled && !!organizationId && entityIds.length > 0,
  })

  // Step 4: Get all unique label IDs that are assigned to entities
  const labelIds = useMemo(() => {
    if (!labelAssignmentsQuery.data) return []
    
    const allLabelIds = new Set<string>()
    entityIds.forEach(entityId => {
      const entityLabelIds = labelAssignmentsQuery.data.get(entityId) || []
      entityLabelIds.forEach(labelId => allLabelIds.add(labelId))
    })
    
    return Array.from(allLabelIds)
  }, [entityIds, labelAssignmentsQuery.data])

  // Step 5: Get the actual label data
  const labelsQuery = useFetchLabelsByIds({
    organizationId: organizationId || '',
    labelIds,
    enabled: enabled && !!organizationId && labelAssignmentsQuery.isFetched && labelIds.length > 0,
  })

  // Step 6: Combine everything into entities with labels
  const entitiesWithLabels = useMemo(() => {
    if (!entitiesQuery.data || !labelAssignmentsQuery.data || !labelsQuery.data) {
      console.log('useEntitiesWithLabels - missing data:', {
        entities: !!entitiesQuery.data,
        assignments: !!labelAssignmentsQuery.data,
        labels: !!labelsQuery.data
      })
      return []
    }

    const result: EntityWithLabels[] = entitiesQuery.data.map((entity: any) => {
      // Get label IDs for this entity
      const entityLabelIds = labelAssignmentsQuery.data.get(entity.id) || []
      
      // Get the actual label objects
      const entityLabels = entityLabelIds
        .map(labelId => labelsQuery.data.get(labelId))
        .filter(Boolean) // Remove any null/undefined labels

      return {
        ...entity,
        labels: entityLabels,
      }
    })

    console.log('useEntitiesWithLabels - combined result:', {
      totalEntities: result.length,
      entitiesWithLabels: result.filter(e => e.labels.length > 0).length,
      sampleEntity: result[0] ? {
        id: result[0].id,
        name: result[0].name,
        labelsCount: result[0].labels.length,
        sampleLabels: result[0].labels.slice(0, 2).map(l => ({ key: l.key, values: l.values }))
      } : null
    })

    return result
  }, [entitiesQuery.data, labelAssignmentsQuery.data, labelsQuery.data])

  // Calculate loading state
  const isLoading = entitiesQuery.isLoading || 
                   labelAssignmentsQuery.isLoading || 
                   labelsQuery.isLoading

  // Calculate error state
  const error = entitiesQuery.error || 
               labelAssignmentsQuery.error || 
               labelsQuery.error

  // Calculate if data is ready
  const isDataReady = !isLoading && 
                     !!entitiesQuery.data && 
                     !!labelAssignmentsQuery.data && 
                     !!labelsQuery.data

  return {
    data: entitiesWithLabels,
    isLoading,
    error,
    isDataReady,
    // Individual query states for debugging
    queries: {
      entities: entitiesQuery,
      labelAssignments: labelAssignmentsQuery,
      labels: labelsQuery,
    }
  }
}

export default useEntitiesWithLabels
