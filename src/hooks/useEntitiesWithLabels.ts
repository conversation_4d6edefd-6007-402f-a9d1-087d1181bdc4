// Removed unused imports
import { useQuery } from '@tanstack/react-query'
import { db } from '@/configs/firebase'
import { ndrPathStore } from '@/firestoreQueries/utils/pathStore'
import { collection, getDocs, query, where } from 'firebase/firestore'
import { ndrQueryKeyStore } from '@/configs/reactQueryKeyStore'
import { EntityTypeWithId } from '@/firestoreQueries/ndr/entities/customEntitiesTypes'

interface EntityWithLabels {
  id: string
  name: string
  labels: Array<{
    id: string
    key: string
    values: string[]
    [key: string]: any
  }>
  [key: string]: any
}

interface UseEntitiesWithLabelsArgs {
  organizationId: string | undefined
  enabled?: boolean
}

// Direct Firestore fetching functions
async function fetchAllEntities(organizationId: string): Promise<EntityTypeWithId[]> {
  console.log('fetchAllEntities - starting for org:', organizationId)

  const entitiesPath = ndrPathStore.entities({ organizationId })
  const entitiesRef = collection(db, entitiesPath)
  const entitiesQuery = query(entitiesRef, where('isActive', '==', true))
  const entitiesSnapshot = await getDocs(entitiesQuery)

  const entities = entitiesSnapshot.docs.map(doc => ({
    id: doc.id,
    ...doc.data(),
  })) as EntityTypeWithId[]

  console.log('fetchAllEntities - fetched entities:', entities.length)
  return entities
}

async function fetchAllLabelAssignments(organizationId: string): Promise<Map<string, string[]>> {
  console.log('fetchAllLabelAssignments - starting for org:', organizationId)

  const assignmentsPath = ndrPathStore.labelAssignments({ organizationId })
  const assignmentsRef = collection(db, assignmentsPath)
  const assignmentsSnapshot = await getDocs(assignmentsRef)

  const assignmentsMap = new Map<string, string[]>()
  assignmentsSnapshot.docs.forEach(doc => {
    const data = doc.data()
    if (data.entityId && data.labelIds) {
      assignmentsMap.set(data.entityId, data.labelIds)
    }
  })

  console.log('fetchAllLabelAssignments - fetched assignments for entities:', assignmentsMap.size)
  return assignmentsMap
}

async function fetchAllLabels(organizationId: string): Promise<Map<string, any>> {
  console.log('fetchAllLabels - starting for org:', organizationId)

  const labelsPath = ndrPathStore.labels({ organizationId })
  const labelsRef = collection(db, labelsPath)
  const labelsSnapshot = await getDocs(labelsRef)

  const labelsMap = new Map<string, any>()
  labelsSnapshot.docs.forEach(doc => {
    labelsMap.set(doc.id, {
      id: doc.id,
      ...doc.data(),
    })
  })

  console.log('fetchAllLabels - fetched labels:', labelsMap.size)
  return labelsMap
}

async function fetchEntitiesWithLabels(organizationId: string): Promise<EntityWithLabels[]> {
  console.log('fetchEntitiesWithLabels - starting comprehensive fetch for org:', organizationId)

  try {
    // Fetch all data in parallel
    const [entities, labelAssignments, labels] = await Promise.all([
      fetchAllEntities(organizationId),
      fetchAllLabelAssignments(organizationId),
      fetchAllLabels(organizationId),
    ])

    console.log('fetchEntitiesWithLabels - all data fetched:', {
      entities: entities.length,
      assignments: labelAssignments.size,
      labels: labels.size
    })

    // Combine entities with their labels
    const entitiesWithLabels: EntityWithLabels[] = entities.map(entity => {
      const entityLabelIds = labelAssignments.get(entity.id) || []
      const entityLabels = entityLabelIds
        .map(labelId => labels.get(labelId))
        .filter(Boolean)

      return {
        ...entity,
        labels: entityLabels,
      }
    })

    const entitiesWithValidLabels = entitiesWithLabels.filter(e => e.labels.length > 0)

    console.log('fetchEntitiesWithLabels - final result:', {
      totalEntities: entitiesWithLabels.length,
      entitiesWithLabels: entitiesWithValidLabels.length,
      sampleEntity: entitiesWithValidLabels[0] ? {
        id: entitiesWithValidLabels[0].id,
        name: entitiesWithValidLabels[0].name,
        labelsCount: entitiesWithValidLabels[0].labels.length,
        sampleLabels: entitiesWithValidLabels[0].labels.slice(0, 2).map(l => ({ key: l.key, values: l.values }))
      } : null
    })

    return entitiesWithLabels

  } catch (error) {
    console.error('fetchEntitiesWithLabels - error:', error)
    throw error
  }
}

/**
 * Hook to fetch all entities with their associated labels directly from Firestore
 * This bypasses existing hooks and fetches everything in one coordinated operation
 */
export function useEntitiesWithLabels({
  organizationId,
  enabled = true
}: UseEntitiesWithLabelsArgs) {

  return useQuery({
    queryKey: [...ndrQueryKeyStore.dashboardEntities({ organizationId: organizationId! }), 'withLabels'],
    queryFn: () => fetchEntitiesWithLabels(organizationId!),
    enabled: enabled && !!organizationId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  })
}

export default useEntitiesWithLabels
