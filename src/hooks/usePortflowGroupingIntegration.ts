import { useMemo, useEffect } from 'react'
import { Node, Edge } from 'reactflow'
import { useGroupViewStore } from '@/zustandStores/useGroupViewStore'
import { useGetLabels } from '@/firestoreQueries/ndr/labels/hooks/useLabels'
import { 
  getAvailableLabelKeys, 
  groupEntitiesByLabels, 
  aggregateEdgesBetweenGroups, 
  createEntityToGroupMap 
} from '@/utils/groupViewUtils'

interface UsePortflowGroupingIntegrationProps {
  organizationId: string | undefined
  originalNodes: Node[]
  originalEdges: Edge[]
  entities?: any[] // This should be the actual entities from the system
  trafficPatterns?: any[] // This should be the actual traffic patterns
}

/**
 * Hook that integrates grouping functionality with the existing Portflow system
 * This hook is designed to work with the existing data flow and layout system
 */
export function usePortflowGroupingIntegration({
  organizationId,
  originalNodes,
  originalEdges,
  entities = [],
  trafficPatterns = [],
}: UsePortflowGroupingIntegrationProps) {
  const {
    currentView,
    isGrouped,
    zoomedGroup,
    setAvailableKeys,
    setGroupedEntities,
    setGroupedEdges,
    setZoomedGroup,
  } = useGroupViewStore()

  // Get all labels for extracting available keys
  const { data: allLabels = [] } = useGetLabels(organizationId)

  // Create a map of entity labels (this would need to be implemented properly)
  const entityLabelsMap = useMemo(() => {
    const map = new Map()
    // TODO: This needs to be implemented to fetch actual entity labels
    // For now, return empty map to prevent errors
    return map
  }, [entities, allLabels])

  // Calculate available label keys
  const availableKeys = useMemo(() => {
    if (!entities.length || !entityLabelsMap.size) return []
    return getAvailableLabelKeys(entities, entityLabelsMap)
  }, [entities, entityLabelsMap])

  // Update available keys in store
  useEffect(() => {
    setAvailableKeys(availableKeys.map(k => k.key))
  }, [availableKeys, setAvailableKeys])

  // Group entities when current view changes
  const groupingResult = useMemo(() => {
    if (!currentView || !isGrouped || !entities.length || !entityLabelsMap.size) {
      return {
        groupedEntities: [],
        groupedEdges: [],
        entityToGroupMap: new Map(),
      }
    }

    const groupedEntities = groupEntitiesByLabels(
      entities,
      entityLabelsMap,
      currentView.primaryKey,
      currentView.secondaryKey,
      currentView.additionalKeys
    )

    const entityToGroupMap = createEntityToGroupMap(groupedEntities)
    
    const groupedEdges = aggregateEdgesBetweenGroups(
      trafficPatterns,
      entityToGroupMap
    )

    return {
      groupedEntities,
      groupedEdges,
      entityToGroupMap,
    }
  }, [currentView, isGrouped, entities, entityLabelsMap, trafficPatterns])

  // Update store when grouping result changes
  useEffect(() => {
    setGroupedEntities(groupingResult.groupedEntities)
    setGroupedEdges(groupingResult.groupedEdges)
  }, [groupingResult.groupedEntities, groupingResult.groupedEdges, setGroupedEntities, setGroupedEdges])

  // Transform nodes and edges based on grouping state
  const { nodes, edges } = useMemo(() => {
    if (!isGrouped || !currentView) {
      return { nodes: originalNodes, edges: originalEdges }
    }

    if (zoomedGroup) {
      // Show individual entities within the zoomed group
      return getZoomedGroupView(
        zoomedGroup,
        originalNodes,
        originalEdges,
        groupingResult.entityToGroupMap
      )
    }

    // Show grouped view
    return getGroupedView(groupingResult.groupedEntities, groupingResult.groupedEdges)
  }, [
    isGrouped,
    currentView,
    zoomedGroup,
    originalNodes,
    originalEdges,
    groupingResult,
  ])

  // Handle node clicks for grouping functionality
  const handleNodeClick = (nodeId: string, nodeData: any) => {
    if (nodeData?.isBackButton) {
      // Return to grouped view
      setZoomedGroup(null)
      return true // Indicate that the click was handled
    }

    if (nodeData?.isGroup && !zoomedGroup) {
      // Zoom into group
      setZoomedGroup(nodeId)
      return true // Indicate that the click was handled
    }

    return false // Let other handlers process the click
  }

  return {
    nodes,
    edges,
    isGrouped,
    currentView,
    zoomedGroup,
    availableKeys,
    groupedEntities: groupingResult.groupedEntities,
    groupedEdges: groupingResult.groupedEdges,
    handleNodeClick,
  }
}

// Helper function to create grouped view
function getGroupedView(groupedEntities: any[], groupedEdges: any[]) {
  const nodes: Node[] = groupedEntities.map((group, index) => ({
    id: group.groupId,
    type: 'groupNode',
    position: calculateGroupPosition(index, groupedEntities.length),
    data: {
      label: group.groupName,
      entityCount: group.entityCount,
      entities: group.entities,
      groupKeys: group.groupKeys,
      aggregatedLabels: group.aggregatedLabels,
      isGroup: true,
    },
    draggable: true,
  }))

  const edges: Edge[] = groupedEdges.map((edge) => ({
    id: edge.id,
    source: edge.sourceGroupId,
    target: edge.targetGroupId,
    type: 'groupEdge',
    data: {
      edgeCount: edge.edgeCount,
      aggregatedData: edge.aggregatedData,
      isGroupEdge: true,
    },
    animated: edge.aggregatedData.isDanger,
  }))

  return { nodes, edges }
}

// Helper function to create zoomed group view
function getZoomedGroupView(
  zoomedGroupId: string,
  originalNodes: Node[],
  originalEdges: Edge[],
  entityToGroupMap: Map<string, string>
) {
  // Get entities that belong to the zoomed group
  const groupEntityIds = new Set<string>()
  entityToGroupMap.forEach((groupId, entityId) => {
    if (groupId === zoomedGroupId) {
      groupEntityIds.add(entityId)
    }
  })

  // Filter original nodes to show only entities in the zoomed group
  const filteredNodes = originalNodes.filter(node => 
    groupEntityIds.has(node.id)
  )

  // Filter original edges to show only edges between entities in the zoomed group
  const filteredEdges = originalEdges.filter(edge => 
    groupEntityIds.has(edge.source) && groupEntityIds.has(edge.target)
  )

  // Add a "back" node to return to grouped view
  const backNode: Node = {
    id: 'back-to-groups',
    type: 'default',
    position: { x: 50, y: 50 },
    data: {
      label: '← Back to Groups',
      isBackButton: true,
    },
    style: {
      background: '#f3f4f6',
      border: '2px solid #d1d5db',
      borderRadius: '8px',
      padding: '8px 16px',
      cursor: 'pointer',
    },
  }

  return {
    nodes: [backNode, ...filteredNodes],
    edges: filteredEdges,
  }
}

// Helper function to calculate group positions
function calculateGroupPosition(index: number, totalGroups: number): { x: number; y: number } {
  // Simple grid layout for groups
  const cols = Math.ceil(Math.sqrt(totalGroups))
  const row = Math.floor(index / cols)
  const col = index % cols
  
  const spacing = 350 // Space between group nodes
  const offsetX = 100
  const offsetY = 100
  
  return {
    x: offsetX + col * spacing,
    y: offsetY + row * spacing,
  }
}
