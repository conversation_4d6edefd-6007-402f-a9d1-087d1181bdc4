# Dynamic Grouping Feature

## Overview

The Dynamic Grouping feature allows users to organize and view their network traffic map based on key-value labels assigned to entities. This feature enables users to group entities by primary and secondary labels, save and load custom views, and personalize the map layout according to their role or preferences.

## Features

### Core Functionality
- **Primary/Secondary Grouping**: Group entities by one or more label keys
- **Saved Views**: Create, save, and manage custom grouping configurations
- **Default Views**: Set a saved view as the default layout that loads automatically
- **Zoom Functionality**: Click on group nodes to zoom in and see individual entities
- **User Persistence**: Views are saved per user and organization

### UI Components
- **Grouped By Button**: Main control for selecting and managing group views
- **Group View Modal**: Interface for creating and managing saved views
- **Group Nodes**: Visual representation of grouped entities on the graph
- **Group Edges**: Aggregated connections between groups

## Architecture

### Data Flow
```
Entity Labels → Grouping Logic → Group Nodes/Edges → ReactFlow Visualization
```

### Key Components

#### 1. Data Layer
- **Types**: `GroupView`, `GroupedEntity`, `GroupedEdge` (in `globalTypes/ndrBenchmarkTypes/groupViews/`)
- **Firestore Queries**: CRUD operations for group views (in `src/firestoreQueries/ndr/groupViews/`)
- **React Query Hooks**: Data fetching and caching (in `src/firestoreQueries/ndr/groupViews/hooks/`)

#### 2. State Management
- **Zustand Store**: `useGroupViewStore` for managing current grouping state
- **Preferences**: Local storage for user preferences and session persistence

#### 3. Business Logic
- **Grouping Utils**: Core algorithms for grouping entities and aggregating edges
- **Integration Hooks**: Adapters for connecting with existing graph system

#### 4. UI Components
- **GroupedByButton**: Main control interface
- **GroupViewModal**: Management interface with tabs for new views and saved views
- **GroupNode**: Custom ReactFlow node for displaying groups
- **GroupEdge**: Custom ReactFlow edge for group connections

## Usage

### For Users

#### Creating a Group View
1. Click the "Grouped By" button in the PortFlow header
2. Click the arrow to open the Group View Modal
3. In the "New View" tab:
   - Enter a view name
   - Select a primary label key (required)
   - Optionally select a secondary label key
   - Add additional keys if needed
   - Check "Set as default view" if desired
4. Click "Save View"

#### Using Saved Views
1. Click the "Grouped By" dropdown
2. Select from available saved views
3. The graph will re-render with the selected grouping

#### Managing Views
1. Open the Group View Modal
2. Go to the "Saved Views" tab
3. Use the actions menu to:
   - Apply a view
   - Set as default
   - Delete a view

#### Zooming into Groups
1. Click on any group node in the graph
2. The view will zoom to show individual entities within that group
3. Click the "← Back to Groups" button to return to the grouped view

### For Developers

#### Adding New Label Keys
Label keys are automatically extracted from entity labels. To add new grouping options:
1. Ensure entities have the appropriate labels assigned
2. The keys will automatically appear in the grouping interface

#### Extending Grouping Logic
To modify how entities are grouped:
1. Update the `groupEntitiesByLabels` function in `src/utils/groupViewUtils.ts`
2. Modify the `GroupedEntity` type if new properties are needed

#### Customizing Group Visualization
To change how groups are displayed:
1. Modify the `GroupNode` component in `src/views/NDR/PortflowPage/components/GroupNode.tsx`
2. Update the `GroupEdge` component for edge visualization

## API Reference

### Core Types

```typescript
interface GroupView {
  id: string
  name: string
  primaryKey: string
  secondaryKey?: string
  additionalKeys?: string[]
  isDefault: boolean
  createdAt: Date
  updatedAt: Date
  userId: string
  organizationId: string
}

interface GroupedEntity {
  groupId: string
  groupName: string
  groupKeys: Record<string, string>
  entities: string[]
  entityCount: number
  aggregatedLabels: Record<string, string[]>
}
```

### Key Hooks

```typescript
// Data fetching
useGetUserGroupViews(organizationId, userId)
useGetDefaultGroupView(organizationId, userId)

// Mutations
useCreateGroupView(organizationId, userId)
useUpdateGroupView(organizationId, userId)
useDeleteGroupView(organizationId)

// State management
useGroupViewStore()

// Integration
usePortflowGroupingAdapter(props)
```

### Utility Functions

```typescript
// Core grouping logic
getAvailableLabelKeys(entities, entityLabels)
groupEntitiesByLabels(entities, entityLabels, primaryKey, secondaryKey?, additionalKeys?)
aggregateEdgesBetweenGroups(trafficPatterns, entityToGroupMap)

// Helper functions
createEntityToGroupMap(groupedEntities)
searchLabelKeys(keys, searchTerm)
```

## Configuration

### Firestore Structure
```
organizations/{orgId}/dashboards/NDR/groupViews/{viewId}
```

### Local Storage
User preferences are stored in localStorage with the key:
```
groupViewPreferences_{organizationId}_{userId}_v{version}
```

## Testing

### Unit Tests
- `src/utils/__tests__/groupViewUtils.test.ts`: Core grouping logic
- `src/firestoreQueries/ndr/groupViews/hooks/__tests__/useGroupViews.test.ts`: React Query hooks

### Integration Tests
Tests cover the complete flow from creating views to applying them to the graph.

### Manual Testing Checklist
- [ ] Create a new group view
- [ ] Apply saved views from dropdown
- [ ] Set a view as default
- [ ] Zoom into group nodes
- [ ] Delete saved views
- [ ] Verify persistence across sessions

## Performance Considerations

### Optimization Strategies
1. **Lazy Loading**: Group views are only fetched when grouping is enabled
2. **Memoization**: Grouping calculations are memoized to prevent unnecessary recalculations
3. **Efficient Rendering**: Only visible group nodes are rendered in detail
4. **Caching**: React Query provides automatic caching of group view data

### Scalability
- The system can handle thousands of entities efficiently
- Group calculations are optimized for large datasets
- Edge aggregation uses efficient algorithms to prevent performance issues

## Troubleshooting

### Common Issues

#### Groups Not Appearing
- Verify entities have the required labels
- Check that label keys exist in the system
- Ensure the grouping view is properly saved and applied

#### Performance Issues
- Check the number of entities being grouped
- Verify that grouping calculations are properly memoized
- Consider reducing the number of visible groups

#### Persistence Issues
- Check localStorage for saved preferences
- Verify Firestore permissions for group views
- Ensure user authentication is working correctly

### Debug Tools
- Use React DevTools to inspect component state
- Check the browser console for grouping-related logs
- Use the Network tab to verify Firestore operations

## Future Enhancements

### Planned Features
1. **Advanced Filtering**: Filter groups based on criteria
2. **Custom Layouts**: Different layout algorithms for grouped views
3. **Export/Import**: Share group views between users
4. **Analytics**: Track usage of different grouping strategies
5. **Nested Grouping**: Support for hierarchical grouping

### Extension Points
- Custom grouping algorithms
- Additional visualization options
- Integration with external labeling systems
- Advanced aggregation strategies
